@import '../../Colors.less';

.privacyContainer {
  position: absolute;
  padding-bottom: 100px;
  scroll-behavior: smooth;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10000;
  background-color: @white-hex;
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  li,
  p {
    margin: 0 0 16px;
  }
  h1 {
    font-size: 40px;
    line-height: 60px;
  }
  h1,
  h2 {
    font-weight: 700;
  }
  h2 {
    font-size: 32px;
    line-height: 48px;
  }
  h3 {
    font-size: 24px;
    line-height: 36px;
  }
  h3,
  h4 {
    font-weight: 700;
  }
  h4 {
    font-size: 20px;
    line-height: 30px;
  }
  h5,
  h6 {
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
  }
  .visible {
    display: block;
  }
  .hidden {
    display: none;
  }
  .page {
    width: 100%;
  }
  .container {
    position: relative;
    width: 90%;
    max-width: 1024px;
    margin: 0 auto;
  }
  .header {
    background: @dark-gray-hex;
    color: @white-hex;
    padding: 20px;
    margin: 0 0 16px;
    padding-left: 10%;
  }
  .headerup {
    background: @white-hex;
    padding: 20px;
  }
  .header .title {
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
    margin: 0;
  }

  .content {
    padding-bottom: 32px;
  }
}
