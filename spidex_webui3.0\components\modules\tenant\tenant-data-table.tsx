"use client";

import { useMemo, useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  UserCheck,
  UserX,
  MapPin,
  Building,
  Tag,
} from "lucide-react";
import { Tenant, TenantPaginationParams, TenantPageSize } from "@/types/tenant";
import { TenantTablePagination } from "./tenant-table-pagination";
import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface TenantDataTableProps {
  data: Tenant[];
  pagination: TenantPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: TenantPageSize[];
  onEdit: (tenant: Tenant) => void;
  onDelete: (tenantId: string) => void;
  onToggleActive: (tenantId: string, active: boolean) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: TenantPageSize) => void;
}

export function TenantDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onEdit,
  onDelete,
  onToggleActive,
  onPageChange,
  onPageSizeChange,
}: TenantDataTableProps) {
  const [tenantToDelete, setTenantToDelete] = useState<Tenant | null>(null);
  const [forceRender, setForceRender] = useState(0);

  const handleDeleteClick = (tenant: Tenant) => {
    // Force a clean state first
    setTenantToDelete(null);
    // Then set the new tenant after a brief delay
    setTimeout(() => {
      setTenantToDelete(tenant);
    }, 10);
  };

  const handleDeleteConfirm = () => {
    if (tenantToDelete) {
      onDelete(tenantToDelete.id);
    }
    setTenantToDelete(null);
    // Force re-render to ensure clean state
    setForceRender((prev) => prev + 1);
  };

  const handleDeleteCancel = () => {
    setTenantToDelete(null);
    // Force re-render to ensure clean state
    setForceRender((prev) => prev + 1);
  };

  const columns: ColumnDef<Tenant>[] = useMemo(
    () => [
      {
        id: "actions",
        header: "Actions",
        cell: ({ row }) => {
          const tenant = row.original;

          if (tenant.deleted) {
            return (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onToggleActive(tenant.id, true)}
                className="h-8 px-2"
              >
                <UserCheck className="h-4 w-4 mr-1" />
                Activate
              </Button>
            );
          }

          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => onEdit(tenant)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onToggleActive(tenant.id, false)}
                  className="text-orange-600"
                >
                  <UserX className="mr-2 h-4 w-4" />
                  Deactivate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => handleDeleteClick(tenant)}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        },
      },
      {
        accessorKey: "name",
        header: "Name",
        cell: ({ row }) => {
          const tenant = row.original;
          return (
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="font-medium">{tenant.name}</div>
                {tenant.deleted && (
                  <div className="text-xs text-muted-foreground">
                    Last modified:{" "}
                    {new Date(tenant.modifiedTime).toLocaleDateString()}
                  </div>
                )}
              </div>
            </div>
          );
        },
      },
      {
        accessorKey: "type",
        header: "Type",
        cell: ({ row }) => {
          const tenant = row.original;
          return (
            <div className="flex items-center gap-2">
              <Tag className="h-4 w-4 text-muted-foreground" />
              <span>{tenant.type}</span>
            </div>
          );
        },
      },
      {
        accessorKey: "orgName",
        header: "Organization",
        cell: ({ row }) => {
          const tenant = row.original;
          return <span>{tenant.orgName}</span>;
        },
      },
      {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => {
          const tenant = row.original;
          if (tenant.deleted) {
            return (
              <Badge variant="secondary" className="bg-red-100 text-red-800">
                Inactive
              </Badge>
            );
          }
          return (
            <Badge
              variant={tenant.enable ? "default" : "secondary"}
              className={
                tenant.enable
                  ? "bg-green-100 text-green-800"
                  : "bg-yellow-100 text-yellow-800"
              }
            >
              {tenant.enable ? "Enabled" : "Disabled"}
            </Badge>
          );
        },
      },
      {
        accessorKey: "coordinates",
        header: "GPS",
        cell: ({ row }) => {
          const tenant = row.original;
          if (tenant.latitude && tenant.longitude) {
            return (
              <div className="flex items-center gap-1 text-sm">
                <MapPin className="h-3 w-3 text-muted-foreground" />
                <span>
                  {tenant.latitude.toFixed(4)}, {tenant.longitude.toFixed(4)}
                </span>
              </div>
            );
          }
          return (
            <span className="text-muted-foreground text-sm">
              No coordinates
            </span>
          );
        },
      },
    ],
    [onEdit, onToggleActive]
  );

  return (
    <>
      <DataTable
        columns={columns}
        data={data}
        showPagination={false}
        rowClassName={(row) => (row.deleted ? "opacity-50" : "")}
      />

      {/* Pagination at bottom of table */}
      <TenantTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        key={`delete-dialog-${forceRender}`}
        open={!!tenantToDelete}
        onOpenChange={(open) => {
          if (!open) {
            handleDeleteCancel();
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will delete the tenant "{tenantToDelete?.name}". This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
