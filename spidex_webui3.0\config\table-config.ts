/**
 * Table Configuration
 * 
 * Centralized configuration for table pagination and display settings
 * across the application. This allows for easy maintenance and updates
 * to table behavior without modifying individual components.
 */

export interface TableConfig {
  /** Default page size for tables */
  defaultPageSize: number;
  /** Available page size options for users to select */
  availablePageSizes: number[];
  /** Maximum number of records to fetch at once */
  maxFetchSize: number;
}

export interface PageSpecificTableConfig {
  /** Page identifier */
  pageId: string;
  /** Page-specific table configuration */
  config: TableConfig;
}

/**
 * Default table configuration used across the application
 */
export const DEFAULT_TABLE_CONFIG: TableConfig = {
  defaultPageSize: 25,
  availablePageSizes: [10, 25, 50, 100],
  maxFetchSize: 1000,
};

/**
 * Digital Carpet specific table configuration
 * All Digital Carpet menu items (except Setup Indoor Map) use page size of 10
 */
export const DIGITAL_CARPET_TABLE_CONFIG: TableConfig = {
  defaultPageSize: 10,
  availablePageSizes: [10, 25, 50, 100],
  maxFetchSize: 1000,
};

/**
 * Page-specific table configurations
 * Override default settings for specific pages/components
 */
export const PAGE_SPECIFIC_TABLE_CONFIGS: PageSpecificTableConfig[] = [
  // Digital Carpet Menu Items (page size 10)
  {
    pageId: 'branch-management',
    config: DIGITAL_CARPET_TABLE_CONFIG,
  },
  {
    pageId: 'location-management',
    config: DIGITAL_CARPET_TABLE_CONFIG,
  },
  {
    pageId: 'area-management',
    config: DIGITAL_CARPET_TABLE_CONFIG,
  },
  {
    pageId: 'gateway-management',
    config: DIGITAL_CARPET_TABLE_CONFIG,
  },
  {
    pageId: 'tag-management',
    config: DIGITAL_CARPET_TABLE_CONFIG,
  },
  {
    pageId: 'asset-management',
    config: DIGITAL_CARPET_TABLE_CONFIG,
  },
  {
    pageId: 'tagged-asset-management',
    config: DIGITAL_CARPET_TABLE_CONFIG,
  },
  {
    pageId: 'onboard-asset-management',
    config: DIGITAL_CARPET_TABLE_CONFIG,
  },
  {
    pageId: 'onboard-gateway-management',
    config: DIGITAL_CARPET_TABLE_CONFIG,
  },
  
  // Other pages can use default or custom configurations
  // Example:
  // {
  //   pageId: 'user-management',
  //   config: {
  //     defaultPageSize: 50,
  //     availablePageSizes: [25, 50, 100, 200],
  //     maxFetchSize: 2000,
  //   },
  // },
];

/**
 * Get table configuration for a specific page
 * @param pageId - The identifier for the page/component
 * @returns TableConfig object with appropriate settings
 */
export function getTableConfig(pageId: string): TableConfig {
  const pageConfig = PAGE_SPECIFIC_TABLE_CONFIGS.find(
    (config) => config.pageId === pageId
  );
  
  return pageConfig ? pageConfig.config : DEFAULT_TABLE_CONFIG;
}

/**
 * Get default page size for a specific page
 * @param pageId - The identifier for the page/component
 * @returns Default page size number
 */
export function getDefaultPageSize(pageId: string): number {
  return getTableConfig(pageId).defaultPageSize;
}

/**
 * Get available page sizes for a specific page
 * @param pageId - The identifier for the page/component
 * @returns Array of available page size options
 */
export function getAvailablePageSizes(pageId: string): number[] {
  return getTableConfig(pageId).availablePageSizes;
}

/**
 * Get maximum fetch size for a specific page
 * @param pageId - The identifier for the page/component
 * @returns Maximum number of records to fetch
 */
export function getMaxFetchSize(pageId: string): number {
  return getTableConfig(pageId).maxFetchSize;
}

/**
 * Check if a page should use Digital Carpet table configuration
 * @param pageId - The identifier for the page/component
 * @returns True if page uses Digital Carpet config
 */
export function isDigitalCarpetPage(pageId: string): boolean {
  const digitalCarpetPages = [
    'branch-management',
    'location-management', 
    'area-management',
    'gateway-management',
    'tag-management',
    'asset-management',
    'tagged-asset-management',
    'onboard-asset-management',
    'onboard-gateway-management',
  ];
  
  return digitalCarpetPages.includes(pageId);
}

/**
 * Update table configuration for a specific page
 * This function can be used to dynamically update configurations
 * @param pageId - The identifier for the page/component
 * @param newConfig - New table configuration
 */
export function updateTableConfig(pageId: string, newConfig: Partial<TableConfig>): void {
  const existingConfigIndex = PAGE_SPECIFIC_TABLE_CONFIGS.findIndex(
    (config) => config.pageId === pageId
  );
  
  if (existingConfigIndex >= 0) {
    // Update existing configuration
    PAGE_SPECIFIC_TABLE_CONFIGS[existingConfigIndex].config = {
      ...PAGE_SPECIFIC_TABLE_CONFIGS[existingConfigIndex].config,
      ...newConfig,
    };
  } else {
    // Add new configuration
    PAGE_SPECIFIC_TABLE_CONFIGS.push({
      pageId,
      config: {
        ...DEFAULT_TABLE_CONFIG,
        ...newConfig,
      },
    });
  }
}
