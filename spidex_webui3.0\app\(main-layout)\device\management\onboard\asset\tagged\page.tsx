import { Suspense } from "react";
import { Metada<PERSON> } from "next";
import TaggedAssetManagement from "@/components/modules/tagged-asset/tagged-asset-management";
import { Skeleton } from "@/components/ui/skeleton";
import {
  TableSkeleton,
  TAGGED_ASSET_TABLE_COLUMNS,
} from "@/components/ui/table-skeleton";
import { Card, CardContent } from "@/components/ui/card";

export const metadata: Metadata = {
  title: "Tagged Asset Management - Spidex",
  description: "Manage tagged assets for device tracking and asset management",
};

function TaggedAssetPageSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Breadcrumb skeleton */}
      <div className="flex items-center space-x-2">
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-24" />
      </div>

      {/* Header skeleton */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      {/* Stats cards skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-6 border rounded-lg space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-8 w-12" />
        </div>
        <div className="p-6 border rounded-lg space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-8 w-12" />
        </div>
        <div className="p-6 border rounded-lg space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-8 w-12" />
        </div>
      </div>

      {/* Search and filters skeleton */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 w-32" />
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Data Table Skeleton */}
      <Card>
        <CardContent className="p-0">
          <TableSkeleton
            columns={TAGGED_ASSET_TABLE_COLUMNS}
            rows={10}
            showPagination={true}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default function TaggedAssetPage() {
  return (
    <Suspense fallback={<TaggedAssetPageSkeleton />}>
      <TaggedAssetManagement />
    </Suspense>
  );
}
