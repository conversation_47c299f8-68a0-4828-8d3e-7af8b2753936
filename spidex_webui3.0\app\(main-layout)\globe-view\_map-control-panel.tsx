import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import * as React from "react";

// Generic item type for map operations
interface MapItem {
  id: string;
  name: string;
  gpsPoint: {
    latitude: string | number;
    longitude: string | number;
  };
  children: MapItem[];
  [key: string]: any;
}

interface ControlPanelProps {
  parentItem: MapItem | null;
  items: MapItem[];
  onSelectItem: (item: MapItem) => void;
  onBack: () => void;
}

function ControlPanel({
  parentItem,
  items,
  onSelectItem,
  onBack,
}: ControlPanelProps) {
  return (
    <Card className="flex flex-col min-w-72">
      <CardHeader>
        <CardTitle>{parentItem ? parentItem.name : "Branches"}</CardTitle>
      </CardHeader>
      <ScrollArea>
        <CardContent className="overflow-scroll">
          {items.map((item) => (
            <div
              key={item.id}
              className="p-2 cursor-pointer border mt-2 bg-background"
              onClick={() => onSelectItem(item)}
            >
              <small className="text-sm font-medium leading-none">
                {item.name}
              </small>
              <p className="text-sm text-muted-foreground">{item.address}</p>
            </div>
          ))}
        </CardContent>
      </ScrollArea>
      <CardFooter>
        {parentItem && (
          <Button className="w-full" onClick={onBack}>
            Back
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}

export default React.memo(ControlPanel);
