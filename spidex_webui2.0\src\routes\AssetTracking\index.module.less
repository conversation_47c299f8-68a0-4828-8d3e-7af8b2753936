@import '../../Colors.less';

.sliderContainer {
  padding: 10px 0 10px 0;
  margin: 0 20px 20px 20px;
  background: @light-grey2-hex;
}
.sliderContainerCompact {
  padding: 20px;
  margin: 0 20px 20px 20px;
  gap: 20px;
  background: @light-grey2-hex;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: auto;
  white-space: nowrap;
}

.cards {
  overflow: auto;
  white-space: nowrap;
  gap: 10px;
  .assetsNumbers {
    margin-top: 10px;
  }
}

.card {
  display: inline-block;
  text-align: center;
  min-width: 200px;
  cursor: pointer;
  padding: 15px;
  background-color: @white-hex;
}

.selectedCard {
  display: inline-block;
  text-align: center;
  min-width: 200px;
  cursor: pointer !important;
  padding: 15px;
  background-color: @light-sky-blue !important;
}

.historyCard {
  cursor: pointer !important;
}
.historyCardCompactView {
  :global {
    .ant-card-body {
      padding: 12px 0px 12px 0px !important;
    }
  }
}
.table {
  margin: 20px;
}

.searchInput {
  margin: 10px 20px 4px 20px;
}

.tagAsset {
  margin-bottom: 10px !important;
}

.popoverAttr {
  margin-bottom: 10px !important;
}

.navBtn {
  svg {
    font-size: 40px;
  }
}

.deviceInfoContainer {
  padding: 10px;
}

.title {
  margin-top: 10px;
  align-items: baseline;
  display: flex;
}

.commandForm {
  margin-top: 20px !important;
}

.locationSelection {
  width: 180px;
  margin-left: 10px;
  margin-bottom: 10px;
  .ant-select {
    margin-bottom: 0px !important;
  }
}

.collapse {
  :global {
    .ant-collapse-header {
      padding: 0 5px 0 5px !important;
    }
  }
}

.sensorIconStyles {
  height: 20px;
  width: 20px;
  margin-right: 10px;
}
.map {
  height: 50vh !important;
  position: sticky !important;
  position: -webkit-sticky !important;
  top: 0 !important;
}
