import { z } from "zod";

// Base Tagged Asset Schema
const BaseTaggedAssetSchema = z.object({
  assetId: z.string().min(1, { message: "Asset selection is required" }),
  deviceId: z.string().min(1, { message: "Tag selection is required" }),
  status: z.string().default("ACTIVE"),
});

// Create Tagged Asset Schema
export const CreateTaggedAssetSchema = BaseTaggedAssetSchema;

// Update Tagged Asset Schema
export const UpdateTaggedAssetSchema = BaseTaggedAssetSchema.extend({
  id: z.string().min(1, { message: "Tagged Asset ID is required" }),
});

// Tagged Asset Search Schema
export const TaggedAssetSearchSchema = z.object({
  searchTerm: z.string().optional(),
  assetType: z.string().optional(),
  status: z.boolean().optional(),
  provisioned: z.boolean().optional(),
});

// Tagged Asset Pagination Schema
export const TaggedAssetPaginationSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
  searchTerm: z.string().optional(),
  assetType: z.string().optional(),
  status: z.boolean().optional(),
  provisioned: z.boolean().optional(),
  showDeleted: z.boolean().default(false),
});

// Form validation schemas
export const TaggedAssetFormSchema = z.object({
  assetId: z.string().min(1, { message: "Please select an asset" }),
  deviceId: z.string().min(1, { message: "Please select a tag" }),
  status: z.string().default("ACTIVE"),
});

// Export types
export type CreateTaggedAssetFormData = z.infer<typeof CreateTaggedAssetSchema>;
export type UpdateTaggedAssetFormData = z.infer<typeof UpdateTaggedAssetSchema>;
export type TaggedAssetSearchFormData = z.infer<typeof TaggedAssetSearchSchema>;
export type TaggedAssetPaginationData = z.infer<
  typeof TaggedAssetPaginationSchema
>;
export type TaggedAssetFormData = z.infer<typeof TaggedAssetFormSchema>;
