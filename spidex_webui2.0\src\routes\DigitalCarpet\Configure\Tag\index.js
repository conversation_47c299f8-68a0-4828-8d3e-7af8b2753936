import React, { useEffect, useState, useContext } from 'react';
import {
  Select,
  Table,
  Button,
  Form,
  Switch,
  Popconfirm,
  CommonCompactView,
  Drawer,
  Typography,
  Input,
  Upload,
  CommonDrawer,
  message,
  Row,
  Col,
  Checkbox,
  Pagination,
} from '../../../../components';
import {
  getAllTagsByPagination,
  addTag,
  deleteTag,
  updateTag,
  getAllGatewayConfigs,
  addAllTags,
} from '../../../../services';
import Context from '../../../../context';
import { CloudUploadOutlined, DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { buildCommonApiValues, calcDrawerWidth } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import { CRUD, Pages, ModuleNames, MastersPageSizeDefault, MastersPageSizeOptions } from '../../../../constants';
import { Link } from 'react-router-dom';
import { get } from 'lodash';
import s from './index.module.less';

const { Option } = Select;
const { Title } = Typography;

const Tag = () => {
  const [context, setContext] = useContext(Context);
  const [models, setModels] = useState([]);
  const [tags, setTags] = useState([]);
  const [tag, setTag] = useState({});
  const [visible, setVisible] = useState(false);
  const [visibleBulk, setVisibleBulk] = useState(false);
  const [action, setAction] = useState('new');
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [csvArray, setCsvArray] = useState([]);
  const [warningMsg, setWarningMsg] = useState('');

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setTags(tableData) : setTags(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const openAddBulk = () => {
    setVisibleBulk(true);
  };

  const makeActive = (data) => {
    updateTagCall({ ...data, deleted: false });
  };

  const closeAdd = () => {
    setVisible(false);
    form.resetFields();
  };

  const closeAddBulk = () => {
    setVisibleBulk(false);
    form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveTagAction({ ...commonValues, ...values, properties: {} });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateTagAction(values);
      setVisible(false);
    }
  };

  const saveTagAction = async (tag) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addTag(tag)
      .then((res) => {
        const tempData = { ...res.data };
        tempData.status = tempData.status === 'true' ? true : false;
        setTags((state) => [res.data, ...state]);
        message.success('Succesfully Added tag');
        form.resetFields();
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Tag, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const [selectedModel, setSelectedModel] = useState('');
  const saveAllTagsAction = async () => {
    const commonValues = buildCommonApiValues(context.profile);
    const values = csvArray.map((x) => ({
      ...x,
      ...commonValues,
      ownerTenantId: context.profile.tenantId,
      status: x.status.toLowerCase() === 'true' ? true : false,
      modelId: selectedModel,
    }));
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addAllTags(values)
      .then(() => {
        setVisibleBulk(false);
        message.success('Succesfully Added tags');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Tags, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editTagAction = (tag) => {
    form.setFieldsValue({ ...tag });
    setAction('edit');
    setTag(tag);
    setVisible(true);
  };

  const updateTagCall = (values) => {
    const data = { ...tag, ...values, modifiedTime: new Date() };

    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateTag(data)
      .then((res) => {
        setTags((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, {
                ...res.data,
                status: res.data.status === 'true' ? true : false,
              });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated tag');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Tag, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateTagAction = async (values) => {
    updateTagCall(values);
  };

  const setDeleteTagAction = (tagId, visible) => {
    setTags((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === tagId).visible = visible;
      tempData
        .filter((x) => x.id !== tagId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteTagAction = (tagId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteTag(tagId)
      .then(() => {
        setTags((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === tagId).visible = false;
          tempState.find((x) => x.id === tagId).deleted = true;
          return [...state].filter((x) => x.id !== tagId);
        });
        message.success('Succesfully Deleted tag');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Tag, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const [totalTags, setTotaltags] = useState({ items: 0, current: 1, pageSize: MastersPageSizeDefault });
  const onPaginationChange = (e, size) => {
    setTotaltags((ps) => ({ ...ps, current: +e, pageSize: size }));
    init(+e - 1, size);
  };

  const init = async (page = 0, pageSize = 0) => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      try {
        const modelRes = await getAllGatewayConfigs(context.profile.tenantId);
        setModels(modelRes.data.filter((x) => x.spdxModelConfig?.tagConfig));
      } catch (e) {
        console.log(e);
        message.error('Unable to get Device Model details, try again later');
      }
      getAllTagsByPagination(context.profile.tenantId, { page: page, size: pageSize | totalTags.pageSize })
        .then((res) => {
          const tempData = [...res.data.content];
          tempData.forEach((i) => {
            i.status = i.status === 'true' ? true : false;
            i.modelId = i.modelId.toString();
          });
          setTableData(tempData);
          setTags(tempData.filter((x) => x.deleted === false));
          setTotaltags((ps) => ({ ...ps, items: res.data.totalElements }));
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to get Tag details, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
    }
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, []);

  const clearForm = () => {
    form.resetFields();
  };

  const [isTrue, setIsTrue] = useState(true);
  const processCSV = (str) => {
    let isValid = true;
    const allTextLines = str.split(/\r\n|\n/);
    const headers = allTextLines[0].split(',');
    const headersReq = ['name', 'description', 'externalId', 'status'];
    const valid = JSON.stringify(headersReq) === JSON.stringify(headers);
    if (!valid) {
      setWarningMsg('Headers do not match please refer the sample file.');
      setCsvArray([]);
      isValid = false;
    }
    allTextLines.forEach((x) => {
      x.split(',').forEach((y) => {
        if (y === '') {
          setWarningMsg('Missing values in file please check again.');
          isValid = false;
        }
      });
    });
    if (!isValid) {
      setCsvArray([]);
    } else {
      const rows = str.slice(str.indexOf('\n') + 1).split('\n');
      const newArray = rows.map((row) => {
        const values = row.split(',');
        const eachObject = headers.reduce((obj, header, i) => {
          obj[header] = values[i];
          return obj;
        }, {});
        return eachObject;
      });
      setCsvArray(newArray);
    }
    setIsTrue(isValid);
  };

  const columnsBulkTag = [
    { title: <strong> Name </strong>, key: 'name', dataIndex: 'name' },
    { title: <strong> Description </strong>, key: 'description', dataIndex: 'description' },
    { title: <strong> External ID </strong>, key: 'externalId', dataIndex: 'externalId' },
    { title: <strong> Status </strong>, key: 'status', dataIndex: 'status' },
  ];

  const tableCols = [
    { title: <strong> Name </strong>, key: 'name', dataIndex: 'name' },
    { title: <strong> Description </strong>, key: 'description', dataIndex: 'description' },
    { title: <strong> External ID </strong>, key: 'externalId', dataIndex: 'externalId' },
    { title: <strong> Model ID </strong>, key: 'modelId', dataIndex: 'modelId' },
    {
      title: <strong> Status </strong>,
      key: 'status',
      render: (record) => <>{record.status ? 'True' : 'False'}</>,
    },
    {
      title: <strong> Actions </strong>,
      width: 310,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <PermissionContainer page={Pages.TAG} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                  Activate
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified by {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.TAG} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editTagAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.TAG} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete tag ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteTagAction(record.id)}
                onCancel={() => setDeleteTagAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteTagAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const tagBreadcrumbsName = get(context.tenantProfile, `moduleNames[${Pages.TAG}].displayName`, ModuleNames.TAG);

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.CONFIGURE, tagBreadcrumbsName]} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.TAG} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Tag
            </Button>
          </PermissionContainer>
          <PermissionContainer page={Pages.TAG} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAddBulk}>
              Bulk Tags <CloudUploadOutlined />
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="id"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={tags}
          rowClassName={(record) => record.deleted && 'rowInactive'}
          pagination={{
            showSizeChanger: true,
            defaultPageSize: MastersPageSizeDefault,
            pageSizeOptions: MastersPageSizeOptions,
            onChange: onPaginationChange,
            current: totalTags.current,
            total: totalTags.items,
          }}
        />
      ) : (
        <>
          <CommonCompactView
            data={tags}
            onEdit={editTagAction}
            onDelete={deleteTagAction}
            permissions={[
              { pageName: Pages.TAG, permission: CRUD.UPDATE, label: 'Edit' },
              { pageName: Pages.TAG, permission: CRUD.DELETE, label: 'Delete' },
            ]}
            title="name"
            dataList={[
              { label: 'Discription', value: 'description' },
              { label: 'Status', value: 'status', type: 'boolean' },
            ]}
          />
          <Row justify="end">
            <Col>
              <div className="m-2">
                <Pagination
                  onChange={onPaginationChange}
                  current={totalTags.current}
                  total={totalTags.items}
                  showSizeChanger={false}
                />
              </div>
            </Col>
          </Row>
        </>
      )}

      <CommonDrawer title="Tag" visible={visible} closeAdd={closeAdd}>
        <Form
          scrollToFirstError={true}
          layout="horizontal"
          initialValues={{}}
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          <Form.Item
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input tag Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Tag Name" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Description"
            name="description"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input tag description!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Tag Description" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Model ID"
            name="modelId"
            rules={[
              {
                required: true,
                message: 'Please select model!',
              },
            ]}
          >
            <Select placeholder="Tag Model">
              {models.map((b) => (
                <Option title={b.modelName} key={b.modelId} value={b.modelId}>
                  {b.modelName}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            hasFeedback
            label="External ID"
            name="externalId"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input tag external ID!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Tag External ID" />
          </Form.Item>
          <Form.Item
            required={false}
            name="status"
            valuePropName="checked"
            label="Status"
            initialValue={false}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Switch />
          </Form.Item>
          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>

      <Drawer
        width={calcDrawerWidth(true)}
        className="commonDrawer"
        onClose={closeAddBulk}
        bodyStyle={{ padding: 0 }}
        visible={visibleBulk}
        getContainer={false}
      >
        <Title className="title">Bulk Tags</Title>
        <Row justify="start" gutter={6} className="content">
          <Col>
            <Link to="/sample_format_for_tag_upload.csv" target="_blank" download>
              <Button type="link">
                Sample File
                <DownloadOutlined />
              </Button>
            </Link>
          </Col>
          <Col>
            <Upload
              accept=".csv"
              multiple={false}
              maxCount={1}
              beforeUpload={(file) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                  processCSV(e.target.result);
                };
                reader.readAsText(file);
                return false;
              }}
            >
              <Button icon={<UploadOutlined />}> Upload</Button>
            </Upload>
          </Col>
          <Col>
            <h4 className={s.warningInfo}>{!isTrue && warningMsg}</h4>
          </Col>
          <Col>
            <Select
              showSearch
              allowClear
              placeholder="Tag Model"
              onSelect={(e) => setSelectedModel(e)}
              onClear={() => setSelectedModel('')}
              style={{ width: context.isCompact ? '100%' : 200 }}
            >
              {models.map((b) => (
                <React.Fragment key={b.modelId}>
                  {b.modelName !== null && (
                    <Option title={b.modelName} key={b.modelId} value={b.modelId}>
                      {b.modelName}
                    </Option>
                  )}
                </React.Fragment>
              ))}
            </Select>
          </Col>
        </Row>
        <div className={s.tableContainer}>
          <Table
            size="small"
            scroll={{ x: true }}
            rowKey="externalId"
            loading={context.isLoading}
            columns={columnsBulkTag}
            dataSource={csvArray}
            rowClassName={(record) => record.deleted && 'rowInactive'}
            className="m-4"
          />
          <Row gutter={10} className={`${s.btnContainer} m-3`}>
            <Col lg={{ span: 4 }}>
              <Button block type="primary" onClick={saveAllTagsAction} disabled={selectedModel === ''}>
                Create Bulk Tags
              </Button>
            </Col>
            <Col lg={{ span: 4 }}>
              <Button block danger type="primary" onClick={closeAddBulk}>
                Close
              </Button>
            </Col>
          </Row>
        </div>
      </Drawer>
    </>
  );
};

export default Tag;
