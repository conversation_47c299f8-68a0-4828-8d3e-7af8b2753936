@import './Colors.less';

@import (css) url('https://fonts.googleapis.com/css2?family=Roboto&display=swap');
@import (css) url('https://fonts.googleapis.com/css2?family=Montserrat&display=swap');
@import (css) url('https://fonts.googleapis.com/css2?family=Lato&display=swap');
@import (css) url('https://fonts.googleapis.com/css2?family=Poppins&display=swap');
@import (css) url('https://fonts.googleapis.com/css2?family=Arima&display=swap');
@import (css) url('https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap');
@import (css) url('https://fonts.googleapis.com/css2?family=Prompt&display=swap');

.ant-layout {
  width: 100vw;
}
.selectedMenuItem {
  .ant-menu-item-selected a,
  .ant-menu-item-selected a:hover {
    color: @white-hex !important;
  }
}
:global {
  .ant-menu-inline .ant-menu-item:not(:last-child) {
    margin-bottom: 0 !important;
  }
  .ant-menu-inline .ant-menu-item {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
  }
  .ant-btn {
    border-radius: 4px !important;
  }
  .compactMenuLogo {
    img {
      height: 30px;
      width: 100%;
      max-width: 200px;
    }
    .compactMenu {
      margin-top: 20px;
    }
  }
}
.compactViewLogo {
  max-height: 28px !important;
  max-width: 150px !important;
}
.siteLayoutBackgroundLogin {
  background-color: @white-hex;
  display: flex;
  align-items: center;
  border-radius: 1em;
  overflow-y: auto;
  .ant-breadcrumb {
    padding: 0 0 10px 0;
  }
}
.siteLayoutBackground {
  background-color: @white-hex;
  padding: 10px;
  border-radius: 1em;
  margin: 0 5px 5px 0;
  overflow-y: auto;
  .ant-breadcrumb {
    padding: 0 0 10px 0;
  }
}
.siteLayoutBackgroundMarginLeft {
  margin-left: 5px;
}
.headerDropdown {
  display: flex;
  align-items: center;
  justify-content: center;
}
.ant-layout-header,
header {
  border-radius: 1em;
  margin: 5px 5px 5px 5px;
  padding: 0 10px 0 10px !important;
  background-color: @white-hex !important;
  box-shadow: 5px 3px @solitude-hex;
  z-index: 10;
  .logo {
    float: left;
    img {
      margin-left: 10px;
      max-width: 200px;
      max-height: 28px;
    }
  }
  .compactLogo {
    float: left;
    img {
      margin-left: 10px;
      max-width: 80px;
    }
  }
  .ant-menu {
    background-color: @white-hex !important;
  }
  li {
    color: @black-hex !important;
  }
}
:global {
  .siteLayoutBackground {
    animation: fadeInUp;
    animation-duration: 1s;
  }
  .ant-menu-submenu-vertical:hover {
    div {
      span {
        font-size: 24px !important;
      }
    }
  }
  .ant-menu-inline-collapsed {
    .ant-menu-item:hover {
      span {
        font-size: 24px !important;
      }
    }
  }

  .ant-layout-header,
  header {
    animation: fadeInDown;
    animation-duration: 2s;
  }
}
.ant-layout-sider {
  animation: fadeInLeft;
  animation-duration: 2s;
  overflow-y: auto !important;
  background-color: @white-hex !important;
  border-radius: 1em;
  margin: 5px;
  .ant-layout-sider-children {
    border-right: 2px solid @solitude-hex !important;
  }
  .ant-menu-inline {
    border: none !important;
  }
}
.scrollbarSidebar {
  height: calc(100vh - 160px) !important;
  background: @white-hex;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}
.scrollbarSidebarCompactView {
  height: calc(100vh - 200px) !important;
  background: @white-hex;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}
.compactSliderIcon {
  color: @black-hex !important;
}
.avatar {
  color: @black-hex !important;
  margin: 0 !important;
  padding: 0 !important;
}
.avatarIcon {
  padding-right: 5px;
  color: @black-hex !important;
}
.spinner {
  color: @primary-color-hex !important;
}
.profile {
  text-align: right;
}

.commonAddButton {
  margin-bottom: 5px;
  color: @blue-hex;
}
.commonAddButton:hover {
  color: @blue2-hex;
}

.commonSaveButton {
  background-color: @blue-hex;
  border-color: @blue-hex;
}
.commonSaveButton:hover {
  background-color: @blue2-hex;
  border-color: @blue2-hex;
}

.content {
  padding: 24px !important;
  margin-top: 90px;
  z-index: -1;
}

.locationSelector {
  width: 31px;
  cursor: pointer;
}

.geoLocation {
  margin-bottom: -24px;
}

.footer {
  button {
    margin-top: 10px;
  }
}
.commonDrawer {
  .ant-drawer-close {
    margin-right: 10px !important;
    position: absolute;
    z-index: 1000 !important;
    right: 0;
    top: 30px;
    .anticon-close {
      color: @primary-color-hex !important;
      padding: 8px !important;
    }
  }
  .ant-drawer-content {
    background-color: transparent !important;
  }
  .ant-drawer-body {
    padding: 20px;
    background-color: @white-hex !important;
  }
  .title {
    position: fixed;
    font-size: 1.5rem;
    font-weight: bold;
    width: 100%;
    z-index: 10;
    background: @white-hex;
    border-bottom: 5px solid @solitude-hex;
    color: @primary-color-hex !important;
    padding: 20px !important;
  }
}

.clearButton {
  color: @white-hex !important;
  background-color: @warning-hex !important;
  border-color: @warning-hex !important;
}
.clearButton:hover {
  color: @white-hex !important;
  border-color: @warning-hover-hex !important;
  background-color: @warning-hover-hex !important;
}
.googleButton {
  color: @white-hex !important;
  background-color: @google-button-hex !important;
  border-color: @google-button-hex !important;
}
.googleButton:hover {
  color: @white-hex !important;
  border-color: @google-button-hover-hex !important;
  background-color: @google-button-hover-hex !important;
}

.cardCustomHeader {
  :global {
    .ant-card-head {
      background-color: @solitude2-hex !important;
    }
  }
}

.compactRow {
  width: 100%;
}

.compactRowDel {
  width: 100%;
  background-color: @light-grey-hex;
}

.compactViewMore {
  border: none !important;
  color: @primary-color-hex !important;
}

.rowInactive {
  background-color: @light-grey-hex;
  color: @disabled-grey-hex;
  a {
    color: @disabled-grey-hex;
    cursor: text;
    pointer-events: none;
  }
  a:hover {
    color: @disabled-grey-hex;
  }
  a:focus {
    color: @disabled-grey-hex;
  }
  a:active {
    color: @disabled-grey-hex;
  }
}

.lastModifiedDate {
  color: @disabled-grey-hex;
  padding-left: 15px;
  padding-top: 5px;
  padding-bottom: 5px;
  margin-bottom: 0;
}

.bottomContainer {
  min-height: 70vh;
  .pageNotFound {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .pageNotFoundInner {
    width: calc(100vw - 250);
    height: calc(100vh - 100);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.leftPaneBottom {
  width: 249px;
  position: fixed;
  background-color: @white-hex;
  bottom: 5px;
  border-radius: 0 0 10px 10px;
  span {
    font-weight: 600;
    font-size: 10px;
  }
}
.leftPaneBottomCompactView {
  width: 100vw;
  position: absolute;
  background-color: @white-hex;
  bottom: 5px;
  border-radius: 0 0 10px 10px;
  span {
    font-weight: 600;
    font-size: 10px;
  }
}
.leftPaneMenu:last-child {
  margin-bottom: 50px;
}

.infoDrawer {
  :global {
    .ant-drawer-close {
      margin-right: 10px !important;
      position: absolute;
      z-index: 1000 !important;
      right: 0;
      top: 30px;
      .anticon-close {
        color: @primary-color-hex !important;
        padding: 8px !important;
      }
    }
    .ant-drawer-content {
      background-color: transparent !important;
    }
    .ant-drawer-body {
      padding: 20px;
      background-color: @white-hex !important;
    }
  }
  .title {
    position: fixed;
    font-size: 1.5rem;
    font-weight: bold;
    width: 100%;
    z-index: 100;
    background: @white-hex;
    border-bottom: 5px solid @solitude-hex;
    color: @primary-color-hex !important;
    padding: 22px !important;
  }
  .infoContainer {
    .info {
      .infoTitle {
        text-align: end;
        margin-right: 10px;
        width: 150px;
      }
    }
    .infoImg {
      img {
        border-radius: 50%;
        width: 150px;
        height: 150px;
      }
      margin-bottom: 20px;
    }
    .subInfoContainer {
      margin-bottom: 20px;
    }
    .editButton {
      margin-top: 20px;
    }
  }
}

.graphContainer {
  margin-top: 50px;
  background-color: @solitude2-hex !important;
  border: 0.1px solid @ant-border-hex !important;
  border-radius: 5px !important;
}
.customH3 {
  text-align: center;
  margin-bottom: 15px;
}
.buttonMargin {
  margin-left: 5px;
}

.lineChartContainer {
  overflow-x: auto;
  width: 100%;
  padding-top: 20px;
  overflow-y: hidden;
}

.searchDiv {
  margin-bottom: 10px;
}

.paginationDiv {
  margin-top: 10px;
  float: right;
  button {
    margin-right: 5px !important;
  }
}

.backBtn {
  background-color: @green-hex !important;
  color: @white-hex !important;
}

.backBtn:hover {
  background-color: @green2-hex !important;
  color: @white-hex !important;
  border-color: @green2-hex !important;
}
//form button
.formButton {
  background-color: @primary-color-theme !important;
  border-color: @primary-color-theme !important;
  color: @white-hex !important;
}
.formButton:hover {
  background-color: fade(@primary-color-theme, 80%) !important;
}
//actions (CRUD)
.actionButton {
  color: @blue-hex !important;
}
//export & download actions
.downloadButton {
  background-color: @blue-hex !important;
  border-color: @blue-hex !important;
  color: @white-hex !important;
}
.promptFontFamily {
  font-family: 'Prompt' !important;
  font-weight: 900 !important;
}
// table header options
.headerBgColor-fafafa {
  .ant-table-thead .ant-table-cell {
    background-color: #fafafa;
  }
}
.headerBgColor-999999 {
  .ant-table-thead .ant-table-cell {
    background-color: #999999;
  }
}
.headerBgColor-ffffff {
  .ant-table-thead .ant-table-cell {
    background-color: #ffffff;
  }
}
.headerBgColor-f44e3b {
  .ant-table-thead .ant-table-cell {
    background-color: #f44e3b;
  }
}
.headerBgColor-fe9200 {
  .ant-table-thead .ant-table-cell {
    background-color: #fe9200;
  }
}
.headerBgColor-fcdc00 {
  .ant-table-thead .ant-table-cell {
    background-color: #fcdc00;
  }
}
.headerBgColor-dbdf00 {
  .ant-table-thead .ant-table-cell {
    background-color: #dbdf00 !important;
  }
}
.headerBgColor-a4dd00 {
  .ant-table-thead .ant-table-cell {
    background-color: #a4dd00;
  }
}
.headerBgColor-68ccca {
  .ant-table-thead .ant-table-cell {
    background-color: #68ccca;
  }
}
.headerBgColor-73d8ff {
  .ant-table-thead .ant-table-cell {
    background-color: #73d8ff;
  }
}
.headerBgColor-aea1ff {
  .ant-table-thead .ant-table-cell {
    background-color: #aea1ff;
  }
}
.headerBgColor-fda1ff {
  .ant-table-thead .ant-table-cell {
    background-color: #fda1ff;
  }
}
.headerBgColor-808080 {
  .ant-table-thead .ant-table-cell {
    background-color: #808080;
  }
}
.headerBgColor-cccccc {
  .ant-table-thead .ant-table-cell {
    background-color: #cccccc;
  }
}
.headerBgColor-d33115 {
  .ant-table-thead .ant-table-cell {
    background-color: #d33115;
  }
}
.headerBgColor-e27300 {
  .ant-table-thead .ant-table-cell {
    background-color: #e27300;
  }
}
.headerBgColor-fcc400 {
  .ant-table-thead .ant-table-cell {
    background-color: #fcc400;
  }
}
.headerBgColor-b0bc00 {
  .ant-table-thead .ant-table-cell {
    background-color: #b0bc00;
  }
}
.headerBgColor-68bc00 {
  .ant-table-thead .ant-table-cell {
    background-color: #68bc00;
  }
}
.headerBgColor-16a5a5 {
  .ant-table-thead .ant-table-cell {
    background-color: #16a5a5;
  }
}
.headerBgColor-009ce0 {
  .ant-table-thead .ant-table-cell {
    background-color: #009ce0;
  }
}
.headerBgColor-7b64ff {
  .ant-table-thead .ant-table-cell {
    background-color: #7b64ff;
  }
}
.headerBgColor-fa28ff {
  .ant-table-thead .ant-table-cell {
    background-color: #fa28ff;
  }
}

.headerTextColor-000000 {
  .ant-table-thead .ant-table-cell {
    color: #000000;
  }
}
.headerTextColor-333333 {
  .ant-table-thead .ant-table-cell {
    color: #333333;
  }
}
.headerTextColor-ffffff {
  .ant-table-thead .ant-table-cell {
    color: #ffffff;
  }
}
.headerTextColor-f8f8ff {
  .ant-table-thead .ant-table-cell {
    color: #f8f8ff;
  }
}
.headerTextColor-696969 {
  .ant-table-thead .ant-table-cell {
    color: #696969;
  }
}
.headerTextColor-808080 {
  .ant-table-thead .ant-table-cell {
    color: #808080;
  }
}
.headerTextColor-a9a9a9 {
  .ant-table-thead .ant-table-cell {
    color: #a9a9a9;
  }
}
.headerTextColor-c0c0c0 {
  .ant-table-thead .ant-table-cell {
    color: #c0c0c0;
  }
}
.headerTextColor-d3d3d3 {
  .ant-table-thead .ant-table-cell {
    color: #d3d3d3;
  }
}
