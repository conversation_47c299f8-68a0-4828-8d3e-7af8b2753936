import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Card,
  Select,
  CommonCompactView,
  Drawer,
  Typography,
  message,
  Row,
  Col,
  Input,
  Space,
  Tooltip,
} from '../../../../components';
import { getAllEventAttrs, getAllTagConfigs, deleteTagConfig, addTagConfig } from '../../../../services';
import Context from '../../../../context';
import { PlusOutlined, MinusCircleOutlined, CodeOutlined, InfoCircleOutlined, CopyFilled } from '@ant-design/icons';
import { calcDrawerWidth, buildCommonApiValues } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import { CRUD, Pages, ModuleNames } from '../../../../constants';
import { get } from 'lodash';
import s from './index.module.less';

const { Paragraph, Title } = Typography;
const { Option } = Select;

const TagConfig = () => {
  const [context, setContext] = useContext(Context);
  const [tagConfigs, setTagConfigs] = useState([]);
  const [eventAttrs, setEventAttrs] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedAttrs, setSelectedAttrs] = useState([]);

  const [form] = Form.useForm();

  const openAdd = () => {
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    setSelectedAttrs([]);
    form.resetFields();
  };

  const onAttributeChange = (e) => {
    setSelectedAttrs(e);
  };

  const clearForm = () => {
    setSelectedAttrs([]);
    form.resetFields();
  };

  const buildDataPayload = (values) => {
    const dataPayload = { ...values };
    const { eventAttrConfigs } = dataPayload.tagConfig;
    dataPayload.spdxModelConfig = {};
    dataPayload.spdxModelConfig.tagConfig = { ...dataPayload.tagConfig };
    delete dataPayload.tagConfig;
    dataPayload.spdxModelConfig.tagConfig.eventAttrConfigs = [];
    dataPayload.spdxModelConfig.gatewayConfig = null;

    for (const [key, value] of Object.entries(eventAttrConfigs)) {
      const attrSelected = eventAttrs.find((x) => x.id === parseInt(key));

      if (!key.includes('characteristics')) {
        dataPayload.spdxModelConfig.tagConfig.eventAttrConfigs.push({
          ...value,
          ...attrSelected,
          attId: attrSelected.attributeId,
          attrName: attrSelected.name,
          id: key,
        });
      } else {
        dataPayload.spdxModelConfig.tagConfig.eventAttrConfigs.forEach((x) => {
          if (x.id === key.split('-')[1]) {
            x.characteristics = value;
          }
        });
      }
    }
    return dataPayload;
  };

  const finishAdd = async () => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    const dataPayload = buildDataPayload({ ...values, ...commonValues });
    await saveTagConfigAction(dataPayload);
    setVisible(false);
    form.resetFields();
  };

  const saveTagConfigAction = async (tag) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addTagConfig(tag)
      .then((res) => {
        setTagConfigs((state) => [res.data, ...state]);
        form.resetFields();
        message.success('Succesfully Added tag config');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Tag Config, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const deleteTagConfigAction = (tagConfigId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteTagConfig(tagConfigId)
      .then(() => {
        setTagConfigs((state) => {
          return [...state].filter((x) => x.id !== tagConfigId);
        });
        message.success('Succesfully Deleted tag config');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Tag Config, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const copyRecord = (record) => {
    if (window.isSecureContext || window.location.hostname === 'localhost') {
      navigator.clipboard.writeText(JSON.stringify(record));
      message.success('Copied to clipboard.');
    } else {
      message.error('Protocol is not a secure context.');
    }
  };

  useEffect(() => {
    const init = async () => {
      if (context.profile.tenantId) {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
        try {
          const eventAttrRes = await getAllEventAttrs(context.profile.tenantId);
          setEventAttrs(eventAttrRes.data.filter((x) => x.deleted === false));
        } catch (e) {
          console.log(e);
          message.error('Unable to get Event Attribute details, try again later');
        }
        getAllTagConfigs(context.profile.tenantId)
          .then((res) => {
            setTagConfigs(res.data.filter((x) => x.spdxModelConfig?.tagConfig));
          })
          .catch((e) => {
            console.log(e);
            message.error('Unable to get Tag Config details, try again later');
          })
          .finally(() => {
            setContext((state) => {
              return {
                ...state,
                isLoading: false,
              };
            });
          });
      }
    };
    init();
    // eslint-disable-next-line
  }, []);

  const tableCols = [
    { title: <strong> ID </strong>, key: 'modelId', dataIndex: 'modelId' },
    { title: <strong> Model Name </strong>, key: 'modelName', dataIndex: 'modelName' },
    {
      title: <strong> Data </strong>,
      key: 'data',
      render: (record) => (
        <span>
          <Tooltip className={s.customTooltip} title={JSON.stringify(record)} placement="bottom">
            <span>
              <CodeOutlined />
            </span>
          </Tooltip>
          <Button type="link" icon={<CopyFilled />} onClick={() => copyRecord(record)} />
        </span>
      ),
    },
    {
      title: <strong> Info </strong>,
      key: 'info',
      render: (record) => (
        <Tooltip
          title={
            <>
              <p>{`Created By: ${record.createdBy}`}</p>
              <p>{`Created At: ${new Date(record.createdTime).toDateString()}`}</p>
            </>
          }
          placement="bottom"
        >
          <span>
            <InfoCircleOutlined />
          </span>
        </Tooltip>
      ),
    },
    {
      title: <strong> Actions </strong>,
      key: 'Actions',
      render: (record) => (
        <PermissionContainer page={Pages.TAG_CONFIG} permission={CRUD.DELETE}>
          <Button type="link" onClick={() => deleteTagConfigAction(record.id)} className="actionButton">
            Delete
          </Button>
        </PermissionContainer>
      ),
    },
  ];

  const tagconfigBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.TAG_CONFIG}].displayName`,
    ModuleNames.TAG_CONFIG
  );

  return (
    <div className={s.tagConfigContainer}>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList
            list={['Home', ModuleNames.DEVICE_CONFIGURE, ModuleNames.CONFIGURE, tagconfigBreadcrumbsName]}
          />
        </Col>
        <Col>
          {!context.isCompact && (
            <PermissionContainer page={Pages.TAG_CONFIG} permission={CRUD.ADD}>
              <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
                Add Tag Config
              </Button>
            </PermissionContainer>
          )}
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="modelId"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={tagConfigs}
        />
      ) : (
        <CommonCompactView
          data={tagConfigs}
          onDelete={deleteTagConfigAction}
          permissions={[{ pageName: Pages.TAG_CONFIG, permission: CRUD.DELETE, label: 'Delete' }]}
          title="modelName"
          dataList={[
            { label: 'Created By', value: 'createdBy' },
            { label: 'Created At', value: 'createdTime', type: 'date' },
          ]}
        />
      )}

      <Drawer
        getContainer={false}
        width={calcDrawerWidth(true)}
        visible={visible}
        className="commonDrawer"
        onClose={closeAdd}
        bodyStyle={{ padding: 0 }}
      >
        <Title className="title">Tag Config</Title>
        <div className="content">
          <Form scrollToFirstError={true} layout="vertical" initialValues={{ deviceType: 'tag' }} form={form}>
            <Row gutter={[4, 4]}>
              <Col span={8}>
                <Card className="cardCustomHeader" title="Basic Details" size="small">
                  <Form.Item
                    hasFeedback
                    label="Model Name"
                    name="modelName"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Model Name!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Model Name" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Model Series"
                    name="modelSeries"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Model Series!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Model Series" />
                  </Form.Item>
                  <Form.Item hasFeedback name="deviceType" hidden>
                    <Input />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Vendor"
                    name="vendor"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Vendor!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Vendor" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="OTA Supported"
                    name="otaSupported"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input OTA Supported!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="OTA Supported" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Warranty Period"
                    name="warrantyPeriod"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Warranty Period!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Warranty Period" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Firmware Version"
                    name="firmwareVersion"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Firmware Version!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Firmware Version" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Event Attributes"
                    name="eventAttributes"
                    rules={[
                      {
                        required: true,
                        message: 'Please input Event Attribute(s) !',
                      },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      placeholder="Event Attribute"
                      onChange={onAttributeChange}
                    >
                      {eventAttrs.map((b) => (
                        <Option title={b.name} key={b.id} value={b.id}>
                          {b.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Card>
              </Col>
              <Col span={8}>
                <Card
                  className={[s.sameColCard, 'cardCustomHeader']}
                  title="Tag Communication Medium Type"
                  size="small"
                >
                  <Form.Item
                    hasFeedback
                    label="Medium ID"
                    name={['tagConfig', 'tagCommMediumType', 'tagCommMedId']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Medium ID !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Medium ID" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Full Name"
                    name={['tagConfig', 'tagCommMediumType', 'fullName']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Full Name !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Full Name" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Short Name"
                    name={['tagConfig', 'tagCommMediumType', 'shortName']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Short Name !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Short Name" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Additional Config"
                    name={['tagConfig', 'tagCommMediumType', 'additionalConfig']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Additional Config !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Additional Config" />
                  </Form.Item>
                </Card>
                <Card title="Media Role Params" size="small" className="cardCustomHeader">
                  <Form.List
                    initialValue={[{ id: null, name: null, dataType: null, value: null }]}
                    name={['tagConfig', 'mediaRoleParams']}
                  >
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map(({ key, name, fieldKey, ...restField }) => (
                          <Space className="customSpace" key={key} size={0} align="baseline">
                            <Form.Item
                              {...restField}
                              name={[name, 'id']}
                              fieldKey={[fieldKey, 'id']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Role ID',
                                },
                              ]}
                            >
                              <Input placeholder="ID" />
                            </Form.Item>
                            <Form.Item
                              {...restField}
                              name={[name, 'name']}
                              fieldKey={[fieldKey, 'name']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Role Name',
                                },
                              ]}
                            >
                              <Input placeholder="Name" />
                            </Form.Item>
                            <Form.Item
                              {...restField}
                              name={[name, 'dataType']}
                              fieldKey={[fieldKey, 'dataType']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Role Data Type',
                                },
                              ]}
                            >
                              <Input placeholder="Data Type" />
                            </Form.Item>
                            <Form.Item
                              {...restField}
                              name={[name, 'value']}
                              fieldKey={[fieldKey, 'value']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Role Value',
                                },
                              ]}
                            >
                              <Input placeholder="Value" />
                            </Form.Item>
                            <MinusCircleOutlined style={{ marginLeft: '5px' }} onClick={() => remove(name)} />
                          </Space>
                        ))}
                        <Form.Item>
                          <Button
                            type="dashed"
                            onClick={() => add()}
                            block
                            className="addMoreButton"
                            icon={<PlusOutlined />}
                          >
                            Add Media Role
                          </Button>
                        </Form.Item>
                      </>
                    )}
                  </Form.List>
                </Card>
              </Col>
              <Col span={8}>
                <Card className={[s.sameColCard, 'cardCustomHeader']} title="Chip Vendor Config" size="small">
                  <Form.Item
                    hasFeedback
                    label="Vendor ID"
                    name={['tagConfig', 'chipVendorConfig', 'vendorId']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Vendor ID !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Vendor ID" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Vendor Name"
                    name={['tagConfig', 'chipVendorConfig', 'name']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Name !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Name" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Interface Protocol"
                    name={['tagConfig', 'chipVendorConfig', 'interfaceProtocol']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Interface Protocol !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Interface Protocol" />
                  </Form.Item>
                </Card>
                <Card title="Manufacturer" size="small" className="cardCustomHeader">
                  <Form.Item
                    hasFeedback
                    label="Tag Manufacturer ID"
                    name={['tagConfig', 'manufacturer', 'tagManfId']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Tag Manufacturer ID !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Tag Manufacturer ID" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Device ID"
                    name={['tagConfig', 'manufacturer', 'deviceId']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Device ID !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Device ID" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Vendor ID"
                    name={['tagConfig', 'manufacturer', 'vendorId']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Vendor ID !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Vendor ID" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Manufacturer Name"
                    name={['tagConfig', 'manufacturer', 'name']}
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Tag Manufacturer Name !',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Manufacturer Name" />
                  </Form.Item>
                </Card>
              </Col>
              <Col span={24}>
                <Card className="cardCustomHeader" title="Event Attributes" size="small">
                  {selectedAttrs.length < 1 ? (
                    <Paragraph type="danger">Select Attributes to configure this section</Paragraph>
                  ) : (
                    <Row gutter={[4, 4]}>
                      {selectedAttrs.map((i) => (
                        <Col key={i} span={12}>
                          <Card
                            className="cardCustomHeader"
                            title={eventAttrs.filter((x) => x.id === i)[0]?.name}
                            size="small"
                          >
                            <Form.Item
                              hasFeedback
                              isListField={true}
                              fieldKey={[`${i}`]}
                              label="Enable"
                              name={['tagConfig', 'eventAttrConfigs', `${i}`, 'enabled']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Enabled !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Enable" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              isListField={true}
                              fieldKey={[`${i}`]}
                              label="Read"
                              name={['tagConfig', 'eventAttrConfigs', `${i}`, 'read']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Read !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Read" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              label="Report"
                              isListField={true}
                              fieldKey={[`${i}`]}
                              name={['tagConfig', 'eventAttrConfigs', `${i}`, 'report']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Report !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Report" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              label="Upper Threshold"
                              isListField={true}
                              fieldKey={[`${i}`]}
                              name={['tagConfig', 'eventAttrConfigs', `${i}`, 'upperThreshold']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Upper Threshold !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Upper Threshold" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              label="Lower Threshold"
                              isListField={true}
                              fieldKey={[`${i}`]}
                              name={['tagConfig', 'eventAttrConfigs', `${i}`, 'lowerThreshold']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Lower Threshold !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Lower Threshold" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              isListField={true}
                              fieldKey={[`${i}`]}
                              label="Read Mode"
                              name={['tagConfig', 'eventAttrConfigs', `${i}`, 'readMode']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Read Mode !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Read Mode" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              isListField={true}
                              fieldKey={[`${i}`]}
                              label="Conn Life Time"
                              name={['tagConfig', 'eventAttrConfigs', `${i}`, 'notifconnlifetime']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Conn Life Time !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Conn Life Time" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              isListField={true}
                              fieldKey={[`${i}`]}
                              label="Report Interval"
                              name={['tagConfig', 'eventAttrConfigs', `${i}`, 'notifreportinterval']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Report Interval !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Report Interval" />
                            </Form.Item>
                            <Form.List
                              initialValue={[
                                {
                                  id: null,
                                },
                              ]}
                              name={['tagConfig', 'eventAttrConfigs', `characteristics-${i}`]}
                            >
                              {(fields, { add, remove }) => (
                                <Row gutter={[4, 4]}>
                                  {fields.map(({ key, name, fieldKey, ...restField }) => (
                                    <Col key={key} span={24}>
                                      <Card className="cardCustomHeader" title="Characteristics" size="small">
                                        <Row>
                                          <Col span={24}>
                                            <Space className="customSpace" size={0} align="baseline">
                                              <Form.Item
                                                {...restField}
                                                name={[name, 'id']}
                                                fieldKey={[fieldKey, 'id']}
                                                rules={[
                                                  {
                                                    required: true,
                                                    whitespace: true,
                                                    message: 'Please input Characteristics ID',
                                                  },
                                                ]}
                                              >
                                                <Input placeholder="ID" />
                                              </Form.Item>
                                              <MinusCircleOutlined
                                                style={{ marginLeft: '5px' }}
                                                onClick={() => remove(name)}
                                              />
                                            </Space>
                                          </Col>
                                          <Col span={24}>
                                            <Card className="cardCustomHeader" title="Operations" size="small">
                                              <Form.List
                                                initialValue={[
                                                  {
                                                    id: null,
                                                    uuid: null,
                                                    handle: null,
                                                  },
                                                ]}
                                                name={[name, 'operations']}
                                              >
                                                {(operationFields, ops) => (
                                                  <Row gutter={[4, 4]}>
                                                    {operationFields.map((formProps) => (
                                                      <Col key={formProps.key} span={24}>
                                                        <Space className="customSpace" size={0} align="baseline">
                                                          <Form.Item
                                                            {...formProps}
                                                            name={[formProps.name, 'uuid']}
                                                            fieldKey={[formProps.fieldKey, 'uuid']}
                                                            rules={[
                                                              {
                                                                required: true,
                                                                whitespace: true,
                                                                message: 'Please input Operations UUID',
                                                              },
                                                            ]}
                                                          >
                                                            <Input placeholder="UUID" />
                                                          </Form.Item>
                                                          <Form.Item
                                                            {...formProps}
                                                            name={[formProps.name, 'id']}
                                                            fieldKey={[formProps.fieldKey, 'id']}
                                                            rules={[
                                                              {
                                                                required: true,
                                                                whitespace: true,
                                                                message: 'Please input Operations ID',
                                                              },
                                                            ]}
                                                          >
                                                            <Input placeholder="ID" />
                                                          </Form.Item>
                                                          <Form.Item
                                                            {...formProps}
                                                            name={[formProps.name, 'handle']}
                                                            fieldKey={[formProps.fieldKey, 'handle']}
                                                            rules={[
                                                              {
                                                                required: true,
                                                                whitespace: true,
                                                                message: 'Please input Operations Handle',
                                                              },
                                                            ]}
                                                          >
                                                            <Input placeholder="Handle" />
                                                          </Form.Item>
                                                          <MinusCircleOutlined
                                                            style={{
                                                              marginLeft: '5px',
                                                            }}
                                                            onClick={() => ops.remove(formProps.name)}
                                                          />
                                                        </Space>
                                                      </Col>
                                                    ))}
                                                    <Col span={12}>
                                                      <Form.Item>
                                                        <Button
                                                          type="dashed"
                                                          onClick={() => ops.add()}
                                                          block
                                                          className="addMoreButton"
                                                          icon={<PlusOutlined />}
                                                        >
                                                          Add
                                                        </Button>
                                                      </Form.Item>
                                                    </Col>
                                                  </Row>
                                                )}
                                              </Form.List>
                                            </Card>
                                          </Col>
                                        </Row>
                                      </Card>
                                    </Col>
                                  ))}
                                  <Col span={24}>
                                    <Row>
                                      <Col span={24}>
                                        <Form.Item>
                                          <Button
                                            type="dashed"
                                            onClick={() => add()}
                                            block
                                            className="addMoreButton"
                                            icon={<PlusOutlined />}
                                          >
                                            Add characteristics (UUID, ID, Handle)
                                          </Button>
                                        </Form.Item>
                                      </Col>
                                    </Row>
                                  </Col>
                                </Row>
                              )}
                            </Form.List>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  )}
                </Card>
              </Col>
            </Row>
            <div className="footer">
              <Row gutter={[4, 4]}>
                <Col span={2}>
                  <Button block className="commonSaveButton formButton" type="primary" onClick={() => finishAdd()}>
                    Save
                  </Button>
                </Col>
                <Col span={2}>
                  <Button block className="clearButton" onClick={() => clearForm()}>
                    Clear
                  </Button>
                </Col>
                <Col span={2}>
                  <Button block danger type="primary" onClick={closeAdd}>
                    Close
                  </Button>
                </Col>
              </Row>
            </div>
          </Form>
        </div>
      </Drawer>
    </div>
  );
};

export default TagConfig;
