import React from "react";
import { useSession } from "next-auth/react";
import {
  format,
  startOfWeek,
  endOfWeek,
  setHours,
  setMinutes,
  setSeconds,
} from "date-fns";
import { Loader2 } from "lucide-react";
import { DateTimePicker } from "@/components/ui/timepicker/datetimepicker";
import { gql, useQuery } from "@apollo/client";
import TimelineWithMap from "./timeline-with-map";

const PROXIMITY_TIMELINE_QUERY = gql`
  query ProximityTimeline(
    $tenantId: String!
    $deviceId: String!
    $fromDate: String!
    $toDate: String!
  ) {
    proximityTimeline(
      tenantId: $tenantId
      deviceId: $deviceId
      fromDate: $fromDate
      toDate: $toDate
    ) {
      eventTime
      dataEventTime
      dataArea
      dwell
      reportedDataTime
      gps {
        latitude
        longitude
      }
    }
  }
`;

export default function AssetTimeline({ deviceId }: { deviceId: string }) {
  const { data: session, status } = useSession();
  const tenantId = session?.user?.tenantId;
  const token = session?.user?.token;
  const [fromDate, setFromDate] = React.useState<Date>(() => {
    const now = new Date();
    const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
    return threeDaysAgo;
  });
  const [toDate, setToDate] = React.useState<Date>(() => {
    return new Date();
  });
  React.useEffect(() => {
    if (fromDate > toDate) {
      setToDate(fromDate);
    }
  }, [fromDate, toDate]);
  const fromDateStr = format(fromDate, "yyyy-MM-dd HH:mm");
  const toDateStr = format(toDate, "yyyy-MM-dd HH:mm");
  const { data, loading, error, refetch } = useQuery(PROXIMITY_TIMELINE_QUERY, {
    variables: {
      tenantId,
      deviceId,
      fromDate: fromDateStr,
      toDate: toDateStr,
    },
    skip: !tenantId || !deviceId || !token || status === "loading",
    context: {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  });
  React.useEffect(() => {
    if (!tenantId || !deviceId || !token || status === "loading") return;
    refetch({ tenantId, deviceId, fromDate: fromDateStr, toDate: toDateStr });
  }, [tenantId, deviceId, token, status, refetch, fromDateStr, toDateStr]);
  return (
    <div className="flex flex-col space-y-4">
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-2">
        <div className="flex flex-row items-center gap-2">
          <label className="text-sm font-medium min-w-[2.5rem]">From</label>
          <DateTimePicker
            value={fromDate}
            onChange={(d) => {
              if (d) setFromDate(d);
            }}
            label=""
            placeholder="From date & time"
          />
        </div>
        <div className="flex flex-row items-center gap-2">
          <label className="text-sm font-medium min-w-[2.5rem]">To</label>
          <DateTimePicker
            value={toDate}
            onChange={(d) => {
              if (d) setToDate(d);
            }}
            label=""
            placeholder="To date & time"
          />
        </div>
      </div>
      {loading ? (
        <div className="flex items-center justify-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : error ? (
        <div>Error loading timeline</div>
      ) : !data?.proximityTimeline?.length ? (
        <div>No history found.</div>
      ) : (
        <TimelineWithMap events={data.proximityTimeline} />
      )}
    </div>
  );
}
