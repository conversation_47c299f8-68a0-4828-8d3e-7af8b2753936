import { Suspense } from "react";
import { Metadata } from "next";
import TagManagement from "@/components/modules/tag/tag-management";

export const metadata: Metadata = {
  title: "Tag Management - Spidex",
  description: "Manage tags for device tracking and asset management",
};

function TagPageSkeleton() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    </div>
  );
}

export default function TagPage() {
  return (
    <Suspense fallback={<TagPageSkeleton />}>
      <TagManagement />
    </Suspense>
  );
}
