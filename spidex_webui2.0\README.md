In the project directory, you can run:

### `1. yarn`

### `2. yarn dev`

Runs the app in the development mode.
Open [http://localhost:3006](http://localhost:3006) to view it in the browser.

### CI / CD

Using Bitbucket pipeline
Runs only when pushed / merged to master branch

### Heroku Deployment

CD currently deploys to Heroku.
Using Webhooks. Heroku Env Vars set in Bit bucket repo config settings

### Tech Stack

React using hooks
React Router Dom
Axios
Ant Design Component Framework
LESS CSS Preprocessor
What 3 Words Integration
Google Map Integration (API created in Google Cloud Console)
Bit Bucket Pipelines

### Known Issues

1. New Gateway Transit is not getting created
2. Tagged Assset Status / Provisioned is not getting updated
3. Update Account returns 200 but new password is not being set

### Running Stand Alone Scripts

Ex (From scripts director):
`node -r esm standAlone/linkRolesPages.js <token> <delete?>`
