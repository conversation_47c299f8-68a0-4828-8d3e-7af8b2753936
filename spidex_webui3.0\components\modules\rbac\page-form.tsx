"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Save, X, RotateCcw } from "lucide-react";
import {
  Page,
  CreatePageFormData,
  UpdatePageFormData,
} from "@/types/rbac";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

const pageFormSchema = z.object({
  pageName: z.string().min(1, "Page name is required").max(100, "Page name must be less than 100 characters"),
  description: z.string().max(500, "Description must be less than 500 characters").optional(),
});

interface PageFormProps {
  page?: Page;
  isLoading?: boolean;
  onSubmit: (data: CreatePageFormData | UpdatePageFormData) => void;
  onCancel: () => void;
  onClear: () => void;
}

export function PageForm({
  page,
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: PageFormProps) {
  const form = useForm<z.infer<typeof pageFormSchema>>({
    resolver: zodResolver(pageFormSchema),
    defaultValues: {
      pageName: page?.pageName || "",
      description: page?.description || "",
    },
  });

  // Update form when page changes
  useEffect(() => {
    if (page) {
      form.reset({
        pageName: page.pageName,
        description: page.description || "",
      });
    }
  }, [page, form]);

  const handleFormSubmit = (values: z.infer<typeof pageFormSchema>) => {
    if (page) {
      // Update existing page
      const formData: UpdatePageFormData = {
        id: page.id,
        pageName: values.pageName,
        description: values.description,
      };
      onSubmit(formData);
    } else {
      // Create new page
      const formData: CreatePageFormData = {
        pageName: values.pageName,
        description: values.description,
      };
      onSubmit(formData);
    }
  };

  const handleClear = () => {
    form.reset({
      pageName: "",
      description: "",
    });
    onClear();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Page Information</CardTitle>
            <CardDescription>
              Enter the basic information for the page
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="pageName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Page Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter page name"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    A unique name for this page (e.g., "user-management", "dashboard")
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter page description (optional)"
                      {...field}
                      disabled={isLoading}
                      rows={3}
                    />
                  </FormControl>
                  <FormDescription>
                    A brief description of what this page does
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={handleClear}
            disabled={isLoading}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Clear Form
          </Button>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? "Saving..." : page ? "Update Page" : "Create Page"}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
