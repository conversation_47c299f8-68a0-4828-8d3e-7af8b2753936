"use client";

import { SessionProvider } from "next-auth/react";

export function ClientSessionProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SessionProvider
      // Disable automatic refetching when tab becomes active
      refetchOnWindowFocus={false}
      // Disable automatic refetching when network reconnects
      refetchWhenOffline={false}
    >
      {children}
    </SessionProvider>
  );
}
