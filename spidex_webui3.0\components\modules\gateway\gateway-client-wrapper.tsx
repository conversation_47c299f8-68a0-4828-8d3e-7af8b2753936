"use client";

import { useState, useEffect } from "react";
import dynamic from "next/dynamic";

// Simple loading component like Branch Management
function GatewayPageLoader() {
  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        <p className="mt-2 text-muted-foreground">Loading...</p>
      </div>
    </div>
  );
}

// Dynamically import the GatewayManagement component with no SSR to prevent hydration issues
const GatewayManagement = dynamic(
  () => import("@/components/modules/gateway/gateway-management"),
  {
    ssr: false,
  }
);

export function GatewayClientWrapper() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <GatewayPageLoader />;
  }

  return (
    <div suppressHydrationWarning>
      <GatewayManagement />
    </div>
  );
}
