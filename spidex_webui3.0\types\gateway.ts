// Gateway Management Types

export interface Gateway {
  id: string;
  name: string;
  description?: string;
  categoryType: string;
  modelId?: string;
  modelName?: string;
  areaId?: string;
  areaName?: string;
  locId?: string;
  locationName?: string;
  branchId?: string;
  branchName?: string;
  tenantId: string;
  deleted: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
  properties?: Record<string, any>;
  // Additional fields from old application
  externalId?: string; // MAC ID
  ownerTenantId?: string;
  provisioned?: boolean;
  parentId?: string;
  locType?: string;
  communicationType?: string;
  gatewayLint?: GatewayLint[];
  coverageMin?: number;
  coverageMax?: number;
  xyzCoordinates?: Record<string, any>;
  virtual?: boolean;
  antenna?: number;
  antennaConfigs?: AntennaConfig[];
  // Computed properties for compatibility
  active?: boolean;
  ipAddress?: string;
  locationId?: string;
  macId?: string;
  createdDate?: string;
  modifiedDate?: string;
}

export interface GatewayLint {
  id?: string;
  areaId: string;
  areaName?: string;
  externalId: string; // MAC ID for lint
  modelId: string;
  antenna?: number;
}

export interface AntennaConfig {
  numericValue?: number;
  name?: string;
}

export interface GatewayOption {
  id: string;
  name: string;
  categoryType: string;
  areaId?: string;
  areaName?: string;
}

// Gateway types from old application
export const GATEWAY_TYPES = {
  FIXED: "fixed",
  TRANSIT: "transit",
  LINT: "lint",
} as const;

export type GatewayType = (typeof GATEWAY_TYPES)[keyof typeof GATEWAY_TYPES];

// Category types
export const CATEGORY_TYPES = {
  FIXED: "fixed",
  TRANSIT: "transit",
} as const;

export type CategoryType = (typeof CATEGORY_TYPES)[keyof typeof CATEGORY_TYPES];

// Gateway Model interface
export interface GatewayModel {
  modelId: string;
  modelName: string;
  deviceType?: string;
  gatewayConfig?: Record<string, any>;
}

// Form data interfaces
export interface CreateGatewayFormData {
  name: string;
  description?: string;
  categoryType: string;
  modelId: string;
  externalId: string; // MAC ID
  areaId?: string; // For fixed/lint gateways
  locId?: string; // For transit gateways
  xyzCoordinates?: {
    additionalProp1?: string;
    additionalProp2?: string;
    additionalProp3?: string;
  };
  gatewayLint?: GatewayLint[]; // For lint type gateways
  coverageMin?: number;
  coverageMax?: number;
  communicationType?: string; // Communication type (BLE, RFID, etc.)
  antenna?: number; // Antenna value for RFID Fixed Reader
  antennaConfigs?: AntennaConfig[]; // Multiple antenna configurations for RFID
}

export interface UpdateGatewayFormData extends CreateGatewayFormData {
  id: string;
}

// Search and filter interfaces
export interface GatewaySearchFilters {
  searchTerm: string;
  categoryType?: string;
  areaId?: string;
  locationId?: string;
  modelId?: string;
  active?: boolean;
}

// Pagination interfaces
export interface GatewayPaginationParams {
  page: number;
  pageSize: number;
}

export type GatewayPageSize = 10 | 25 | 50 | 100;

// Statistics interface
export interface GatewayStats {
  total: number;
  active: number;
  inactive: number;
  fixed: number;
  transit: number;
  lint: number;
}
