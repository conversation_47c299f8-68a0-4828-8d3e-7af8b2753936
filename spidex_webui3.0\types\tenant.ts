// Tenant Management Types

export interface Tenant {
  id: string;
  name: string;
  type: string;
  orgName: string;
  enable: boolean; // enabled/disabled status
  latitude?: number;
  longitude?: number;
  deleted: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
  properties?: Record<string, any>;
}

// Form Data Types
export interface CreateTenantFormData {
  name: string;
  type: string;
  orgName: string;
  enable: boolean;
  latitude?: number;
  longitude?: number;
}

export interface UpdateTenantFormData {
  id: string;
  name: string;
  type: string;
  orgName: string;
  enable: boolean;
  latitude?: number;
  longitude?: number;
  deleted?: boolean;
}

// Search and Pagination Types
export interface TenantSearchFilters {
  searchTerm: string;
  type?: string;
  orgName?: string;
  enable?: boolean;
}

export interface TenantPaginationParams {
  pageNumber: number;
  pageSize: TenantPageSize;
}

export type TenantPageSize = 10 | 50 | 100 | "all";

// API Response Types
export interface TenantApiResponse {
  data: Tenant[];
  totalRecords: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

// Table and UI Types
export interface TenantTableProps {
  data: Tenant[];
  pagination: TenantPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: TenantPageSize[];
  onEdit: (tenant: Tenant) => void;
  onDelete: (tenantId: string) => void;
  onToggleActive: (tenantId: string, active: boolean) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: TenantPageSize) => void;
}
