@import '../../Colors.less';

.loginContainer {
  height: 85vh;
  width: 100vw;
  z-index: 1000000000;
}
.loginForm {
  background-color: @white-hex;
  padding: 20px;
  border-radius: 10px;
}
.logoContainer {
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
}
.footerContainer {
  min-height: 80px;
  z-index: 1000;
  top: calc(100vh - 80px);
  position: absolute;
  display: flex !important;
  width: 100vw;
}
.taggitLoginMainContainer {
  height: 100vh;
  background-color: @light-smoke-gray-hex;
}
.taggitLoginPage {
  background-color: @white-hex;
}
.taggitLoginPageBg {
  background-color: @white-hex;
  height: 100vh;
}
.taggitLogo {
  width: 120px;
}
.taggitImg {
  width: 300px;
}

.taggitCaption {
  color: @black-hex;
}
.taggitDisc {
  border-left: 2px solid @ant-border-hex;
  height: 250px;
}
.taggitDiscLine {
  margin-bottom: 0px;
  text-align: center;
  color: @dark-gray-hex;
}
.taggitDiscTitle {
  text-align: center;
  color: @red-hex;
  font-style: italic;
}

.formField {
  border: none;
  background-color: @grey-light-hex !important;
  color: @black-hex;
  input {
    background-color: @grey-light-hex !important;
    color: @black-hex;
  }
}
.taggitSignInBtn {
  color: @white-hex !important;
  background-color: @black-hex !important;
  width: 120px !important;
}
.taggitForgotPassword {
  padding: 0 !important;
}
.taggitFooterContainer {
  background-color: @white-hex !important;
  min-height: 30px !important;
  top: calc(100vh - 30px);
  position: absolute;
  display: flex !important;
  width: 100vw;
}
.taggitFooterDisc {
  color: @gray-hex;
  text-align: center;

  .footerLinText {
    color: @gray-hex;
  }
}

.particleContainer {
  height: 100vh;
  width: 100vw;
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
}
.particle {
  position: absolute;
  border-radius: 50%;
}
@keyframes particle-animation-1 {
  100% {
    transform: translate3d(63vw, 32vh, 5px);
  }
}
.particle:nth-child(1) {
  animation: particle-animation-1 60s infinite;
  opacity: 0.29;
  height: 7px;
  width: 7px;
  animation-delay: -0.2s;
  transform: translate3d(34vw, 52vh, 30px);
  background: @primary-color-hex;
}
@keyframes particle-animation-2 {
  100% {
    transform: translate3d(37vw, 21vh, 65px);
  }
}
.particle:nth-child(2) {
  animation: particle-animation-2 60s infinite;
  opacity: 0.61;
  height: 6px;
  width: 6px;
  animation-delay: -0.4s;
  transform: translate3d(41vw, 86vh, 35px);
  background: @primary-color-hex;
}
@keyframes particle-animation-3 {
  100% {
    transform: translate3d(88vw, 90vh, 26px);
  }
}
.particle:nth-child(3) {
  animation: particle-animation-3 60s infinite;
  opacity: 0.06;
  height: 8px;
  width: 8px;
  animation-delay: -0.6s;
  transform: translate3d(86vw, 13vh, 9px);
  background: @primary-color-hex;
}
@keyframes particle-animation-4 {
  100% {
    transform: translate3d(15vw, 76vh, 70px);
  }
}
.particle:nth-child(4) {
  animation: particle-animation-4 60s infinite;
  opacity: 0.48;
  height: 7px;
  width: 7px;
  animation-delay: -0.8s;
  transform: translate3d(50vw, 87vh, 93px);
  background: @primary-color-hex;
}
@keyframes particle-animation-5 {
  100% {
    transform: translate3d(29vw, 39vh, 18px);
  }
}
.particle:nth-child(5) {
  animation: particle-animation-5 60s infinite;
  opacity: 0.21;
  height: 10px;
  width: 10px;
  animation-delay: -1s;
  transform: translate3d(89vw, 69vh, 10px);
  background: @primary-color-hex;
}
@keyframes particle-animation-6 {
  100% {
    transform: translate3d(46vw, 33vh, 67px);
  }
}
.particle:nth-child(6) {
  animation: particle-animation-6 60s infinite;
  opacity: 0.66;
  height: 8px;
  width: 8px;
  animation-delay: -1.2s;
  transform: translate3d(52vw, 62vh, 11px);
  background: @primary-color-hex;
}
@keyframes particle-animation-7 {
  100% {
    transform: translate3d(34vw, 17vh, 7px);
  }
}
.particle:nth-child(7) {
  animation: particle-animation-7 60s infinite;
  opacity: 0.49;
  height: 8px;
  width: 8px;
  animation-delay: -1.4s;
  transform: translate3d(43vw, 1vh, 66px);
  background: @primary-color-hex;
}
@keyframes particle-animation-8 {
  100% {
    transform: translate3d(88vw, 69vh, 92px);
  }
}
.particle:nth-child(8) {
  animation: particle-animation-8 60s infinite;
  opacity: 0.7;
  height: 7px;
  width: 7px;
  animation-delay: -1.6s;
  transform: translate3d(83vw, 23vh, 65px);
  background: @primary-color-hex;
}
@keyframes particle-animation-9 {
  100% {
    transform: translate3d(28vw, 87vh, 1px);
  }
}
.particle:nth-child(9) {
  animation: particle-animation-9 60s infinite;
  opacity: 0.18;
  height: 6px;
  width: 6px;
  animation-delay: -1.8s;
  transform: translate3d(23vw, 69vh, 23px);
  background: @primary-color-hex;
}
@keyframes particle-animation-10 {
  100% {
    transform: translate3d(35vw, 63vh, 57px);
  }
}
.particle:nth-child(10) {
  animation: particle-animation-10 60s infinite;
  opacity: 0.14;
  height: 10px;
  width: 10px;
  animation-delay: -2s;
  transform: translate3d(78vw, 28vh, 57px);
  background: @primary-color-hex;
}
@keyframes particle-animation-11 {
  100% {
    transform: translate3d(9vw, 69vh, 38px);
  }
}
.particle:nth-child(11) {
  animation: particle-animation-11 60s infinite;
  opacity: 0.02;
  height: 6px;
  width: 6px;
  animation-delay: -2.2s;
  transform: translate3d(34vw, 27vh, 46px);
  background: @primary-color-hex;
}
@keyframes particle-animation-12 {
  100% {
    transform: translate3d(33vw, 40vh, 34px);
  }
}
.particle:nth-child(12) {
  animation: particle-animation-12 60s infinite;
  opacity: 0.17;
  height: 6px;
  width: 6px;
  animation-delay: -2.4s;
  transform: translate3d(26vw, 74vh, 90px);
  background: @primary-color-hex;
}
@keyframes particle-animation-13 {
  100% {
    transform: translate3d(45vw, 17vh, 100px);
  }
}
.particle:nth-child(13) {
  animation: particle-animation-13 60s infinite;
  opacity: 0.49;
  height: 6px;
  width: 6px;
  animation-delay: -2.6s;
  transform: translate3d(33vw, 66vh, 16px);
  background: @primary-color-hex;
}
@keyframes particle-animation-14 {
  100% {
    transform: translate3d(14vw, 8vh, 99px);
  }
}
.particle:nth-child(14) {
  animation: particle-animation-14 60s infinite;
  opacity: 0.15;
  height: 10px;
  width: 10px;
  animation-delay: -2.8s;
  transform: translate3d(29vw, 46vh, 10px);
  background: @primary-color-hex;
}
@keyframes particle-animation-15 {
  100% {
    transform: translate3d(2vw, 74vh, 36px);
  }
}
.particle:nth-child(15) {
  animation: particle-animation-15 60s infinite;
  opacity: 0.62;
  height: 9px;
  width: 9px;
  animation-delay: -3s;
  transform: translate3d(22vw, 34vh, 31px);
  background: @primary-color-hex;
}
@keyframes particle-animation-16 {
  100% {
    transform: translate3d(31vw, 49vh, 43px);
  }
}
.particle:nth-child(16) {
  animation: particle-animation-16 60s infinite;
  opacity: 0.61;
  height: 6px;
  width: 6px;
  animation-delay: -3.2s;
  transform: translate3d(74vw, 50vh, 96px);
  background: @primary-color-hex;
}
@keyframes particle-animation-17 {
  100% {
    transform: translate3d(76vw, 7vh, 11px);
  }
}
.particle:nth-child(17) {
  animation: particle-animation-17 60s infinite;
  opacity: 0.83;
  height: 10px;
  width: 10px;
  animation-delay: -3.4s;
  transform: translate3d(79vw, 14vh, 91px);
  background: @primary-color-hex;
}
@keyframes particle-animation-18 {
  100% {
    transform: translate3d(81vw, 42vh, 13px);
  }
}
.particle:nth-child(18) {
  animation: particle-animation-18 60s infinite;
  opacity: 0.67;
  height: 8px;
  width: 8px;
  animation-delay: -3.6s;
  transform: translate3d(60vw, 83vh, 16px);
  background: @primary-color-hex;
}
@keyframes particle-animation-19 {
  100% {
    transform: translate3d(4vw, 88vh, 59px);
  }
}
.particle:nth-child(19) {
  animation: particle-animation-19 60s infinite;
  opacity: 0.58;
  height: 7px;
  width: 7px;
  animation-delay: -3.8s;
  transform: translate3d(66vw, 84vh, 11px);
  background: @primary-color-hex;
}
@keyframes particle-animation-20 {
  100% {
    transform: translate3d(33vw, 33vh, 100px);
  }
}
.particle:nth-child(20) {
  animation: particle-animation-20 60s infinite;
  opacity: 0.48;
  height: 8px;
  width: 8px;
  animation-delay: -4s;
  transform: translate3d(88vw, 7vh, 67px);
  background: @primary-color-hex;
}
@keyframes particle-animation-21 {
  100% {
    transform: translate3d(34vw, 21vh, 91px);
  }
}
.particle:nth-child(21) {
  animation: particle-animation-21 60s infinite;
  opacity: 0.31;
  height: 9px;
  width: 9px;
  animation-delay: -4.2s;
  transform: translate3d(27vw, 7vh, 62px);
  background: @primary-color-hex;
}
@keyframes particle-animation-22 {
  100% {
    transform: translate3d(39vw, 40vh, 63px);
  }
}
.particle:nth-child(22) {
  animation: particle-animation-22 60s infinite;
  opacity: 0.97;
  height: 9px;
  width: 9px;
  animation-delay: -4.4s;
  transform: translate3d(73vw, 31vh, 95px);
  background: @primary-color-hex;
}
@keyframes particle-animation-23 {
  100% {
    transform: translate3d(50vw, 65vh, 94px);
  }
}
.particle:nth-child(23) {
  animation: particle-animation-23 60s infinite;
  opacity: 0.95;
  height: 8px;
  width: 8px;
  animation-delay: -4.6s;
  transform: translate3d(81vw, 30vh, 55px);
  background: @primary-color-hex;
}
@keyframes particle-animation-24 {
  100% {
    transform: translate3d(82vw, 51vh, 13px);
  }
}
.particle:nth-child(24) {
  animation: particle-animation-24 60s infinite;
  opacity: 0.12;
  height: 6px;
  width: 6px;
  animation-delay: -4.8s;
  transform: translate3d(58vw, 39vh, 81px);
  background: @primary-color-hex;
}
@keyframes particle-animation-25 {
  100% {
    transform: translate3d(66vw, 35vh, 14px);
  }
}
.particle:nth-child(25) {
  animation: particle-animation-25 60s infinite;
  opacity: 0.37;
  height: 8px;
  width: 8px;
  animation-delay: -5s;
  transform: translate3d(28vw, 16vh, 61px);
  background: @primary-color-hex;
}
@keyframes particle-animation-26 {
  100% {
    transform: translate3d(74vw, 33vh, 39px);
  }
}
.particle:nth-child(26) {
  animation: particle-animation-26 60s infinite;
  opacity: 0.89;
  height: 8px;
  width: 8px;
  animation-delay: -5.2s;
  transform: translate3d(22vw, 24vh, 51px);
  background: @primary-color-hex;
}
@keyframes particle-animation-27 {
  100% {
    transform: translate3d(41vw, 86vh, 16px);
  }
}
.particle:nth-child(27) {
  animation: particle-animation-27 60s infinite;
  opacity: 0.87;
  height: 9px;
  width: 9px;
  animation-delay: -5.4s;
  transform: translate3d(59vw, 10vh, 30px);
  background: @primary-color-hex;
}
@keyframes particle-animation-28 {
  100% {
    transform: translate3d(42vw, 59vh, 2px);
  }
}
.particle:nth-child(28) {
  animation: particle-animation-28 60s infinite;
  opacity: 0.33;
  height: 7px;
  width: 7px;
  animation-delay: -5.6s;
  transform: translate3d(32vw, 60vh, 80px);
  background: @primary-color-hex;
}
@keyframes particle-animation-29 {
  100% {
    transform: translate3d(59vw, 14vh, 28px);
  }
}
.particle:nth-child(29) {
  animation: particle-animation-29 60s infinite;
  opacity: 0.04;
  height: 7px;
  width: 7px;
  animation-delay: -5.8s;
  transform: translate3d(59vw, 52vh, 33px);
  background: @primary-color-hex;
}
@keyframes particle-animation-30 {
  100% {
    transform: translate3d(58vw, 60vh, 87px);
  }
}
.particle:nth-child(30) {
  animation: particle-animation-30 60s infinite;
  opacity: 0.8;
  height: 9px;
  width: 9px;
  animation-delay: -6s;
  transform: translate3d(25vw, 45vh, 14px);
  background: @primary-color-hex;
}
@keyframes particle-animation-31 {
  100% {
    transform: translate3d(55vw, 69vh, 74px);
  }
}
.particle:nth-child(31) {
  animation: particle-animation-31 60s infinite;
  opacity: 0.19;
  height: 6px;
  width: 6px;
  animation-delay: -6.2s;
  transform: translate3d(54vw, 10vh, 72px);
  background: @primary-color-hex;
}
@keyframes particle-animation-32 {
  100% {
    transform: translate3d(43vw, 78vh, 81px);
  }
}
.particle:nth-child(32) {
  animation: particle-animation-32 60s infinite;
  opacity: 0.55;
  height: 10px;
  width: 10px;
  animation-delay: -6.4s;
  transform: translate3d(23vw, 53vh, 86px);
  background: @primary-color-hex;
}
@keyframes particle-animation-33 {
  100% {
    transform: translate3d(90vw, 6vh, 43px);
  }
}
.particle:nth-child(33) {
  animation: particle-animation-33 60s infinite;
  opacity: 0.42;
  height: 6px;
  width: 6px;
  animation-delay: -6.6s;
  transform: translate3d(59vw, 71vh, 76px);
  background: @primary-color-hex;
}
@keyframes particle-animation-34 {
  100% {
    transform: translate3d(82vw, 40vh, 30px);
  }
}
.particle:nth-child(34) {
  animation: particle-animation-34 60s infinite;
  opacity: 0.19;
  height: 8px;
  width: 8px;
  animation-delay: -6.8s;
  transform: translate3d(27vw, 41vh, 1px);
  background: @primary-color-hex;
}
@keyframes particle-animation-35 {
  100% {
    transform: translate3d(33vw, 66vh, 82px);
  }
}
.particle:nth-child(35) {
  animation: particle-animation-35 60s infinite;
  opacity: 0.62;
  height: 7px;
  width: 7px;
  animation-delay: -7s;
  transform: translate3d(24vw, 42vh, 58px);
  background: @primary-color-hex;
}
@keyframes particle-animation-36 {
  100% {
    transform: translate3d(51vw, 69vh, 68px);
  }
}
.particle:nth-child(36) {
  animation: particle-animation-36 60s infinite;
  opacity: 0.99;
  height: 9px;
  width: 9px;
  animation-delay: -7.2s;
  transform: translate3d(57vw, 49vh, 84px);
  background: @primary-color-hex;
}
@keyframes particle-animation-37 {
  100% {
    transform: translate3d(65vw, 23vh, 95px);
  }
}
.particle:nth-child(37) {
  animation: particle-animation-37 60s infinite;
  opacity: 0.26;
  height: 7px;
  width: 7px;
  animation-delay: -7.4s;
  transform: translate3d(37vw, 69vh, 81px);
  background: @primary-color-hex;
}
@keyframes particle-animation-38 {
  100% {
    transform: translate3d(15vw, 54vh, 10px);
  }
}
.particle:nth-child(38) {
  animation: particle-animation-38 60s infinite;
  opacity: 0.93;
  height: 7px;
  width: 7px;
  animation-delay: -7.6s;
  transform: translate3d(6vw, 46vh, 53px);
  background: @primary-color-hex;
}
@keyframes particle-animation-39 {
  100% {
    transform: translate3d(6vw, 29vh, 38px);
  }
}
.particle:nth-child(39) {
  animation: particle-animation-39 60s infinite;
  opacity: 0.11;
  height: 7px;
  width: 7px;
  animation-delay: -7.8s;
  transform: translate3d(12vw, 28vh, 25px);
  background: @primary-color-hex;
}
@keyframes particle-animation-40 {
  100% {
    transform: translate3d(56vw, 54vh, 33px);
  }
}
.particle:nth-child(40) {
  animation: particle-animation-40 60s infinite;
  opacity: 0.71;
  height: 6px;
  width: 6px;
  animation-delay: -8s;
  transform: translate3d(61vw, 58vh, 32px);
  background: @primary-color-hex;
}
@keyframes particle-animation-41 {
  100% {
    transform: translate3d(79vw, 31vh, 25px);
  }
}
.particle:nth-child(41) {
  animation: particle-animation-41 60s infinite;
  opacity: 0.03;
  height: 7px;
  width: 7px;
  animation-delay: -8.2s;
  transform: translate3d(61vw, 30vh, 9px);
  background: @primary-color-hex;
}
@keyframes particle-animation-42 {
  100% {
    transform: translate3d(46vw, 26vh, 26px);
  }
}
.particle:nth-child(42) {
  animation: particle-animation-42 60s infinite;
  opacity: 0.29;
  height: 8px;
  width: 8px;
  animation-delay: -8.4s;
  transform: translate3d(83vw, 19vh, 32px);
  background: @primary-color-hex;
}
@keyframes particle-animation-43 {
  100% {
    transform: translate3d(90vw, 36vh, 81px);
  }
}
.particle:nth-child(43) {
  animation: particle-animation-43 60s infinite;
  opacity: 0.88;
  height: 9px;
  width: 9px;
  animation-delay: -8.6s;
  transform: translate3d(54vw, 6vh, 3px);
  background: @primary-color-hex;
}
@keyframes particle-animation-44 {
  100% {
    transform: translate3d(73vw, 86vh, 59px);
  }
}
.particle:nth-child(44) {
  animation: particle-animation-44 60s infinite;
  opacity: 0.02;
  height: 9px;
  width: 9px;
  animation-delay: -8.8s;
  transform: translate3d(25vw, 70vh, 60px);
  background: @primary-color-hex;
}
@keyframes particle-animation-45 {
  100% {
    transform: translate3d(88vw, 1vh, 6px);
  }
}
.particle:nth-child(45) {
  animation: particle-animation-45 60s infinite;
  opacity: 0.85;
  height: 10px;
  width: 10px;
  animation-delay: -9s;
  transform: translate3d(37vw, 70vh, 1px);
  background: @primary-color-hex;
}
@keyframes particle-animation-46 {
  100% {
    transform: translate3d(57vw, 9vh, 77px);
  }
}
.particle:nth-child(46) {
  animation: particle-animation-46 60s infinite;
  opacity: 0.53;
  height: 7px;
  width: 7px;
  animation-delay: -9.2s;
  transform: translate3d(61vw, 3vh, 83px);
  background: @primary-color-hex;
}
@keyframes particle-animation-47 {
  100% {
    transform: translate3d(37vw, 22vh, 13px);
  }
}
.particle:nth-child(47) {
  animation: particle-animation-47 60s infinite;
  opacity: 0.28;
  height: 8px;
  width: 8px;
  animation-delay: -9.4s;
  transform: translate3d(26vw, 2vh, 25px);
  background: @primary-color-hex;
}
@keyframes particle-animation-48 {
  100% {
    transform: translate3d(39vw, 60vh, 31px);
  }
}
.particle:nth-child(48) {
  animation: particle-animation-48 60s infinite;
  opacity: 0.48;
  height: 7px;
  width: 7px;
  animation-delay: -9.6s;
  transform: translate3d(71vw, 19vh, 62px);
  background: @primary-color-hex;
}
@keyframes particle-animation-49 {
  100% {
    transform: translate3d(19vw, 9vh, 8px);
  }
}
.particle:nth-child(49) {
  animation: particle-animation-49 60s infinite;
  opacity: 0.69;
  height: 9px;
  width: 9px;
  animation-delay: -9.8s;
  transform: translate3d(65vw, 84vh, 91px);
  background: @primary-color-hex;
}
@keyframes particle-animation-50 {
  100% {
    transform: translate3d(58vw, 64vh, 97px);
  }
}
.particle:nth-child(50) {
  animation: particle-animation-50 60s infinite;
  opacity: 0.31;
  height: 6px;
  width: 6px;
  animation-delay: -10s;
  transform: translate3d(58vw, 18vh, 80px);
  background: @primary-color-hex;
}
@keyframes particle-animation-51 {
  100% {
    transform: translate3d(60vw, 29vh, 95px);
  }
}
.particle:nth-child(51) {
  animation: particle-animation-51 60s infinite;
  opacity: 0.49;
  height: 8px;
  width: 8px;
  animation-delay: -10.2s;
  transform: translate3d(62vw, 46vh, 32px);
  background: @primary-color-hex;
}
@keyframes particle-animation-52 {
  100% {
    transform: translate3d(63vw, 32vh, 96px);
  }
}
.particle:nth-child(52) {
  animation: particle-animation-52 60s infinite;
  opacity: 0.69;
  height: 7px;
  width: 7px;
  animation-delay: -10.4s;
  transform: translate3d(66vw, 17vh, 48px);
  background: @primary-color-hex;
}
@keyframes particle-animation-53 {
  100% {
    transform: translate3d(20vw, 18vh, 68px);
  }
}
.particle:nth-child(53) {
  animation: particle-animation-53 60s infinite;
  opacity: 0.45;
  height: 8px;
  width: 8px;
  animation-delay: -10.6s;
  transform: translate3d(13vw, 1vh, 72px);
  background: @primary-color-hex;
}
@keyframes particle-animation-54 {
  100% {
    transform: translate3d(64vw, 1vh, 73px);
  }
}
.particle:nth-child(54) {
  animation: particle-animation-54 60s infinite;
  opacity: 0.22;
  height: 6px;
  width: 6px;
  animation-delay: -10.8s;
  transform: translate3d(82vw, 28vh, 3px);
  background: @primary-color-hex;
}
@keyframes particle-animation-55 {
  100% {
    transform: translate3d(71vw, 42vh, 49px);
  }
}
.particle:nth-child(55) {
  animation: particle-animation-55 60s infinite;
  opacity: 0.01;
  height: 6px;
  width: 6px;
  animation-delay: -11s;
  transform: translate3d(82vw, 16vh, 33px);
  background: @primary-color-hex;
}
@keyframes particle-animation-56 {
  100% {
    transform: translate3d(18vw, 62vh, 80px);
  }
}
.particle:nth-child(56) {
  animation: particle-animation-56 60s infinite;
  opacity: 0.36;
  height: 7px;
  width: 7px;
  animation-delay: -11.2s;
  transform: translate3d(80vw, 88vh, 32px);
  background: @primary-color-hex;
}
@keyframes particle-animation-57 {
  100% {
    transform: translate3d(87vw, 46vh, 47px);
  }
}
.particle:nth-child(57) {
  animation: particle-animation-57 60s infinite;
  opacity: 0.16;
  height: 8px;
  width: 8px;
  animation-delay: -11.4s;
  transform: translate3d(30vw, 85vh, 84px);
  background: @primary-color-hex;
}
@keyframes particle-animation-58 {
  100% {
    transform: translate3d(52vw, 69vh, 38px);
  }
}
.particle:nth-child(58) {
  animation: particle-animation-58 60s infinite;
  opacity: 0.94;
  height: 9px;
  width: 9px;
  animation-delay: -11.6s;
  transform: translate3d(14vw, 42vh, 62px);
  background: @primary-color-hex;
}
@keyframes particle-animation-59 {
  100% {
    transform: translate3d(70vw, 70vh, 95px);
  }
}
.particle:nth-child(59) {
  animation: particle-animation-59 60s infinite;
  opacity: 0.36;
  height: 8px;
  width: 8px;
  animation-delay: -11.8s;
  transform: translate3d(13vw, 5vh, 68px);
  background: @primary-color-hex;
}
@keyframes particle-animation-60 {
  100% {
    transform: translate3d(8vw, 9vh, 34px);
  }
}
.particle:nth-child(60) {
  animation: particle-animation-60 60s infinite;
  opacity: 0.14;
  height: 6px;
  width: 6px;
  animation-delay: -12s;
  transform: translate3d(65vw, 15vh, 100px);
  background: @primary-color-hex;
}
@keyframes particle-animation-61 {
  100% {
    transform: translate3d(14vw, 34vh, 76px);
  }
}
.particle:nth-child(61) {
  animation: particle-animation-61 60s infinite;
  opacity: 0.09;
  height: 6px;
  width: 6px;
  animation-delay: -12.2s;
  transform: translate3d(41vw, 54vh, 32px);
  background: @primary-color-hex;
}
@keyframes particle-animation-62 {
  100% {
    transform: translate3d(54vw, 15vh, 21px);
  }
}
.particle:nth-child(62) {
  animation: particle-animation-62 60s infinite;
  opacity: 0.31;
  height: 9px;
  width: 9px;
  animation-delay: -12.4s;
  transform: translate3d(18vw, 19vh, 55px);
  background: @primary-color-hex;
}
@keyframes particle-animation-63 {
  100% {
    transform: translate3d(37vw, 7vh, 83px);
  }
}
.particle:nth-child(63) {
  animation: particle-animation-63 60s infinite;
  opacity: 0.43;
  height: 10px;
  width: 10px;
  animation-delay: -12.6s;
  transform: translate3d(6vw, 86vh, 45px);
  background: @primary-color-hex;
}
@keyframes particle-animation-64 {
  100% {
    transform: translate3d(61vw, 23vh, 82px);
  }
}
.particle:nth-child(64) {
  animation: particle-animation-64 60s infinite;
  opacity: 0.89;
  height: 7px;
  width: 7px;
  animation-delay: -12.8s;
  transform: translate3d(29vw, 43vh, 28px);
  background: @primary-color-hex;
}
@keyframes particle-animation-65 {
  100% {
    transform: translate3d(38vw, 30vh, 45px);
  }
}
.particle:nth-child(65) {
  animation: particle-animation-65 60s infinite;
  opacity: 0.8;
  height: 8px;
  width: 8px;
  animation-delay: -13s;
  transform: translate3d(78vw, 19vh, 10px);
  background: @primary-color-hex;
}
@keyframes particle-animation-66 {
  100% {
    transform: translate3d(41vw, 3vh, 30px);
  }
}
.particle:nth-child(66) {
  animation: particle-animation-66 60s infinite;
  opacity: 0.87;
  height: 6px;
  width: 6px;
  animation-delay: -13.2s;
  transform: translate3d(34vw, 53vh, 96px);
  background: @primary-color-hex;
}
@keyframes particle-animation-67 {
  100% {
    transform: translate3d(83vw, 61vh, 71px);
  }
}
.particle:nth-child(67) {
  animation: particle-animation-67 60s infinite;
  opacity: 0.49;
  height: 10px;
  width: 10px;
  animation-delay: -13.4s;
  transform: translate3d(76vw, 83vh, 90px);
  background: @primary-color-hex;
}
@keyframes particle-animation-68 {
  100% {
    transform: translate3d(6vw, 41vh, 17px);
  }
}
.particle:nth-child(68) {
  animation: particle-animation-68 60s infinite;
  opacity: 0.76;
  height: 7px;
  width: 7px;
  animation-delay: -13.6s;
  transform: translate3d(53vw, 60vh, 92px);
  background: @primary-color-hex;
}
@keyframes particle-animation-69 {
  100% {
    transform: translate3d(5vw, 37vh, 35px);
  }
}
.particle:nth-child(69) {
  animation: particle-animation-69 60s infinite;
  opacity: 0.2;
  height: 7px;
  width: 7px;
  animation-delay: -13.8s;
  transform: translate3d(1vw, 55vh, 53px);
  background: @primary-color-hex;
}
@keyframes particle-animation-70 {
  100% {
    transform: translate3d(42vw, 57vh, 64px);
  }
}
.particle:nth-child(70) {
  animation: particle-animation-70 60s infinite;
  opacity: 0.17;
  height: 8px;
  width: 8px;
  animation-delay: -14s;
  transform: translate3d(90vw, 58vh, 25px);
  background: @primary-color-hex;
}
@keyframes particle-animation-71 {
  100% {
    transform: translate3d(78vw, 29vh, 85px);
  }
}
.particle:nth-child(71) {
  animation: particle-animation-71 60s infinite;
  opacity: 0.06;
  height: 7px;
  width: 7px;
  animation-delay: -14.2s;
  transform: translate3d(13vw, 24vh, 95px);
  background: @primary-color-hex;
}
@keyframes particle-animation-72 {
  100% {
    transform: translate3d(9vw, 57vh, 41px);
  }
}
.particle:nth-child(72) {
  animation: particle-animation-72 60s infinite;
  opacity: 0.92;
  height: 10px;
  width: 10px;
  animation-delay: -14.4s;
  transform: translate3d(61vw, 7vh, 11px);
  background: @primary-color-hex;
}
@keyframes particle-animation-73 {
  100% {
    transform: translate3d(74vw, 77vh, 99px);
  }
}
.particle:nth-child(73) {
  animation: particle-animation-73 60s infinite;
  opacity: 0.01;
  height: 10px;
  width: 10px;
  animation-delay: -14.6s;
  transform: translate3d(75vw, 85vh, 57px);
  background: @primary-color-hex;
}
@keyframes particle-animation-74 {
  100% {
    transform: translate3d(59vw, 40vh, 11px);
  }
}
.particle:nth-child(74) {
  animation: particle-animation-74 60s infinite;
  opacity: 0.2;
  height: 10px;
  width: 10px;
  animation-delay: -14.8s;
  transform: translate3d(10vw, 66vh, 3px);
  background: @primary-color-hex;
}
@keyframes particle-animation-75 {
  100% {
    transform: translate3d(37vw, 50vh, 62px);
  }
}
.particle:nth-child(75) {
  animation: particle-animation-75 60s infinite;
  opacity: 0.92;
  height: 10px;
  width: 10px;
  animation-delay: -15s;
  transform: translate3d(36vw, 77vh, 69px);
  background: @primary-color-hex;
}
@keyframes particle-animation-76 {
  100% {
    transform: translate3d(66vw, 40vh, 13px);
  }
}
.particle:nth-child(76) {
  animation: particle-animation-76 60s infinite;
  opacity: 0.52;
  height: 8px;
  width: 8px;
  animation-delay: -15.2s;
  transform: translate3d(75vw, 73vh, 70px);
  background: @primary-color-hex;
}
@keyframes particle-animation-77 {
  100% {
    transform: translate3d(55vw, 78vh, 40px);
  }
}
.particle:nth-child(77) {
  animation: particle-animation-77 60s infinite;
  opacity: 0.43;
  height: 10px;
  width: 10px;
  animation-delay: -15.4s;
  transform: translate3d(18vw, 3vh, 97px);
  background: @primary-color-hex;
}
@keyframes particle-animation-78 {
  100% {
    transform: translate3d(62vw, 30vh, 37px);
  }
}
.particle:nth-child(78) {
  animation: particle-animation-78 60s infinite;
  opacity: 0.58;
  height: 6px;
  width: 6px;
  animation-delay: -15.6s;
  transform: translate3d(53vw, 63vh, 57px);
  background: @primary-color-hex;
}
@keyframes particle-animation-79 {
  100% {
    transform: translate3d(72vw, 45vh, 89px);
  }
}
.particle:nth-child(79) {
  animation: particle-animation-79 60s infinite;
  opacity: 0.45;
  height: 7px;
  width: 7px;
  animation-delay: -15.8s;
  transform: translate3d(32vw, 33vh, 97px);
  background: @primary-color-hex;
}
@keyframes particle-animation-80 {
  100% {
    transform: translate3d(81vw, 47vh, 31px);
  }
}
.particle:nth-child(80) {
  animation: particle-animation-80 60s infinite;
  opacity: 0.69;
  height: 10px;
  width: 10px;
  animation-delay: -16s;
  transform: translate3d(53vw, 87vh, 9px);
  background: @primary-color-hex;
}
@keyframes particle-animation-81 {
  100% {
    transform: translate3d(9vw, 79vh, 96px);
  }
}
.particle:nth-child(81) {
  animation: particle-animation-81 60s infinite;
  opacity: 0.4;
  height: 9px;
  width: 9px;
  animation-delay: -16.2s;
  transform: translate3d(71vw, 15vh, 53px);
  background: @primary-color-hex;
}
@keyframes particle-animation-82 {
  100% {
    transform: translate3d(63vw, 5vh, 34px);
  }
}
.particle:nth-child(82) {
  animation: particle-animation-82 60s infinite;
  opacity: 0.82;
  height: 7px;
  width: 7px;
  animation-delay: -16.4s;
  transform: translate3d(43vw, 87vh, 5px);
  background: @primary-color-hex;
}
@keyframes particle-animation-83 {
  100% {
    transform: translate3d(88vw, 53vh, 91px);
  }
}
.particle:nth-child(83) {
  animation: particle-animation-83 60s infinite;
  opacity: 0.25;
  height: 6px;
  width: 6px;
  animation-delay: -16.6s;
  transform: translate3d(19vw, 21vh, 61px);
  background: @primary-color-hex;
}
@keyframes particle-animation-84 {
  100% {
    transform: translate3d(6vw, 54vh, 28px);
  }
}
.particle:nth-child(84) {
  animation: particle-animation-84 60s infinite;
  opacity: 0.26;
  height: 9px;
  width: 9px;
  animation-delay: -16.8s;
  transform: translate3d(10vw, 87vh, 31px);
  background: @primary-color-hex;
}
@keyframes particle-animation-85 {
  100% {
    transform: translate3d(45vw, 10vh, 69px);
  }
}
.particle:nth-child(85) {
  animation: particle-animation-85 60s infinite;
  opacity: 0.08;
  height: 8px;
  width: 8px;
  animation-delay: -17s;
  transform: translate3d(64vw, 46vh, 20px);
  background: @primary-color-hex;
}
@keyframes particle-animation-86 {
  100% {
    transform: translate3d(71vw, 82vh, 81px);
  }
}
.particle:nth-child(86) {
  animation: particle-animation-86 60s infinite;
  opacity: 0.69;
  height: 10px;
  width: 10px;
  animation-delay: -17.2s;
  transform: translate3d(33vw, 27vh, 18px);
  background: @primary-color-hex;
}
@keyframes particle-animation-87 {
  100% {
    transform: translate3d(35vw, 23vh, 71px);
  }
}
.particle:nth-child(87) {
  animation: particle-animation-87 60s infinite;
  opacity: 0.28;
  height: 7px;
  width: 7px;
  animation-delay: -17.4s;
  transform: translate3d(74vw, 3vh, 71px);
  background: @primary-color-hex;
}
@keyframes particle-animation-88 {
  100% {
    transform: translate3d(6vw, 60vh, 76px);
  }
}
.particle:nth-child(88) {
  animation: particle-animation-88 60s infinite;
  opacity: 0.13;
  height: 9px;
  width: 9px;
  animation-delay: -17.6s;
  transform: translate3d(79vw, 61vh, 99px);
  background: @primary-color-hex;
}
@keyframes particle-animation-89 {
  100% {
    transform: translate3d(78vw, 45vh, 51px);
  }
}
.particle:nth-child(89) {
  animation: particle-animation-89 60s infinite;
  opacity: 0.47;
  height: 7px;
  width: 7px;
  animation-delay: -17.8s;
  transform: translate3d(50vw, 20vh, 37px);
  background: @primary-color-hex;
}
@keyframes particle-animation-90 {
  100% {
    transform: translate3d(29vw, 58vh, 48px);
  }
}
.particle:nth-child(90) {
  animation: particle-animation-90 60s infinite;
  opacity: 0.62;
  height: 10px;
  width: 10px;
  animation-delay: -18s;
  transform: translate3d(48vw, 40vh, 27px);
  background: @primary-color-hex;
}
@keyframes particle-animation-91 {
  100% {
    transform: translate3d(84vw, 75vh, 1px);
  }
}
.particle:nth-child(91) {
  animation: particle-animation-91 60s infinite;
  opacity: 0.76;
  height: 9px;
  width: 9px;
  animation-delay: -18.2s;
  transform: translate3d(52vw, 26vh, 71px);
  background: @primary-color-hex;
}
@keyframes particle-animation-92 {
  100% {
    transform: translate3d(72vw, 89vh, 4px);
  }
}
.particle:nth-child(92) {
  animation: particle-animation-92 60s infinite;
  opacity: 0.94;
  height: 7px;
  width: 7px;
  animation-delay: -18.4s;
  transform: translate3d(2vw, 82vh, 68px);
  background: @primary-color-hex;
}
@keyframes particle-animation-93 {
  100% {
    transform: translate3d(25vw, 38vh, 94px);
  }
}
.particle:nth-child(93) {
  animation: particle-animation-93 60s infinite;
  opacity: 0.69;
  height: 9px;
  width: 9px;
  animation-delay: -18.6s;
  transform: translate3d(30vw, 51vh, 49px);
  background: @primary-color-hex;
}
@keyframes particle-animation-94 {
  100% {
    transform: translate3d(9vw, 4vh, 19px);
  }
}
.particle:nth-child(94) {
  animation: particle-animation-94 60s infinite;
  opacity: 0.2;
  height: 8px;
  width: 8px;
  animation-delay: -18.8s;
  transform: translate3d(64vw, 19vh, 4px);
  background: @primary-color-hex;
}
@keyframes particle-animation-95 {
  100% {
    transform: translate3d(61vw, 22vh, 31px);
  }
}
.particle:nth-child(95) {
  animation: particle-animation-95 60s infinite;
  opacity: 0.86;
  height: 9px;
  width: 9px;
  animation-delay: -19s;
  transform: translate3d(49vw, 5vh, 48px);
  background: @primary-color-hex;
}
@keyframes particle-animation-96 {
  100% {
    transform: translate3d(83vw, 60vh, 86px);
  }
}
.particle:nth-child(96) {
  animation: particle-animation-96 60s infinite;
  opacity: 0.27;
  height: 8px;
  width: 8px;
  animation-delay: -19.2s;
  transform: translate3d(4vw, 30vh, 26px);
  background: @primary-color-hex;
}
@keyframes particle-animation-97 {
  100% {
    transform: translate3d(76vw, 23vh, 13px);
  }
}
.particle:nth-child(97) {
  animation: particle-animation-97 60s infinite;
  opacity: 0.44;
  height: 10px;
  width: 10px;
  animation-delay: -19.4s;
  transform: translate3d(17vw, 40vh, 79px);
  background: @primary-color-hex;
}
@keyframes particle-animation-98 {
  100% {
    transform: translate3d(54vw, 17vh, 35px);
  }
}
.particle:nth-child(98) {
  animation: particle-animation-98 60s infinite;
  opacity: 0.3;
  height: 9px;
  width: 9px;
  animation-delay: -19.6s;
  transform: translate3d(74vw, 54vh, 88px);
  background: @primary-color-hex;
}
@keyframes particle-animation-99 {
  100% {
    transform: translate3d(53vw, 11vh, 58px);
  }
}
.particle:nth-child(99) {
  animation: particle-animation-99 60s infinite;
  opacity: 0.9;
  height: 6px;
  width: 6px;
  animation-delay: -19.8s;
  transform: translate3d(45vw, 56vh, 36px);
  background: @primary-color-hex;
}
@keyframes particle-animation-100 {
  100% {
    transform: translate3d(16vw, 86vh, 72px);
  }
}
.particle:nth-child(100) {
  animation: particle-animation-100 60s infinite;
  opacity: 0.67;
  height: 7px;
  width: 7px;
  animation-delay: -20s;
  transform: translate3d(75vw, 66vh, 34px);
  background: @primary-color-hex;
}
