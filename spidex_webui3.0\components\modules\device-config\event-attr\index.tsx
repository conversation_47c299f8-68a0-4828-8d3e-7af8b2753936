import type { EventAttr } from "@/lib/types";
import EventAttrTable from "./event-attr-table";
import { Separator } from "@radix-ui/react-separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import AddEventAttributeBtn from "./add-event-attr-btn";
import { auth } from "@/lib/auth/auth";
import { getAllEventAttrs } from "@/lib/server-data/event-attr";

export default async function EventAttr() {
  const session = await auth();
  const tenantId = session?.user.tenantId || "";
  const eventAttrs = await getAllEventAttrs(tenantId);
  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="#">Device Configuration</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Event Attribute</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <AddEventAttributeBtn />
      </header>
      <main className="p-4">
        <EventAttrTable data={eventAttrs} />
      </main>
    </>
  );
}
