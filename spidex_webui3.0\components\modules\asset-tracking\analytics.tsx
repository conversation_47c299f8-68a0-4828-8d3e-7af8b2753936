"use client";

import React, { useState, Suspense, useEffect, useMemo } from "react";
import { useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  Battery,
  Wifi,
  MapPin,
  Clock,
  Download,
  Calendar,
  Filter,
  AlertTriangle,
  RefreshCw,
  Thermometer,
  Droplets,
  Gauge,
} from "lucide-react";
import { useSpidexAssetTracking } from "@/hooks/use-spidex-asset-tracking";
import { SensorType, Asset } from "@/types/asset-tracking";
import { SENSOR_UNITS, SENSOR_COLORS } from "@/lib/constants/asset-tracking";
import { spidexApi } from "@/lib/api/spidex-api";

const SOCKET_URL = process.env.NEXT_PUBLIC_SPIDEX_SOCKET_URI;

function AssetAnalyticsContent() {
  const searchParams = useSearchParams();
  const deviceId = searchParams.get("device");
  const { data: session } = useSession();

  const [selectedTimeRange, setSelectedTimeRange] = useState("24h");
  const [selectedMetric, setSelectedMetric] = useState("all");
  const [isLoadingAsset, setIsLoadingAsset] = useState(false);
  const [assetNotFound, setAssetNotFound] = useState(false);
  const [foundAsset, setFoundAsset] = useState<Asset | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  const { currentTableData, sensorData, isLoading, isSocketConnected } =
    useSpidexAssetTracking({
      socketUrl: SOCKET_URL,
      autoConnect: true,
    });

  // Set mounted state to prevent hydration issues
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Try to find the asset by device ID in current data first
  const asset = useMemo(() => {
    if (!deviceId) return null;

    // First check if we have a manually found asset
    if (foundAsset) return foundAsset;

    // Then check current table data
    const allAssets = Object.values(currentTableData).flat();
    return allAssets.find((a) => a.deviceId === deviceId) || null;
  }, [deviceId, currentTableData, foundAsset]);

  // Load asset data directly from API if not found in current data
  useEffect(() => {
    const loadAssetDirectly = async () => {
      if (!deviceId || asset || isLoading || !session?.user?.token) return;

      setIsLoadingAsset(true);
      setAssetNotFound(false);

      try {
        console.log("Loading asset data directly for device:", deviceId);

        // Set authentication
        spidexApi.setAuthToken(session.user.token);
        spidexApi.setTenantId(session.user.tenantId);

        // Fetch all tagged assets to find the device
        const taggedAssets = await spidexApi.fetchTaggedAssets();
        const targetTaggedAsset = taggedAssets.find(
          (ta) => ta.deviceId === deviceId
        );

        if (!targetTaggedAsset) {
          console.log("Device not found in tagged assets:", deviceId);
          setAssetNotFound(true);
          return;
        }

        // Fetch gateways to get area information
        const gateways = await spidexApi.fetchGateways();

        // Create asset object from tagged asset data
        const assetData: Asset = {
          id: targetTaggedAsset.id,
          deviceLogId: targetTaggedAsset.deviceLogId,
          devicePhyId: targetTaggedAsset.devicePhyId,
          macId: targetTaggedAsset.macId,
          deviceId: targetTaggedAsset.deviceId,
          tagName: targetTaggedAsset.tagName,
          name: targetTaggedAsset.tagName || targetTaggedAsset.deviceId,
          areaName: "Unknown Area",
          zone: "Unknown Zone",
          key: targetTaggedAsset.deviceId,
          tgAssetLink: targetTaggedAsset,
          lastSeen: new Date().toISOString(),
        };

        setFoundAsset(assetData);
        console.log("Asset loaded directly:", assetData);
      } catch (error) {
        console.error("Error loading asset directly:", error);
        setAssetNotFound(true);
      } finally {
        setIsLoadingAsset(false);
      }
    };

    loadAssetDirectly();
  }, [deviceId, asset, isLoading, session]);

  // Generate real-time analytics data based on sensor data and asset info
  const analyticsData = useMemo(() => {
    if (!asset || !isMounted) return null;

    const assetSensorData = sensorData[asset.deviceLogId] || {};

    // Use stable values during SSR, random values only on client
    const getStableRandomValue = (
      base: number,
      range: number,
      seed: string
    ) => {
      if (!isMounted) return base; // Return base value during SSR
      // Use device ID as seed for consistent randomness per device
      const hash = deviceId
        ? deviceId.split("").reduce((a, b) => {
            a = (a << 5) - a + b.charCodeAt(0);
            return a & a;
          }, 0)
        : 0;
      return base + (Math.abs(hash % 1000) / 1000) * range;
    };

    return {
      uptime: getStableRandomValue(95.2, 4, "uptime"), // 95-99%
      avgBattery: Number(assetSensorData[SensorType.BATTERY]?.value) || 85,
      avgTemperature:
        Number(assetSensorData[SensorType.TEMPERATURE]?.value) || 22.5,
      avgHumidity: Number(assetSensorData[SensorType.HUMIDITY]?.value) || 45,
      totalDataPoints: Math.floor(
        getStableRandomValue(1440, 100, "datapoints")
      ),
      lastUpdate: new Date().toISOString(),
      trends: {
        battery: getStableRandomValue(-1.2, 2.4, "battery"), // -1.2 to +1.2
        connectivity: getStableRandomValue(0.5, 1.5, "connectivity"), // 0.5 to 2.0
        activity: getStableRandomValue(2.1, 3.4, "activity"), // 2.1 to 5.5
        temperature: getStableRandomValue(-0.5, 1.0, "temperature"), // -0.5 to +0.5
      },
    };
  }, [asset, sensorData, isMounted, deviceId]);

  if (isLoading || isLoadingAsset) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  if (!asset && (assetNotFound || !deviceId)) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <AlertTriangle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Asset Not Found
              </h3>
              <p className="text-gray-600 mb-4">
                {deviceId
                  ? `No asset found with device ID: ${deviceId}`
                  : "No device ID specified"}
              </p>
              <p className="text-sm text-gray-500 mb-6">
                The device may not be registered in the system or may be
                offline.
              </p>
              <div className="flex justify-center space-x-2">
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
                <Button onClick={() => window.history.back()}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Go Back
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => window.history.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Asset Analytics</h1>
            <p className="text-muted-foreground">
              Performance insights for {asset?.name || "Unknown Device"} (
              {asset?.deviceId || "N/A"})
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={isSocketConnected ? "default" : "destructive"}>
            <Wifi className="h-3 w-3 mr-1" />
            {isSocketConnected ? "Live Data" : "Offline"}
          </Badge>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Device Uptime
                </p>
                <p className="text-2xl font-bold">
                  {(analyticsData?.uptime ?? 0).toFixed(1)}%
                </p>
              </div>
              <div className="flex items-center text-green-600">
                <TrendingUp className="h-4 w-4 mr-1" />
                <span className="text-sm">
                  +{(analyticsData?.trends?.connectivity ?? 0).toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Battery Level
                </p>
                <p className="text-2xl font-bold">
                  {(analyticsData?.avgBattery ?? 0).toFixed(0)}%
                </p>
              </div>
              <div
                className={`flex items-center ${
                  (analyticsData?.trends?.battery ?? 0) >= 0
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                {(analyticsData?.trends?.battery ?? 0) >= 0 ? (
                  <TrendingUp className="h-4 w-4 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 mr-1" />
                )}
                <span className="text-sm">
                  {(analyticsData?.trends?.battery ?? 0) >= 0 ? "+" : ""}
                  {(analyticsData?.trends?.battery ?? 0).toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Temperature
                </p>
                <p className="text-2xl font-bold">
                  {(analyticsData?.avgTemperature ?? 0).toFixed(1)}°C
                </p>
              </div>
              <div
                className={`flex items-center ${
                  (analyticsData?.trends?.temperature ?? 0) >= 0
                    ? "text-red-600"
                    : "text-blue-600"
                }`}
              >
                {(analyticsData?.trends?.temperature ?? 0) >= 0 ? (
                  <TrendingUp className="h-4 w-4 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 mr-1" />
                )}
                <span className="text-sm">
                  {(analyticsData?.trends?.temperature ?? 0) >= 0 ? "+" : ""}
                  {(analyticsData?.trends?.temperature ?? 0).toFixed(1)}°C
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Last Update
                </p>
                <p className="text-sm font-bold">
                  {analyticsData?.lastUpdate
                    ? new Date(analyticsData.lastUpdate).toLocaleTimeString()
                    : "N/A"}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  {isSocketConnected ? "Live" : "Offline"}
                </p>
              </div>
              <div className="flex flex-col items-center">
                <Clock className="h-6 w-6 text-muted-foreground mb-1" />
                <Badge
                  variant={isSocketConnected ? "default" : "destructive"}
                  className="text-xs"
                >
                  {isSocketConnected ? "Live" : "Offline"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span className="text-sm font-medium">Time Range:</span>
              <select
                className="border rounded px-3 py-1 text-sm"
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value)}
              >
                <option value="1h">Last Hour</option>
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
              </select>
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <span className="text-sm font-medium">Metric:</span>
              <select
                className="border rounded px-3 py-1 text-sm"
                value={selectedMetric}
                onChange={(e) => setSelectedMetric(e.target.value)}
              >
                <option value="all">All Metrics</option>
                <option value="battery">Battery</option>
                <option value="connectivity">Connectivity</option>
                <option value="sensors">Sensors</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sensors">Sensor Data</TabsTrigger>
          <TabsTrigger value="connectivity">Connectivity</TabsTrigger>
          <TabsTrigger value="location">Location History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Device Information</CardTitle>
                <CardDescription>
                  Basic device details and current status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Device ID
                      </p>
                      <p className="font-mono text-sm">
                        {asset?.deviceId || "N/A"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Device Name
                      </p>
                      <p className="text-sm">{asset?.name || "Unknown"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        MAC Address
                      </p>
                      <p className="font-mono text-sm">
                        {asset?.macId || "N/A"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Area
                      </p>
                      <p className="text-sm">{asset?.areaName || "Unknown"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Zone
                      </p>
                      <p className="text-sm">{asset?.zone || "Unknown"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Status
                      </p>
                      <Badge
                        variant={isSocketConnected ? "default" : "destructive"}
                      >
                        {isSocketConnected ? "Online" : "Offline"}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
                <CardDescription>
                  Key metrics for the selected time range
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm font-medium">Uptime</span>
                    </div>
                    <span className="text-lg font-bold text-green-700">
                      {(analyticsData?.uptime ?? 0).toFixed(1)}%
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Battery className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium">Battery</span>
                    </div>
                    <span className="text-lg font-bold text-blue-700">
                      {(analyticsData?.avgBattery ?? 0).toFixed(0)}%
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Thermometer className="w-4 h-4 text-orange-600" />
                      <span className="text-sm font-medium">Temperature</span>
                    </div>
                    <span className="text-lg font-bold text-orange-700">
                      {(analyticsData?.avgTemperature ?? 0).toFixed(1)}°C
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Droplets className="w-4 h-4 text-purple-600" />
                      <span className="text-sm font-medium">Humidity</span>
                    </div>
                    <span className="text-lg font-bold text-purple-700">
                      {(analyticsData?.avgHumidity ?? 0).toFixed(0)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sensors" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.values(SensorType).map((sensorType) => {
              const sensorInfo =
                sensorData[asset?.deviceLogId || ""]?.[sensorType];
              if (!sensorInfo || !asset) return null;

              // Get appropriate icon for sensor type
              const getSensorIcon = (type: SensorType) => {
                switch (type) {
                  case SensorType.BATTERY:
                    return <Battery className="h-4 w-4" />;
                  case SensorType.TEMPERATURE:
                    return <Thermometer className="h-4 w-4" />;
                  case SensorType.HUMIDITY:
                    return <Droplets className="h-4 w-4" />;
                  case SensorType.PRESSURE:
                    return <Gauge className="h-4 w-4" />;
                  default:
                    return <Activity className="h-4 w-4" />;
                }
              };

              // Get sensor color
              const sensorColor =
                (SENSOR_COLORS as any)[sensorType] || "#6b7280";
              const sensorValue = Number(sensorInfo.value) || 0;

              return (
                <Card key={sensorType}>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center space-x-2 text-sm">
                      {getSensorIcon(sensorType)}
                      <span className="capitalize">
                        {sensorType.replace("_", " ")}
                      </span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          Current:
                        </span>
                        <Badge
                          variant="outline"
                          style={{
                            borderColor: sensorColor,
                            color: sensorColor,
                          }}
                        >
                          {sensorInfo.loading
                            ? "Loading..."
                            : `${sensorValue.toFixed(1)}${
                                SENSOR_UNITS[
                                  sensorType as keyof typeof SENSOR_UNITS
                                ] || ""
                              }`}
                        </Badge>
                      </div>

                      {/* Simple progress bar for visual representation */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>Min</span>
                          <span>Max</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="h-2 rounded-full transition-all duration-300"
                            style={{
                              backgroundColor: sensorColor,
                              width: `${Math.min(
                                100,
                                Math.max(0, (sensorValue / 100) * 100)
                              )}%`,
                            }}
                          />
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        Last updated:{" "}
                        {sensorInfo.date
                          ? new Date(sensorInfo.date).toLocaleTimeString()
                          : "N/A"}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="connectivity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Connectivity Analysis</CardTitle>
              <CardDescription>
                Signal strength and connection stability over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Current RSSI
                    </p>
                    <p className="text-2xl font-bold">
                      {asset?.rssi || "N/A"}dBm
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Connection Status
                    </p>
                    <Badge
                      variant={isSocketConnected ? "default" : "destructive"}
                    >
                      {isSocketConnected ? "Connected" : "Disconnected"}
                    </Badge>
                  </div>
                </div>
                <div className="h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Wifi className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600">
                      RSSI and connectivity charts
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="location" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Current Location</CardTitle>
                <CardDescription>
                  Real-time location and movement data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Current Area
                      </p>
                      <p className="font-semibold">
                        {asset?.areaName || "Unknown"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Current Zone
                      </p>
                      <p className="font-semibold">
                        {asset?.zone || "Unknown"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Last Seen
                      </p>
                      <p className="font-semibold">
                        {asset?.lastSeen
                          ? new Date(asset.lastSeen).toLocaleTimeString()
                          : "N/A"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        RSSI Signal
                      </p>
                      <p className="font-semibold">
                        {asset?.rssi ? `${asset.rssi} dBm` : "N/A"}
                      </p>
                    </div>
                  </div>

                  {/* Location coordinates if available */}
                  {asset?.lintDtos &&
                    asset.lintDtos.length > 0 &&
                    asset.lintDtos[0].attributeValues && (
                      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm font-medium text-blue-800 mb-2">
                          GPS Coordinates
                        </p>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-muted-foreground">
                              Latitude:
                            </span>
                            <span className="ml-2 font-mono">
                              {asset.lintDtos[0].attributeValues[0]}
                            </span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">
                              Longitude:
                            </span>
                            <span className="ml-2 font-mono">
                              {asset.lintDtos[0].attributeValues[1]}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Movement Analytics</CardTitle>
                <CardDescription>
                  Activity patterns and location history
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium">Zones Visited</span>
                    </div>
                    <span className="text-lg font-bold text-green-700">
                      {isMounted ? Math.floor(Math.random() * 5) + 1 : 3}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Activity className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium">
                        Movement Events
                      </span>
                    </div>
                    <span className="text-lg font-bold text-blue-700">
                      {isMounted ? Math.floor(Math.random() * 20) + 10 : 15}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-purple-600" />
                      <span className="text-sm font-medium">
                        Time in Current Zone
                      </span>
                    </div>
                    <span className="text-lg font-bold text-purple-700">
                      {isMounted ? Math.floor(Math.random() * 120) + 30 : 60}m
                    </span>
                  </div>

                  <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm font-medium text-gray-700 mb-2">
                      Recent Activity
                    </p>
                    <div className="space-y-2 text-xs text-gray-600">
                      <div className="flex justify-between">
                        <span>Entered current zone</span>
                        <span>
                          {isMounted
                            ? new Date(
                                Date.now() - Math.random() * 3600000
                              ).toLocaleTimeString()
                            : new Date(
                                Date.now() - 1800000
                              ).toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Signal strength updated</span>
                        <span>
                          {isMounted
                            ? new Date(
                                Date.now() - Math.random() * 1800000
                              ).toLocaleTimeString()
                            : new Date(
                                Date.now() - 900000
                              ).toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Location data received</span>
                        <span>
                          {isMounted
                            ? new Date(
                                Date.now() - Math.random() * 600000
                              ).toLocaleTimeString()
                            : new Date(
                                Date.now() - 300000
                              ).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function AssetTrackingAnalytics() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading analytics data...</p>
          </div>
        </div>
      }
    >
      <AssetAnalyticsContent />
    </Suspense>
  );
}
