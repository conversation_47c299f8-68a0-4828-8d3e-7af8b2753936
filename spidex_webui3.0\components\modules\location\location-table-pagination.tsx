"use client";

import { LocationPaginationParams, LocationPageSize } from "@/types/location";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";

interface LocationTablePaginationProps {
  pagination: LocationPaginationParams;
  totalRecords: number;
  currentPageRecords: number;
  totalPages: number;
  availablePageSizes: LocationPageSize[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: LocationPageSize) => void;
}

export function LocationTablePagination({
  pagination,
  totalRecords,
  currentPageRecords,
  totalPages,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
}: LocationTablePaginationProps) {
  const { pageNumber, pageSize } = pagination;

  const startRecord =
    totalRecords === 0
      ? 0
      : (pageNumber - 1) * (pageSize === "all" ? totalRecords : pageSize) + 1;
  const endRecord =
    pageSize === "all"
      ? totalRecords
      : Math.min(pageNumber * pageSize, totalRecords);

  const canGoPrevious = pageNumber > 1;
  const canGoNext = pageNumber < totalPages;

  // Generate page numbers to show
  const getVisiblePages = () => {
    const delta = 2; // Number of pages to show on each side of current page
    const range = [];
    const rangeWithDots = [];

    for (
      let i = Math.max(2, pageNumber - delta);
      i <= Math.min(totalPages - 1, pageNumber + delta);
      i++
    ) {
      range.push(i);
    }

    if (pageNumber - delta > 2) {
      rangeWithDots.push(1, "...");
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (pageNumber + delta < totalPages - 1) {
      rangeWithDots.push("...", totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const visiblePages = getVisiblePages();

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 px-4 py-2 border-t">
      {/* Records info */}
      <div className="text-sm text-muted-foreground">
        {totalRecords === 0 ? (
          "No records found"
        ) : (
          <>
            Showing {startRecord} to {endRecord} of {totalRecords} location
            {totalRecords !== 1 ? "s" : ""}
          </>
        )}
      </div>

      {/* Pagination controls */}
      <div className="flex items-center gap-2">
        {/* Page size selector */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Show</span>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) =>
              onPageSizeChange(value as LocationPageSize)
            }
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availablePageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size === "all" ? "All" : size.toString()}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Page navigation */}
        {totalPages > 1 && (
          <div className="flex items-center gap-1">
            {/* First page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={!canGoPrevious}
              className="h-8 w-8 p-0"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>

            {/* Previous page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pageNumber - 1)}
              disabled={!canGoPrevious}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {/* Page numbers */}
            {visiblePages.map((page, index) => (
              <Button
                key={index}
                variant={page === pageNumber ? "default" : "outline"}
                size="sm"
                onClick={() => typeof page === "number" && onPageChange(page)}
                disabled={page === "..."}
                className="h-8 w-8 p-0"
              >
                {page}
              </Button>
            ))}

            {/* Next page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pageNumber + 1)}
              disabled={!canGoNext}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            {/* Last page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={!canGoNext}
              className="h-8 w-8 p-0"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
