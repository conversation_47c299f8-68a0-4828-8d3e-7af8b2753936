import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  GoogleMapW3W,
  Popconfirm,
  CommonCompactView,
  message,
  Row,
  Col,
  Input,
  Checkbox,
} from '../../../../components';
import { getAllBranches, deleteBranch, addBranch, updateBranch } from '../../../../services';
import Context from '../../../../context';
import { Link } from 'react-router-dom';
import { PlusOutlined } from '@ant-design/icons';
import { buildCommonApiValues } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import mapSvg from '../../../../assets/images/gmap.svg';
import CommonDrawer from '../../../../components/CommonDrawer';
import { get } from 'lodash';
import { CRUD, <PERSON>s, ModuleNames } from '../../../../constants';

const Branch = () => {
  const [context, setContext] = useContext(Context);
  const [branches, setBranches] = useState([]);
  const [branch, setBranch] = useState({});
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');
  const [isMaps, setIsMaps] = useState(false);
  const [mapValues, setMapValues] = useState({});
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);

  const [form] = Form.useForm();
  const [formInitValues, setFormInitValues] = useState(null);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const makeActive = (data) => {
    updateBranchCall({ ...data, deleted: false });
  };

  const switchTableData = () => {
    showDeleted ? setBranches(tableData) : setBranches(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const closeAdd = () => {
    setVisible(false);
    if (action === 'edit') form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveBranchAction({ ...commonValues, ...values, properties: {} });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateBranchAction(values);
      if (type === 'add') form.resetFields({});
      setFormInitValues(null);
      setVisible(false);
    }
  };

  const saveBranchAction = (branch) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addBranch(branch)
      .then((res) => {
        setBranches((state) => [res.data, ...state]);
        message.success('Succesfully Added branch');
        form.resetFields();
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Branch, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editBranchAction = (branch) => {
    form.setFieldsValue({ ...branch });
    setAction('edit');
    setBranch(branch);
    setVisible(true);
  };

  const updateBranchCall = (values) => {
    const data = { ...branch, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateBranch(data)
      .then((res) => {
        setBranches((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, { ...i, ...res.data });
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        message.success('Succesfully Updated branch');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Branch, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateBranchAction = (values) => {
    updateBranchCall(values);
  };

  const setDeleteBranchAction = (branchId, visible) => {
    setBranches((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === branchId).visible = visible;
      tempData
        .filter((x) => x.id !== branchId)
        .forEach((b) => {
          b.visible = false;
        });
      return tempData;
    });
  };

  const deleteBranchAction = (branchId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteBranch(branchId)
      .then(() => {
        setBranches((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === branchId).visible = false;
          tempState.find((x) => x.id === branchId).deleted = true;
          return tempState.filter((x) => x.id !== branchId);
        });
        message.success('Succesfully Deleted branch');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Branch, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  useEffect(() => {
    form.setFieldsValue({ ...form.getFieldsValue(), ...mapValues });
    // eslint-disable-next-line
  }, [mapValues]);

  useEffect(() => {
    if (formInitValues) {
      form.setFieldsValue({ ...formInitValues });
      setVisible(true);
    }
    // eslint-disable-next-line
  }, [formInitValues]);

  const clearForm = () => {
    form.resetFields();
  };

  useEffect(() => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      getAllBranches(context.profile.tenantId)
        .then((res) => {
          setTableData(res.data);
          setBranches(res.data.filter((x) => x.deleted === false));
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to get Branch details, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
    }
    // eslint-disable-next-line
  }, []);

  const tableCols = [
    {
      title: <strong> Name </strong>,
      key: 'name',
      render: (record) => (
        <PermissionContainer page={Pages.LOCATION} permission={CRUD.VIEW}>
          <Link to={`/device/management/configure/location/${record.id}`}>{record.name}</Link>
        </PermissionContainer>
      ),
    },
    { title: <strong> Address </strong>, key: 'address', dataIndex: 'address' },
    { title: <strong> Geo </strong>, key: 'geoJson', dataIndex: 'geoJson' },
    {
      title: <strong> GPS </strong>,
      key: 'gpsPoint',
      render: (record) => (
        <>
          {record.gpsPoint?.latitude},{record.gpsPoint?.longitude}
        </>
      ),
    },

    {
      title: <strong> Actions </strong>,
      key: 'Actions',
      width: 330,
      render: (record) => (
        <>
          {record.deleted ? (
            <Row>
              <Col>
                <PermissionContainer page={Pages.BRANCH} permission={CRUD.UPDATE}>
                  <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                    Activate
                  </Button>
                </PermissionContainer>
              </Col>
              <Col>
                <p className="lastModifiedDate">
                  Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
                </p>
              </Col>
            </Row>
          ) : (
            <>
              <PermissionContainer page={Pages.BRANCH} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => editBranchAction(record)} className="actionButton">
                  Edit
                </Button>
              </PermissionContainer>
              <PermissionContainer page={Pages.BRANCH} permission={CRUD.DELETE}>
                <Popconfirm
                  title={`Are you sure to delete branch ${record.name}?`}
                  visible={record.visible || false}
                  onConfirm={() => deleteBranchAction(record.id)}
                  onCancel={() => setDeleteBranchAction(record.id, false)}
                >
                  <Button
                    type="link"
                    onClick={() => {
                      setDeleteBranchAction(record.id, true);
                    }}
                    className="actionButton"
                  >
                    Delete
                  </Button>
                </Popconfirm>
              </PermissionContainer>
            </>
          )}
        </>
      ),
    },
  ];

  const branchBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.BRANCH}].displayName`,
    ModuleNames.BRANCH
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.CONFIGURE, branchBreadcrumbsName]} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.BRANCH} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Branch
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      {!context.isCompact ? (
        <>
          <Table
            size="small"
            scroll={{ x: true }}
            rowKey="id"
            loading={context.isLoading}
            columns={tableCols}
            dataSource={branches}
            rowClassName={(record) => record.deleted && 'rowInactive'}
          />
        </>
      ) : (
        <CommonCompactView
          data={branches}
          onEdit={editBranchAction}
          onDelete={deleteBranchAction}
          permissions={[
            { pageName: Pages.BRANCH, permission: CRUD.UPDATE, label: 'Edit' },
            { pageName: Pages.BRANCH, permission: CRUD.DELETE, label: 'Delete' },
          ]}
          title="name"
          dataList={[
            { label: 'GPS', value: 'gpsPoint', type: 'gps' },
            { label: 'GEO', value: 'geoJson' },
          ]}
        />
      )}
      <CommonDrawer title="Branch" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input branch Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Branch Name" />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Address"
            name="address"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input branch Address!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Branch Address" />
          </Form.Item>
          <Form.Item label="Geo" required>
            <Row justify="space-between" className="geoLocation">
              <Col xs={{ span: 21 }} span={22}>
                <Form.Item
                  shouldUpdate={true}
                  hasFeedback
                  name="geoJson"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: 'Please input branch Geo Location JSON!',
                      min: 1,
                      max: 2000,
                    },
                  ]}
                >
                  <Input placeholder="Branch Geo" />
                </Form.Item>
              </Col>
              <Col className="locationSelector">
                <img src={mapSvg} alt="mapSvg" onClick={() => setIsMaps(true)} />
              </Col>
            </Row>
          </Form.Item>
          <Form.Item label="GPS" required>
            <Input.Group compact>
              <Form.Item
                name={['gpsPoint', 'latitude']}
                noStyle
                hasFeedback
                rules={[{ required: true, message: 'Latitude is required' }]}
              >
                <Input style={{ width: '50%' }} placeholder="Latitude" />
              </Form.Item>
              <Form.Item
                name={['gpsPoint', 'longitude']}
                noStyle
                hasFeedback
                rules={[{ required: true, message: 'Longitude is required' }]}
              >
                <Input style={{ width: '50%' }} placeholder="Longitude" />
              </Form.Item>
            </Input.Group>
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" type="primary" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
        {isMaps && <GoogleMapW3W setIsMaps={setIsMaps} onCloseUpdate={setMapValues}></GoogleMapW3W>}
      </CommonDrawer>
    </>
  );
};

export default Branch;
