"use client";

import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { CheckCircle, XCircle } from "lucide-react";
import { TaggedAsset } from "@/types/tagged-asset";
import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";

import { cn } from "@/lib/utils";
import { TaggedAssetTablePagination } from "../tagged-asset/tagged-asset-table-pagination";

interface OnboardAssetDataTableProps {
  data: TaggedAsset[];
  isLoading?: boolean;
  onProvision: (taggedAsset: TaggedAsset, provisioned: boolean) => void;

  // Pagination props
  pagination: { page: number; pageSize: number };
  totalRecords: number;
  totalPages: number;
  availablePageSizes: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
}

export function OnboardAssetDataTable({
  data,
  isLoading = false,
  onProvision,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
}: OnboardAssetDataTableProps) {
  const columns: ColumnDef<TaggedAsset>[] = [
    {
      accessorKey: "taggedAssetInfo.assetName",
      header: "Asset Name",
      cell: ({ row }) => {
        const assetName = row.original.taggedAssetInfo?.assetName || "N/A";
        return <div className="font-medium">{assetName}</div>;
      },
    },
    {
      accessorKey: "taggedAssetInfo.assetExternalId",
      header: "Asset External ID",
      cell: ({ row }) => {
        const externalId =
          row.original.taggedAssetInfo?.assetExternalId || "N/A";
        return (
          <div className="text-sm text-muted-foreground">{externalId}</div>
        );
      },
    },
    {
      accessorKey: "taggedAssetInfo.tagName",
      header: "Tag Name",
      cell: ({ row }) => {
        const tagName = row.original.taggedAssetInfo?.tagName || "N/A";
        return <div className="font-medium">{tagName}</div>;
      },
    },
    {
      accessorKey: "taggedAssetInfo.tagExternalId",
      header: "Tag External ID",
      cell: ({ row }) => {
        const tagExternalId =
          row.original.taggedAssetInfo?.tagExternalId || "N/A";
        return (
          <div className="text-sm text-muted-foreground">{tagExternalId}</div>
        );
      },
    },
    {
      accessorKey: "taggedAssetInfo.assetType",
      header: "Asset Type",
      cell: ({ row }) => {
        const assetType = row.original.taggedAssetInfo?.assetType || "N/A";
        return (
          <Badge variant="outline" className="capitalize">
            {assetType}
          </Badge>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const deleted = row.original.deleted;

        if (deleted) {
          return (
            <Badge variant="destructive">
              <XCircle className="mr-1 h-3 w-3" />
              Inactive
            </Badge>
          );
        }

        // Show the actual status value from the API
        return (
          <Badge variant="default" className="capitalize">
            <CheckCircle className="mr-1 h-3 w-3" />
            {status || "N/A"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "provisioned",
      header: "Provisioned",
      cell: ({ row }) => {
        const taggedAsset = row.original;
        const provisioned = taggedAsset.provisioned || false;

        return (
          <div className="flex items-center space-x-2">
            <Switch
              checked={provisioned}
              onCheckedChange={(checked) => onProvision(taggedAsset, checked)}
              disabled={isLoading || taggedAsset.deleted}
            />
            <span className="text-sm text-muted-foreground">
              {provisioned ? "Yes" : "No"}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "createdBy",
      header: "Created By",
      cell: ({ row }) => {
        const createdBy = row.getValue("createdBy") as string;
        return (
          <div className="text-sm text-muted-foreground">
            {createdBy || "N/A"}
          </div>
        );
      },
    },
    {
      accessorKey: "createdDate",
      header: "Created Date",
      cell: ({ row }) => {
        const createdDate = row.getValue("createdDate") as string;
        if (!createdDate)
          return <div className="text-sm text-muted-foreground">N/A</div>;

        try {
          const date = new Date(createdDate);
          return (
            <div className="text-sm text-muted-foreground">
              {date.toLocaleDateString()}
            </div>
          );
        } catch {
          return (
            <div className="text-sm text-muted-foreground">Invalid Date</div>
          );
        }
      },
    },
  ];

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border">
        <DataTable
          columns={columns}
          data={data}
          isLoading={isLoading}
          rowClassName={() =>
            cn(
              "[&_tr[data-state=selected]]:bg-muted/50",
              "[&_tr:hover]:bg-muted/50"
            )
          }
        />
      </div>

      {/* Pagination */}
      <TaggedAssetTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />
    </div>
  );
}
