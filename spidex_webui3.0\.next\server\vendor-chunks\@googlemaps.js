"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@googlemaps";
exports.ids = ["vendor-chunks/@googlemaps"];
exports.modules = {

/***/ "(ssr)/./node_modules/@googlemaps/js-api-loader/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@googlemaps/js-api-loader/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ID: () => (/* binding */ DEFAULT_ID),\n/* harmony export */   Loader: () => (/* binding */ Loader),\n/* harmony export */   LoaderStatus: () => (/* binding */ LoaderStatus)\n/* harmony export */ });\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\nvar fastDeepEqual;\nvar hasRequiredFastDeepEqual;\n\nfunction requireFastDeepEqual () {\n\tif (hasRequiredFastDeepEqual) return fastDeepEqual;\n\thasRequiredFastDeepEqual = 1;\n\n\t// do not edit .js files directly - edit src/index.jst\n\n\n\n\tfastDeepEqual = function equal(a, b) {\n\t  if (a === b) return true;\n\n\t  if (a && b && typeof a == 'object' && typeof b == 'object') {\n\t    if (a.constructor !== b.constructor) return false;\n\n\t    var length, i, keys;\n\t    if (Array.isArray(a)) {\n\t      length = a.length;\n\t      if (length != b.length) return false;\n\t      for (i = length; i-- !== 0;)\n\t        if (!equal(a[i], b[i])) return false;\n\t      return true;\n\t    }\n\n\n\n\t    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n\t    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n\t    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n\t    keys = Object.keys(a);\n\t    length = keys.length;\n\t    if (length !== Object.keys(b).length) return false;\n\n\t    for (i = length; i-- !== 0;)\n\t      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n\t    for (i = length; i-- !== 0;) {\n\t      var key = keys[i];\n\n\t      if (!equal(a[key], b[key])) return false;\n\t    }\n\n\t    return true;\n\t  }\n\n\t  // true if both NaN, false otherwise\n\t  return a!==a && b!==b;\n\t};\n\treturn fastDeepEqual;\n}\n\nvar fastDeepEqualExports = requireFastDeepEqual();\nvar isEqual = /*@__PURE__*/getDefaultExportFromCjs(fastDeepEqualExports);\n\n/**\n * Copyright 2019 Google LLC. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at.\n *\n *      Http://www.apache.org/licenses/LICENSE-2.0.\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_ID = \"__googleMapsScriptId\";\n/**\n * The status of the [[Loader]].\n */\nvar LoaderStatus;\n(function (LoaderStatus) {\n    LoaderStatus[LoaderStatus[\"INITIALIZED\"] = 0] = \"INITIALIZED\";\n    LoaderStatus[LoaderStatus[\"LOADING\"] = 1] = \"LOADING\";\n    LoaderStatus[LoaderStatus[\"SUCCESS\"] = 2] = \"SUCCESS\";\n    LoaderStatus[LoaderStatus[\"FAILURE\"] = 3] = \"FAILURE\";\n})(LoaderStatus || (LoaderStatus = {}));\n/**\n * [[Loader]] makes it easier to add Google Maps JavaScript API to your application\n * dynamically using\n * [Promises](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise).\n * It works by dynamically creating and appending a script node to the the\n * document head and wrapping the callback function so as to return a promise.\n *\n * ```\n * const loader = new Loader({\n *   apiKey: \"\",\n *   version: \"weekly\",\n *   libraries: [\"places\"]\n * });\n *\n * loader.load().then((google) => {\n *   const map = new google.maps.Map(...)\n * })\n * ```\n */\nclass Loader {\n    /**\n     * Creates an instance of Loader using [[LoaderOptions]]. No defaults are set\n     * using this library, instead the defaults are set by the Google Maps\n     * JavaScript API server.\n     *\n     * ```\n     * const loader = Loader({apiKey, version: 'weekly', libraries: ['places']});\n     * ```\n     */\n    constructor({ apiKey, authReferrerPolicy, channel, client, id = DEFAULT_ID, language, libraries = [], mapIds, nonce, region, retries = 3, url = \"https://maps.googleapis.com/maps/api/js\", version, }) {\n        this.callbacks = [];\n        this.done = false;\n        this.loading = false;\n        this.errors = [];\n        this.apiKey = apiKey;\n        this.authReferrerPolicy = authReferrerPolicy;\n        this.channel = channel;\n        this.client = client;\n        this.id = id || DEFAULT_ID; // Do not allow empty string\n        this.language = language;\n        this.libraries = libraries;\n        this.mapIds = mapIds;\n        this.nonce = nonce;\n        this.region = region;\n        this.retries = retries;\n        this.url = url;\n        this.version = version;\n        if (Loader.instance) {\n            if (!isEqual(this.options, Loader.instance.options)) {\n                throw new Error(`Loader must not be called again with different options. ${JSON.stringify(this.options)} !== ${JSON.stringify(Loader.instance.options)}`);\n            }\n            return Loader.instance;\n        }\n        Loader.instance = this;\n    }\n    get options() {\n        return {\n            version: this.version,\n            apiKey: this.apiKey,\n            channel: this.channel,\n            client: this.client,\n            id: this.id,\n            libraries: this.libraries,\n            language: this.language,\n            region: this.region,\n            mapIds: this.mapIds,\n            nonce: this.nonce,\n            url: this.url,\n            authReferrerPolicy: this.authReferrerPolicy,\n        };\n    }\n    get status() {\n        if (this.errors.length) {\n            return LoaderStatus.FAILURE;\n        }\n        if (this.done) {\n            return LoaderStatus.SUCCESS;\n        }\n        if (this.loading) {\n            return LoaderStatus.LOADING;\n        }\n        return LoaderStatus.INITIALIZED;\n    }\n    get failed() {\n        return this.done && !this.loading && this.errors.length >= this.retries + 1;\n    }\n    /**\n     * CreateUrl returns the Google Maps JavaScript API script url given the [[LoaderOptions]].\n     *\n     * @ignore\n     * @deprecated\n     */\n    createUrl() {\n        let url = this.url;\n        url += `?callback=__googleMapsCallback&loading=async`;\n        if (this.apiKey) {\n            url += `&key=${this.apiKey}`;\n        }\n        if (this.channel) {\n            url += `&channel=${this.channel}`;\n        }\n        if (this.client) {\n            url += `&client=${this.client}`;\n        }\n        if (this.libraries.length > 0) {\n            url += `&libraries=${this.libraries.join(\",\")}`;\n        }\n        if (this.language) {\n            url += `&language=${this.language}`;\n        }\n        if (this.region) {\n            url += `&region=${this.region}`;\n        }\n        if (this.version) {\n            url += `&v=${this.version}`;\n        }\n        if (this.mapIds) {\n            url += `&map_ids=${this.mapIds.join(\",\")}`;\n        }\n        if (this.authReferrerPolicy) {\n            url += `&auth_referrer_policy=${this.authReferrerPolicy}`;\n        }\n        return url;\n    }\n    deleteScript() {\n        const script = document.getElementById(this.id);\n        if (script) {\n            script.remove();\n        }\n    }\n    /**\n     * Load the Google Maps JavaScript API script and return a Promise.\n     * @deprecated, use importLibrary() instead.\n     */\n    load() {\n        return this.loadPromise();\n    }\n    /**\n     * Load the Google Maps JavaScript API script and return a Promise.\n     *\n     * @ignore\n     * @deprecated, use importLibrary() instead.\n     */\n    loadPromise() {\n        return new Promise((resolve, reject) => {\n            this.loadCallback((err) => {\n                if (!err) {\n                    resolve(window.google);\n                }\n                else {\n                    reject(err.error);\n                }\n            });\n        });\n    }\n    importLibrary(name) {\n        this.execute();\n        return google.maps.importLibrary(name);\n    }\n    /**\n     * Load the Google Maps JavaScript API script with a callback.\n     * @deprecated, use importLibrary() instead.\n     */\n    loadCallback(fn) {\n        this.callbacks.push(fn);\n        this.execute();\n    }\n    /**\n     * Set the script on document.\n     */\n    setScript() {\n        var _a, _b;\n        if (document.getElementById(this.id)) {\n            // TODO wrap onerror callback for cases where the script was loaded elsewhere\n            this.callback();\n            return;\n        }\n        const params = {\n            key: this.apiKey,\n            channel: this.channel,\n            client: this.client,\n            libraries: this.libraries.length && this.libraries,\n            v: this.version,\n            mapIds: this.mapIds,\n            language: this.language,\n            region: this.region,\n            authReferrerPolicy: this.authReferrerPolicy,\n        };\n        // keep the URL minimal:\n        Object.keys(params).forEach(\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        (key) => !params[key] && delete params[key]);\n        if (!((_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.maps) === null || _b === void 0 ? void 0 : _b.importLibrary)) {\n            // tweaked copy of https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n            // which also sets the base url, the id, and the nonce\n            /* eslint-disable */\n            ((g) => {\n                // @ts-ignore\n                let h, a, k, p = \"The Google Maps JavaScript API\", c = \"google\", l = \"importLibrary\", q = \"__ib__\", m = document, b = window;\n                // @ts-ignore\n                b = b[c] || (b[c] = {});\n                // @ts-ignore\n                const d = b.maps || (b.maps = {}), r = new Set(), e = new URLSearchParams(), u = () => \n                // @ts-ignore\n                h || (h = new Promise((f, n) => __awaiter(this, void 0, void 0, function* () {\n                    var _a;\n                    yield (a = m.createElement(\"script\"));\n                    a.id = this.id;\n                    e.set(\"libraries\", [...r] + \"\");\n                    // @ts-ignore\n                    for (k in g)\n                        e.set(k.replace(/[A-Z]/g, (t) => \"_\" + t[0].toLowerCase()), g[k]);\n                    e.set(\"callback\", c + \".maps.\" + q);\n                    a.src = this.url + `?` + e;\n                    d[q] = f;\n                    a.onerror = () => (h = n(Error(p + \" could not load.\")));\n                    // @ts-ignore\n                    a.nonce = this.nonce || ((_a = m.querySelector(\"script[nonce]\")) === null || _a === void 0 ? void 0 : _a.nonce) || \"\";\n                    m.head.append(a);\n                })));\n                // @ts-ignore\n                d[l] ? console.warn(p + \" only loads once. Ignoring:\", g) : (d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n)));\n            })(params);\n            /* eslint-enable */\n        }\n        // While most libraries populate the global namespace when loaded via bootstrap params,\n        // this is not the case for \"marker\" when used with the inline bootstrap loader\n        // (and maybe others in the future). So ensure there is an importLibrary for each:\n        const libraryPromises = this.libraries.map((library) => this.importLibrary(library));\n        // ensure at least one library, to kick off loading...\n        if (!libraryPromises.length) {\n            libraryPromises.push(this.importLibrary(\"core\"));\n        }\n        Promise.all(libraryPromises).then(() => this.callback(), (error) => {\n            const event = new ErrorEvent(\"error\", { error }); // for backwards compat\n            this.loadErrorCallback(event);\n        });\n    }\n    /**\n     * Reset the loader state.\n     */\n    reset() {\n        this.deleteScript();\n        this.done = false;\n        this.loading = false;\n        this.errors = [];\n        this.onerrorEvent = null;\n    }\n    resetIfRetryingFailed() {\n        if (this.failed) {\n            this.reset();\n        }\n    }\n    loadErrorCallback(e) {\n        this.errors.push(e);\n        if (this.errors.length <= this.retries) {\n            const delay = this.errors.length * Math.pow(2, this.errors.length);\n            console.error(`Failed to load Google Maps script, retrying in ${delay} ms.`);\n            setTimeout(() => {\n                this.deleteScript();\n                this.setScript();\n            }, delay);\n        }\n        else {\n            this.onerrorEvent = e;\n            this.callback();\n        }\n    }\n    callback() {\n        this.done = true;\n        this.loading = false;\n        this.callbacks.forEach((cb) => {\n            cb(this.onerrorEvent);\n        });\n        this.callbacks = [];\n    }\n    execute() {\n        this.resetIfRetryingFailed();\n        if (this.loading) {\n            // do nothing but wait\n            return;\n        }\n        if (this.done) {\n            this.callback();\n        }\n        else {\n            // short circuit and warn if google.maps is already loaded\n            if (window.google && window.google.maps && window.google.maps.version) {\n                console.warn(\"Google Maps already loaded outside @googlemaps/js-api-loader. \" +\n                    \"This may result in undesirable behavior as options and script parameters may not match.\");\n                this.callback();\n                return;\n            }\n            this.loading = true;\n            this.setScript();\n        }\n    }\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@googlemaps/js-api-loader/dist/index.mjs\n");

/***/ })

};
;