/* eslint-disable react/jsx-key */
import { useEffect, useState, useContext, useRef } from 'react';
import {
  Table,
  Input,
  Checkbox,
  Upload,
  Button,
  Form,
  Select,
  Popconfirm,
  CommonDrawer,
  CommonCompactView,
  Drawer,
  Tabs,
  message,
  Row,
  Col,
  Pagination,
} from '../../../components';
import {
  updateVendor,
  deleteVendor,
  addVendor,
  getSearchVendors,
  getAllVendorsByPagination,
  getAllWorkersByPagination,
  getAllVehiclesByPagination,
} from '../../../services';
import Context from '../../../context';
import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { buildCommonApiValues, csvFile, normFile, calcDrawerWidth } from '../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../shared';
import { get } from 'lodash';
import { country, CRUD, InputRegex, Pages, state, ModuleNames, MastersPageSizeDefault } from '../../../constants';
import s from './index.module.less';

const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

const Vendor = () => {
  const [context, setContext] = useContext(Context);
  const [vendors, setVendors] = useState([]);
  const [totalVendors, setTotalVendors] = useState({ items: 0, current: 1, pageSize: MastersPageSizeDefault });
  const [workers, setWorkers] = useState([]);
  const pageSizeWorkers = useRef(0);
  const pageSizeVehicles = useRef(0);
  const [vehicles, setVehicles] = useState([]);
  const [vendorsOriginal, setVendorsOriginal] = useState([]);
  const [vendor, setVendor] = useState({});
  const [visible, setVisible] = useState(false);
  const [visibleAsset, setVisibleAsset] = useState(false);
  const [vendorInfo, setVendorInfo] = useState(false);
  const [action, setAction] = useState('new');
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setVendors(tableData) : setVendors(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const [formInitValues, setFormInitValues] = useState({ attributes: [{ type: null, value: null, name: null }] });

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    setVendorInfo(false);
    if (action === 'edit') form.resetFields();
  };

  const onPaginationChangeWorkers = (e) => {
    getWorkers(+e - 1);
    setTotalWorkers((ps) => ({ ...ps, current: +e - 1 }));
  };
  const [totalWorkers, setTotalWorkers] = useState({ items: 0, current: 0, pageSize: MastersPageSizeDefault });
  const getWorkers = async (page = pageSizeWorkers.current) => {
    const makeCall = () => {
      getAllWorkersByPagination(
        context.profile.tenantId,
        {
          page: page,
          size: MastersPageSizeDefault,
        },
        vendor.id
      ).then((res) => {
        setWorkers([...(res.data?.content || [])]);
        setTotalWorkers((ps) => ({ ...ps, items: res.data.totalElements }));
      });
    };
    makeCall();
  };

  const onPaginationChangeVehicles = (e) => {
    getVehicles(+e - 1);
    setTotalVehicles((ps) => ({ ...ps, current: +e - 1 }));
  };
  const [totalVehicles, setTotalVehicles] = useState({ items: 0, current: 0, pageSize: MastersPageSizeDefault });
  const getVehicles = async (page = pageSizeVehicles.current) => {
    const makeVehicleCall = () => {
      getAllVehiclesByPagination(
        context.profile.tenantId,
        {
          page: page,
          size: MastersPageSizeDefault,
        },
        vendor.id
      ).then((res) => {
        setVehicles([...(res.data?.content || [])]);
        setTotalVehicles((ps) => ({ ...ps, items: res.data.totalElements }));
      });
    };
    makeVehicleCall();
  };

  const showDrawer = async (vendorSelected) => {
    setVisibleAsset(true);
    setVendor(vendorSelected);
  };

  useEffect(() => {
    const init = async () => {
      if (!vendor?.id) return;
      await getWorkers();
      await getVehicles();
    };
    init();
    // eslint-disable-next-line
  }, [vendor]);

  const onClose = () => {
    setVisibleAsset(false);
  };

  const dummyCustomRequest = (e) => {
    e.onSuccess(null, e.file);
  };

  const [tableModeNormal, setTableModeNormal] = useState(true);

  const onVendorSearch = async (e) => {
    if (e) {
      try {
        const vendors = await getSearchVendors(context.profile.tenantId, e);
        setVendors(vendors.data.filter((x) => x.deleted === false));
        setTableModeNormal(false);
      } catch {
        message.error('Could not retrieve vendors');
      }
    } else {
      setTableModeNormal(true);
      setVendors(vendorsOriginal);
    }
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();

    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveVendorAction({
        registrationTime: commonValues.createdTime,
        lastModified: commonValues.modifiedTime,
        modifiedBy: commonValues.modifiedBy,
        createdBy: commonValues.createdBy,
        tenantId: commonValues.tenantId,
        ...values,
      });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      updateVendorAction(values);
      if (type === 'add') form.resetFields({});
      setFormInitValues(null);
      setVisible(false);
    }
  };

  const saveVendorAction = async (vendor) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const newVendor = { ...vendor, vendorIdImage: vendor.vendorIdImageUpload };
    delete newVendor.vendorIdImageUpload;
    addVendor(newVendor)
      .then((res) => {
        setVendors((state) => [res.data, ...state]);
        message.success('Succesfully Added vendor');
        form.resetFields();
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Vendor, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editVendorAction = (vendor) => {
    let skill = '';
    if (vendor.skilled === 1) skill = 'skilled';
    if (vendor.unSkilled === 1) skill = 'unSkilled';
    if (vendor.semiSkilled === 1) skill = 'semiSkilled';
    form.setFieldsValue({ ...vendor, skill });
    setAction('edit');
    setVendor(vendor);
    setVisible(true);
  };

  const updateVendorCall = (values) => {
    const data = { ...vendor, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateVendor(data)
      .then((res) => {
        setVendors((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, res.data);
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        message.success('Succesfully Updated vendor');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update vendor, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateVendorAction = (values) => {
    updateVendorCall(values);
  };

  const makeActive = (data) => {
    updateVendorCall({ ...data, deleted: false });
  };

  const setDeleteVendorAction = (userId, visible) => {
    setVendors((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === userId).visible = visible;
      tempData
        .filter((x) => x.id !== userId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteVendorAction = (userId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteVendor(userId)
      .then(() => {
        setVendors((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === userId).visible = false;
          tempState.find((x) => x.id === userId).deleted = true;
          return [...state].filter((x) => x.id !== userId);
        });
        message.success('Succesfully Deleted Vendor');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Vendor, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const clearForm = () => {
    form.resetFields();
  };

  const onPaginationChange = (e) => {
    init(+e - 1);
  };

  const init = async (page = 0) => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: true,
        };
      });
      const pagination = {
        size: totalVendors.pageSize,
        page: page,
      };

      getAllVendorsByPagination(context.profile.tenantId, pagination)
        .then((res) => {
          setTableData(res.data.content);
          const vendors = (res.data?.content || []).filter((x) => x.deleted === false);
          setVendors(vendors);
          setVendorsOriginal(vendors);
          setTotalVendors((ps) => ({ ...ps, current: page, items: res.data.totalElements }));
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to get Vendor details, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
    }
  };

  const permissions = context.pagePermissions;
  const permitWorker =
    permissions.filter(
      (p) => p.pageName === Pages.WORKER && p.permissions.filter((crud) => crud === CRUD.VIEW).length > 0
    ).length > 0;
  const permitVehicle =
    permissions.filter(
      (p) => p.pageName === Pages.VEHICLE && p.permissions.filter((crud) => crud === CRUD.VIEW).length > 0
    ).length > 0;
  const checkAssetPagesPermissions = () => {
    if (permitWorker) return 'worker';
    if (permitVehicle) return 'vehicle';
    return '';
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, []);

  const viewVendorInfo = (e) => {
    setVendor(e);
    setVendorInfo(true);
  };

  const csvFileDownload = () => {
    const exportData = tableData.map(({ vendorName, vendorId }) => {
      return { vendorId, vendorName };
    });
    csvFile({ data: exportData, fileName: `vendors_${new Date().toDateString()}`.replaceAll(' ', '_').toLowerCase() });
  };

  const idTypes = ['Aadhaar', 'PAN', 'Driving License', 'Voter ID', 'Registration Certificate', 'Lease Agreement'];

  const tableCols = [
    { title: <strong> Name </strong>, key: 'vendorName', dataIndex: 'vendorName', width: '30%' },
    { title: <strong> ID </strong>, key: 'vendorId', dataIndex: 'vendorId', width: '30%' },
    {
      title: <strong> Actions </strong>,
      width: 350,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                Activate
              </Button>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.VENDOR} permission={CRUD.VIEW}>
              <Button type="link" onClick={() => viewVendorInfo(record)} className="actionButton">
                View
              </Button>
            </PermissionContainer>

            <PermissionContainer page={Pages.VENDOR} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editVendorAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>

            <Button type="link" onClick={() => showDrawer(record)} className="actionButton">
              Assets
            </Button>

            <PermissionContainer page={Pages.VENDOR} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete user ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteVendorAction(record.id)}
                onCancel={() => setDeleteVendorAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteVendorAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];
  const tableColsForWorker = [
    { title: <strong> Name </strong>, key: 'workerName', dataIndex: 'workerName' },
    { title: <strong> Vendor </strong>, dataIndex: 'vendorName' },
    { title: <strong> Skill Category </strong>, key: 'skillCategory', dataIndex: 'skillCategory', width: 160 },
    { title: <strong> Skill Type </strong>, key: 'skillType', dataIndex: 'skillType', width: 160 },
  ];
  const tableColsForVehicle = [
    { title: <strong> Number </strong>, key: 'vechicleNumber', dataIndex: 'vechicleNumber' },
    { title: <strong> Vendor </strong>, key: 'vendorName', dataIndex: 'vendorName' },
    { title: <strong> Type </strong>, key: 'vehicleType', dataIndex: 'vehicleType' },
  ];

  const vendorBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.VENDOR}].displayName`,
    ModuleNames.VENDOR
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.MASTERS, vendorBreadcrumbsName, 'Register Vendor']} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.VENDOR} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Vendor
            </Button>
          </PermissionContainer>
          <PermissionContainer page={Pages.VENDOR} permission={CRUD.VIEW}>
            <Button size="small" onClick={csvFileDownload} type="primary" className="downloadButton">
              Export <DownloadOutlined />
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <Row className="searchDiv" justify="end">
        <Col xs={24} lg={6}>
          <Search placeholder="Enter Search" allowClear enterButton onSearch={onVendorSearch} />
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <>
            {tableModeNormal ? (
              <>
                <Table
                  size="small"
                  scroll={{ x: true }}
                  rowKey="id"
                  columns={tableCols}
                  dataSource={vendors}
                  rowClassName={(record) => record.deleted && 'rowInactive'}
                  pagination={false}
                />
                <Row justify="end">
                  <Col>
                    <div className="m-2">
                      <Pagination
                        onChange={onPaginationChange}
                        current={totalVendors.current + 1}
                        total={totalVendors.items}
                        defaultPageSize={totalVendors.pageSize}
                        showSizeChanger={false}
                        showQuickJumper={false}
                      />
                    </div>
                  </Col>
                </Row>
              </>
            ) : (
              <Table
                size="small"
                scroll={{ x: true }}
                rowKey="id"
                columns={tableCols}
                dataSource={vendors}
                rowClassName={(record) => record.deleted && 'rowInactive'}
              />
            )}
          </>
        ) : (
          <>
            <CommonCompactView
              data={vendors}
              onEdit={editVendorAction}
              onDelete={deleteVendorAction}
              permissions={[
                { pageName: Pages.VENDOR, permission: CRUD.UPDATE, label: 'Edit' },
                { pageName: Pages.VENDOR, permission: CRUD.DELETE, label: 'Delete' },
              ]}
              title="vendorName"
              dataList={[
                { label: 'ID', value: 'vendorId' },
                { label: 'Type', value: 'vendorType' },
              ]}
            />
            <Row justify="end">
              <Col>
                <div className="m-2">
                  <Pagination
                    onChange={onPaginationChange}
                    current={totalVendors.current + 1}
                    total={totalVendors.items}
                    defaultPageSize={totalVendors.pageSize}
                    showSizeChanger={false}
                    showQuickJumper={false}
                  />
                </div>
              </Col>
            </Row>
          </>
        )}
      </>

      <CommonDrawer title="Vendor Infomation" visible={vendorInfo} closeAdd={closeAdd}>
        <div className="infoDrawer">
          {vendor ? (
            <Row className="infoContainer" span={24}>
              <Col className="info" span={24}>
                <Row className="infoImg" justify="center" span={24}>
                  <Col>
                    <img
                      src={`data:image/jpg;base64,${vendor.vendorIdImage}`}
                      onError={({ currentTarget }) => {
                        currentTarget.onerror = null; // prevents looping
                        currentTarget.src = '/assets/images/notFound.png';
                      }}
                      alt="Not Found"
                    />
                  </Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Name :
                  </Col>
                  <Col>{vendor.vendorName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Vendor ID :
                  </Col>
                  <Col>{vendor.vendorType}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    ID Type :
                  </Col>
                  <Col>{vendor.vendorIdType}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    ID Number :
                  </Col>
                  <Col>{vendor.vendorIdNumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Phone Number :
                  </Col>
                  <Col>{vendor.vendorPhoneNumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Address 1 :
                  </Col>
                  <Col>{vendor.vendorAddress?.address1}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Adderess 2 :
                  </Col>
                  <Col>{vendor.vendorAddress?.address2}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    City :
                  </Col>
                  <Col>{vendor.vendorAddress?.city}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    State :
                  </Col>
                  <Col>{vendor.vendorAddress?.state}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Country :
                  </Col>
                  <Col>{vendor.vendorAddress?.country}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    pincode :
                  </Col>
                  <Col>{vendor.vendorAddress?.pincode}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Address Phone :
                  </Col>
                  <Col>{vendor.vendorAddress?.phonenumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Contact Person :
                  </Col>
                  <Col>{vendor.vendorContactPersonName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    GST Number :
                  </Col>
                  <Col>{vendor.vendorGstNumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Properties 1 :
                  </Col>
                  <Col>{vendor.properties?.additionalProp1}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Properties 2 :
                  </Col>
                  <Col>{vendor.properties?.additionalProp2}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Properties 3 :
                  </Col>
                  <Col>{vendor.properties?.additionalProp3}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Mail ID :
                  </Col>
                  <Col>{vendor.mailId}</Col>
                </Row>
                {vendor.attributes && (
                  <Row>
                    <Col className="infoTitle" span={10}>
                      Attribute :
                    </Col>
                    <Col>
                      {
                        <div>
                          <Row gutter={20} justify="space-between">
                            <Col> Name </Col>
                            <Col> Value </Col>
                            <Col> Type </Col>
                          </Row>
                          {vendor.attributes?.map((i) => (
                            <Row gutter={20} justify="space-between">
                              <Col>{i.name}</Col>
                              <Col>{i.value}</Col>
                              <Col>{i.type}</Col>
                            </Row>
                          ))}
                        </div>
                      }
                    </Col>
                  </Row>
                )}
                <br />
                <Row justify="center">
                  <Button type="primary" onClick={() => editVendorAction(vendor)}>
                    Edit
                  </Button>
                </Row>
              </Col>
            </Row>
          ) : (
            <></>
          )}
        </div>
      </CommonDrawer>

      <CommonDrawer title="Vendor" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          autoComplete={'new-password'}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 16 }}
          labelCol={{ span: 8 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Name"
            name="vendorName"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Legal Name Vendor!',
                min: 3,
                max: 30,
              },
              { pattern: `^[a-zA-Z ]+$`, message: 'Use Letters only!' },
            ]}
          >
            <Input placeholder="Full Name" />
          </Form.Item>

          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Vendor ID"
            name="vendorId"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input MEIL Vendor ID!',
                min: 3,
                max: 10,
              },
              { pattern: `^[a-zA-Z0-9]+$`, message: 'Use Letters and Numbers only!' },
            ]}
          >
            <Input placeholder="MEIL Vendor ID" />
          </Form.Item>

          <Form.Item
            label="GST Number"
            name="vendorGstNumber"
            rules={[
              {
                required: true,
                whitespace: true,
                min: 15,
                max: 15,
                message: 'Please input GST Number',
              },
              { pattern: `^[a-zA-Z0-9]+$`, message: 'Use Letters and Numbers only!' },
            ]}
          >
            <Input placeholder="GST Number" />
          </Form.Item>

          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="ID Type"
            name="vendorIdType"
            rules={[
              {
                required: true,
                message: 'Please input ID Type!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="ID Type"
            >
              {idTypes.map((id, index) => (
                <Option title={id} key={index} value={id}>
                  {id}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="ID Number"
            name="vendorIdNumber"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input ID Number!',
                min: 5,
                max: 20,
              },
              { pattern: `^[a-zA-Z0-9]+$`, message: 'Use Letters and Numbers only!' },
            ]}
          >
            <Input placeholder="ID Number" />
          </Form.Item>

          <Form.Item valuePropName="file" name="vendorIdImageUpload" label="Upload" getValueFromEvent={normFile}>
            <Upload maxCount={1} customRequest={dummyCustomRequest} name="vendorIdImageUpload">
              <Button icon={<UploadOutlined />}>Upload</Button>
            </Upload>
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Point of Contact"
            name="vendorContactPersonName"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Contact Person Name!',
                min: 3,
                max: 20,
              },
              { pattern: `^[a-zA-Z ]+$`, message: 'Use Letters only!' },
            ]}
          >
            <Input placeholder="Contact Person Name" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Phone Number"
            name="vendorPhoneNumber"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Mobile Number!',
              },
              { pattern: InputRegex.Mobile, message: 'Enter a valid Mobile Number' },
            ]}
          >
            <Input placeholder="Phone Number" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Mail Id"
            name="mailId"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Email ID!',
              },
              { pattern: InputRegex.Email, message: 'Enter a valid Email ID' },
            ]}
          >
            <Input placeholder="Mail Id" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Address Line 1"
            name={['vendorAddress', 'address1']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Address/H. No.!',
                min: 4,
              },
            ]}
          >
            <Input placeholder="Locality" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Address Line 2"
            name={['vendorAddress', 'address2']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Address/Street!',
                min: 4,
              },
            ]}
          >
            <Input placeholder="Street" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="City"
            name={['vendorAddress', 'city']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input City!',
                min: 3,
              },
            ]}
          >
            <Input placeholder="City" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="State"
            name={['vendorAddress', 'state']}
            rules={[
              {
                required: true,
                message: 'Please input State!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="State"
            >
              {Object.keys(state).map((b) => (
                <Option title={state[b]} key={state[b]} value={state[b]}>
                  {state[b]}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            initialValue="India"
            label="Country"
            name={['vendorAddress', 'country']}
            rules={[
              {
                required: true,
                message: 'Please input Country!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Country"
            >
              {Object.keys(country).map((b) => (
                <Option title={country[b]} key={country[b]} value={country[b]}>
                  {country[b]}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Pincode"
            name={['vendorAddress', 'pincode']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Pincode!',
                min: 6,
              },
              { pattern: `^[0-9]+$`, message: 'Use Numbers only!' },
            ]}
          >
            <Input placeholder="Pincode" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Office Contact"
            name="officeContactPhno"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Office Phone Number!',
              },
              { pattern: InputRegex.Mobile, message: 'Enter a valid Mobile Number' },
            ]}
          >
            <Input placeholder="Phone Number" />
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>

      <Drawer
        width={calcDrawerWidth(true)}
        className={s.drawer}
        onClose={onClose}
        bodyStyle={{ padding: '0px' }}
        visible={visibleAsset}
      >
        <h1 className={s.title}> Assets Information </h1>
        <Tabs defaultActiveKey={checkAssetPagesPermissions()} className="p-3">
          {permitWorker && (
            <TabPane tab="Worker" key="worker">
              <Table
                size="small"
                scroll={{ x: true }}
                rowKey="id"
                columns={tableColsForWorker}
                dataSource={workers}
                pagination={false}
              />
              <Row justify="end">
                <Col>
                  <div className="m-2">
                    <Pagination
                      onChange={onPaginationChangeWorkers}
                      current={totalWorkers.current + 1}
                      total={totalWorkers.items}
                      defaultPageSize={totalWorkers.pageSize}
                      showSizeChanger={false}
                      showQuickJumper={false}
                    />
                  </div>
                </Col>
              </Row>
            </TabPane>
          )}
          {permitVehicle && (
            <TabPane tab="Vehicle" key="vehicle">
              <Table
                size="small"
                scroll={{ x: true }}
                rowKey="id"
                columns={tableColsForVehicle}
                dataSource={vehicles}
                pagination={false}
              />
              <Row justify="end">
                <Col>
                  <div className="m-2">
                    <Pagination
                      onChange={onPaginationChangeVehicles}
                      current={totalVehicles.current + 1}
                      total={totalVehicles.items}
                      defaultPageSize={totalVehicles.pageSize}
                      showSizeChanger={false}
                      showQuickJumper={false}
                    />
                  </div>
                </Col>
              </Row>
            </TabPane>
          )}
        </Tabs>
      </Drawer>
    </>
  );
};

export default Vendor;
