import { query } from "@/lib/api/apollo-client";
import { auth } from "@/lib/auth/auth";
import { gql } from "@apollo/client";
import { redirect } from "next/navigation";

const GET_LOCATIONS = gql`
  query Locations($tenantId: String!) {
    locations(tenantId: $tenantId) {
      id
      name
    }
  }
`;

export default async function AssetTrackingRedirectPage() {
  const session = await auth();
  const tenantId = session?.user.tenantId;
  const context = {
    headers: { Authorization: `Bearer ${session?.user.token}` },
  };
  const {
    data: { locations = [] },
  } = await query({ query: GET_LOCATIONS, variables: { tenantId }, context });
  if (locations.length > 0) {
    redirect(`/asset-tracking/${locations[0].id}`);
  }
  return null;
}
