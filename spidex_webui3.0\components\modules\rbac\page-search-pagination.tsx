"use client";

import { useState } from "react";
import { Search, X, Filter, RotateCcw } from "lucide-react";
import { PageSearchFilters } from "@/types/rbac";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";

interface PageSearchPaginationProps {
  searchFilters: PageSearchFilters;
  showDeleted: boolean;
  totalRecords: number;
  totalAllRecords: number;
  activeRecordsCount: number;
  inactiveRecordsCount: number;
  onUpdateSearchFilters: (filters: Partial<PageSearchFilters>) => void;
  onClearSearch: () => void;
  onToggleShowDeleted: () => void;
}

export function PageSearchPagination({
  searchFilters,
  showDeleted,
  totalRecords,
  totalAllRecords,
  activeRecordsCount,
  inactiveRecordsCount,
  onUpdateSearchFilters,
  onClearSearch,
  onToggleShowDeleted,
}: PageSearchPaginationProps) {
  const [localFilters, setLocalFilters] = useState<PageSearchFilters>(searchFilters);

  const handleFilterChange = (key: keyof PageSearchFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onUpdateSearchFilters({ [key]: value });
  };

  const handleClearFilters = () => {
    setLocalFilters({});
    onClearSearch();
  };

  const hasActiveFilters = Object.values(searchFilters).some(value => 
    value !== undefined && value !== "" && value !== null
  );

  return (
    <div className="space-y-4">
      {/* Search Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Page Name Search */}
        <div className="space-y-2">
          <Label htmlFor="page-name-search">Page Name</Label>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              id="page-name-search"
              placeholder="Search by page name..."
              value={localFilters.pageName || ""}
              onChange={(e) => handleFilterChange("pageName", e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <Label>&nbsp;</Label>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleClearFilters}
              disabled={!hasActiveFilters}
              className="flex-1"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear
            </Button>
          </div>
        </div>
      </div>

      {/* Show Deleted Toggle and Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="show-deleted"
              checked={showDeleted}
              onCheckedChange={onToggleShowDeleted}
            />
            <Label htmlFor="show-deleted">Show Deleted</Label>
          </div>
          
          {hasActiveFilters && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Filter className="h-3 w-3" />
              Filters Active
            </Badge>
          )}
        </div>

        {/* Record Counts */}
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <span>Total: {totalAllRecords}</span>
          <span>Active: {activeRecordsCount}</span>
          <span>Deleted: {inactiveRecordsCount}</span>
          <span>Showing: {totalRecords}</span>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium">Active filters:</span>
          {searchFilters.pageName && (
            <Badge variant="outline" className="flex items-center gap-1">
              Name: {searchFilters.pageName}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => handleFilterChange("pageName", "")}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
