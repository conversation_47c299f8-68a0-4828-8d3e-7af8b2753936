import axios, { AxiosInstance } from "axios";
import { 
  Asset, 
  AssetApiResponse, 
  AssetPaginatedResponse,
  CreateAssetFormData,
  UpdateAssetFormData,
  AssetPaginationParams
} from "@/types/asset";
import { SPIDEX_API_BASE_URL } from "@/lib/constants";

class AssetAPI {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: SPIDEX_API_BASE_URL,
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/api.spidex.v1+json",
      },
    });

    // Add request interceptor to include auth token
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem("token");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error("Asset API Error:", error);
        throw error;
      }
    );
  }

  /**
   * Get all assets with pagination
   */
  async getAllAssetsByPagination(
    tenantId: string,
    params: AssetPaginationParams
  ): Promise<AssetPaginatedResponse> {
    try {
      const response = await this.api.get("/asset/allby", {
        params: { tenantId, ...params },
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching assets:", error);
      throw error;
    }
  }

  /**
   * Get all assets
   */
  async getAllAssets(tenantId: string): Promise<AssetApiResponse<Asset[]>> {
    try {
      const response = await this.api.get("/asset/all", {
        params: { tenantId },
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching all assets:", error);
      throw error;
    }
  }

  /**
   * Create a new asset
   */
  async createAsset(assetData: CreateAssetFormData & { tenantId: string; createdBy: string; modifiedBy: string }): Promise<AssetApiResponse<Asset>> {
    try {
      const response = await this.api.post("/asset", assetData);
      return response.data;
    } catch (error) {
      console.error("Error creating asset:", error);
      throw error;
    }
  }

  /**
   * Update an existing asset
   */
  async updateAsset(assetData: UpdateAssetFormData & { tenantId: string; modifiedBy: string }): Promise<AssetApiResponse<Asset>> {
    try {
      const response = await this.api.put("/asset", assetData);
      return response.data;
    } catch (error) {
      console.error("Error updating asset:", error);
      throw error;
    }
  }

  /**
   * Delete an asset
   */
  async deleteAsset(assetId: string): Promise<AssetApiResponse<void>> {
    try {
      const response = await this.api.delete(`/asset/${assetId}`);
      return response.data;
    } catch (error) {
      console.error("Error deleting asset:", error);
      throw error;
    }
  }

  /**
   * Get asset by external ID
   */
  async getAssetInfo(assetExternalId: string): Promise<AssetApiResponse<Asset>> {
    try {
      const response = await this.api.get(`/asset/${assetExternalId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching asset info:", error);
      throw error;
    }
  }

  /**
   * Toggle asset active/inactive status
   */
  async toggleAssetStatus(asset: Asset): Promise<AssetApiResponse<Asset>> {
    try {
      const updatedAsset = {
        ...asset,
        deleted: !asset.deleted,
        modifiedBy: localStorage.getItem("username") || "system",
        modifiedDate: new Date().toISOString(),
      };
      
      const response = await this.api.put("/asset", updatedAsset);
      return response.data;
    } catch (error) {
      console.error("Error toggling asset status:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const assetAPI = new AssetAPI();
export default assetAPI;
