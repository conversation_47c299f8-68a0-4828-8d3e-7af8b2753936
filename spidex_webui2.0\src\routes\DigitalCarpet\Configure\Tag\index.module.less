@import '../../../../Colors.less';

.drawer {
  .title {
    font-size: 1.5rem;
    font-weight: bold;
    background-color: @white-hex !important;
    color: @primary-color-hex;
    padding: 24px !important;
    border-bottom: 5px solid #f0f2f5;
  }
  .uploadContainer {
    padding: 0 5px 10px 20px;
  }
  .tableContainer {
    padding: 0 20px 0 20px !important;
  }
  :global {
    .ant-drawer-close {
      margin-right: 15px !important;
      position: fixed !important;
      .anticon-close {
        color: @primary-color-hex !important;
        padding: 10px !important;
      }
    }
    .ant-drawer-content {
      background-color: transparent !important;
    }
    .ant-drawer-body {
      padding: 20px;
      background-color: @white-hex !important;
    }
    .ant-spin-nested-loading {
      width: 100%;
    }
  }
}
.warningInfo {
  padding-top: 4px;
  color: red;
}
