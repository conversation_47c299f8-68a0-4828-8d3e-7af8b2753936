// Location Management Types

export interface GpsPoint {
  latitude: string;
  longitude: string;
}

export interface Location {
  id: string;
  name: string;
  address: string;
  branchId: string;
  branchName?: string; // This will be populated from branch data
  geoJson: string;
  gpsPoint: GpsPoint;
  properties: Record<string, any> | null;
  tenantId: string;
  deleted: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
}

export interface CreateLocationFormData {
  name: string;
  address: string;
  branchId: string;
  geoJson: string;
  gpsPoint: GpsPoint;
}

export interface UpdateLocationFormData {
  id: string;
  name: string;
  address: string;
  branchId: string;
  geoJson: string;
  gpsPoint: GpsPoint;
}

export interface LocationSearchFilters {
  searchTerm?: string;
  branchId?: string;
  deleted?: boolean;
}

export interface LocationPaginationParams {
  pageNumber: number;
  pageSize: LocationPageSize;
}

// Page size options
export type LocationPageSize = 10 | 50 | 100 | "all";

export const LOCATION_PAGE_SIZES: LocationPageSize[] = [10, 50, 100, "all"];
export const DEFAULT_LOCATION_PAGE_SIZE: LocationPageSize = 10;
export const LOCATION_PAGE_SIZE_ALL: LocationPageSize = "all";

// API Response types
export interface LocationApiResponse {
  data: Location[];
  total: number;
  page: number;
  pageSize: number;
}

// Form validation types
export interface LocationFormErrors {
  name?: string;
  address?: string;
  branchId?: string;
  gpsPoint?: {
    latitude?: string;
    longitude?: string;
  };
}

// Table column types
export interface LocationTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  responsive?: string[];
  render?: (value: any, record: Location) => React.ReactNode;
}

// Action types for location management
export type LocationAction = "create" | "update" | "delete" | "view";

// Location status for UI display
export type LocationStatus = "active" | "inactive";

export interface LocationStats {
  total: number;
  active: number;
  inactive: number;
  byBranch: Record<string, number>;
}
