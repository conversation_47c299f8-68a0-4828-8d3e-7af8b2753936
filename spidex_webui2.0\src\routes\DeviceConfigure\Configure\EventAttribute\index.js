import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Select,
  Form,
  Switch,
  Popconfirm,
  CommonCompactView,
  CommonDrawer,
  InputNumber,
  Input,
  message,
  Row,
  Col,
} from '../../../../components';
import { getAllEventAttrs, addEventAttr, updateEventAttr, deleteEventAttr } from '../../../../services';
import Context from '../../../../context';
import { PlusOutlined } from '@ant-design/icons';
import { buildCommonApiValues } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import { get } from 'lodash';
import { CRUD, Pages, ModuleNames } from '../../../../constants';

const { Option } = Select;

const EventAttr = () => {
  const [context, setContext] = useContext(Context);
  const [eventAttrs, setEventAttrs] = useState([]);
  const [eventAttr, setEventAttr] = useState({});
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');

  const [form] = Form.useForm();

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveEventAttrAction({ ...commonValues, ...values });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateEventAttrAction(values);
      setVisible(false);
    }
  };

  const saveEventAttrAction = async (eventAttr) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addEventAttr(eventAttr)
      .then((res) => {
        const tempData = { ...res.data };
        tempData.status = tempData.status === 'true' ? true : false;
        setEventAttrs((state) => [tempData, ...state]);
        message.success('Succesfully Added event attribute');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Event Attribute, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editEventAttrAction = (eventAttr) => {
    form.setFieldsValue({ ...eventAttr });
    setAction('edit');
    setEventAttr(eventAttr);
    setVisible(true);
  };

  const updateEventAttrCall = (values) => {
    const data = { ...eventAttr, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateEventAttr(data)
      .then((res) => {
        setEventAttrs((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, {
                ...res.data,
                status: res.data.status === 'true' ? true : false,
              });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated event attribute');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Event Attribute, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateEventAttrAction = async (values) => {
    updateEventAttrCall(values);
  };

  const setDeleteEventAttrAction = (eventAttrId, visible) => {
    setEventAttrs((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === eventAttrId).visible = visible;
      tempData
        .filter((x) => x.id !== eventAttrId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteEventAttrAction = (eventAttrId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteEventAttr(eventAttrId)
      .then(() => {
        setEventAttrs((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === eventAttrId).visible = false;
          tempState.find((x) => x.id === eventAttrId).deleted = true;
          return [...state].filter((x) => x.id !== eventAttrId);
        });
        message.success('Succesfully Deleted event attribute');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Event Attribute, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  useEffect(() => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      getAllEventAttrs(context.profile.tenantId)
        .then((res) => {
          setEventAttrs(res.data.filter((x) => x.deleted === false));
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to get Event Attribute details, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
    }
    // eslint-disable-next-line
  }, []);

  const clearForm = () => {
    form.resetFields();
  };

  const tableCols = [
    { title: <strong> Name </strong>, key: 'name', dataIndex: 'name' },
    { title: <strong> Description </strong>, key: 'description', dataIndex: 'description' },
    { title: <strong> Category </strong>, key: 'category', dataIndex: 'category' },
    { title: <strong> Unit Type </strong>, key: 'unitType', dataIndex: 'unitType' },
    { title: <strong> Data Type </strong>, key: 'dataType', dataIndex: 'dataType' },
    {
      title: <strong> EPE </strong>,
      key: 'epeProperties',
      render: (record) => (
        <>
          {record.epeProperties.ingress} {record.epeProperties.egress ? `, ${record.epeProperties?.egress}` : ''}
        </>
      ),
    },
    {
      title: <strong> Multiplexing </strong>,
      render: (record) => <>{record.epeProperties.deMultiplexing}</>,
    },
    {
      title: <strong> Data Conversion </strong>,
      render: (record) => <>{record.epeProperties.dataConversion}</>,
    },
    {
      title: <strong> Enable </strong>,
      key: 'enable',
      render: (record) => <>{record.enable ? 'True' : 'False'}</>,
    },
    {
      title: <strong> Actions </strong>,
      key: 'Actions',
      render: (record) => (
        <>
          <PermissionContainer page={Pages.EVENT_ATTRIBUTE} permission={CRUD.UPDATE}>
            <Button type="link" onClick={() => editEventAttrAction(record)} className="actionButton">
              Edit
            </Button>
          </PermissionContainer>
          <PermissionContainer page={Pages.EVENT_ATTRIBUTE} permission={CRUD.DELETE}>
            <Popconfirm
              title={`Are you sure to delete event attribute ${record.name}?`}
              visible={record.visible || false}
              onConfirm={() => deleteEventAttrAction(record.id)}
              onCancel={() => setDeleteEventAttrAction(record.id, false)}
            >
              <Button type="link" onClick={() => setDeleteEventAttrAction(record.id, true)} className="actionButton">
                Delete
              </Button>
            </Popconfirm>
          </PermissionContainer>
        </>
      ),
    },
  ];
  const eventattributeBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.EVENT_ATTRIBUTE}].displayName`,
    ModuleNames.EVENT_ATTRIBUTE
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList
            list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.CONFIGURE, eventattributeBreadcrumbsName]}
          />
        </Col>
        <Col>
          <PermissionContainer page={Pages.EVENT_ATTRIBUTE} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Event Attribute
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="id"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={eventAttrs}
          rowClassName={(record) => record.deleted && 'rowInactive'}
        />
      ) : (
        <CommonCompactView
          data={eventAttrs}
          onEdit={editEventAttrAction}
          onDelete={deleteEventAttrAction}
          permissions={[
            { pageName: Pages.EVENT_ATTRIBUTE, permission: CRUD.UPDATE, label: 'Edit' },
            { pageName: Pages.EVENT_ATTRIBUTE, permission: CRUD.DELETE, label: 'Delete' },
          ]}
          title="name"
          dataList={[
            { label: 'Unit', value: 'unitType' },
            { label: 'Type', value: 'dataType' },
            { label: 'Category', value: 'category' },
          ]}
        />
      )}

      <CommonDrawer title="Attribute" visible={visible} closeAdd={closeAdd}>
        <Form
          scrollToFirstError={true}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          <Form.Item
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input event attribute Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Event Attribute Name" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Description"
            name="description"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input event attribute description!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Event Attribute Description" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Category"
            name="category"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input event attribute category!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Event Attribute Category" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Event Type"
            name="eventType"
            rules={[
              {
                required: true,
                message: 'Please Select Event type!',
              },
            ]}
          >
            <Select placeholder="Event Types">
              <Option name="Gateway" value="Gateway">
                Gateway
              </Option>
              <Option name="Tag" value="Tag">
                Tag
              </Option>
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Attribute ID"
            name="attributeId"
            rules={[
              {
                required: true,
                type: 'number',
                message: 'Please input Attribute ID',
              },
            ]}
          >
            <InputNumber min={1} max={100} placeholder="Attribute ID (1-100)" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Data Type"
            name="dataType"
            rules={[
              {
                required: true,
                message: 'Please input event attribute data type!',
                min: 1,
                max: 200,
                whitespace: true,
              },
            ]}
          >
            <Input placeholder="Event Attribute Data Type" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Unit Type"
            name="unitType"
            rules={[
              {
                required: true,
                message: 'Please input event attribute Unit Type!',
                min: 1,
                max: 200,
                whitespace: true,
              },
            ]}
          >
            <Input placeholder="Event Attribute Unit Type" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Converter Class"
            name="converterClass"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input event attribute converter class!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Event Attribute Converter Class" />
          </Form.Item>
          <Form.Item label="EPE Properties" required>
            <Input.Group compact>
              <Form.Item
                name={['epeProperties', 'ingress']}
                noStyle
                hasFeedback
                rules={[{ required: true, whitespace: true, message: 'Ingress is required' }]}
              >
                <Input style={{ width: '50%' }} placeholder="Ingress" />
              </Form.Item>
              <Form.Item
                name={['epeProperties', 'egress']}
                noStyle
                hasFeedback
                rules={[{ required: true, whitespace: true, message: 'Egress is required' }]}
              >
                <Input style={{ width: '50%' }} placeholder="Egress" />
              </Form.Item>
            </Input.Group>
          </Form.Item>
          <Form.Item label="Multiplexing, Data Conversion" required labelCol={24} wrapperCol={24}>
            <Input.Group compact>
              <Form.Item
                name={['epeProperties', 'deMultiplexing']}
                noStyle
                hasFeedback
                rules={[{ required: true, message: 'Multiplexing is required' }]}
              >
                <Select placeholder="Multiplexing" style={{ width: '50%' }}>
                  <Option name="true" value="true">
                    True
                  </Option>
                  <Option name="false" value="false">
                    False
                  </Option>
                </Select>
              </Form.Item>
              <Form.Item
                name={['epeProperties', 'dataConversion']}
                noStyle
                hasFeedback
                rules={[{ required: true, message: 'Data Conversion is required' }]}
              >
                <Select placeholder="Data Conversion" style={{ width: '50%' }}>
                  <Option name="true" value="true">
                    True
                  </Option>
                  <Option name="false" value="false">
                    False
                  </Option>
                </Select>
              </Form.Item>
            </Input.Group>
          </Form.Item>

          <Form.Item label="Enable" initialValue={false} name="enable" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" type="primary" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </>
  );
};

export default EventAttr;
