import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Select,
  Popconfirm,
  GoogleMapW3W,
  CommonCompactView,
  message,
  Row,
  Col,
  Input,
  Checkbox,
} from '../../../../components';
import {
  getAllGateways,
  getAllTransitGateways,
  addTransitGateway,
  updateTransitGateway,
  deleteTransitGateway,
} from '../../../../services';
import Context from '../../../../context';
import { PlusOutlined } from '@ant-design/icons';
import { buildCommonApiValues } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import mapSvg from '../../../../assets/images/gmap.svg';
import CommonDrawer from '../../../../components/CommonDrawer';
import { get } from 'lodash';
import { CRUD, Pages, TransitAreaModes, ModuleNames } from '../../../../constants';

const { Option } = Select;

const GatewayTransit = () => {
  const [context, setContext] = useContext(Context);
  const [gateways, setGateways] = useState([]);
  const [transit, setTransit] = useState([]);
  const [transits, setTransits] = useState([]);
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');
  const [isMaps, setIsMaps] = useState(false);
  const [mapValues, setMapValues] = useState({});
  const [isMapsSource, setIsMapsSource] = useState(false);
  const [mapValuesSource, setMapValuesSource] = useState({});
  const [isMapsDestination, setIsMapsDestination] = useState(false);
  const [mapValuesDestination, setMapValuesDestination] = useState({});
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setTransits(tableData) : setTransits(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveTransitAction({ ...commonValues, ...values });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateTransitAction(values);
      setVisible(false);
    }
  };

  const saveTransitAction = async (transitGateway) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addTransitGateway(transitGateway)
      .then((res) => {
        const tempData = { ...res.data };
        tempData.status = tempData.status === 'true' ? true : false;
        setTransits((state) => [res.data, ...state]);
        message.success('Succesfully Added Transit');
        form.resetFields();
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Transit, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editTransitAction = (transitGateway) => {
    form.setFieldsValue({ ...transitGateway });
    setAction('edit');
    setTransit(transitGateway);
    setVisible(true);
  };

  const updateTransitGatewayCall = (values) => {
    const data = { ...transit, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateTransitGateway(data)
      .then((res) => {
        setTransits((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, {
                ...res.data,
                status: res.data.status === 'true' ? true : false,
              });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated Transit');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Transit, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateTransitAction = (values) => {
    updateTransitGatewayCall(values);
  };

  const makeActive = (data) => {
    updateTransitGatewayCall({ ...data, deleted: false });
  };

  const setDeleteTransitAction = (userId, visible) => {
    setTransits((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === userId).visible = visible;
      tempData
        .filter((x) => x.id !== userId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteTransitAction = (tagId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteTransitGateway(tagId)
      .then(() => {
        setTransits((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === tagId).visible = false;
          tempState.find((x) => x.id === tagId).deleted = true;
          return [...state].filter((x) => x.id !== tagId);
        });
        message.success('Succesfully Deleted transit');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Transit, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  useEffect(() => {
    const init = async () => {
      if (context.profile.tenantId) {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
        try {
          const gatewayRes = await getAllGateways(context.profile.tenantId);
          setGateways(gatewayRes.data.filter((x) => x.categoryType === 'transit'));
        } catch (e) {
          console.log(e);
          message.error('Unable to get Gateway details, try again later');
        }
        await getAllTransitGateways(context.profile.tenantId)
          .then((res) => {
            setTableData(res.data);

            setTransits(res.data.filter((x) => x.deleted === false));
          })
          .catch((e) => {
            console.log(e);
            message.error('Unable to get Transit details, try again later');
          })
          .finally(() => {
            setContext((state) => {
              return {
                ...state,
                isLoading: false,
              };
            });
          });
      }
    };
    init();
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    form.setFieldsValue({ ...form.getFieldsValue(), geofence: mapValues.geoJson });
    // eslint-disable-next-line
  }, [mapValues]);

  useEffect(() => {
    form.setFieldsValue({ ...form.getFieldsValue(), sourceLocation: mapValuesSource.gpsPoint });
    // eslint-disable-next-line
  }, [mapValuesSource]);

  useEffect(() => {
    form.setFieldsValue({ ...form.getFieldsValue(), destinationLocation: mapValuesDestination.gpsPoint });
    // eslint-disable-next-line
  }, [mapValuesDestination]);

  const clearForm = () => {
    form.resetFields();
  };

  const tableCols = [
    { title: <strong> Name </strong>, key: 'name', dataIndex: 'name' },
    { title: <strong> Description </strong>, key: 'description', dataIndex: 'description' },
    { title: <strong> Geo </strong>, key: 'geofence', dataIndex: 'geofence' },
    {
      title: <strong> Destination Location </strong>,
      key: 'destinationLocation',
      render: (record) => (
        <Row>
          <Col>{record.destinationLocation?.latitude},</Col>
          <Col>{record.destinationLocation?.longitude} </Col>
        </Row>
      ),
    },
    {
      title: <strong> Source Location </strong>,
      key: 'sourceLocation',
      render: (record) => (
        <Row>
          <Col>{record.sourceLocation?.latitude},</Col>
          <Col>{record.sourceLocation?.longitude}</Col>
        </Row>
      ),
    },
    {
      title: <strong> Actions </strong>,
      width: 310,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <PermissionContainer page={Pages.BRANCH} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                  Activate
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.TRANSIT} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editTransitAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.TRANSIT} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete transit gateway ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteTransitAction(record.id)}
                onCancel={() => setDeleteTransitAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteTransitAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const transitareaBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.TRANSIT}].displayName`,
    ModuleNames.TRANSIT_AREA
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList
            list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.CONFIGURE, transitareaBreadcrumbsName]}
          />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.TRANSIT} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Transit
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="id"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={transits}
          rowClassName={(record) => record.deleted && 'rowInactive'}
        />
      ) : (
        <CommonCompactView
          data={transits}
          onEdit={editTransitAction}
          onDelete={deleteTransitAction}
          permissions={[
            { pageName: Pages.TRANSIT, permission: CRUD.UPDATE, label: 'Edit' },
            { pageName: Pages.TRANSIT, permission: CRUD.DELETE, label: 'Delete' },
          ]}
          title="name"
          dataList={[]}
        />
      )}
      <CommonDrawer title="Transit" visible={visible} closeAdd={closeAdd}>
        <Form
          scrollToFirstError={true}
          layout="horizontal"
          initialValues={{}}
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          <Form.Item
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Transit Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Transit Name" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Description"
            name="description"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Transit description!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Transit Description" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Mode"
            name="mode"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Transit description!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Select showSearch placeholder="Mode">
              {Object.values(TransitAreaModes).map((b, index) => (
                <Option title={b} key={index} value={b}>
                  {b}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="Geofence" required>
            <Row justify="space-between" className="geoLocation">
              <Col xs={{ span: 21 }} span={22}>
                <Form.Item
                  shouldUpdate={true}
                  hasFeedback
                  name="geofence"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: 'Please input Transit geofence!',
                      min: 1,
                      max: 200,
                    },
                  ]}
                >
                  <Input placeholder="Transit Geofence" />
                </Form.Item>
              </Col>
              <Col className="locationSelector">
                <img src={mapSvg} alt="mapSvg" onClick={() => setIsMaps(true)} />
              </Col>
            </Row>
          </Form.Item>

          <Form.Item label="Source" required>
            <Row justify="space-between">
              <Col xs={{ span: 21 }} span={22}>
                <Input.Group compact>
                  <Form.Item
                    name={['sourceLocation', 'latitude']}
                    noStyle
                    hasFeedback
                    rules={[{ required: true, message: 'Latitude is required' }]}
                  >
                    <Input style={{ width: '50%' }} placeholder="Latitude" />
                  </Form.Item>
                  <Form.Item
                    name={['sourceLocation', 'longitude']}
                    noStyle
                    hasFeedback
                    rules={[{ required: true, message: 'Longitude is required' }]}
                  >
                    <Input style={{ width: '50%' }} placeholder="Longitude" />
                  </Form.Item>
                </Input.Group>
              </Col>
              <Col className="locationSelector">
                <img src={mapSvg} alt="mapSvg" onClick={() => setIsMapsSource(true)} />
              </Col>
            </Row>
          </Form.Item>

          <Form.Item label="Destination" required>
            <Row justify="space-between">
              <Col xs={{ span: 21 }} span={22}>
                <Input.Group compact>
                  <Form.Item
                    name={['destinationLocation', 'latitude']}
                    noStyle
                    hasFeedback
                    rules={[{ required: true, message: 'Latitude is required' }]}
                  >
                    <Input style={{ width: '50%' }} placeholder="Latitude" />
                  </Form.Item>
                  <Form.Item
                    name={['destinationLocation', 'longitude']}
                    noStyle
                    hasFeedback
                    rules={[{ required: true, message: 'Longitude is required' }]}
                  >
                    <Input style={{ width: '50%' }} placeholder="Longitude" />
                  </Form.Item>
                </Input.Group>
              </Col>
              <Col className="locationSelector">
                <img src={mapSvg} alt="mapSvg" onClick={() => setIsMapsDestination(true)} />
              </Col>
            </Row>
          </Form.Item>
          <Form.Item
            label="Bind To"
            name="gatewayId"
            rules={[
              {
                required: true,
                message: 'Please Select Gateway!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Gateway"
            >
              {gateways.map((b) => (
                <Option title={b.name} key={b.id} value={b.id}>
                  {b.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>

        {isMaps && <GoogleMapW3W setIsMaps={setIsMaps} onCloseUpdate={setMapValues}></GoogleMapW3W>}
        {isMapsSource && <GoogleMapW3W setIsMaps={setIsMapsSource} onCloseUpdate={setMapValuesSource}></GoogleMapW3W>}
        {isMapsDestination && (
          <GoogleMapW3W setIsMaps={setIsMapsDestination} onCloseUpdate={setMapValuesDestination}></GoogleMapW3W>
        )}
      </CommonDrawer>
    </>
  );
};

export default GatewayTransit;
