"use client";

import { useState } from "react";
import { Plus } from "lucide-react";
import { useSession } from "next-auth/react";
import { useTenantManagement } from "@/hooks/use-tenant-management";
import {
  Tenant,
  CreateTenantFormData,
  UpdateTenantFormData,
} from "@/types/tenant";
import { TenantDataTable } from "./tenant-data-table";
import { TenantSearchPagination } from "./tenant-search-pagination";
import { TenantForm } from "./tenant-form";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";
import { toast } from "sonner";

export default function TenantManagement() {
  const { data: session, status } = useSession();
  const {
    tenants: paginatedTenants,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    createTenant,
    updateTenant,
    deleteTenant,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useTenantManagement();

  // Form state
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingTenant, setEditingTenant] = useState<Tenant | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // Show loading state while session is loading
  if (status === "loading") {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if not authenticated
  if (status === "unauthenticated") {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-destructive">Access Denied</h2>
          <p className="text-muted-foreground mt-2">
            You must be logged in to access tenant management.
          </p>
        </div>
      </div>
    );
  }

  // Handle form submissions
  const handleCreateTenant = async (data: CreateTenantFormData) => {
    try {
      setFormLoading(true);
      await createTenant(data);
      setIsFormOpen(false);
      toast.success("Tenant created successfully", {
        description: `${data.name} has been added to the system.`,
      });
      console.log("Tenant created successfully");
    } catch (error) {
      console.error("Failed to create tenant:", error);
      toast.error("Failed to create tenant", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while creating the tenant.",
      });
    } finally {
      setFormLoading(false);
    }
  };

  const handleUpdateTenant = async (data: UpdateTenantFormData) => {
    try {
      setFormLoading(true);
      await updateTenant(data);
      setIsFormOpen(false);
      toast.success("Tenant updated successfully", {
        description: `${data.name} has been updated.`,
      });
      console.log("Tenant updated successfully");
    } catch (error) {
      console.error("Failed to update tenant:", error);
      toast.error("Failed to update tenant", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while updating the tenant.",
      });
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteTenant = async (tenantId: string) => {
    try {
      await deleteTenant(tenantId);
      toast.success("Tenant deleted successfully");
      console.log("Tenant deleted successfully");
    } catch (error) {
      console.error("Failed to delete tenant:", error);
    }
  };

  // Toggle Active functionality - for activating deleted tenants
  const handleToggleActive = async (tenantId: string, active: boolean) => {
    const tenant = paginatedTenants.find((t: any) => t.id === tenantId);
    if (!tenant) return;

    try {
      await updateTenant({
        id: tenant.id,
        name: tenant.name,
        type: tenant.type,
        orgName: tenant.orgName,
        enable: tenant.enable,
        latitude: tenant.latitude,
        longitude: tenant.longitude,
        deleted: !active, // If activating (active=true), set deleted=false
      });
      console.log(
        `Tenant ${active ? "activated" : "deactivated"} successfully`
      );
    } catch (error) {
      console.error(`Failed to ${active ? "activate" : "deactivate"} tenant`);
      console.error("Error toggling tenant status:", error);
    }
  };

  const openCreateForm = () => {
    setEditingTenant(null);
    setIsFormOpen(true);
  };

  const openEditForm = (tenant: Tenant) => {
    setEditingTenant(tenant);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setEditingTenant(null);
  };

  const handleFormSubmit = async (
    data: CreateTenantFormData | UpdateTenantFormData
  ) => {
    if (editingTenant) {
      await handleUpdateTenant(data as UpdateTenantFormData);
    } else {
      await handleCreateTenant(data as CreateTenantFormData);
    }
  };

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/user-management">
                User Management
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Tenant Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={openCreateForm} className="gap-2 ml-auto">
          <Plus className="h-4 w-4" />
          Add Tenant
        </Button>
      </header>
      <main className="p-4">
        {/* Search and Filters */}
        <TenantSearchPagination
          searchFilters={searchFilters}
          showDeleted={showDeleted}
          onSearchChange={updateSearchFilters}
          onToggleShowDeleted={toggleShowDeleted}
          onClearFilters={clearSearch}
        />

        {/* Data Table */}
        <TenantDataTable
          data={paginatedTenants}
          pagination={pagination}
          totalRecords={totalRecords}
          totalPages={totalPages}
          availablePageSizes={availablePageSizes}
          onEdit={openEditForm}
          onDelete={handleDeleteTenant}
          onToggleActive={handleToggleActive}
          onPageChange={goToPage}
          onPageSizeChange={changePageSize}
        />
      </main>

      {/* Tenant Form Sheet */}
      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-1/4 sm:max-w-4xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle>
              {editingTenant ? "Edit Tenant" : "Create New Tenant"}
            </SheetTitle>
            <SheetDescription>
              {editingTenant
                ? "Update the tenant information and settings."
                : "Create a new tenant organization with appropriate settings."}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <TenantForm
              tenant={editingTenant || undefined}
              isLoading={formLoading}
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
              onClear={() => console.log("Form cleared")}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
