"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/websocket-driver";
exports.ids = ["vendor-chunks/websocket-driver"];
exports.modules = {

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js":
/*!***************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Protocol references:\n//\n// * http://tools.ietf.org/html/draft-hixie-thewebsocketprotocol-75\n// * http://tools.ietf.org/html/draft-hixie-thewebsocketprotocol-76\n// * http://tools.ietf.org/html/draft-ietf-hybi-thewebsocketprotocol-17\n\nvar Base   = __webpack_require__(/*! ./driver/base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Client = __webpack_require__(/*! ./driver/client */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/client.js\"),\n    Server = __webpack_require__(/*! ./driver/server */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/server.js\");\n\nvar Driver = {\n  client: function(url, options) {\n    options = options || {};\n    if (options.masking === undefined) options.masking = true;\n    return new Client(url, options);\n  },\n\n  server: function(options) {\n    options = options || {};\n    if (options.requireMasking === undefined) options.requireMasking = true;\n    return new Server(options);\n  },\n\n  http: function() {\n    return Server.http.apply(Server, arguments);\n  },\n\n  isSecureRequest: function(request) {\n    return Server.isSecureRequest(request);\n  },\n\n  isWebSocket: function(request) {\n    return Base.isWebSocket(request);\n  },\n\n  validateOptions: function(options, validKeys) {\n    Base.validateOptions(options, validKeys);\n  }\n};\n\nmodule.exports = Driver;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js":
/*!********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/base.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer  = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    Emitter = (__webpack_require__(/*! events */ \"events\").EventEmitter),\n    util    = __webpack_require__(/*! util */ \"util\"),\n    streams = __webpack_require__(/*! ../streams */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/streams.js\"),\n    Headers = __webpack_require__(/*! ./headers */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js\"),\n    Reader  = __webpack_require__(/*! ./stream_reader */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/stream_reader.js\");\n\nvar Base = function(request, url, options) {\n  Emitter.call(this);\n  Base.validateOptions(options || {}, ['maxLength', 'masking', 'requireMasking', 'protocols']);\n\n  this._request   = request;\n  this._reader    = new Reader();\n  this._options   = options || {};\n  this._maxLength = this._options.maxLength || this.MAX_LENGTH;\n  this._headers   = new Headers();\n  this.__queue    = [];\n  this.readyState = 0;\n  this.url        = url;\n\n  this.io = new streams.IO(this);\n  this.messages = new streams.Messages(this);\n  this._bindEventListeners();\n};\nutil.inherits(Base, Emitter);\n\nBase.isWebSocket = function(request) {\n  var connection = request.headers.connection || '',\n      upgrade    = request.headers.upgrade || '';\n\n  return request.method === 'GET' &&\n         connection.toLowerCase().split(/ *, */).indexOf('upgrade') >= 0 &&\n         upgrade.toLowerCase() === 'websocket';\n};\n\nBase.validateOptions = function(options, validKeys) {\n  for (var key in options) {\n    if (validKeys.indexOf(key) < 0)\n      throw new Error('Unrecognized option: ' + key);\n  }\n};\n\nvar instance = {\n  // This is 64MB, small enough for an average VPS to handle without\n  // crashing from process out of memory\n  MAX_LENGTH: 0x3ffffff,\n\n  STATES: ['connecting', 'open', 'closing', 'closed'],\n\n  _bindEventListeners: function() {\n    var self = this;\n\n    // Protocol errors are informational and do not have to be handled\n    this.messages.on('error', function() {});\n\n    this.on('message', function(event) {\n      var messages = self.messages;\n      if (messages.readable) messages.emit('data', event.data);\n    });\n\n    this.on('error', function(error) {\n      var messages = self.messages;\n      if (messages.readable) messages.emit('error', error);\n    });\n\n    this.on('close', function() {\n      var messages = self.messages;\n      if (!messages.readable) return;\n      messages.readable = messages.writable = false;\n      messages.emit('end');\n    });\n  },\n\n  getState: function() {\n    return this.STATES[this.readyState] || null;\n  },\n\n  addExtension: function(extension) {\n    return false;\n  },\n\n  setHeader: function(name, value) {\n    if (this.readyState > 0) return false;\n    this._headers.set(name, value);\n    return true;\n  },\n\n  start: function() {\n    if (this.readyState !== 0) return false;\n\n    if (!Base.isWebSocket(this._request))\n      return this._failHandshake(new Error('Not a WebSocket request'));\n\n    var response;\n\n    try {\n      response = this._handshakeResponse();\n    } catch (error) {\n      return this._failHandshake(error);\n    }\n\n    this._write(response);\n    if (this._stage !== -1) this._open();\n    return true;\n  },\n\n  _failHandshake: function(error) {\n    var headers = new Headers();\n    headers.set('Content-Type', 'text/plain');\n    headers.set('Content-Length', Buffer.byteLength(error.message, 'utf8'));\n\n    headers = ['HTTP/1.1 400 Bad Request', headers.toString(), error.message];\n    this._write(Buffer.from(headers.join('\\r\\n'), 'utf8'));\n    this._fail('protocol_error', error.message);\n\n    return false;\n  },\n\n  text: function(message) {\n    return this.frame(message);\n  },\n\n  binary: function(message) {\n    return false;\n  },\n\n  ping: function() {\n    return false;\n  },\n\n  pong: function() {\n      return false;\n  },\n\n  close: function(reason, code) {\n    if (this.readyState !== 1) return false;\n    this.readyState = 3;\n    this.emit('close', new Base.CloseEvent(null, null));\n    return true;\n  },\n\n  _open: function() {\n    this.readyState = 1;\n    this.__queue.forEach(function(args) { this.frame.apply(this, args) }, this);\n    this.__queue = [];\n    this.emit('open', new Base.OpenEvent());\n  },\n\n  _queue: function(message) {\n    this.__queue.push(message);\n    return true;\n  },\n\n  _write: function(chunk) {\n    var io = this.io;\n    if (io.readable) io.emit('data', chunk);\n  },\n\n  _fail: function(type, message) {\n    this.readyState = 2;\n    this.emit('error', new Error(message));\n    this.close();\n  }\n};\n\nfor (var key in instance)\n  Base.prototype[key] = instance[key];\n\n\nBase.ConnectEvent = function() {};\n\nBase.OpenEvent = function() {};\n\nBase.CloseEvent = function(code, reason) {\n  this.code   = code;\n  this.reason = reason;\n};\n\nBase.MessageEvent = function(data) {\n  this.data = data;\n};\n\nBase.PingEvent = function(data) {\n  this.data = data;\n};\n\nBase.PongEvent = function(data) {\n  this.data = data;\n};\n\nmodule.exports = Base;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/client.js":
/*!**********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/client.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer     = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    crypto     = __webpack_require__(/*! crypto */ \"crypto\"),\n    url        = __webpack_require__(/*! url */ \"url\"),\n    util       = __webpack_require__(/*! util */ \"util\"),\n    HttpParser = __webpack_require__(/*! ../http_parser */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js\"),\n    Base       = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Hybi       = __webpack_require__(/*! ./hybi */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi.js\"),\n    Proxy      = __webpack_require__(/*! ./proxy */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/proxy.js\");\n\nvar Client = function(_url, options) {\n  this.version = 'hybi-' + Hybi.VERSION;\n  Hybi.call(this, null, _url, options);\n\n  this.readyState = -1;\n  this._key       = Client.generateKey();\n  this._accept    = Hybi.generateAccept(this._key);\n  this._http      = new HttpParser('response');\n\n  var uri  = url.parse(this.url),\n      auth = uri.auth && Buffer.from(uri.auth, 'utf8').toString('base64');\n\n  if (this.VALID_PROTOCOLS.indexOf(uri.protocol) < 0)\n    throw new Error(this.url + ' is not a valid WebSocket URL');\n\n  this._pathname = (uri.pathname || '/') + (uri.search || '');\n\n  this._headers.set('Host', uri.host);\n  this._headers.set('Upgrade', 'websocket');\n  this._headers.set('Connection', 'Upgrade');\n  this._headers.set('Sec-WebSocket-Key', this._key);\n  this._headers.set('Sec-WebSocket-Version', Hybi.VERSION);\n\n  if (this._protocols.length > 0)\n    this._headers.set('Sec-WebSocket-Protocol', this._protocols.join(', '));\n\n  if (auth)\n    this._headers.set('Authorization', 'Basic ' + auth);\n};\nutil.inherits(Client, Hybi);\n\nClient.generateKey = function() {\n  return crypto.randomBytes(16).toString('base64');\n};\n\nvar instance = {\n  VALID_PROTOCOLS: ['ws:', 'wss:'],\n\n  proxy: function(origin, options) {\n    return new Proxy(this, origin, options);\n  },\n\n  start: function() {\n    if (this.readyState !== -1) return false;\n    this._write(this._handshakeRequest());\n    this.readyState = 0;\n    return true;\n  },\n\n  parse: function(chunk) {\n    if (this.readyState === 3) return;\n    if (this.readyState > 0) return Hybi.prototype.parse.call(this, chunk);\n\n    this._http.parse(chunk);\n    if (!this._http.isComplete()) return;\n\n    this._validateHandshake();\n    if (this.readyState === 3) return;\n\n    this._open();\n    this.parse(this._http.body);\n  },\n\n  _handshakeRequest: function() {\n    var extensions = this._extensions.generateOffer();\n    if (extensions)\n      this._headers.set('Sec-WebSocket-Extensions', extensions);\n\n    var start   = 'GET ' + this._pathname + ' HTTP/1.1',\n        headers = [start, this._headers.toString(), ''];\n\n    return Buffer.from(headers.join('\\r\\n'), 'utf8');\n  },\n\n  _failHandshake: function(message) {\n    message = 'Error during WebSocket handshake: ' + message;\n    this.readyState = 3;\n    this.emit('error', new Error(message));\n    this.emit('close', new Base.CloseEvent(this.ERRORS.protocol_error, message));\n  },\n\n  _validateHandshake: function() {\n    this.statusCode = this._http.statusCode;\n    this.headers    = this._http.headers;\n\n    if (this._http.error)\n      return this._failHandshake(this._http.error.message);\n\n    if (this._http.statusCode !== 101)\n      return this._failHandshake('Unexpected response code: ' + this._http.statusCode);\n\n    var headers    = this._http.headers,\n        upgrade    = headers['upgrade'] || '',\n        connection = headers['connection'] || '',\n        accept     = headers['sec-websocket-accept'] || '',\n        protocol   = headers['sec-websocket-protocol'] || '';\n\n    if (upgrade === '')\n      return this._failHandshake(\"'Upgrade' header is missing\");\n    if (upgrade.toLowerCase() !== 'websocket')\n      return this._failHandshake(\"'Upgrade' header value is not 'WebSocket'\");\n\n    if (connection === '')\n      return this._failHandshake(\"'Connection' header is missing\");\n    if (connection.toLowerCase() !== 'upgrade')\n      return this._failHandshake(\"'Connection' header value is not 'Upgrade'\");\n\n    if (accept !== this._accept)\n      return this._failHandshake('Sec-WebSocket-Accept mismatch');\n\n    this.protocol = null;\n\n    if (protocol !== '') {\n      if (this._protocols.indexOf(protocol) < 0)\n        return this._failHandshake('Sec-WebSocket-Protocol mismatch');\n      else\n        this.protocol = protocol;\n    }\n\n    try {\n      this._extensions.activate(this.headers['sec-websocket-extensions']);\n    } catch (e) {\n      return this._failHandshake(e.message);\n    }\n  }\n};\n\nfor (var key in instance)\n  Client.prototype[key] = instance[key];\n\nmodule.exports = Client;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft75.js":
/*!***********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/draft75.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    Base   = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    util   = __webpack_require__(/*! util */ \"util\");\n\nvar Draft75 = function(request, url, options) {\n  Base.apply(this, arguments);\n  this._stage  = 0;\n  this.version = 'hixie-75';\n\n  this._headers.set('Upgrade', 'WebSocket');\n  this._headers.set('Connection', 'Upgrade');\n  this._headers.set('WebSocket-Origin', this._request.headers.origin);\n  this._headers.set('WebSocket-Location', this.url);\n};\nutil.inherits(Draft75, Base);\n\nvar instance = {\n  close: function() {\n    if (this.readyState === 3) return false;\n    this.readyState = 3;\n    this.emit('close', new Base.CloseEvent(null, null));\n    return true;\n  },\n\n  parse: function(chunk) {\n    if (this.readyState > 1) return;\n\n    this._reader.put(chunk);\n\n    this._reader.eachByte(function(octet) {\n      var message;\n\n      switch (this._stage) {\n        case -1:\n          this._body.push(octet);\n          this._sendHandshakeBody();\n          break;\n\n        case 0:\n          this._parseLeadingByte(octet);\n          break;\n\n        case 1:\n          this._length = (octet & 0x7F) + 128 * this._length;\n\n          if (this._closing && this._length === 0) {\n            return this.close();\n          }\n          else if ((octet & 0x80) !== 0x80) {\n            if (this._length === 0) {\n              this._stage = 0;\n            }\n            else {\n              this._skipped = 0;\n              this._stage   = 2;\n            }\n          }\n          break;\n\n        case 2:\n          if (octet === 0xFF) {\n            this._stage = 0;\n            message = Buffer.from(this._buffer).toString('utf8', 0, this._buffer.length);\n            this.emit('message', new Base.MessageEvent(message));\n          }\n          else {\n            if (this._length) {\n              this._skipped += 1;\n              if (this._skipped === this._length)\n                this._stage = 0;\n            } else {\n              this._buffer.push(octet);\n              if (this._buffer.length > this._maxLength) return this.close();\n            }\n          }\n          break;\n      }\n    }, this);\n  },\n\n  frame: function(buffer) {\n    if (this.readyState === 0) return this._queue([buffer]);\n    if (this.readyState > 1) return false;\n\n    if (typeof buffer !== 'string') buffer = buffer.toString();\n\n    var length = Buffer.byteLength(buffer),\n        frame  = Buffer.allocUnsafe(length + 2);\n\n    frame[0] = 0x00;\n    frame.write(buffer, 1);\n    frame[frame.length - 1] = 0xFF;\n\n    this._write(frame);\n    return true;\n  },\n\n  _handshakeResponse: function() {\n    var start   = 'HTTP/1.1 101 Web Socket Protocol Handshake',\n        headers = [start, this._headers.toString(), ''];\n\n    return Buffer.from(headers.join('\\r\\n'), 'utf8');\n  },\n\n  _parseLeadingByte: function(octet) {\n    if ((octet & 0x80) === 0x80) {\n      this._length = 0;\n      this._stage  = 1;\n    } else {\n      delete this._length;\n      delete this._skipped;\n      this._buffer = [];\n      this._stage  = 2;\n    }\n  }\n};\n\nfor (var key in instance)\n  Draft75.prototype[key] = instance[key];\n\nmodule.exports = Draft75;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft75.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft76.js":
/*!***********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/draft76.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer  = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    Base    = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Draft75 = __webpack_require__(/*! ./draft75 */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft75.js\"),\n    crypto  = __webpack_require__(/*! crypto */ \"crypto\"),\n    util    = __webpack_require__(/*! util */ \"util\");\n\n\nvar numberFromKey = function(key) {\n  return parseInt((key.match(/[0-9]/g) || []).join(''), 10);\n};\n\nvar spacesInKey = function(key) {\n  return (key.match(/ /g) || []).length;\n};\n\n\nvar Draft76 = function(request, url, options) {\n  Draft75.apply(this, arguments);\n  this._stage  = -1;\n  this._body   = [];\n  this.version = 'hixie-76';\n\n  this._headers.clear();\n\n  this._headers.set('Upgrade', 'WebSocket');\n  this._headers.set('Connection', 'Upgrade');\n  this._headers.set('Sec-WebSocket-Origin', this._request.headers.origin);\n  this._headers.set('Sec-WebSocket-Location', this.url);\n};\nutil.inherits(Draft76, Draft75);\n\nvar instance = {\n  BODY_SIZE: 8,\n\n  start: function() {\n    if (!Draft75.prototype.start.call(this)) return false;\n    this._started = true;\n    this._sendHandshakeBody();\n    return true;\n  },\n\n  close: function() {\n    if (this.readyState === 3) return false;\n    if (this.readyState === 1) this._write(Buffer.from([0xFF, 0x00]));\n    this.readyState = 3;\n    this.emit('close', new Base.CloseEvent(null, null));\n    return true;\n  },\n\n  _handshakeResponse: function() {\n    var headers = this._request.headers,\n        key1    = headers['sec-websocket-key1'],\n        key2    = headers['sec-websocket-key2'];\n\n    if (!key1) throw new Error('Missing required header: Sec-WebSocket-Key1');\n    if (!key2) throw new Error('Missing required header: Sec-WebSocket-Key2');\n\n    var number1 = numberFromKey(key1),\n        spaces1 = spacesInKey(key1),\n\n        number2 = numberFromKey(key2),\n        spaces2 = spacesInKey(key2);\n\n    if (number1 % spaces1 !== 0 || number2 % spaces2 !== 0)\n      throw new Error('Client sent invalid Sec-WebSocket-Key headers');\n\n    this._keyValues = [number1 / spaces1, number2 / spaces2];\n\n    var start   = 'HTTP/1.1 101 WebSocket Protocol Handshake',\n        headers = [start, this._headers.toString(), ''];\n\n    return Buffer.from(headers.join('\\r\\n'), 'binary');\n  },\n\n  _handshakeSignature: function() {\n    if (this._body.length < this.BODY_SIZE) return null;\n\n    var md5    = crypto.createHash('md5'),\n        buffer = Buffer.allocUnsafe(8 + this.BODY_SIZE);\n\n    buffer.writeUInt32BE(this._keyValues[0], 0);\n    buffer.writeUInt32BE(this._keyValues[1], 4);\n    Buffer.from(this._body).copy(buffer, 8, 0, this.BODY_SIZE);\n\n    md5.update(buffer);\n    return Buffer.from(md5.digest('binary'), 'binary');\n  },\n\n  _sendHandshakeBody: function() {\n    if (!this._started) return;\n    var signature = this._handshakeSignature();\n    if (!signature) return;\n\n    this._write(signature);\n    this._stage = 0;\n    this._open();\n\n    if (this._body.length > this.BODY_SIZE)\n      this.parse(this._body.slice(this.BODY_SIZE));\n  },\n\n  _parseLeadingByte: function(octet) {\n    if (octet !== 0xFF)\n      return Draft75.prototype._parseLeadingByte.call(this, octet);\n\n    this._closing = true;\n    this._length  = 0;\n    this._stage   = 1;\n  }\n};\n\nfor (var key in instance)\n  Draft76.prototype[key] = instance[key];\n\nmodule.exports = Draft76;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft76.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js":
/*!***********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/headers.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("\n\nvar Headers = function() {\n  this.clear();\n};\n\nHeaders.prototype.ALLOWED_DUPLICATES = ['set-cookie', 'set-cookie2', 'warning', 'www-authenticate'];\n\nHeaders.prototype.clear = function() {\n  this._sent  = {};\n  this._lines = [];\n};\n\nHeaders.prototype.set = function(name, value) {\n  if (value === undefined) return;\n\n  name = this._strip(name);\n  value = this._strip(value);\n\n  var key = name.toLowerCase();\n  if (!this._sent.hasOwnProperty(key) || this.ALLOWED_DUPLICATES.indexOf(key) >= 0) {\n    this._sent[key] = true;\n    this._lines.push(name + ': ' + value + '\\r\\n');\n  }\n};\n\nHeaders.prototype.toString = function() {\n  return this._lines.join('');\n};\n\nHeaders.prototype._strip = function(string) {\n  return string.toString().replace(/^ */, '').replace(/ *$/, '');\n};\n\nmodule.exports = Headers;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWRyaXZlci9saWIvd2Vic29ja2V0L2RyaXZlci9oZWFkZXJzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aXNcXERvY3VtZW50c1xcUHJvamVjdHNcXFNwaWRleFxcc3BpZGV4X3dlYnVpMy4wXFxub2RlX21vZHVsZXNcXHdlYnNvY2tldC1kcml2ZXJcXGxpYlxcd2Vic29ja2V0XFxkcml2ZXJcXGhlYWRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgSGVhZGVycyA9IGZ1bmN0aW9uKCkge1xuICB0aGlzLmNsZWFyKCk7XG59O1xuXG5IZWFkZXJzLnByb3RvdHlwZS5BTExPV0VEX0RVUExJQ0FURVMgPSBbJ3NldC1jb29raWUnLCAnc2V0LWNvb2tpZTInLCAnd2FybmluZycsICd3d3ctYXV0aGVudGljYXRlJ107XG5cbkhlYWRlcnMucHJvdG90eXBlLmNsZWFyID0gZnVuY3Rpb24oKSB7XG4gIHRoaXMuX3NlbnQgID0ge307XG4gIHRoaXMuX2xpbmVzID0gW107XG59O1xuXG5IZWFkZXJzLnByb3RvdHlwZS5zZXQgPSBmdW5jdGlvbihuYW1lLCB2YWx1ZSkge1xuICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCkgcmV0dXJuO1xuXG4gIG5hbWUgPSB0aGlzLl9zdHJpcChuYW1lKTtcbiAgdmFsdWUgPSB0aGlzLl9zdHJpcCh2YWx1ZSk7XG5cbiAgdmFyIGtleSA9IG5hbWUudG9Mb3dlckNhc2UoKTtcbiAgaWYgKCF0aGlzLl9zZW50Lmhhc093blByb3BlcnR5KGtleSkgfHwgdGhpcy5BTExPV0VEX0RVUExJQ0FURVMuaW5kZXhPZihrZXkpID49IDApIHtcbiAgICB0aGlzLl9zZW50W2tleV0gPSB0cnVlO1xuICAgIHRoaXMuX2xpbmVzLnB1c2gobmFtZSArICc6ICcgKyB2YWx1ZSArICdcXHJcXG4nKTtcbiAgfVxufTtcblxuSGVhZGVycy5wcm90b3R5cGUudG9TdHJpbmcgPSBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHRoaXMuX2xpbmVzLmpvaW4oJycpO1xufTtcblxuSGVhZGVycy5wcm90b3R5cGUuX3N0cmlwID0gZnVuY3Rpb24oc3RyaW5nKSB7XG4gIHJldHVybiBzdHJpbmcudG9TdHJpbmcoKS5yZXBsYWNlKC9eICovLCAnJykucmVwbGFjZSgvICokLywgJycpO1xufTtcblxubW9kdWxlLmV4cG9ydHMgPSBIZWFkZXJzO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi.js":
/*!********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/hybi.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer     = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    crypto     = __webpack_require__(/*! crypto */ \"crypto\"),\n    util       = __webpack_require__(/*! util */ \"util\"),\n    Extensions = __webpack_require__(/*! websocket-extensions */ \"(ssr)/./node_modules/websocket-extensions/lib/websocket_extensions.js\"),\n    Base       = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Frame      = __webpack_require__(/*! ./hybi/frame */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js\"),\n    Message    = __webpack_require__(/*! ./hybi/message */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/message.js\");\n\nvar Hybi = function(request, url, options) {\n  Base.apply(this, arguments);\n\n  this._extensions     = new Extensions();\n  this._stage          = 0;\n  this._masking        = this._options.masking;\n  this._protocols      = this._options.protocols || [];\n  this._requireMasking = this._options.requireMasking;\n  this._pingCallbacks  = {};\n\n  if (typeof this._protocols === 'string')\n    this._protocols = this._protocols.split(/ *, */);\n\n  if (!this._request) return;\n\n  var protos    = this._request.headers['sec-websocket-protocol'],\n      supported = this._protocols;\n\n  if (protos !== undefined) {\n    if (typeof protos === 'string') protos = protos.split(/ *, */);\n    this.protocol = protos.filter(function(p) { return supported.indexOf(p) >= 0 })[0];\n  }\n\n  this.version = 'hybi-' + Hybi.VERSION;\n};\nutil.inherits(Hybi, Base);\n\nHybi.VERSION = '13';\n\nHybi.mask = function(payload, mask, offset) {\n  if (!mask || mask.length === 0) return payload;\n  offset = offset || 0;\n\n  for (var i = 0, n = payload.length - offset; i < n; i++) {\n    payload[offset + i] = payload[offset + i] ^ mask[i % 4];\n  }\n  return payload;\n};\n\nHybi.generateAccept = function(key) {\n  var sha1 = crypto.createHash('sha1');\n  sha1.update(key + Hybi.GUID);\n  return sha1.digest('base64');\n};\n\nHybi.GUID = '258EAFA5-E914-47DA-95CA-C5AB0DC85B11';\n\nvar instance = {\n  FIN:    0x80,\n  MASK:   0x80,\n  RSV1:   0x40,\n  RSV2:   0x20,\n  RSV3:   0x10,\n  OPCODE: 0x0F,\n  LENGTH: 0x7F,\n\n  OPCODES: {\n    continuation: 0,\n    text:         1,\n    binary:       2,\n    close:        8,\n    ping:         9,\n    pong:         10\n  },\n\n  OPCODE_CODES:    [0, 1, 2, 8, 9, 10],\n  MESSAGE_OPCODES: [0, 1, 2],\n  OPENING_OPCODES: [1, 2],\n\n  ERRORS: {\n    normal_closure:       1000,\n    going_away:           1001,\n    protocol_error:       1002,\n    unacceptable:         1003,\n    encoding_error:       1007,\n    policy_violation:     1008,\n    too_large:            1009,\n    extension_error:      1010,\n    unexpected_condition: 1011\n  },\n\n  ERROR_CODES:        [1000, 1001, 1002, 1003, 1007, 1008, 1009, 1010, 1011],\n  DEFAULT_ERROR_CODE: 1000,\n  MIN_RESERVED_ERROR: 3000,\n  MAX_RESERVED_ERROR: 4999,\n\n  // http://www.w3.org/International/questions/qa-forms-utf-8.en.php\n  UTF8_MATCH: /^([\\x00-\\x7F]|[\\xC2-\\xDF][\\x80-\\xBF]|\\xE0[\\xA0-\\xBF][\\x80-\\xBF]|[\\xE1-\\xEC\\xEE\\xEF][\\x80-\\xBF]{2}|\\xED[\\x80-\\x9F][\\x80-\\xBF]|\\xF0[\\x90-\\xBF][\\x80-\\xBF]{2}|[\\xF1-\\xF3][\\x80-\\xBF]{3}|\\xF4[\\x80-\\x8F][\\x80-\\xBF]{2})*$/,\n\n  addExtension: function(extension) {\n    this._extensions.add(extension);\n    return true;\n  },\n\n  parse: function(chunk) {\n    this._reader.put(chunk);\n    var buffer = true;\n    while (buffer) {\n      switch (this._stage) {\n        case 0:\n          buffer = this._reader.read(1);\n          if (buffer) this._parseOpcode(buffer[0]);\n          break;\n\n        case 1:\n          buffer = this._reader.read(1);\n          if (buffer) this._parseLength(buffer[0]);\n          break;\n\n        case 2:\n          buffer = this._reader.read(this._frame.lengthBytes);\n          if (buffer) this._parseExtendedLength(buffer);\n          break;\n\n        case 3:\n          buffer = this._reader.read(4);\n          if (buffer) {\n            this._stage = 4;\n            this._frame.maskingKey = buffer;\n          }\n          break;\n\n        case 4:\n          buffer = this._reader.read(this._frame.length);\n          if (buffer) {\n            this._stage = 0;\n            this._emitFrame(buffer);\n          }\n          break;\n\n        default:\n          buffer = null;\n      }\n    }\n  },\n\n  text: function(message) {\n    if (this.readyState > 1) return false;\n    return this.frame(message, 'text');\n  },\n\n  binary: function(message) {\n    if (this.readyState > 1) return false;\n    return this.frame(message, 'binary');\n  },\n\n  ping: function(message, callback) {\n    if (this.readyState > 1) return false;\n    message = message || '';\n    if (callback) this._pingCallbacks[message] = callback;\n    return this.frame(message, 'ping');\n  },\n\n  pong: function(message) {\n      if (this.readyState > 1) return false;\n      message = message ||'';\n      return this.frame(message, 'pong');\n  },\n\n  close: function(reason, code) {\n    reason = reason || '';\n    code   = code   || this.ERRORS.normal_closure;\n\n    if (this.readyState <= 0) {\n      this.readyState = 3;\n      this.emit('close', new Base.CloseEvent(code, reason));\n      return true;\n    } else if (this.readyState === 1) {\n      this.readyState = 2;\n      this._extensions.close(function() { this.frame(reason, 'close', code) }, this);\n      return true;\n    } else {\n      return false;\n    }\n  },\n\n  frame: function(buffer, type, code) {\n    if (this.readyState <= 0) return this._queue([buffer, type, code]);\n    if (this.readyState > 2) return false;\n\n    if (buffer instanceof Array)    buffer = Buffer.from(buffer);\n    if (typeof buffer === 'number') buffer = buffer.toString();\n\n    var message = new Message(),\n        isText  = (typeof buffer === 'string'),\n        payload, copy;\n\n    message.rsv1   = message.rsv2 = message.rsv3 = false;\n    message.opcode = this.OPCODES[type || (isText ? 'text' : 'binary')];\n\n    payload = isText ? Buffer.from(buffer, 'utf8') : buffer;\n\n    if (code) {\n      copy = payload;\n      payload = Buffer.allocUnsafe(2 + copy.length);\n      payload.writeUInt16BE(code, 0);\n      copy.copy(payload, 2);\n    }\n    message.data = payload;\n\n    var onMessageReady = function(message) {\n      var frame = new Frame();\n\n      frame.final   = true;\n      frame.rsv1    = message.rsv1;\n      frame.rsv2    = message.rsv2;\n      frame.rsv3    = message.rsv3;\n      frame.opcode  = message.opcode;\n      frame.masked  = !!this._masking;\n      frame.length  = message.data.length;\n      frame.payload = message.data;\n\n      if (frame.masked) frame.maskingKey = crypto.randomBytes(4);\n\n      this._sendFrame(frame);\n    };\n\n    if (this.MESSAGE_OPCODES.indexOf(message.opcode) >= 0)\n      this._extensions.processOutgoingMessage(message, function(error, message) {\n        if (error) return this._fail('extension_error', error.message);\n        onMessageReady.call(this, message);\n      }, this);\n    else\n      onMessageReady.call(this, message);\n\n    return true;\n  },\n\n  _sendFrame: function(frame) {\n    var length = frame.length,\n        header = (length <= 125) ? 2 : (length <= 65535 ? 4 : 10),\n        offset = header + (frame.masked ? 4 : 0),\n        buffer = Buffer.allocUnsafe(offset + length),\n        masked = frame.masked ? this.MASK : 0;\n\n    buffer[0] = (frame.final ? this.FIN : 0) |\n                (frame.rsv1 ? this.RSV1 : 0) |\n                (frame.rsv2 ? this.RSV2 : 0) |\n                (frame.rsv3 ? this.RSV3 : 0) |\n                frame.opcode;\n\n    if (length <= 125) {\n      buffer[1] = masked | length;\n    } else if (length <= 65535) {\n      buffer[1] = masked | 126;\n      buffer.writeUInt16BE(length, 2);\n    } else {\n      buffer[1] = masked | 127;\n      buffer.writeUInt32BE(Math.floor(length / 0x100000000), 2);\n      buffer.writeUInt32BE(length % 0x100000000, 6);\n    }\n\n    frame.payload.copy(buffer, offset);\n\n    if (frame.masked) {\n      frame.maskingKey.copy(buffer, header);\n      Hybi.mask(buffer, frame.maskingKey, offset);\n    }\n\n    this._write(buffer);\n  },\n\n  _handshakeResponse: function() {\n    var secKey  = this._request.headers['sec-websocket-key'],\n        version = this._request.headers['sec-websocket-version'];\n\n    if (version !== Hybi.VERSION)\n      throw new Error('Unsupported WebSocket version: ' + version);\n\n    if (typeof secKey !== 'string')\n      throw new Error('Missing handshake request header: Sec-WebSocket-Key');\n\n    this._headers.set('Upgrade', 'websocket');\n    this._headers.set('Connection', 'Upgrade');\n    this._headers.set('Sec-WebSocket-Accept', Hybi.generateAccept(secKey));\n\n    if (this.protocol) this._headers.set('Sec-WebSocket-Protocol', this.protocol);\n\n    var extensions = this._extensions.generateResponse(this._request.headers['sec-websocket-extensions']);\n    if (extensions) this._headers.set('Sec-WebSocket-Extensions', extensions);\n\n    var start   = 'HTTP/1.1 101 Switching Protocols',\n        headers = [start, this._headers.toString(), ''];\n\n    return Buffer.from(headers.join('\\r\\n'), 'utf8');\n  },\n\n  _shutdown: function(code, reason, error) {\n    delete this._frame;\n    delete this._message;\n    this._stage = 5;\n\n    var sendCloseFrame = (this.readyState === 1);\n    this.readyState = 2;\n\n    this._extensions.close(function() {\n      if (sendCloseFrame) this.frame(reason, 'close', code);\n      this.readyState = 3;\n      if (error) this.emit('error', new Error(reason));\n      this.emit('close', new Base.CloseEvent(code, reason));\n    }, this);\n  },\n\n  _fail: function(type, message) {\n    if (this.readyState > 1) return;\n    this._shutdown(this.ERRORS[type], message, true);\n  },\n\n  _parseOpcode: function(octet) {\n    var rsvs = [this.RSV1, this.RSV2, this.RSV3].map(function(rsv) {\n      return (octet & rsv) === rsv;\n    });\n\n    var frame = this._frame = new Frame();\n\n    frame.final  = (octet & this.FIN) === this.FIN;\n    frame.rsv1   = rsvs[0];\n    frame.rsv2   = rsvs[1];\n    frame.rsv3   = rsvs[2];\n    frame.opcode = (octet & this.OPCODE);\n\n    this._stage = 1;\n\n    if (!this._extensions.validFrameRsv(frame))\n      return this._fail('protocol_error',\n          'One or more reserved bits are on: reserved1 = ' + (frame.rsv1 ? 1 : 0) +\n          ', reserved2 = ' + (frame.rsv2 ? 1 : 0) +\n          ', reserved3 = ' + (frame.rsv3 ? 1 : 0));\n\n    if (this.OPCODE_CODES.indexOf(frame.opcode) < 0)\n      return this._fail('protocol_error', 'Unrecognized frame opcode: ' + frame.opcode);\n\n    if (this.MESSAGE_OPCODES.indexOf(frame.opcode) < 0 && !frame.final)\n      return this._fail('protocol_error', 'Received fragmented control frame: opcode = ' + frame.opcode);\n\n    if (this._message && this.OPENING_OPCODES.indexOf(frame.opcode) >= 0)\n      return this._fail('protocol_error', 'Received new data frame but previous continuous frame is unfinished');\n  },\n\n  _parseLength: function(octet) {\n    var frame = this._frame;\n    frame.masked = (octet & this.MASK) === this.MASK;\n    frame.length = (octet & this.LENGTH);\n\n    if (frame.length >= 0 && frame.length <= 125) {\n      this._stage = frame.masked ? 3 : 4;\n      if (!this._checkFrameLength()) return;\n    } else {\n      this._stage = 2;\n      frame.lengthBytes = (frame.length === 126 ? 2 : 8);\n    }\n\n    if (this._requireMasking && !frame.masked)\n      return this._fail('unacceptable', 'Received unmasked frame but masking is required');\n  },\n\n  _parseExtendedLength: function(buffer) {\n    var frame = this._frame;\n    frame.length = this._readUInt(buffer);\n\n    this._stage = frame.masked ? 3 : 4;\n\n    if (this.MESSAGE_OPCODES.indexOf(frame.opcode) < 0 && frame.length > 125)\n      return this._fail('protocol_error', 'Received control frame having too long payload: ' + frame.length);\n\n    if (!this._checkFrameLength()) return;\n  },\n\n  _checkFrameLength: function() {\n    var length = this._message ? this._message.length : 0;\n\n    if (length + this._frame.length > this._maxLength) {\n      this._fail('too_large', 'WebSocket frame length too large');\n      return false;\n    } else {\n      return true;\n    }\n  },\n\n  _emitFrame: function(buffer) {\n    var frame   = this._frame,\n        payload = frame.payload = Hybi.mask(buffer, frame.maskingKey),\n        opcode  = frame.opcode,\n        message,\n        code, reason,\n        callbacks, callback;\n\n    delete this._frame;\n\n    if (opcode === this.OPCODES.continuation) {\n      if (!this._message) return this._fail('protocol_error', 'Received unexpected continuation frame');\n      this._message.pushFrame(frame);\n    }\n\n    if (opcode === this.OPCODES.text || opcode === this.OPCODES.binary) {\n      this._message = new Message();\n      this._message.pushFrame(frame);\n    }\n\n    if (frame.final && this.MESSAGE_OPCODES.indexOf(opcode) >= 0)\n      return this._emitMessage(this._message);\n\n    if (opcode === this.OPCODES.close) {\n      code   = (payload.length >= 2) ? payload.readUInt16BE(0) : null;\n      reason = (payload.length > 2) ? this._encode(payload.slice(2)) : null;\n\n      if (!(payload.length === 0) &&\n          !(code !== null && code >= this.MIN_RESERVED_ERROR && code <= this.MAX_RESERVED_ERROR) &&\n          this.ERROR_CODES.indexOf(code) < 0)\n        code = this.ERRORS.protocol_error;\n\n      if (payload.length > 125 || (payload.length > 2 && !reason))\n        code = this.ERRORS.protocol_error;\n\n      this._shutdown(code || this.DEFAULT_ERROR_CODE, reason || '');\n    }\n\n    if (opcode === this.OPCODES.ping) {\n      this.frame(payload, 'pong');\n      this.emit('ping', new Base.PingEvent(payload.toString()))\n    }\n\n    if (opcode === this.OPCODES.pong) {\n      callbacks = this._pingCallbacks;\n      message   = this._encode(payload);\n      callback  = callbacks[message];\n\n      delete callbacks[message];\n      if (callback) callback()\n\n      this.emit('pong', new Base.PongEvent(payload.toString()))\n    }\n  },\n\n  _emitMessage: function(message) {\n    var message = this._message;\n    message.read();\n\n    delete this._message;\n\n    this._extensions.processIncomingMessage(message, function(error, message) {\n      if (error) return this._fail('extension_error', error.message);\n\n      var payload = message.data;\n      if (message.opcode === this.OPCODES.text) payload = this._encode(payload);\n\n      if (payload === null)\n        return this._fail('encoding_error', 'Could not decode a text frame as UTF-8');\n      else\n        this.emit('message', new Base.MessageEvent(payload));\n    }, this);\n  },\n\n  _encode: function(buffer) {\n    try {\n      var string = buffer.toString('binary', 0, buffer.length);\n      if (!this.UTF8_MATCH.test(string)) return null;\n    } catch (e) {}\n    return buffer.toString('utf8', 0, buffer.length);\n  },\n\n  _readUInt: function(buffer) {\n    if (buffer.length === 2) return buffer.readUInt16BE(0);\n\n    return buffer.readUInt32BE(0) * 0x100000000 +\n           buffer.readUInt32BE(4);\n  }\n};\n\nfor (var key in instance)\n  Hybi.prototype[key] = instance[key];\n\nmodule.exports = Hybi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js":
/*!**************************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js ***!
  \**************************************************************************/
/***/ ((module) => {

eval("\n\nvar Frame = function() {};\n\nvar instance = {\n  final:        false,\n  rsv1:         false,\n  rsv2:         false,\n  rsv3:         false,\n  opcode:       null,\n  masked:       false,\n  maskingKey:   null,\n  lengthBytes:  1,\n  length:       0,\n  payload:      null\n};\n\nfor (var key in instance)\n  Frame.prototype[key] = instance[key];\n\nmodule.exports = Frame;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWRyaXZlci9saWIvd2Vic29ja2V0L2RyaXZlci9oeWJpL2ZyYW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmlzXFxEb2N1bWVudHNcXFByb2plY3RzXFxTcGlkZXhcXHNwaWRleF93ZWJ1aTMuMFxcbm9kZV9tb2R1bGVzXFx3ZWJzb2NrZXQtZHJpdmVyXFxsaWJcXHdlYnNvY2tldFxcZHJpdmVyXFxoeWJpXFxmcmFtZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBGcmFtZSA9IGZ1bmN0aW9uKCkge307XG5cbnZhciBpbnN0YW5jZSA9IHtcbiAgZmluYWw6ICAgICAgICBmYWxzZSxcbiAgcnN2MTogICAgICAgICBmYWxzZSxcbiAgcnN2MjogICAgICAgICBmYWxzZSxcbiAgcnN2MzogICAgICAgICBmYWxzZSxcbiAgb3Bjb2RlOiAgICAgICBudWxsLFxuICBtYXNrZWQ6ICAgICAgIGZhbHNlLFxuICBtYXNraW5nS2V5OiAgIG51bGwsXG4gIGxlbmd0aEJ5dGVzOiAgMSxcbiAgbGVuZ3RoOiAgICAgICAwLFxuICBwYXlsb2FkOiAgICAgIG51bGxcbn07XG5cbmZvciAodmFyIGtleSBpbiBpbnN0YW5jZSlcbiAgRnJhbWUucHJvdG90eXBlW2tleV0gPSBpbnN0YW5jZVtrZXldO1xuXG5tb2R1bGUuZXhwb3J0cyA9IEZyYW1lO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/frame.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/message.js":
/*!****************************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/hybi/message.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer);\n\nvar Message = function() {\n  this.rsv1    = false;\n  this.rsv2    = false;\n  this.rsv3    = false;\n  this.opcode  = null;\n  this.length  = 0;\n  this._chunks = [];\n};\n\nvar instance = {\n  read: function() {\n    return this.data = this.data || Buffer.concat(this._chunks, this.length);\n  },\n\n  pushFrame: function(frame) {\n    this.rsv1 = this.rsv1 || frame.rsv1;\n    this.rsv2 = this.rsv2 || frame.rsv2;\n    this.rsv3 = this.rsv3 || frame.rsv3;\n\n    if (this.opcode === null) this.opcode = frame.opcode;\n\n    this._chunks.push(frame.payload);\n    this.length += frame.length;\n  }\n};\n\nfor (var key in instance)\n  Message.prototype[key] = instance[key];\n\nmodule.exports = Message;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWRyaXZlci9saWIvd2Vic29ja2V0L2RyaXZlci9oeWJpL21lc3NhZ2UuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsYUFBYSw0RkFBNkI7O0FBRTFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmlzXFxEb2N1bWVudHNcXFByb2plY3RzXFxTcGlkZXhcXHNwaWRleF93ZWJ1aTMuMFxcbm9kZV9tb2R1bGVzXFx3ZWJzb2NrZXQtZHJpdmVyXFxsaWJcXHdlYnNvY2tldFxcZHJpdmVyXFxoeWJpXFxtZXNzYWdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIEJ1ZmZlciA9IHJlcXVpcmUoJ3NhZmUtYnVmZmVyJykuQnVmZmVyO1xuXG52YXIgTWVzc2FnZSA9IGZ1bmN0aW9uKCkge1xuICB0aGlzLnJzdjEgICAgPSBmYWxzZTtcbiAgdGhpcy5yc3YyICAgID0gZmFsc2U7XG4gIHRoaXMucnN2MyAgICA9IGZhbHNlO1xuICB0aGlzLm9wY29kZSAgPSBudWxsO1xuICB0aGlzLmxlbmd0aCAgPSAwO1xuICB0aGlzLl9jaHVua3MgPSBbXTtcbn07XG5cbnZhciBpbnN0YW5jZSA9IHtcbiAgcmVhZDogZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIHRoaXMuZGF0YSA9IHRoaXMuZGF0YSB8fCBCdWZmZXIuY29uY2F0KHRoaXMuX2NodW5rcywgdGhpcy5sZW5ndGgpO1xuICB9LFxuXG4gIHB1c2hGcmFtZTogZnVuY3Rpb24oZnJhbWUpIHtcbiAgICB0aGlzLnJzdjEgPSB0aGlzLnJzdjEgfHwgZnJhbWUucnN2MTtcbiAgICB0aGlzLnJzdjIgPSB0aGlzLnJzdjIgfHwgZnJhbWUucnN2MjtcbiAgICB0aGlzLnJzdjMgPSB0aGlzLnJzdjMgfHwgZnJhbWUucnN2MztcblxuICAgIGlmICh0aGlzLm9wY29kZSA9PT0gbnVsbCkgdGhpcy5vcGNvZGUgPSBmcmFtZS5vcGNvZGU7XG5cbiAgICB0aGlzLl9jaHVua3MucHVzaChmcmFtZS5wYXlsb2FkKTtcbiAgICB0aGlzLmxlbmd0aCArPSBmcmFtZS5sZW5ndGg7XG4gIH1cbn07XG5cbmZvciAodmFyIGtleSBpbiBpbnN0YW5jZSlcbiAgTWVzc2FnZS5wcm90b3R5cGVba2V5XSA9IGluc3RhbmNlW2tleV07XG5cbm1vZHVsZS5leHBvcnRzID0gTWVzc2FnZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi/message.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/proxy.js":
/*!*********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/proxy.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer     = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer),\n    Stream     = (__webpack_require__(/*! stream */ \"stream\").Stream),\n    url        = __webpack_require__(/*! url */ \"url\"),\n    util       = __webpack_require__(/*! util */ \"util\"),\n    Base       = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Headers    = __webpack_require__(/*! ./headers */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js\"),\n    HttpParser = __webpack_require__(/*! ../http_parser */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js\");\n\nvar PORTS = { 'ws:': 80, 'wss:': 443 };\n\nvar Proxy = function(client, origin, options) {\n  this._client  = client;\n  this._http    = new HttpParser('response');\n  this._origin  = (typeof client.url === 'object') ? client.url : url.parse(client.url);\n  this._url     = (typeof origin === 'object') ? origin : url.parse(origin);\n  this._options = options || {};\n  this._state   = 0;\n\n  this.readable = this.writable = true;\n  this._paused  = false;\n\n  this._headers = new Headers();\n  this._headers.set('Host', this._origin.host);\n  this._headers.set('Connection', 'keep-alive');\n  this._headers.set('Proxy-Connection', 'keep-alive');\n\n  var auth = this._url.auth && Buffer.from(this._url.auth, 'utf8').toString('base64');\n  if (auth) this._headers.set('Proxy-Authorization', 'Basic ' + auth);\n};\nutil.inherits(Proxy, Stream);\n\nvar instance = {\n  setHeader: function(name, value) {\n    if (this._state !== 0) return false;\n    this._headers.set(name, value);\n    return true;\n  },\n\n  start: function() {\n    if (this._state !== 0) return false;\n    this._state = 1;\n\n    var origin = this._origin,\n        port   = origin.port || PORTS[origin.protocol],\n        start  = 'CONNECT ' + origin.hostname + ':' + port + ' HTTP/1.1';\n\n    var headers = [start, this._headers.toString(), ''];\n\n    this.emit('data', Buffer.from(headers.join('\\r\\n'), 'utf8'));\n    return true;\n  },\n\n  pause: function() {\n    this._paused = true;\n  },\n\n  resume: function() {\n    this._paused = false;\n    this.emit('drain');\n  },\n\n  write: function(chunk) {\n    if (!this.writable) return false;\n\n    this._http.parse(chunk);\n    if (!this._http.isComplete()) return !this._paused;\n\n    this.statusCode = this._http.statusCode;\n    this.headers    = this._http.headers;\n\n    if (this.statusCode === 200) {\n      this.emit('connect', new Base.ConnectEvent());\n    } else {\n      var message = \"Can't establish a connection to the server at \" + this._origin.href;\n      this.emit('error', new Error(message));\n    }\n    this.end();\n    return !this._paused;\n  },\n\n  end: function(chunk) {\n    if (!this.writable) return;\n    if (chunk !== undefined) this.write(chunk);\n    this.readable = this.writable = false;\n    this.emit('close');\n    this.emit('end');\n  },\n\n  destroy: function() {\n    this.end();\n  }\n};\n\nfor (var key in instance)\n  Proxy.prototype[key] = instance[key];\n\nmodule.exports = Proxy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/proxy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/server.js":
/*!**********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/server.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar util       = __webpack_require__(/*! util */ \"util\"),\n    HttpParser = __webpack_require__(/*! ../http_parser */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js\"),\n    Base       = __webpack_require__(/*! ./base */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/base.js\"),\n    Draft75    = __webpack_require__(/*! ./draft75 */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft75.js\"),\n    Draft76    = __webpack_require__(/*! ./draft76 */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/draft76.js\"),\n    Hybi       = __webpack_require__(/*! ./hybi */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/hybi.js\");\n\nvar Server = function(options) {\n  Base.call(this, null, null, options);\n  this._http = new HttpParser('request');\n};\nutil.inherits(Server, Base);\n\nvar instance = {\n  EVENTS: ['open', 'message', 'error', 'close', 'ping', 'pong'],\n\n  _bindEventListeners: function() {\n    this.messages.on('error', function() {});\n    this.on('error', function() {});\n  },\n\n  parse: function(chunk) {\n    if (this._delegate) return this._delegate.parse(chunk);\n\n    this._http.parse(chunk);\n    if (!this._http.isComplete()) return;\n\n    this.method  = this._http.method;\n    this.url     = this._http.url;\n    this.headers = this._http.headers;\n    this.body    = this._http.body;\n\n    var self = this;\n    this._delegate = Server.http(this, this._options);\n    this._delegate.messages = this.messages;\n    this._delegate.io = this.io;\n    this._open();\n\n    this.EVENTS.forEach(function(event) {\n      this._delegate.on(event, function(e) { self.emit(event, e) });\n    }, this);\n\n    this.protocol = this._delegate.protocol;\n    this.version  = this._delegate.version;\n\n    this.parse(this._http.body);\n    this.emit('connect', new Base.ConnectEvent());\n  },\n\n  _open: function() {\n    this.__queue.forEach(function(msg) {\n      this._delegate[msg[0]].apply(this._delegate, msg[1]);\n    }, this);\n    this.__queue = [];\n  }\n};\n\n['addExtension', 'setHeader', 'start', 'frame', 'text', 'binary', 'ping', 'close'].forEach(function(method) {\n  instance[method] = function() {\n    if (this._delegate) {\n      return this._delegate[method].apply(this._delegate, arguments);\n    } else {\n      this.__queue.push([method, arguments]);\n      return true;\n    }\n  };\n});\n\nfor (var key in instance)\n  Server.prototype[key] = instance[key];\n\nServer.isSecureRequest = function(request) {\n  if (request.connection && request.connection.authorized !== undefined) return true;\n  if (request.socket && request.socket.secure) return true;\n\n  var headers = request.headers;\n  if (!headers) return false;\n  if (headers['https'] === 'on') return true;\n  if (headers['x-forwarded-ssl'] === 'on') return true;\n  if (headers['x-forwarded-scheme'] === 'https') return true;\n  if (headers['x-forwarded-proto'] === 'https') return true;\n\n  return false;\n};\n\nServer.determineUrl = function(request) {\n  var scheme = this.isSecureRequest(request) ? 'wss:' : 'ws:';\n  return scheme + '//' + request.headers.host + request.url;\n};\n\nServer.http = function(request, options) {\n  options = options || {};\n  if (options.requireMasking === undefined) options.requireMasking = true;\n\n  var headers = request.headers,\n      version = headers['sec-websocket-version'],\n      key     = headers['sec-websocket-key'],\n      key1    = headers['sec-websocket-key1'],\n      key2    = headers['sec-websocket-key2'],\n      url     = this.determineUrl(request);\n\n  if (version || key)\n    return new Hybi(request, url, options);\n  else if (key1 || key2)\n    return new Draft76(request, url, options);\n  else\n    return new Draft75(request, url, options);\n};\n\nmodule.exports = Server;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/server.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/driver/stream_reader.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/driver/stream_reader.js ***!
  \*****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer);\n\nvar StreamReader = function() {\n  this._queue     = [];\n  this._queueSize = 0;\n  this._offset    = 0;\n};\n\nStreamReader.prototype.put = function(buffer) {\n  if (!buffer || buffer.length === 0) return;\n  if (!Buffer.isBuffer(buffer)) buffer = Buffer.from(buffer);\n  this._queue.push(buffer);\n  this._queueSize += buffer.length;\n};\n\nStreamReader.prototype.read = function(length) {\n  if (length > this._queueSize) return null;\n  if (length === 0) return Buffer.alloc(0);\n\n  this._queueSize -= length;\n\n  var queue  = this._queue,\n      remain = length,\n      first  = queue[0],\n      buffers, buffer;\n\n  if (first.length >= length) {\n    if (first.length === length) {\n      return queue.shift();\n    } else {\n      buffer = first.slice(0, length);\n      queue[0] = first.slice(length);\n      return buffer;\n    }\n  }\n\n  for (var i = 0, n = queue.length; i < n; i++) {\n    if (remain < queue[i].length) break;\n    remain -= queue[i].length;\n  }\n  buffers = queue.splice(0, i);\n\n  if (remain > 0 && queue.length > 0) {\n    buffers.push(queue[0].slice(0, remain));\n    queue[0] = queue[0].slice(remain);\n  }\n  return Buffer.concat(buffers, length);\n};\n\nStreamReader.prototype.eachByte = function(callback, context) {\n  var buffer, n, index;\n\n  while (this._queue.length > 0) {\n    buffer = this._queue[0];\n    n = buffer.length;\n\n    while (this._offset < n) {\n      index = this._offset;\n      this._offset += 1;\n      callback.call(context, buffer[index]);\n    }\n    this._offset = 0;\n    this._queue.shift();\n  }\n};\n\nmodule.exports = StreamReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvd2Vic29ja2V0LWRyaXZlci9saWIvd2Vic29ja2V0L2RyaXZlci9zdHJlYW1fcmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGFBQWEsNEZBQTZCOztBQUUxQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsb0NBQW9DLE9BQU87QUFDM0M7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXG5vZGVfbW9kdWxlc1xcd2Vic29ja2V0LWRyaXZlclxcbGliXFx3ZWJzb2NrZXRcXGRyaXZlclxcc3RyZWFtX3JlYWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBCdWZmZXIgPSByZXF1aXJlKCdzYWZlLWJ1ZmZlcicpLkJ1ZmZlcjtcblxudmFyIFN0cmVhbVJlYWRlciA9IGZ1bmN0aW9uKCkge1xuICB0aGlzLl9xdWV1ZSAgICAgPSBbXTtcbiAgdGhpcy5fcXVldWVTaXplID0gMDtcbiAgdGhpcy5fb2Zmc2V0ICAgID0gMDtcbn07XG5cblN0cmVhbVJlYWRlci5wcm90b3R5cGUucHV0ID0gZnVuY3Rpb24oYnVmZmVyKSB7XG4gIGlmICghYnVmZmVyIHx8IGJ1ZmZlci5sZW5ndGggPT09IDApIHJldHVybjtcbiAgaWYgKCFCdWZmZXIuaXNCdWZmZXIoYnVmZmVyKSkgYnVmZmVyID0gQnVmZmVyLmZyb20oYnVmZmVyKTtcbiAgdGhpcy5fcXVldWUucHVzaChidWZmZXIpO1xuICB0aGlzLl9xdWV1ZVNpemUgKz0gYnVmZmVyLmxlbmd0aDtcbn07XG5cblN0cmVhbVJlYWRlci5wcm90b3R5cGUucmVhZCA9IGZ1bmN0aW9uKGxlbmd0aCkge1xuICBpZiAobGVuZ3RoID4gdGhpcy5fcXVldWVTaXplKSByZXR1cm4gbnVsbDtcbiAgaWYgKGxlbmd0aCA9PT0gMCkgcmV0dXJuIEJ1ZmZlci5hbGxvYygwKTtcblxuICB0aGlzLl9xdWV1ZVNpemUgLT0gbGVuZ3RoO1xuXG4gIHZhciBxdWV1ZSAgPSB0aGlzLl9xdWV1ZSxcbiAgICAgIHJlbWFpbiA9IGxlbmd0aCxcbiAgICAgIGZpcnN0ICA9IHF1ZXVlWzBdLFxuICAgICAgYnVmZmVycywgYnVmZmVyO1xuXG4gIGlmIChmaXJzdC5sZW5ndGggPj0gbGVuZ3RoKSB7XG4gICAgaWYgKGZpcnN0Lmxlbmd0aCA9PT0gbGVuZ3RoKSB7XG4gICAgICByZXR1cm4gcXVldWUuc2hpZnQoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgYnVmZmVyID0gZmlyc3Quc2xpY2UoMCwgbGVuZ3RoKTtcbiAgICAgIHF1ZXVlWzBdID0gZmlyc3Quc2xpY2UobGVuZ3RoKTtcbiAgICAgIHJldHVybiBidWZmZXI7XG4gICAgfVxuICB9XG5cbiAgZm9yICh2YXIgaSA9IDAsIG4gPSBxdWV1ZS5sZW5ndGg7IGkgPCBuOyBpKyspIHtcbiAgICBpZiAocmVtYWluIDwgcXVldWVbaV0ubGVuZ3RoKSBicmVhaztcbiAgICByZW1haW4gLT0gcXVldWVbaV0ubGVuZ3RoO1xuICB9XG4gIGJ1ZmZlcnMgPSBxdWV1ZS5zcGxpY2UoMCwgaSk7XG5cbiAgaWYgKHJlbWFpbiA+IDAgJiYgcXVldWUubGVuZ3RoID4gMCkge1xuICAgIGJ1ZmZlcnMucHVzaChxdWV1ZVswXS5zbGljZSgwLCByZW1haW4pKTtcbiAgICBxdWV1ZVswXSA9IHF1ZXVlWzBdLnNsaWNlKHJlbWFpbik7XG4gIH1cbiAgcmV0dXJuIEJ1ZmZlci5jb25jYXQoYnVmZmVycywgbGVuZ3RoKTtcbn07XG5cblN0cmVhbVJlYWRlci5wcm90b3R5cGUuZWFjaEJ5dGUgPSBmdW5jdGlvbihjYWxsYmFjaywgY29udGV4dCkge1xuICB2YXIgYnVmZmVyLCBuLCBpbmRleDtcblxuICB3aGlsZSAodGhpcy5fcXVldWUubGVuZ3RoID4gMCkge1xuICAgIGJ1ZmZlciA9IHRoaXMuX3F1ZXVlWzBdO1xuICAgIG4gPSBidWZmZXIubGVuZ3RoO1xuXG4gICAgd2hpbGUgKHRoaXMuX29mZnNldCA8IG4pIHtcbiAgICAgIGluZGV4ID0gdGhpcy5fb2Zmc2V0O1xuICAgICAgdGhpcy5fb2Zmc2V0ICs9IDE7XG4gICAgICBjYWxsYmFjay5jYWxsKGNvbnRleHQsIGJ1ZmZlcltpbmRleF0pO1xuICAgIH1cbiAgICB0aGlzLl9vZmZzZXQgPSAwO1xuICAgIHRoaXMuX3F1ZXVlLnNoaWZ0KCk7XG4gIH1cbn07XG5cbm1vZHVsZS5leHBvcnRzID0gU3RyZWFtUmVhZGVyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/driver/stream_reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js":
/*!********************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/http_parser.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar NodeHTTPParser = (__webpack_require__(/*! http-parser-js */ \"(ssr)/./node_modules/http-parser-js/http-parser.js\").HTTPParser),\n    Buffer         = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer);\n\nvar TYPES = {\n  request:  NodeHTTPParser.REQUEST  || 'request',\n  response: NodeHTTPParser.RESPONSE || 'response'\n};\n\nvar HttpParser = function(type) {\n  this._type     = type;\n  this._parser   = new NodeHTTPParser(TYPES[type]);\n  this._complete = false;\n  this.headers   = {};\n\n  var current = null,\n      self    = this;\n\n  this._parser.onHeaderField = function(b, start, length) {\n    current = b.toString('utf8', start, start + length).toLowerCase();\n  };\n\n  this._parser.onHeaderValue = function(b, start, length) {\n    var value = b.toString('utf8', start, start + length);\n\n    if (self.headers.hasOwnProperty(current))\n      self.headers[current] += ', ' + value;\n    else\n      self.headers[current] = value;\n  };\n\n  this._parser.onHeadersComplete = this._parser[NodeHTTPParser.kOnHeadersComplete] =\n  function(majorVersion, minorVersion, headers, method, pathname, statusCode) {\n    var info = arguments[0];\n\n    if (typeof info === 'object') {\n      method     = info.method;\n      pathname   = info.url;\n      statusCode = info.statusCode;\n      headers    = info.headers;\n    }\n\n    self.method     = (typeof method === 'number') ? HttpParser.METHODS[method] : method;\n    self.statusCode = statusCode;\n    self.url        = pathname;\n\n    if (!headers) return;\n\n    for (var i = 0, n = headers.length, key, value; i < n; i += 2) {\n      key   = headers[i].toLowerCase();\n      value = headers[i+1];\n      if (self.headers.hasOwnProperty(key))\n        self.headers[key] += ', ' + value;\n      else\n        self.headers[key] = value;\n    }\n\n    self._complete = true;\n  };\n};\n\nHttpParser.METHODS = {\n  0:  'DELETE',\n  1:  'GET',\n  2:  'HEAD',\n  3:  'POST',\n  4:  'PUT',\n  5:  'CONNECT',\n  6:  'OPTIONS',\n  7:  'TRACE',\n  8:  'COPY',\n  9:  'LOCK',\n  10: 'MKCOL',\n  11: 'MOVE',\n  12: 'PROPFIND',\n  13: 'PROPPATCH',\n  14: 'SEARCH',\n  15: 'UNLOCK',\n  16: 'BIND',\n  17: 'REBIND',\n  18: 'UNBIND',\n  19: 'ACL',\n  20: 'REPORT',\n  21: 'MKACTIVITY',\n  22: 'CHECKOUT',\n  23: 'MERGE',\n  24: 'M-SEARCH',\n  25: 'NOTIFY',\n  26: 'SUBSCRIBE',\n  27: 'UNSUBSCRIBE',\n  28: 'PATCH',\n  29: 'PURGE',\n  30: 'MKCALENDAR',\n  31: 'LINK',\n  32: 'UNLINK'\n};\n\nvar VERSION = process.version\n  ? process.version.match(/[0-9]+/g).map(function(n) { return parseInt(n, 10) })\n  : [];\n\nif (VERSION[0] === 0 && VERSION[1] === 12) {\n  HttpParser.METHODS[16] = 'REPORT';\n  HttpParser.METHODS[17] = 'MKACTIVITY';\n  HttpParser.METHODS[18] = 'CHECKOUT';\n  HttpParser.METHODS[19] = 'MERGE';\n  HttpParser.METHODS[20] = 'M-SEARCH';\n  HttpParser.METHODS[21] = 'NOTIFY';\n  HttpParser.METHODS[22] = 'SUBSCRIBE';\n  HttpParser.METHODS[23] = 'UNSUBSCRIBE';\n  HttpParser.METHODS[24] = 'PATCH';\n  HttpParser.METHODS[25] = 'PURGE';\n}\n\nHttpParser.prototype.isComplete = function() {\n  return this._complete;\n};\n\nHttpParser.prototype.parse = function(chunk) {\n  var consumed = this._parser.execute(chunk, 0, chunk.length);\n\n  if (typeof consumed !== 'number') {\n    this.error     = consumed;\n    this._complete = true;\n    return;\n  }\n\n  if (this._complete)\n    this.body = (consumed < chunk.length)\n              ? chunk.slice(consumed)\n              : Buffer.alloc(0);\n};\n\nmodule.exports = HttpParser;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/http_parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/websocket-driver/lib/websocket/streams.js":
/*!****************************************************************!*\
  !*** ./node_modules/websocket-driver/lib/websocket/streams.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\n/**\n\nStreams in a WebSocket connection\n---------------------------------\n\nWe model a WebSocket as two duplex streams: one stream is for the wire protocol\nover an I/O socket, and the other is for incoming/outgoing messages.\n\n\n                        +----------+      +---------+      +----------+\n    [1] write(chunk) -->| ~~~~~~~~ +----->| parse() +----->| ~~~~~~~~ +--> emit('data') [2]\n                        |          |      +----+----+      |          |\n                        |          |           |           |          |\n                        |    IO    |           | [5]       | Messages |\n                        |          |           V           |          |\n                        |          |      +---------+      |          |\n    [4] emit('data') <--+ ~~~~~~~~ |<-----+ frame() |<-----+ ~~~~~~~~ |<-- write(chunk) [3]\n                        +----------+      +---------+      +----------+\n\n\nMessage transfer in each direction is simple: IO receives a byte stream [1] and\nsends this stream for parsing. The parser will periodically emit a complete\nmessage text on the Messages stream [2]. Similarly, when messages are written\nto the Messages stream [3], they are framed using the WebSocket wire format and\nemitted via IO [4].\n\nThere is a feedback loop via [5] since some input from [1] will be things like\nping, pong and close frames. In these cases the protocol responds by emitting\nresponses directly back to [4] rather than emitting messages via [2].\n\nFor the purposes of flow control, we consider the sources of each Readable\nstream to be as follows:\n\n* [2] receives input from [1]\n* [4] receives input from [1] and [3]\n\nThe classes below express the relationships described above without prescribing\nanything about how parse() and frame() work, other than assuming they emit\n'data' events to the IO and Messages streams. They will work with any protocol\ndriver having these two methods.\n**/\n\n\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream),\n    util   = __webpack_require__(/*! util */ \"util\");\n\n\nvar IO = function(driver) {\n  this.readable = this.writable = true;\n  this._paused  = false;\n  this._driver  = driver;\n};\nutil.inherits(IO, Stream);\n\n// The IO pause() and resume() methods will be called when the socket we are\n// piping to gets backed up and drains. Since IO output [4] comes from IO input\n// [1] and Messages input [3], we need to tell both of those to return false\n// from write() when this stream is paused.\n\nIO.prototype.pause = function() {\n  this._paused = true;\n  this._driver.messages._paused = true;\n};\n\nIO.prototype.resume = function() {\n  this._paused = false;\n  this.emit('drain');\n\n  var messages = this._driver.messages;\n  messages._paused = false;\n  messages.emit('drain');\n};\n\n// When we receive input from a socket, send it to the parser and tell the\n// source whether to back off.\nIO.prototype.write = function(chunk) {\n  if (!this.writable) return false;\n  this._driver.parse(chunk);\n  return !this._paused;\n};\n\n// The IO end() method will be called when the socket piping into it emits\n// 'close' or 'end', i.e. the socket is closed. In this situation the Messages\n// stream will not emit any more data so we emit 'end'.\nIO.prototype.end = function(chunk) {\n  if (!this.writable) return;\n  if (chunk !== undefined) this.write(chunk);\n  this.writable = false;\n\n  var messages = this._driver.messages;\n  if (messages.readable) {\n    messages.readable = messages.writable = false;\n    messages.emit('end');\n  }\n};\n\nIO.prototype.destroy = function() {\n  this.end();\n};\n\n\nvar Messages = function(driver) {\n  this.readable = this.writable = true;\n  this._paused  = false;\n  this._driver  = driver;\n};\nutil.inherits(Messages, Stream);\n\n// The Messages pause() and resume() methods will be called when the app that's\n// processing the messages gets backed up and drains. If we're emitting\n// messages too fast we should tell the source to slow down. Message output [2]\n// comes from IO input [1].\n\nMessages.prototype.pause = function() {\n  this._driver.io._paused = true;\n};\n\nMessages.prototype.resume = function() {\n  this._driver.io._paused = false;\n  this._driver.io.emit('drain');\n};\n\n// When we receive messages from the user, send them to the formatter and tell\n// the source whether to back off.\nMessages.prototype.write = function(message) {\n  if (!this.writable) return false;\n  if (typeof message === 'string') this._driver.text(message);\n  else this._driver.binary(message);\n  return !this._paused;\n};\n\n// The Messages end() method will be called when a stream piping into it emits\n// 'end'. Many streams may be piped into the WebSocket and one of them ending\n// does not mean the whole socket is done, so just process the input and move\n// on leaving the socket open.\nMessages.prototype.end = function(message) {\n  if (message !== undefined) this.write(message);\n};\n\nMessages.prototype.destroy = function() {};\n\n\nexports.IO = IO;\nexports.Messages = Messages;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/websocket-driver/lib/websocket/streams.js\n");

/***/ })

};
;