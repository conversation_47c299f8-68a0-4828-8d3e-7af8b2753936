"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { Package, Tag, Building } from "lucide-react";
import {
  CreateTaggedAssetFormData,
  UpdateTaggedAssetFormData,
  TaggedAsset,
  AssetOption,
  TagOption,
} from "@/types/tagged-asset";
import {
  CreateTaggedAssetSchema,
  UpdateTaggedAssetSchema,
} from "@/lib/schemas/tagged-asset";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";

interface TaggedAssetFormProps {
  taggedAsset?: TaggedAsset;
  assetOptions: AssetOption[];
  tagOptions: TagOption[];
  isLoading?: boolean;
  onSubmit: (
    data: CreateTaggedAssetFormData | UpdateTaggedAssetFormData
  ) => Promise<void> | void;
  onCancel: () => void;
  onClear?: () => void;
}

export function TaggedAssetForm({
  taggedAsset,
  assetOptions,
  tagOptions,
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: TaggedAssetFormProps) {
  const isEditing = !!taggedAsset;
  const schema = isEditing ? UpdateTaggedAssetSchema : CreateTaggedAssetSchema;

  const form = useForm<CreateTaggedAssetFormData | UpdateTaggedAssetFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      assetId: "",
      deviceId: "",
      status: "ACTIVE",
    },
  });

  // Reset form when taggedAsset data changes (for editing)
  useEffect(() => {
    if (taggedAsset) {
      console.log("🔄 Resetting tagged asset form with data:", taggedAsset);
      const formData = {
        id: taggedAsset.id,
        assetId: taggedAsset.assetId,
        deviceId: taggedAsset.deviceId,
        status: taggedAsset.status,
      };
      console.log("🔄 Tagged asset form data being set:", formData);

      // Use setTimeout to ensure the form is ready
      setTimeout(() => {
        form.reset(formData);
      }, 0);
    } else {
      console.log("🆕 Resetting tagged asset form for new tagged asset");
      const emptyFormData = {
        assetId: "",
        deviceId: "",
        status: "ACTIVE",
      };
      console.log("🆕 Empty tagged asset form data being set:", emptyFormData);
      form.reset(emptyFormData);
    }
  }, [taggedAsset, form, isEditing]);

  const handleSubmit = async (
    data: CreateTaggedAssetFormData | UpdateTaggedAssetFormData
  ) => {
    try {
      console.log("📝 Tagged asset form submission data:", data);
      console.log("🔧 Is editing:", isEditing);
      console.log("🔧 Tagged asset prop:", taggedAsset);
      console.log("🔧 Form errors:", form.formState.errors);
      console.log("🔧 Form is valid:", form.formState.isValid);

      // Trigger validation manually to ensure all fields are validated
      const isValid = await form.trigger();
      console.log("🔧 Manual validation result:", isValid);

      if (!isValid) {
        console.error("❌ Form validation failed:", form.formState.errors);
        return;
      }

      console.log("🔄 Submitting tagged asset data:", data);
      await onSubmit(data);

      // Reset form after successful submission for new tagged assets
      if (!isEditing) {
        console.log("🧹 Clearing form after successful creation");
        form.reset();
      }
    } catch (error) {
      console.error("❌ Tagged asset form submission error:", error);
    }
  };

  const handleClear = () => {
    form.reset();
    if (onClear) {
      onClear();
    }
  };

  // Debug form values
  const formValues = form.watch();
  useEffect(() => {
    console.log("👀 Current tagged asset form values:", formValues);
  }, [formValues]);

  // Debug asset and tag options
  useEffect(() => {
    console.log("🏢 Asset options received:", assetOptions);
    console.log("🏷️ Tag options received:", tagOptions);
  }, [assetOptions, tagOptions]);

  // Get selected asset and tag info for display
  const selectedAsset = assetOptions.find(
    (a) => a.id === form.watch("assetId")
  );
  const selectedTag = tagOptions.find((t) => t.id === form.watch("deviceId"));

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Asset Selection */}
        <Card>
          <CardContent className="space-y-4 pt-6">
            <FormField
              control={form.control}
              name="assetId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Asset</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose the asset" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {assetOptions.length === 0 ? (
                        <SelectItem value="" disabled>
                          No assets available
                        </SelectItem>
                      ) : (
                        assetOptions.map((asset) => (
                          <SelectItem key={asset.id} value={asset.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{asset.name}</span>
                              <span className="text-sm text-muted-foreground">
                                {asset.externalId} • {asset.assetType}
                              </span>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedAsset && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Package className="h-4 w-4" />
                  <span className="font-medium">Selected Asset</span>
                </div>
                <div className="text-sm space-y-1">
                  <div>
                    <strong>Name:</strong> {selectedAsset.name}
                  </div>
                  <div>
                    <strong>External ID:</strong> {selectedAsset.externalId}
                  </div>
                  <div>
                    <strong>Type:</strong> {selectedAsset.assetType}
                  </div>
                </div>
              </div>
            )}
            {/* Tag Selection */}
            <FormField
              control={form.control}
              name="deviceId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tag Device</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose the tag device" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {tagOptions.length === 0 ? (
                        <SelectItem value="" disabled>
                          No tag devices available
                        </SelectItem>
                      ) : (
                        tagOptions.map((tag) => (
                          <SelectItem key={tag.id} value={tag.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{tag.name}</span>
                              <span className="text-sm text-muted-foreground">
                                {tag.externalId} • Model: {tag.modelId}
                              </span>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedTag && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Tag className="h-4 w-4" />
                  <span className="font-medium">Selected Tag</span>
                </div>
                <div className="text-sm space-y-1">
                  <div>
                    <strong>Name:</strong> {selectedTag.name}
                  </div>
                  <div>
                    <strong>External ID:</strong> {selectedTag.externalId}
                  </div>
                  <div>
                    <strong>Model ID:</strong> {selectedTag.modelId}
                  </div>
                </div>
              </div>
            )}
            {/* Configuration */}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Active Status</FormLabel>
                    <FormDescription>
                      Enable or disable this tagged asset for tracking.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={
                        field.value === "ACTIVE" || field.value === "TAGGED"
                      }
                      onCheckedChange={(checked) =>
                        field.onChange(checked ? "ACTIVE" : "INACTIVE")
                      }
                      disabled={isLoading}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>
        <Separator />
        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            type="submit"
            disabled={isLoading}
            className="flex-1 sm:flex-none bg-green-700 text-white hover:bg-green-800 rounded"
          >
            {isLoading
              ? "Saving..."
              : isEditing
              ? "Update Tagged Asset"
              : "Create Tagged Asset"}
          </Button>

          {!isEditing && (
            <Button
              type="button"
              variant="outline"
              onClick={handleClear}
              disabled={isLoading}
              className="border bg-white border-gray-700 text-black hover:bg-green-20 rounded"
            >
              Clear
            </Button>
          )}

          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
            className="border bg-white border-red-700 text-black hover:bg-green-20 rounded"
          >
            Close
          </Button>
        </div>
      </form>
    </Form>
  );
}
