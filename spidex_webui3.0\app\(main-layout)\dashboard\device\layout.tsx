const title = "Dashboard";

export const metadata = {
  title,
};

export default function Layout({
  children,
  metrics,
  branches,
  summary,
}: {
  children: React.ReactNode;
  metrics: React.ReactNode;
  branches: React.ReactNode;
  summary: React.ReactNode;
}) {
  return (
    <div className="flex flex-col py-4 px-4 space-y-4">
      {children}
      {/* {metrics} */}
      <div className="flex gap-4">
        {branches}
        {/* {summary} */}
      </div>
    </div>
  );
}
