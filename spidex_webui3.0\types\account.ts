// Account Management Types

export interface User {
  id: string;
  userId: string;
  name: string;
  email?: string;
  mobileNumber?: string;
  password?: string;
  tenantId: string;
  tenantName?: string;
  roles: Role[];
  active: boolean;
  deleted: boolean;
  enabled: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
  lastLogin?: number;
  profilePicture?: string;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  deleted: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
}

export interface Permission {
  id: string;
  name: string;
  description?: string;
  module: string;
  action: string; // CREATE, READ, UPDATE, DELETE, VIEW
}

export interface Tenant {
  id: string;
  name: string;
  description?: string;
  domain?: string;
  active: boolean;
  deleted: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
  settings?: TenantSettings;
}

export interface TenantSettings {
  maxUsers?: number;
  features?: string[];
  customization?: {
    logo?: string;
    theme?: string;
    colors?: Record<string, string>;
  };
}

// Form Data Types
export interface CreateAccountFormData {
  name: string;
  userId: string;
  emailId?: string;
  mobileNumber: string;
  tenantId: string;
  roles: string[]; // Array of role IDs
  password?: string;
}

export interface UpdateAccountFormData {
  id: string;
  name: string;
  userId: string;
  emailId?: string;
  mobileNumber: string;
  tenantId: string;
  roles: string[]; // Array of role IDs
  active?: boolean;
  deleted?: boolean;
}

export interface PasswordResetFormData {
  userId: string;
  currentPassword?: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ForgotPasswordFormData {
  email: string;
  userId?: string;
}

// Search and Filter Types
export interface AccountSearchFilters {
  searchTerm: string;
  tenantId: string;
  roleId: string;
  active?: boolean;
  deleted: boolean;
}

export interface AccountPaginationParams {
  pageNumber: number;
  pageSize: AccountPageSize;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// API Response Types
export interface AccountApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedAccountResponse {
  accounts: User[];
  total: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

// Table Column Types
export interface AccountTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: number;
  render?: (value: any, record: User) => React.ReactNode;
}

// Action Types
export type AccountAction =
  | "create"
  | "edit"
  | "delete"
  | "activate"
  | "deactivate"
  | "resetPassword"
  | "view";

export interface AccountActionConfig {
  action: AccountAction;
  label: string;
  icon?: string;
  permission?: string;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  disabled?: (user: User) => boolean;
}

// Constants
export const ACCOUNT_PAGE_SIZES = [10, 50, 100] as const;
export const ACCOUNT_PAGE_SIZE_ALL = "all" as const;

export type AccountPageSize =
  | (typeof ACCOUNT_PAGE_SIZES)[number]
  | typeof ACCOUNT_PAGE_SIZE_ALL;

export const DEFAULT_ACCOUNT_PAGE_SIZE: AccountPageSize = 10;

// Status Types
export type AccountStatus = "active" | "inactive" | "deleted";

export interface AccountStatusConfig {
  status: AccountStatus;
  label: string;
  color: string;
  bgColor: string;
}

// Export utility types
export type CreateAccountPayload = Omit<
  User,
  "id" | "createdTime" | "modifiedTime" | "deleted"
>;
export type UpdateAccountPayload = Partial<User> & { id: string };
