import { Suspense } from "react";
import { Metadata } from "next";
import LocationManagement from "@/components/modules/location/location-management";
import { Skeleton } from "@/components/ui/skeleton";
import {
  TableSkeleton,
  LOCATION_TABLE_COLUMNS,
} from "@/components/ui/table-skeleton";
import { Card, CardContent } from "@/components/ui/card";

export const metadata: Metadata = {
  title: "Location Management - Spidex",
  description:
    "Create and manage location configurations for your organization",
};

function LocationPageSkeleton() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Breadcrumb skeleton */}
      <div className="flex items-center space-x-2">
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-24" />
      </div>

      {/* Header skeleton */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Search and filters skeleton */}
      <div className="space-y-4">
        <div className="rounded-lg border">
          <div className="p-6 space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="space-y-2">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-64" />
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-16" />
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <Skeleton className="h-10 flex-1" />
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-20" />
            </div>
            <div className="flex items-center gap-4 pt-2 border-t">
              <div className="flex items-center space-x-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-32" />
              </div>
              <Skeleton className="h-4 w-48" />
            </div>
          </div>
        </div>
      </div>

      {/* Data Table Skeleton */}
      <Card>
        <CardContent className="p-0">
          <TableSkeleton
            columns={LOCATION_TABLE_COLUMNS}
            rows={10}
            showPagination={true}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default function LocationPage() {
  return (
    <Suspense fallback={<LocationPageSkeleton />}>
      <LocationManagement />
    </Suspense>
  );
}
