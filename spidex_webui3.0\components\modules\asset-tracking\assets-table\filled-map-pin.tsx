import React from "react";

export default function FilledMapPin({
  color = "#ef4444",
  isHighlighted = false,
  width = 36,
  height = 36,
}) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={color}
      stroke="#fff"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      style={{
        filter: "drop-shadow(0 2px 4px rgba(0,0,0,0.15))",
        transform: isHighlighted ? "scale(1.35)" : undefined,
        transition: "transform 0.2s",
      }}
      className={isHighlighted ? "animate-bounce" : ""}
    >
      <path d="M12 21s-6-5.686-6-10A6 6 0 1 1 18 11c0 4.314-6 10-6 10z" />
      <circle cx="12" cy="11" r="2.5" fill="#fff" />
    </svg>
  );
}
