"use client";
import { signOut } from "next-auth/react";

export default function SignoutBtn() {
  const handleSignOut = async () => {
    console.log("Initiating logout...");
    try {
      await signOut({
        callbackUrl: "/login",
        redirect: true,
      });
      console.log("Logout successful");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return <button onClick={handleSignOut}>Logout</button>;
}
