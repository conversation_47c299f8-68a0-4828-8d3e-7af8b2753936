"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { assetTrackingAPI } from "@/lib/api/asset-tracking";

/**
 * Hook to ensure the old assetTrackingAPI has the current session token
 */
export function useAuthenticatedAssetAPI() {
  const { data: session } = useSession();

  useEffect(() => {
    if (session?.user?.token) {
      // Store the token in localStorage so the old API can access it
      localStorage.setItem("token", session.user.token);
    } else {
      // Clear the token if no session
      localStorage.removeItem("token");
    }
  }, [session]);

  return assetTrackingAPI;
}
