"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-map-gl";
exports.ids = ["vendor-chunks/react-map-gl"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/attribution-control.js":
/*!******************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/attribution-control.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js\");\n/* harmony import */ var _use_control__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/use-control.js\");\n\n\n\nfunction AttributionControl(props) {\n    const ctrl = (0,_use_control__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(({ mapLib }) => new mapLib.AttributionControl(props), {\n        position: props.position\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        (0,_utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._container, props.style);\n    }, [props.style]);\n    return null;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(AttributionControl));\n//# sourceMappingURL=attribution-control.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL2NvbXBvbmVudHMvYXR0cmlidXRpb24tY29udHJvbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF3QztBQUNxQjtBQUN0QjtBQUN2QztBQUNBLGlCQUFpQix3REFBVSxJQUFJLFFBQVE7QUFDdkM7QUFDQSxLQUFLO0FBQ0wsSUFBSSxnREFBUztBQUNiLFFBQVEseUVBQWU7QUFDdkIsS0FBSztBQUNMO0FBQ0E7QUFDQSxpRUFBZSwyQ0FBSSxvQkFBb0IsRUFBQztBQUN4QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtbWFwLWdsXFxkaXN0XFxlc21cXGNvbXBvbmVudHNcXGF0dHJpYnV0aW9uLWNvbnRyb2wuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCBtZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgYXBwbHlSZWFjdFN0eWxlIH0gZnJvbSAnLi4vdXRpbHMvYXBwbHktcmVhY3Qtc3R5bGUnO1xuaW1wb3J0IHVzZUNvbnRyb2wgZnJvbSAnLi91c2UtY29udHJvbCc7XG5mdW5jdGlvbiBBdHRyaWJ1dGlvbkNvbnRyb2wocHJvcHMpIHtcbiAgICBjb25zdCBjdHJsID0gdXNlQ29udHJvbCgoeyBtYXBMaWIgfSkgPT4gbmV3IG1hcExpYi5BdHRyaWJ1dGlvbkNvbnRyb2wocHJvcHMpLCB7XG4gICAgICAgIHBvc2l0aW9uOiBwcm9wcy5wb3NpdGlvblxuICAgIH0pO1xuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGFwcGx5UmVhY3RTdHlsZShjdHJsLl9jb250YWluZXIsIHByb3BzLnN0eWxlKTtcbiAgICB9LCBbcHJvcHMuc3R5bGVdKTtcbiAgICByZXR1cm4gbnVsbDtcbn1cbmV4cG9ydCBkZWZhdWx0IG1lbW8oQXR0cmlidXRpb25Db250cm9sKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWF0dHJpYnV0aW9uLWNvbnRyb2wuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/attribution-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/fullscreen-control.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/fullscreen-control.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js\");\n/* harmony import */ var _use_control__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/use-control.js\");\n\n\n\nfunction FullscreenControl(props) {\n    const ctrl = (0,_use_control__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(({ mapLib }) => new mapLib.FullscreenControl({\n        container: props.containerId && document.getElementById(props.containerId)\n    }), { position: props.position });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        (0,_utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._controlContainer, props.style);\n    }, [props.style]);\n    return null;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(FullscreenControl));\n//# sourceMappingURL=fullscreen-control.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL2NvbXBvbmVudHMvZnVsbHNjcmVlbi1jb250cm9sLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXdDO0FBQ3FCO0FBQ3RCO0FBQ3ZDO0FBQ0EsaUJBQWlCLHdEQUFVLElBQUksUUFBUTtBQUN2QztBQUNBLEtBQUssS0FBSywwQkFBMEI7QUFDcEMsSUFBSSxnREFBUztBQUNiLFFBQVEseUVBQWU7QUFDdkIsS0FBSztBQUNMO0FBQ0E7QUFDQSxpRUFBZSwyQ0FBSSxtQkFBbUIsRUFBQztBQUN2QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtbWFwLWdsXFxkaXN0XFxlc21cXGNvbXBvbmVudHNcXGZ1bGxzY3JlZW4tY29udHJvbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QsIG1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBhcHBseVJlYWN0U3R5bGUgfSBmcm9tICcuLi91dGlscy9hcHBseS1yZWFjdC1zdHlsZSc7XG5pbXBvcnQgdXNlQ29udHJvbCBmcm9tICcuL3VzZS1jb250cm9sJztcbmZ1bmN0aW9uIEZ1bGxzY3JlZW5Db250cm9sKHByb3BzKSB7XG4gICAgY29uc3QgY3RybCA9IHVzZUNvbnRyb2woKHsgbWFwTGliIH0pID0+IG5ldyBtYXBMaWIuRnVsbHNjcmVlbkNvbnRyb2woe1xuICAgICAgICBjb250YWluZXI6IHByb3BzLmNvbnRhaW5lcklkICYmIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKHByb3BzLmNvbnRhaW5lcklkKVxuICAgIH0pLCB7IHBvc2l0aW9uOiBwcm9wcy5wb3NpdGlvbiB9KTtcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBhcHBseVJlYWN0U3R5bGUoY3RybC5fY29udHJvbENvbnRhaW5lciwgcHJvcHMuc3R5bGUpO1xuICAgIH0sIFtwcm9wcy5zdHlsZV0pO1xuICAgIHJldHVybiBudWxsO1xufVxuZXhwb3J0IGRlZmF1bHQgbWVtbyhGdWxsc2NyZWVuQ29udHJvbCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1mdWxsc2NyZWVuLWNvbnRyb2wuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/fullscreen-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/geolocate-control.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/geolocate-control.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js\");\n/* harmony import */ var _use_control__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/use-control.js\");\n\n\n\nfunction GeolocateControl(props, ref) {\n    const thisRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({ props });\n    const ctrl = (0,_use_control__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(({ mapLib }) => {\n        const gc = new mapLib.GeolocateControl(props);\n        // Hack: fix GeolocateControl reuse\n        // When using React strict mode, the component is mounted twice.\n        // GeolocateControl's UI creation is asynchronous. Removing and adding it back causes the UI to be initialized twice.\n        // @ts-expect-error private method\n        const setupUI = gc._setupUI;\n        // @ts-expect-error private method\n        gc._setupUI = args => {\n            if (!gc._container.hasChildNodes()) {\n                setupUI(args);\n            }\n        };\n        gc.on('geolocate', e => {\n            var _a, _b;\n            (_b = (_a = thisRef.current.props).onGeolocate) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n        });\n        gc.on('error', e => {\n            var _a, _b;\n            (_b = (_a = thisRef.current.props).onError) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n        });\n        gc.on('outofmaxbounds', e => {\n            var _a, _b;\n            (_b = (_a = thisRef.current.props).onOutOfMaxBounds) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n        });\n        gc.on('trackuserlocationstart', e => {\n            var _a, _b;\n            (_b = (_a = thisRef.current.props).onTrackUserLocationStart) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n        });\n        gc.on('trackuserlocationend', e => {\n            var _a, _b;\n            (_b = (_a = thisRef.current.props).onTrackUserLocationEnd) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n        });\n        return gc;\n    }, { position: props.position });\n    thisRef.current.props = props;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => ctrl, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        (0,_utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._container, props.style);\n    }, [props.style]);\n    return null;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_0__.memo)((0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(GeolocateControl)));\n//# sourceMappingURL=geolocate-control.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL2NvbXBvbmVudHMvZ2VvbG9jYXRlLWNvbnRyb2wuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUY7QUFDcEI7QUFDdEI7QUFDdkM7QUFDQSxvQkFBb0IsNkNBQU0sR0FBRyxPQUFPO0FBQ3BDLGlCQUFpQix3REFBVSxJQUFJLFFBQVE7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLEtBQUssSUFBSSwwQkFBMEI7QUFDbkM7QUFDQSxJQUFJLDBEQUFtQjtBQUN2QixJQUFJLGdEQUFTO0FBQ2IsUUFBUSx5RUFBZTtBQUN2QixLQUFLO0FBQ0w7QUFDQTtBQUNBLGlFQUFlLDJDQUFJLENBQUMsaURBQVUsbUJBQW1CLEVBQUM7QUFDbEQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aXNcXERvY3VtZW50c1xcUHJvamVjdHNcXFNwaWRleFxcc3BpZGV4X3dlYnVpMy4wXFxub2RlX21vZHVsZXNcXHJlYWN0LW1hcC1nbFxcZGlzdFxcZXNtXFxjb21wb25lbnRzXFxnZW9sb2NhdGUtY29udHJvbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VJbXBlcmF0aXZlSGFuZGxlLCB1c2VSZWYsIHVzZUVmZmVjdCwgZm9yd2FyZFJlZiwgbWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGFwcGx5UmVhY3RTdHlsZSB9IGZyb20gJy4uL3V0aWxzL2FwcGx5LXJlYWN0LXN0eWxlJztcbmltcG9ydCB1c2VDb250cm9sIGZyb20gJy4vdXNlLWNvbnRyb2wnO1xuZnVuY3Rpb24gR2VvbG9jYXRlQ29udHJvbChwcm9wcywgcmVmKSB7XG4gICAgY29uc3QgdGhpc1JlZiA9IHVzZVJlZih7IHByb3BzIH0pO1xuICAgIGNvbnN0IGN0cmwgPSB1c2VDb250cm9sKCh7IG1hcExpYiB9KSA9PiB7XG4gICAgICAgIGNvbnN0IGdjID0gbmV3IG1hcExpYi5HZW9sb2NhdGVDb250cm9sKHByb3BzKTtcbiAgICAgICAgLy8gSGFjazogZml4IEdlb2xvY2F0ZUNvbnRyb2wgcmV1c2VcbiAgICAgICAgLy8gV2hlbiB1c2luZyBSZWFjdCBzdHJpY3QgbW9kZSwgdGhlIGNvbXBvbmVudCBpcyBtb3VudGVkIHR3aWNlLlxuICAgICAgICAvLyBHZW9sb2NhdGVDb250cm9sJ3MgVUkgY3JlYXRpb24gaXMgYXN5bmNocm9ub3VzLiBSZW1vdmluZyBhbmQgYWRkaW5nIGl0IGJhY2sgY2F1c2VzIHRoZSBVSSB0byBiZSBpbml0aWFsaXplZCB0d2ljZS5cbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciBwcml2YXRlIG1ldGhvZFxuICAgICAgICBjb25zdCBzZXR1cFVJID0gZ2MuX3NldHVwVUk7XG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgcHJpdmF0ZSBtZXRob2RcbiAgICAgICAgZ2MuX3NldHVwVUkgPSBhcmdzID0+IHtcbiAgICAgICAgICAgIGlmICghZ2MuX2NvbnRhaW5lci5oYXNDaGlsZE5vZGVzKCkpIHtcbiAgICAgICAgICAgICAgICBzZXR1cFVJKGFyZ3MpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICBnYy5vbignZ2VvbG9jYXRlJywgZSA9PiB7XG4gICAgICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICAgICAgKF9iID0gKF9hID0gdGhpc1JlZi5jdXJyZW50LnByb3BzKS5vbkdlb2xvY2F0ZSkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmNhbGwoX2EsIGUpO1xuICAgICAgICB9KTtcbiAgICAgICAgZ2Mub24oJ2Vycm9yJywgZSA9PiB7XG4gICAgICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICAgICAgKF9iID0gKF9hID0gdGhpc1JlZi5jdXJyZW50LnByb3BzKS5vbkVycm9yKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IuY2FsbChfYSwgZSk7XG4gICAgICAgIH0pO1xuICAgICAgICBnYy5vbignb3V0b2ZtYXhib3VuZHMnLCBlID0+IHtcbiAgICAgICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgICAgICAoX2IgPSAoX2EgPSB0aGlzUmVmLmN1cnJlbnQucHJvcHMpLm9uT3V0T2ZNYXhCb3VuZHMpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5jYWxsKF9hLCBlKTtcbiAgICAgICAgfSk7XG4gICAgICAgIGdjLm9uKCd0cmFja3VzZXJsb2NhdGlvbnN0YXJ0JywgZSA9PiB7XG4gICAgICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICAgICAgKF9iID0gKF9hID0gdGhpc1JlZi5jdXJyZW50LnByb3BzKS5vblRyYWNrVXNlckxvY2F0aW9uU3RhcnQpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5jYWxsKF9hLCBlKTtcbiAgICAgICAgfSk7XG4gICAgICAgIGdjLm9uKCd0cmFja3VzZXJsb2NhdGlvbmVuZCcsIGUgPT4ge1xuICAgICAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgICAgIChfYiA9IChfYSA9IHRoaXNSZWYuY3VycmVudC5wcm9wcykub25UcmFja1VzZXJMb2NhdGlvbkVuZCkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmNhbGwoX2EsIGUpO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIGdjO1xuICAgIH0sIHsgcG9zaXRpb246IHByb3BzLnBvc2l0aW9uIH0pO1xuICAgIHRoaXNSZWYuY3VycmVudC5wcm9wcyA9IHByb3BzO1xuICAgIHVzZUltcGVyYXRpdmVIYW5kbGUocmVmLCAoKSA9PiBjdHJsLCBbXSk7XG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgYXBwbHlSZWFjdFN0eWxlKGN0cmwuX2NvbnRhaW5lciwgcHJvcHMuc3R5bGUpO1xuICAgIH0sIFtwcm9wcy5zdHlsZV0pO1xuICAgIHJldHVybiBudWxsO1xufVxuZXhwb3J0IGRlZmF1bHQgbWVtbyhmb3J3YXJkUmVmKEdlb2xvY2F0ZUNvbnRyb2wpKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdlb2xvY2F0ZS1jb250cm9sLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/geolocate-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/layer.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/layer.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _map__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/map.js\");\n/* harmony import */ var _utils_assert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/assert */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/assert.js\");\n/* harmony import */ var _utils_deep_equal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/deep-equal */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/deep-equal.js\");\n\n\n\n\n/* eslint-disable complexity, max-statements */\nfunction updateLayer(map, id, props, prevProps) {\n    (0,_utils_assert__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props.id === prevProps.id, 'layer id changed');\n    (0,_utils_assert__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props.type === prevProps.type, 'layer type changed');\n    if (props.type === 'custom' || prevProps.type === 'custom') {\n        return;\n    }\n    const { layout = {}, paint = {}, filter, minzoom, maxzoom, beforeId } = props;\n    if (beforeId !== prevProps.beforeId) {\n        map.moveLayer(id, beforeId);\n    }\n    if (layout !== prevProps.layout) {\n        const prevLayout = prevProps.layout || {};\n        for (const key in layout) {\n            if (!(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_3__.deepEqual)(layout[key], prevLayout[key])) {\n                map.setLayoutProperty(id, key, layout[key]);\n            }\n        }\n        for (const key in prevLayout) {\n            if (!layout.hasOwnProperty(key)) {\n                map.setLayoutProperty(id, key, undefined);\n            }\n        }\n    }\n    if (paint !== prevProps.paint) {\n        const prevPaint = prevProps.paint || {};\n        for (const key in paint) {\n            if (!(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_3__.deepEqual)(paint[key], prevPaint[key])) {\n                map.setPaintProperty(id, key, paint[key]);\n            }\n        }\n        for (const key in prevPaint) {\n            if (!paint.hasOwnProperty(key)) {\n                map.setPaintProperty(id, key, undefined);\n            }\n        }\n    }\n    if (!(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_3__.deepEqual)(filter, prevProps.filter)) {\n        map.setFilter(id, filter);\n    }\n    if (minzoom !== prevProps.minzoom || maxzoom !== prevProps.maxzoom) {\n        map.setLayerZoomRange(id, minzoom, maxzoom);\n    }\n}\nfunction createLayer(map, id, props) {\n    // @ts-ignore\n    if (map.style && map.style._loaded && (!('source' in props) || map.getSource(props.source))) {\n        const options = { ...props, id };\n        delete options.beforeId;\n        // @ts-ignore\n        map.addLayer(options, props.beforeId);\n    }\n}\n/* eslint-enable complexity, max-statements */\nlet layerCounter = 0;\nfunction Layer(props) {\n    const map = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map__WEBPACK_IMPORTED_MODULE_1__.MapContext).map.getMap();\n    const propsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n    const [, setStyleLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => props.id || `jsx-layer-${layerCounter++}`, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (map) {\n            const forceUpdate = () => setStyleLoaded(version => version + 1);\n            map.on('styledata', forceUpdate);\n            forceUpdate();\n            return () => {\n                map.off('styledata', forceUpdate);\n                // @ts-ignore\n                if (map.style && map.style._loaded && map.getLayer(id)) {\n                    map.removeLayer(id);\n                }\n            };\n        }\n        return undefined;\n    }, [map]);\n    // @ts-ignore\n    const layer = map && map.style && map.getLayer(id);\n    if (layer) {\n        try {\n            updateLayer(map, id, props, propsRef.current);\n        }\n        catch (error) {\n            console.warn(error); // eslint-disable-line\n        }\n    }\n    else {\n        createLayer(map, id, props);\n    }\n    // Store last rendered props\n    propsRef.current = props;\n    return null;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layer);\n//# sourceMappingURL=layer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/layer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/map.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/map.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapContext: () => (/* binding */ MapContext),\n/* harmony export */   \"default\": () => (/* binding */ Map)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _use_map__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-map */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/use-map.js\");\n/* harmony import */ var _mapbox_mapbox__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../mapbox/mapbox */ \"(ssr)/./node_modules/react-map-gl/dist/esm/mapbox/mapbox.js\");\n/* harmony import */ var _mapbox_create_ref__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../mapbox/create-ref */ \"(ssr)/./node_modules/react-map-gl/dist/esm/mapbox/create-ref.js\");\n/* harmony import */ var _utils_use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/use-isomorphic-layout-effect */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/use-isomorphic-layout-effect.js\");\n/* harmony import */ var _utils_set_globals__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/set-globals */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/set-globals.js\");\n\n\n\n\n\n\n\nconst MapContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nfunction Map(props, ref, defaultLib) {\n    const mountedMapsContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_use_map__WEBPACK_IMPORTED_MODULE_1__.MountedMapsContext);\n    const [mapInstance, setMapInstance] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const { current: contextValue } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({ mapLib: null, map: null });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const mapLib = props.mapLib;\n        let isMounted = true;\n        let mapbox;\n        Promise.resolve(mapLib || defaultLib)\n            .then((module) => {\n            if (!isMounted) {\n                return;\n            }\n            if (!module) {\n                throw new Error('Invalid mapLib');\n            }\n            const mapboxgl = 'Map' in module ? module : module.default;\n            if (!mapboxgl.Map) {\n                throw new Error('Invalid mapLib');\n            }\n            // workerUrl & workerClass may change the result of supported()\n            // https://github.com/visgl/react-map-gl/discussions/2027\n            (0,_utils_set_globals__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(mapboxgl, props);\n            if (!mapboxgl.supported || mapboxgl.supported(props)) {\n                if (props.reuseMaps) {\n                    mapbox = _mapbox_mapbox__WEBPACK_IMPORTED_MODULE_2__[\"default\"].reuse(props, containerRef.current);\n                }\n                if (!mapbox) {\n                    mapbox = new _mapbox_mapbox__WEBPACK_IMPORTED_MODULE_2__[\"default\"](mapboxgl.Map, props, containerRef.current);\n                }\n                contextValue.map = (0,_mapbox_create_ref__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(mapbox);\n                contextValue.mapLib = mapboxgl;\n                setMapInstance(mapbox);\n                mountedMapsContext === null || mountedMapsContext === void 0 ? void 0 : mountedMapsContext.onMapMount(contextValue.map, props.id);\n            }\n            else {\n                throw new Error('Map is not supported by this browser');\n            }\n        })\n            .catch(error => {\n            const { onError } = props;\n            if (onError) {\n                onError({\n                    type: 'error',\n                    target: null,\n                    originalEvent: null,\n                    error\n                });\n            }\n            else {\n                console.error(error); // eslint-disable-line\n            }\n        });\n        return () => {\n            isMounted = false;\n            if (mapbox) {\n                mountedMapsContext === null || mountedMapsContext === void 0 ? void 0 : mountedMapsContext.onMapUnmount(props.id);\n                if (props.reuseMaps) {\n                    mapbox.recycle();\n                }\n                else {\n                    mapbox.destroy();\n                }\n            }\n        };\n    }, []);\n    (0,_utils_use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(() => {\n        if (mapInstance) {\n            mapInstance.setProps(props);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => contextValue.map, [mapInstance]);\n    const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n        position: 'relative',\n        width: '100%',\n        height: '100%',\n        ...props.style\n    }), [props.style]);\n    const CHILD_CONTAINER_STYLE = {\n        height: '100%'\n    };\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { id: props.id, ref: containerRef, style: style }, mapInstance && (react__WEBPACK_IMPORTED_MODULE_0__.createElement(MapContext.Provider, { value: contextValue },\n        react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { \"mapboxgl-children\": \"\", style: CHILD_CONTAINER_STYLE }, props.children)))));\n}\n//# sourceMappingURL=map.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/marker.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/marker.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_apply_react_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/apply-react-style */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js\");\n/* harmony import */ var _map__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./map */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/map.js\");\n/* harmony import */ var _utils_deep_equal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/deep-equal */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/deep-equal.js\");\n/* global document */\n\n\n\n\n\n\n/* eslint-disable complexity,max-statements */\nfunction Marker(props, ref) {\n    const { map, mapLib } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map__WEBPACK_IMPORTED_MODULE_3__.MapContext);\n    const thisRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({ props });\n    thisRef.current.props = props;\n    const marker = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        let hasChildren = false;\n        react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(props.children, el => {\n            if (el) {\n                hasChildren = true;\n            }\n        });\n        const options = {\n            ...props,\n            element: hasChildren ? document.createElement('div') : null\n        };\n        const mk = new mapLib.Marker(options);\n        mk.setLngLat([props.longitude, props.latitude]);\n        mk.getElement().addEventListener('click', (e) => {\n            var _a, _b;\n            (_b = (_a = thisRef.current.props).onClick) === null || _b === void 0 ? void 0 : _b.call(_a, {\n                type: 'click',\n                target: mk,\n                originalEvent: e\n            });\n        });\n        mk.on('dragstart', e => {\n            var _a, _b;\n            const evt = e;\n            evt.lngLat = marker.getLngLat();\n            (_b = (_a = thisRef.current.props).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, evt);\n        });\n        mk.on('drag', e => {\n            var _a, _b;\n            const evt = e;\n            evt.lngLat = marker.getLngLat();\n            (_b = (_a = thisRef.current.props).onDrag) === null || _b === void 0 ? void 0 : _b.call(_a, evt);\n        });\n        mk.on('dragend', e => {\n            var _a, _b;\n            const evt = e;\n            evt.lngLat = marker.getLngLat();\n            (_b = (_a = thisRef.current.props).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, evt);\n        });\n        return mk;\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        marker.addTo(map.getMap());\n        return () => {\n            marker.remove();\n        };\n    }, []);\n    const { longitude, latitude, offset, style, draggable = false, popup = null, rotation = 0, rotationAlignment = 'auto', pitchAlignment = 'auto' } = props;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        (0,_utils_apply_react_style__WEBPACK_IMPORTED_MODULE_2__.applyReactStyle)(marker.getElement(), style);\n    }, [style]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => marker, []);\n    if (marker.getLngLat().lng !== longitude || marker.getLngLat().lat !== latitude) {\n        marker.setLngLat([longitude, latitude]);\n    }\n    if (offset && !(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_4__.arePointsEqual)(marker.getOffset(), offset)) {\n        marker.setOffset(offset);\n    }\n    if (marker.isDraggable() !== draggable) {\n        marker.setDraggable(draggable);\n    }\n    if (marker.getRotation() !== rotation) {\n        marker.setRotation(rotation);\n    }\n    if (marker.getRotationAlignment() !== rotationAlignment) {\n        marker.setRotationAlignment(rotationAlignment);\n    }\n    if (marker.getPitchAlignment() !== pitchAlignment) {\n        marker.setPitchAlignment(pitchAlignment);\n    }\n    if (marker.getPopup() !== popup) {\n        marker.setPopup(popup);\n    }\n    return (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(props.children, marker.getElement());\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_0__.memo)((0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(Marker)));\n//# sourceMappingURL=marker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/marker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/navigation-control.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/navigation-control.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js\");\n/* harmony import */ var _use_control__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/use-control.js\");\n\n\n\nfunction NavigationControl(props) {\n    const ctrl = (0,_use_control__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(({ mapLib }) => new mapLib.NavigationControl(props), {\n        position: props.position\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        (0,_utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._container, props.style);\n    }, [props.style]);\n    return null;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(NavigationControl));\n//# sourceMappingURL=navigation-control.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL2NvbXBvbmVudHMvbmF2aWdhdGlvbi1jb250cm9sLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXdDO0FBQ3FCO0FBQ3RCO0FBQ3ZDO0FBQ0EsaUJBQWlCLHdEQUFVLElBQUksUUFBUTtBQUN2QztBQUNBLEtBQUs7QUFDTCxJQUFJLGdEQUFTO0FBQ2IsUUFBUSx5RUFBZTtBQUN2QixLQUFLO0FBQ0w7QUFDQTtBQUNBLGlFQUFlLDJDQUFJLG1CQUFtQixFQUFDO0FBQ3ZDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmlzXFxEb2N1bWVudHNcXFByb2plY3RzXFxTcGlkZXhcXHNwaWRleF93ZWJ1aTMuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1tYXAtZ2xcXGRpc3RcXGVzbVxcY29tcG9uZW50c1xcbmF2aWdhdGlvbi1jb250cm9sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgbWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGFwcGx5UmVhY3RTdHlsZSB9IGZyb20gJy4uL3V0aWxzL2FwcGx5LXJlYWN0LXN0eWxlJztcbmltcG9ydCB1c2VDb250cm9sIGZyb20gJy4vdXNlLWNvbnRyb2wnO1xuZnVuY3Rpb24gTmF2aWdhdGlvbkNvbnRyb2wocHJvcHMpIHtcbiAgICBjb25zdCBjdHJsID0gdXNlQ29udHJvbCgoeyBtYXBMaWIgfSkgPT4gbmV3IG1hcExpYi5OYXZpZ2F0aW9uQ29udHJvbChwcm9wcyksIHtcbiAgICAgICAgcG9zaXRpb246IHByb3BzLnBvc2l0aW9uXG4gICAgfSk7XG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgYXBwbHlSZWFjdFN0eWxlKGN0cmwuX2NvbnRhaW5lciwgcHJvcHMuc3R5bGUpO1xuICAgIH0sIFtwcm9wcy5zdHlsZV0pO1xuICAgIHJldHVybiBudWxsO1xufVxuZXhwb3J0IGRlZmF1bHQgbWVtbyhOYXZpZ2F0aW9uQ29udHJvbCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLWNvbnRyb2wuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/navigation-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/popup.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/popup.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_apply_react_style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/apply-react-style */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js\");\n/* harmony import */ var _map__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./map */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/map.js\");\n/* harmony import */ var _utils_deep_equal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/deep-equal */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/deep-equal.js\");\n\n\n\n\n\n// Adapted from https://github.com/mapbox/mapbox-gl-js/blob/v1.13.0/src/ui/popup.js\nfunction getClassList(className) {\n    return new Set(className ? className.trim().split(/\\s+/) : []);\n}\n/* eslint-disable complexity,max-statements */\nfunction Popup(props, ref) {\n    const { map, mapLib } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_map__WEBPACK_IMPORTED_MODULE_3__.MapContext);\n    const container = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n        return document.createElement('div');\n    }, []);\n    const thisRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({ props });\n    thisRef.current.props = props;\n    const popup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n        const options = { ...props };\n        const pp = new mapLib.Popup(options);\n        pp.setLngLat([props.longitude, props.latitude]);\n        pp.once('open', e => {\n            var _a, _b;\n            (_b = (_a = thisRef.current.props).onOpen) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n        });\n        return pp;\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n        const onClose = e => {\n            var _a, _b;\n            (_b = (_a = thisRef.current.props).onClose) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n        };\n        popup.on('close', onClose);\n        popup.setDOMContent(container).addTo(map.getMap());\n        return () => {\n            // https://github.com/visgl/react-map-gl/issues/1825\n            // onClose should not be fired if the popup is removed by unmounting\n            // When using React strict mode, the component is mounted twice.\n            // Firing the onClose callback here would be a false signal to remove the component.\n            popup.off('close', onClose);\n            if (popup.isOpen()) {\n                popup.remove();\n            }\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n        (0,_utils_apply_react_style__WEBPACK_IMPORTED_MODULE_2__.applyReactStyle)(popup.getElement(), props.style);\n    }, [props.style]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, () => popup, []);\n    if (popup.isOpen()) {\n        if (popup.getLngLat().lng !== props.longitude || popup.getLngLat().lat !== props.latitude) {\n            popup.setLngLat([props.longitude, props.latitude]);\n        }\n        if (props.offset && !(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_4__.deepEqual)(popup.options.offset, props.offset)) {\n            popup.setOffset(props.offset);\n        }\n        if (popup.options.anchor !== props.anchor || popup.options.maxWidth !== props.maxWidth) {\n            popup.options.anchor = props.anchor;\n            popup.setMaxWidth(props.maxWidth);\n        }\n        if (popup.options.className !== props.className) {\n            const prevClassList = getClassList(popup.options.className);\n            const nextClassList = getClassList(props.className);\n            for (const c of prevClassList) {\n                if (!nextClassList.has(c)) {\n                    popup.removeClassName(c);\n                }\n            }\n            for (const c of nextClassList) {\n                if (!prevClassList.has(c)) {\n                    popup.addClassName(c);\n                }\n            }\n            popup.options.className = props.className;\n        }\n    }\n    return (0,react_dom__WEBPACK_IMPORTED_MODULE_0__.createPortal)(props.children, container);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(Popup)));\n//# sourceMappingURL=popup.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/popup.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/scale-control.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/scale-control.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js\");\n/* harmony import */ var _use_control__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/use-control.js\");\n\n\n\nfunction ScaleControl(props) {\n    const ctrl = (0,_use_control__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(({ mapLib }) => new mapLib.ScaleControl(props), {\n        position: props.position\n    });\n    const propsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n    const prevProps = propsRef.current;\n    propsRef.current = props;\n    const { style } = props;\n    if (props.maxWidth !== undefined && props.maxWidth !== prevProps.maxWidth) {\n        ctrl.options.maxWidth = props.maxWidth;\n    }\n    if (props.unit !== undefined && props.unit !== prevProps.unit) {\n        ctrl.setUnit(props.unit);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        (0,_utils_apply_react_style__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._container, style);\n    }, [style]);\n    return null;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(ScaleControl));\n//# sourceMappingURL=scale-control.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL2NvbXBvbmVudHMvc2NhbGUtY29udHJvbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFnRDtBQUNhO0FBQ3RCO0FBQ3ZDO0FBQ0EsaUJBQWlCLHdEQUFVLElBQUksUUFBUTtBQUN2QztBQUNBLEtBQUs7QUFDTCxxQkFBcUIsNkNBQU07QUFDM0I7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdEQUFTO0FBQ2IsUUFBUSx5RUFBZTtBQUN2QixLQUFLO0FBQ0w7QUFDQTtBQUNBLGlFQUFlLDJDQUFJLGNBQWMsRUFBQztBQUNsQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtbWFwLWdsXFxkaXN0XFxlc21cXGNvbXBvbmVudHNcXHNjYWxlLWNvbnRyb2wuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYsIG1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBhcHBseVJlYWN0U3R5bGUgfSBmcm9tICcuLi91dGlscy9hcHBseS1yZWFjdC1zdHlsZSc7XG5pbXBvcnQgdXNlQ29udHJvbCBmcm9tICcuL3VzZS1jb250cm9sJztcbmZ1bmN0aW9uIFNjYWxlQ29udHJvbChwcm9wcykge1xuICAgIGNvbnN0IGN0cmwgPSB1c2VDb250cm9sKCh7IG1hcExpYiB9KSA9PiBuZXcgbWFwTGliLlNjYWxlQ29udHJvbChwcm9wcyksIHtcbiAgICAgICAgcG9zaXRpb246IHByb3BzLnBvc2l0aW9uXG4gICAgfSk7XG4gICAgY29uc3QgcHJvcHNSZWYgPSB1c2VSZWYocHJvcHMpO1xuICAgIGNvbnN0IHByZXZQcm9wcyA9IHByb3BzUmVmLmN1cnJlbnQ7XG4gICAgcHJvcHNSZWYuY3VycmVudCA9IHByb3BzO1xuICAgIGNvbnN0IHsgc3R5bGUgfSA9IHByb3BzO1xuICAgIGlmIChwcm9wcy5tYXhXaWR0aCAhPT0gdW5kZWZpbmVkICYmIHByb3BzLm1heFdpZHRoICE9PSBwcmV2UHJvcHMubWF4V2lkdGgpIHtcbiAgICAgICAgY3RybC5vcHRpb25zLm1heFdpZHRoID0gcHJvcHMubWF4V2lkdGg7XG4gICAgfVxuICAgIGlmIChwcm9wcy51bml0ICE9PSB1bmRlZmluZWQgJiYgcHJvcHMudW5pdCAhPT0gcHJldlByb3BzLnVuaXQpIHtcbiAgICAgICAgY3RybC5zZXRVbml0KHByb3BzLnVuaXQpO1xuICAgIH1cbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBhcHBseVJlYWN0U3R5bGUoY3RybC5fY29udGFpbmVyLCBzdHlsZSk7XG4gICAgfSwgW3N0eWxlXSk7XG4gICAgcmV0dXJuIG51bGw7XG59XG5leHBvcnQgZGVmYXVsdCBtZW1vKFNjYWxlQ29udHJvbCk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zY2FsZS1jb250cm9sLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/scale-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/source.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/source.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _map__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/map.js\");\n/* harmony import */ var _utils_assert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/assert */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/assert.js\");\n/* harmony import */ var _utils_deep_equal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/deep-equal */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/deep-equal.js\");\n\n\n\n\n\n\nlet sourceCounter = 0;\nfunction createSource(map, id, props) {\n    // @ts-ignore\n    if (map.style && map.style._loaded) {\n        const options = { ...props };\n        delete options.id;\n        delete options.children;\n        // @ts-ignore\n        map.addSource(id, options);\n        return map.getSource(id);\n    }\n    return null;\n}\n/* eslint-disable complexity */\nfunction updateSource(source, props, prevProps) {\n    (0,_utils_assert__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props.id === prevProps.id, 'source id changed');\n    (0,_utils_assert__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props.type === prevProps.type, 'source type changed');\n    let changedKey = '';\n    let changedKeyCount = 0;\n    for (const key in props) {\n        if (key !== 'children' && key !== 'id' && !(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_3__.deepEqual)(prevProps[key], props[key])) {\n            changedKey = key;\n            changedKeyCount++;\n        }\n    }\n    if (!changedKeyCount) {\n        return;\n    }\n    const type = props.type;\n    if (type === 'geojson') {\n        source.setData(props.data);\n    }\n    else if (type === 'image') {\n        source.updateImage({\n            url: props.url,\n            coordinates: props.coordinates\n        });\n    }\n    else if ('setCoordinates' in source && changedKeyCount === 1 && changedKey === 'coordinates') {\n        source.setCoordinates(props.coordinates);\n    }\n    else if ('setUrl' in source && changedKey === 'url') {\n        source.setUrl(props.url);\n    }\n    else if ('setTiles' in source && changedKey === 'tiles') {\n        source.setTiles(props.tiles);\n    }\n    else {\n        // eslint-disable-next-line\n        console.warn(`Unable to update <Source> prop: ${changedKey}`);\n    }\n}\n/* eslint-enable complexity */\nfunction Source(props) {\n    const map = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map__WEBPACK_IMPORTED_MODULE_1__.MapContext).map.getMap();\n    const propsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n    const [, setStyleLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => props.id || `jsx-source-${sourceCounter++}`, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (map) {\n            /* global setTimeout */\n            const forceUpdate = () => setTimeout(() => setStyleLoaded(version => version + 1), 0);\n            map.on('styledata', forceUpdate);\n            forceUpdate();\n            return () => {\n                var _a;\n                map.off('styledata', forceUpdate);\n                // @ts-ignore\n                if (map.style && map.style._loaded && map.getSource(id)) {\n                    // Parent effects are destroyed before child ones, see\n                    // https://github.com/facebook/react/issues/16728\n                    // Source can only be removed after all child layers are removed\n                    const allLayers = (_a = map.getStyle()) === null || _a === void 0 ? void 0 : _a.layers;\n                    if (allLayers) {\n                        for (const layer of allLayers) {\n                            // @ts-ignore (2339) source does not exist on all layer types\n                            if (layer.source === id) {\n                                map.removeLayer(layer.id);\n                            }\n                        }\n                    }\n                    map.removeSource(id);\n                }\n            };\n        }\n        return undefined;\n    }, [map]);\n    // @ts-ignore\n    let source = map && map.style && map.getSource(id);\n    if (source) {\n        updateSource(source, props, propsRef.current);\n    }\n    else {\n        source = createSource(map, id, props);\n    }\n    propsRef.current = props;\n    return ((source &&\n        react__WEBPACK_IMPORTED_MODULE_0__.Children.map(props.children, child => child &&\n            (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n                source: id\n            }))) ||\n        null);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Source);\n//# sourceMappingURL=source.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/source.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/use-control.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/use-control.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _map__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/map.js\");\n\n\nfunction useControl(onCreate, arg1, arg2, arg3) {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map__WEBPACK_IMPORTED_MODULE_1__.MapContext);\n    const ctrl = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => onCreate(context), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        const opts = (arg3 || arg2 || arg1);\n        const onAdd = typeof arg1 === 'function' && typeof arg2 === 'function' ? arg1 : null;\n        const onRemove = typeof arg2 === 'function' ? arg2 : typeof arg1 === 'function' ? arg1 : null;\n        const { map } = context;\n        if (!map.hasControl(ctrl)) {\n            map.addControl(ctrl, opts === null || opts === void 0 ? void 0 : opts.position);\n            if (onAdd) {\n                onAdd(context);\n            }\n        }\n        return () => {\n            if (onRemove) {\n                onRemove(context);\n            }\n            // Map might have been removed (parent effects are destroyed before child ones)\n            if (map.hasControl(ctrl)) {\n                map.removeControl(ctrl);\n            }\n        };\n    }, []);\n    return ctrl;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useControl);\n//# sourceMappingURL=use-control.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/use-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/components/use-map.js":
/*!******************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/components/use-map.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapProvider: () => (/* binding */ MapProvider),\n/* harmony export */   MountedMapsContext: () => (/* binding */ MountedMapsContext),\n/* harmony export */   useMap: () => (/* binding */ useMap)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _map__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/map.js\");\n\n\n\nconst MountedMapsContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst MapProvider = props => {\n    const [maps, setMaps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const onMapMount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((map, id = 'default') => {\n        setMaps(currMaps => {\n            if (id === 'current') {\n                throw new Error(\"'current' cannot be used as map id\");\n            }\n            if (currMaps[id]) {\n                throw new Error(`Multiple maps with the same id: ${id}`);\n            }\n            return { ...currMaps, [id]: map };\n        });\n    }, []);\n    const onMapUnmount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((id = 'default') => {\n        setMaps(currMaps => {\n            if (currMaps[id]) {\n                const nextMaps = { ...currMaps };\n                delete nextMaps[id];\n                return nextMaps;\n            }\n            return currMaps;\n        });\n    }, []);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(MountedMapsContext.Provider, { value: {\n            maps,\n            onMapMount,\n            onMapUnmount\n        } }, props.children));\n};\nfunction useMap() {\n    var _a;\n    const maps = (_a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(MountedMapsContext)) === null || _a === void 0 ? void 0 : _a.maps;\n    const currentMap = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map__WEBPACK_IMPORTED_MODULE_1__.MapContext);\n    const mapsWithCurrent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        return { ...maps, current: currentMap === null || currentMap === void 0 ? void 0 : currentMap.map };\n    }, [maps, currentMap]);\n    return mapsWithCurrent;\n}\n//# sourceMappingURL=use-map.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/components/use-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/exports-mapbox.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/exports-mapbox.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AttributionControl: () => (/* binding */ AttributionControl),\n/* harmony export */   FullscreenControl: () => (/* binding */ FullscreenControl),\n/* harmony export */   GeolocateControl: () => (/* binding */ GeolocateControl),\n/* harmony export */   Layer: () => (/* binding */ Layer),\n/* harmony export */   Map: () => (/* binding */ Map),\n/* harmony export */   MapProvider: () => (/* reexport safe */ _components_use_map__WEBPACK_IMPORTED_MODULE_11__.MapProvider),\n/* harmony export */   Marker: () => (/* binding */ Marker),\n/* harmony export */   NavigationControl: () => (/* binding */ NavigationControl),\n/* harmony export */   Popup: () => (/* binding */ Popup),\n/* harmony export */   ScaleControl: () => (/* binding */ ScaleControl),\n/* harmony export */   Source: () => (/* binding */ Source),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useControl: () => (/* reexport safe */ _components_use_control__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   useMap: () => (/* binding */ useMap)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_map__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/map */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/map.js\");\n/* harmony import */ var _components_marker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/marker */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/marker.js\");\n/* harmony import */ var _components_popup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/popup */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/popup.js\");\n/* harmony import */ var _components_attribution_control__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/attribution-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/attribution-control.js\");\n/* harmony import */ var _components_fullscreen_control__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/fullscreen-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/fullscreen-control.js\");\n/* harmony import */ var _components_geolocate_control__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/geolocate-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/geolocate-control.js\");\n/* harmony import */ var _components_navigation_control__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/navigation-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/navigation-control.js\");\n/* harmony import */ var _components_scale_control__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/scale-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/scale-control.js\");\n/* harmony import */ var _components_layer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/layer */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/layer.js\");\n/* harmony import */ var _components_source__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/source */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/source.js\");\n/* harmony import */ var _components_use_map__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./components/use-map */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/use-map.js\");\n/* harmony import */ var _components_use_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./components/use-control */ \"(ssr)/./node_modules/react-map-gl/dist/esm/components/use-control.js\");\n/* harmony import */ var _types_public__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./types/public */ \"(ssr)/./node_modules/react-map-gl/dist/esm/types/public.js\");\n/* harmony import */ var _types_style_spec_mapbox__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./types/style-spec-mapbox */ \"(ssr)/./node_modules/react-map-gl/dist/esm/types/style-spec-mapbox.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nfunction useMap() {\n    return (0,_components_use_map__WEBPACK_IMPORTED_MODULE_11__.useMap)();\n}\nconst mapLib = __webpack_require__.e(/*! import() */ \"vendor-chunks/mapbox-gl\").then(__webpack_require__.t.bind(__webpack_require__, /*! mapbox-gl */ \"(ssr)/./node_modules/mapbox-gl/dist/mapbox-gl.js\", 23));\nconst Map = (() => {\n    return react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function Map(props, ref) {\n        return (0,_components_map__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, ref, mapLib);\n    });\n})();\nconst Marker = _components_marker__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\nconst Popup = _components_popup__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nconst AttributionControl = _components_attribution_control__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst FullscreenControl = _components_fullscreen_control__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst NavigationControl = _components_navigation_control__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\nconst GeolocateControl = _components_geolocate_control__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst ScaleControl = _components_scale_control__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\nconst Layer = _components_layer__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\nconst Source = _components_source__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Map);\n// Types\n\n\n//# sourceMappingURL=exports-mapbox.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/exports-mapbox.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AttributionControl: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.AttributionControl),\n/* harmony export */   FullscreenControl: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.FullscreenControl),\n/* harmony export */   GeolocateControl: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.GeolocateControl),\n/* harmony export */   Layer: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.Layer),\n/* harmony export */   Map: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.Map),\n/* harmony export */   MapProvider: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.MapProvider),\n/* harmony export */   Marker: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.Marker),\n/* harmony export */   NavigationControl: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.NavigationControl),\n/* harmony export */   Popup: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.Popup),\n/* harmony export */   ScaleControl: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.ScaleControl),\n/* harmony export */   Source: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.Source),\n/* harmony export */   \"default\": () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   useControl: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.useControl),\n/* harmony export */   useMap: () => (/* reexport safe */ _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__.useMap)\n/* harmony export */ });\n/* harmony import */ var _exports_mapbox__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exports-mapbox */ \"(ssr)/./node_modules/react-map-gl/dist/esm/exports-mapbox.js\");\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFpQztBQUNxQjtBQUN0RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtbWFwLWdsXFxkaXN0XFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4vZXhwb3J0cy1tYXBib3gnO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBkZWZhdWx0IH0gZnJvbSAnLi9leHBvcnRzLW1hcGJveCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/mapbox/create-ref.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/mapbox/create-ref.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createRef)\n/* harmony export */ });\n/** These methods may break the react binding if called directly */\nconst skipMethods = [\n    'setMaxBounds',\n    'setMinZoom',\n    'setMaxZoom',\n    'setMinPitch',\n    'setMaxPitch',\n    'setRenderWorldCopies',\n    'setProjection',\n    'setStyle',\n    'addSource',\n    'removeSource',\n    'addLayer',\n    'removeLayer',\n    'setLayerZoomRange',\n    'setFilter',\n    'setPaintProperty',\n    'setLayoutProperty',\n    'setLight',\n    'setTerrain',\n    'setFog',\n    'remove'\n];\nfunction createRef(mapInstance) {\n    if (!mapInstance) {\n        return null;\n    }\n    const map = mapInstance.map;\n    const result = {\n        getMap: () => map,\n        // Overwrite getters to use our shadow transform\n        getCenter: () => mapInstance.transform.center,\n        getZoom: () => mapInstance.transform.zoom,\n        getBearing: () => mapInstance.transform.bearing,\n        getPitch: () => mapInstance.transform.pitch,\n        getPadding: () => mapInstance.transform.padding,\n        getBounds: () => mapInstance.transform.getBounds(),\n        project: (lnglat) => {\n            const tr = map.transform;\n            map.transform = mapInstance.transform;\n            const result = map.project(lnglat);\n            map.transform = tr;\n            return result;\n        },\n        unproject: (point) => {\n            const tr = map.transform;\n            map.transform = mapInstance.transform;\n            const result = map.unproject(point);\n            map.transform = tr;\n            return result;\n        },\n        // options diverge between mapbox and maplibre\n        queryTerrainElevation: (lnglat, options) => {\n            const tr = map.transform;\n            map.transform = mapInstance.transform;\n            const result = map.queryTerrainElevation(lnglat, options);\n            map.transform = tr;\n            return result;\n        },\n        queryRenderedFeatures: (geometry, options) => {\n            const tr = map.transform;\n            map.transform = mapInstance.transform;\n            const result = map.queryRenderedFeatures(geometry, options);\n            map.transform = tr;\n            return result;\n        }\n    };\n    for (const key of getMethodNames(map)) {\n        // @ts-expect-error\n        if (!(key in result) && !skipMethods.includes(key)) {\n            result[key] = map[key].bind(map);\n        }\n    }\n    return result;\n}\nfunction getMethodNames(obj) {\n    const result = new Set();\n    let proto = obj;\n    while (proto) {\n        for (const key of Object.getOwnPropertyNames(proto)) {\n            if (key[0] !== '_' &&\n                typeof obj[key] === 'function' &&\n                key !== 'fire' &&\n                key !== 'setEventedParent') {\n                result.add(key);\n            }\n        }\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Array.from(result);\n}\n//# sourceMappingURL=create-ref.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/mapbox/create-ref.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/mapbox/mapbox.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/mapbox/mapbox.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Mapbox)\n/* harmony export */ });\n/* harmony import */ var _utils_transform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/transform */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/transform.js\");\n/* harmony import */ var _utils_style_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/style-utils */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/style-utils.js\");\n/* harmony import */ var _utils_deep_equal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/deep-equal */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/deep-equal.js\");\n\n\n\nconst DEFAULT_STYLE = { version: 8, sources: {}, layers: [] };\nconst pointerEvents = {\n    mousedown: 'onMouseDown',\n    mouseup: 'onMouseUp',\n    mouseover: 'onMouseOver',\n    mousemove: 'onMouseMove',\n    click: 'onClick',\n    dblclick: 'onDblClick',\n    mouseenter: 'onMouseEnter',\n    mouseleave: 'onMouseLeave',\n    mouseout: 'onMouseOut',\n    contextmenu: 'onContextMenu',\n    touchstart: 'onTouchStart',\n    touchend: 'onTouchEnd',\n    touchmove: 'onTouchMove',\n    touchcancel: 'onTouchCancel'\n};\nconst cameraEvents = {\n    movestart: 'onMoveStart',\n    move: 'onMove',\n    moveend: 'onMoveEnd',\n    dragstart: 'onDragStart',\n    drag: 'onDrag',\n    dragend: 'onDragEnd',\n    zoomstart: 'onZoomStart',\n    zoom: 'onZoom',\n    zoomend: 'onZoomEnd',\n    rotatestart: 'onRotateStart',\n    rotate: 'onRotate',\n    rotateend: 'onRotateEnd',\n    pitchstart: 'onPitchStart',\n    pitch: 'onPitch',\n    pitchend: 'onPitchEnd'\n};\nconst otherEvents = {\n    wheel: 'onWheel',\n    boxzoomstart: 'onBoxZoomStart',\n    boxzoomend: 'onBoxZoomEnd',\n    boxzoomcancel: 'onBoxZoomCancel',\n    resize: 'onResize',\n    load: 'onLoad',\n    render: 'onRender',\n    idle: 'onIdle',\n    remove: 'onRemove',\n    data: 'onData',\n    styledata: 'onStyleData',\n    sourcedata: 'onSourceData',\n    error: 'onError'\n};\nconst settingNames = [\n    'minZoom',\n    'maxZoom',\n    'minPitch',\n    'maxPitch',\n    'maxBounds',\n    'projection',\n    'renderWorldCopies'\n];\nconst handlerNames = [\n    'scrollZoom',\n    'boxZoom',\n    'dragRotate',\n    'dragPan',\n    'keyboard',\n    'doubleClickZoom',\n    'touchZoomRotate',\n    'touchPitch'\n];\n/**\n * A wrapper for mapbox-gl's Map class\n */\nclass Mapbox {\n    constructor(MapClass, props, container) {\n        // mapboxgl.Map instance\n        this._map = null;\n        // Internal states\n        this._internalUpdate = false;\n        this._inRender = false;\n        this._hoveredFeatures = null;\n        this._deferredEvents = {\n            move: false,\n            zoom: false,\n            pitch: false,\n            rotate: false\n        };\n        this._onEvent = (e) => {\n            // @ts-ignore\n            const cb = this.props[otherEvents[e.type]];\n            if (cb) {\n                cb(e);\n            }\n            else if (e.type === 'error') {\n                console.error(e.error); // eslint-disable-line\n            }\n        };\n        this._onPointerEvent = (e) => {\n            if (e.type === 'mousemove' || e.type === 'mouseout') {\n                this._updateHover(e);\n            }\n            // @ts-ignore\n            const cb = this.props[pointerEvents[e.type]];\n            if (cb) {\n                if (this.props.interactiveLayerIds && e.type !== 'mouseover' && e.type !== 'mouseout') {\n                    e.features = this._hoveredFeatures || this._queryRenderedFeatures(e.point);\n                }\n                cb(e);\n                delete e.features;\n            }\n        };\n        this._onCameraEvent = (e) => {\n            if (!this._internalUpdate) {\n                // @ts-ignore\n                const cb = this.props[cameraEvents[e.type]];\n                if (cb) {\n                    cb(e);\n                }\n            }\n            if (e.type in this._deferredEvents) {\n                this._deferredEvents[e.type] = false;\n            }\n        };\n        this._MapClass = MapClass;\n        this.props = props;\n        this._initialize(container);\n    }\n    get map() {\n        return this._map;\n    }\n    get transform() {\n        return this._renderTransform;\n    }\n    setProps(props) {\n        const oldProps = this.props;\n        this.props = props;\n        const settingsChanged = this._updateSettings(props, oldProps);\n        if (settingsChanged) {\n            this._createShadowTransform(this._map);\n        }\n        const sizeChanged = this._updateSize(props);\n        const viewStateChanged = this._updateViewState(props, true);\n        this._updateStyle(props, oldProps);\n        this._updateStyleComponents(props, oldProps);\n        this._updateHandlers(props, oldProps);\n        // If 1) view state has changed to match props and\n        //    2) the props change is not triggered by map events,\n        // it's driven by an external state change. Redraw immediately\n        if (settingsChanged || sizeChanged || (viewStateChanged && !this._map.isMoving())) {\n            this.redraw();\n        }\n    }\n    static reuse(props, container) {\n        const that = Mapbox.savedMaps.pop();\n        if (!that) {\n            return null;\n        }\n        const map = that.map;\n        // When reusing the saved map, we need to reparent the map(canvas) and other child nodes\n        // intoto the new container from the props.\n        // Step 1: reparenting child nodes from old container to new container\n        const oldContainer = map.getContainer();\n        container.className = oldContainer.className;\n        while (oldContainer.childNodes.length > 0) {\n            container.appendChild(oldContainer.childNodes[0]);\n        }\n        // Step 2: replace the internal container with new container from the react component\n        // @ts-ignore\n        map._container = container;\n        // With maplibre-gl as mapLib, map uses ResizeObserver to observe when its container resizes.\n        // When reusing the saved map, we need to disconnect the observer and observe the new container.\n        // Step 3: telling the ResizeObserver to disconnect and observe the new container\n        // @ts-ignore\n        const resizeObserver = map._resizeObserver;\n        if (resizeObserver) {\n            resizeObserver.disconnect();\n            resizeObserver.observe(container);\n        }\n        // Step 4: apply new props\n        that.setProps({ ...props, styleDiffing: false });\n        map.resize();\n        const { initialViewState } = props;\n        if (initialViewState) {\n            if (initialViewState.bounds) {\n                map.fitBounds(initialViewState.bounds, { ...initialViewState.fitBoundsOptions, duration: 0 });\n            }\n            else {\n                that._updateViewState(initialViewState, false);\n            }\n        }\n        // Simulate load event\n        if (map.isStyleLoaded()) {\n            map.fire('load');\n        }\n        else {\n            map.once('styledata', () => map.fire('load'));\n        }\n        // Force reload\n        // @ts-ignore\n        map._update();\n        return that;\n    }\n    /* eslint-disable complexity,max-statements */\n    _initialize(container) {\n        const { props } = this;\n        const { mapStyle = DEFAULT_STYLE } = props;\n        const mapOptions = {\n            ...props,\n            ...props.initialViewState,\n            accessToken: props.mapboxAccessToken || getAccessTokenFromEnv() || null,\n            container,\n            style: (0,_utils_style_utils__WEBPACK_IMPORTED_MODULE_1__.normalizeStyle)(mapStyle)\n        };\n        const viewState = mapOptions.initialViewState || mapOptions.viewState || mapOptions;\n        Object.assign(mapOptions, {\n            center: [viewState.longitude || 0, viewState.latitude || 0],\n            zoom: viewState.zoom || 0,\n            pitch: viewState.pitch || 0,\n            bearing: viewState.bearing || 0\n        });\n        if (props.gl) {\n            // eslint-disable-next-line\n            const getContext = HTMLCanvasElement.prototype.getContext;\n            // Hijack canvas.getContext to return our own WebGLContext\n            // This will be called inside the mapboxgl.Map constructor\n            // @ts-expect-error\n            HTMLCanvasElement.prototype.getContext = () => {\n                // Unhijack immediately\n                HTMLCanvasElement.prototype.getContext = getContext;\n                return props.gl;\n            };\n        }\n        const map = new this._MapClass(mapOptions);\n        // Props that are not part of constructor options\n        if (viewState.padding) {\n            map.setPadding(viewState.padding);\n        }\n        if (props.cursor) {\n            map.getCanvas().style.cursor = props.cursor;\n        }\n        this._createShadowTransform(map);\n        // Hack\n        // Insert code into map's render cycle\n        const renderMap = map._render;\n        map._render = (arg) => {\n            this._inRender = true;\n            renderMap.call(map, arg);\n            this._inRender = false;\n        };\n        const runRenderTaskQueue = map._renderTaskQueue.run;\n        map._renderTaskQueue.run = (arg) => {\n            runRenderTaskQueue.call(map._renderTaskQueue, arg);\n            this._onBeforeRepaint();\n        };\n        map.on('render', () => this._onAfterRepaint());\n        // Insert code into map's event pipeline\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        const fireEvent = map.fire;\n        map.fire = this._fireEvent.bind(this, fireEvent);\n        // add listeners\n        map.on('resize', () => {\n            this._renderTransform.resize(map.transform.width, map.transform.height);\n        });\n        map.on('styledata', () => {\n            this._updateStyleComponents(this.props, {});\n            // Projection can be set in stylesheet\n            (0,_utils_transform__WEBPACK_IMPORTED_MODULE_0__.syncProjection)(map.transform, this._renderTransform);\n        });\n        map.on('sourcedata', () => this._updateStyleComponents(this.props, {}));\n        for (const eventName in pointerEvents) {\n            map.on(eventName, this._onPointerEvent);\n        }\n        for (const eventName in cameraEvents) {\n            map.on(eventName, this._onCameraEvent);\n        }\n        for (const eventName in otherEvents) {\n            map.on(eventName, this._onEvent);\n        }\n        this._map = map;\n    }\n    /* eslint-enable complexity,max-statements */\n    recycle() {\n        // Clean up unnecessary elements before storing for reuse.\n        const container = this.map.getContainer();\n        const children = container.querySelector('[mapboxgl-children]');\n        children === null || children === void 0 ? void 0 : children.remove();\n        Mapbox.savedMaps.push(this);\n    }\n    destroy() {\n        this._map.remove();\n    }\n    // Force redraw the map now. Typically resize() and jumpTo() is reflected in the next\n    // render cycle, which is managed by Mapbox's animation loop.\n    // This removes the synchronization issue caused by requestAnimationFrame.\n    redraw() {\n        const map = this._map;\n        // map._render will throw error if style does not exist\n        // https://github.com/mapbox/mapbox-gl-js/blob/fb9fc316da14e99ff4368f3e4faa3888fb43c513\n        //   /src/ui/map.js#L1834\n        if (!this._inRender && map.style) {\n            // cancel the scheduled update\n            if (map._frame) {\n                map._frame.cancel();\n                map._frame = null;\n            }\n            // the order is important - render() may schedule another update\n            map._render();\n        }\n    }\n    _createShadowTransform(map) {\n        const renderTransform = (0,_utils_transform__WEBPACK_IMPORTED_MODULE_0__.cloneTransform)(map.transform);\n        map.painter.transform = renderTransform;\n        this._renderTransform = renderTransform;\n    }\n    /* Trigger map resize if size is controlled\n       @param {object} nextProps\n       @returns {bool} true if size has changed\n     */\n    _updateSize(nextProps) {\n        // Check if size is controlled\n        const { viewState } = nextProps;\n        if (viewState) {\n            const map = this._map;\n            if (viewState.width !== map.transform.width || viewState.height !== map.transform.height) {\n                map.resize();\n                return true;\n            }\n        }\n        return false;\n    }\n    // Adapted from map.jumpTo\n    /* Update camera to match props\n       @param {object} nextProps\n       @param {bool} triggerEvents - should fire camera events\n       @returns {bool} true if anything is changed\n     */\n    _updateViewState(nextProps, triggerEvents) {\n        if (this._internalUpdate) {\n            return false;\n        }\n        const map = this._map;\n        const tr = this._renderTransform;\n        // Take a snapshot of the transform before mutation\n        const { zoom, pitch, bearing } = tr;\n        const isMoving = map.isMoving();\n        if (isMoving) {\n            // All movement of the camera is done relative to the sea level\n            tr.cameraElevationReference = 'sea';\n        }\n        const changed = (0,_utils_transform__WEBPACK_IMPORTED_MODULE_0__.applyViewStateToTransform)(tr, {\n            ...(0,_utils_transform__WEBPACK_IMPORTED_MODULE_0__.transformToViewState)(map.transform),\n            ...nextProps\n        });\n        if (isMoving) {\n            // Reset camera reference\n            tr.cameraElevationReference = 'ground';\n        }\n        if (changed && triggerEvents) {\n            const deferredEvents = this._deferredEvents;\n            // Delay DOM control updates to the next render cycle\n            deferredEvents.move = true;\n            deferredEvents.zoom || (deferredEvents.zoom = zoom !== tr.zoom);\n            deferredEvents.rotate || (deferredEvents.rotate = bearing !== tr.bearing);\n            deferredEvents.pitch || (deferredEvents.pitch = pitch !== tr.pitch);\n        }\n        // Avoid manipulating the real transform when interaction/animation is ongoing\n        // as it would interfere with Mapbox's handlers\n        if (!isMoving) {\n            (0,_utils_transform__WEBPACK_IMPORTED_MODULE_0__.applyViewStateToTransform)(map.transform, nextProps);\n        }\n        return changed;\n    }\n    /* Update camera constraints and projection settings to match props\n       @param {object} nextProps\n       @param {object} currProps\n       @returns {bool} true if anything is changed\n     */\n    _updateSettings(nextProps, currProps) {\n        const map = this._map;\n        let changed = false;\n        for (const propName of settingNames) {\n            if (propName in nextProps && !(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(nextProps[propName], currProps[propName])) {\n                changed = true;\n                const setter = map[`set${propName[0].toUpperCase()}${propName.slice(1)}`];\n                setter === null || setter === void 0 ? void 0 : setter.call(map, nextProps[propName]);\n            }\n        }\n        return changed;\n    }\n    /* Update map style to match props\n       @param {object} nextProps\n       @param {object} currProps\n       @returns {bool} true if style is changed\n     */\n    _updateStyle(nextProps, currProps) {\n        if (nextProps.cursor !== currProps.cursor) {\n            this._map.getCanvas().style.cursor = nextProps.cursor || '';\n        }\n        if (nextProps.mapStyle !== currProps.mapStyle) {\n            const { mapStyle = DEFAULT_STYLE, styleDiffing = true } = nextProps;\n            const options = {\n                diff: styleDiffing\n            };\n            if ('localIdeographFontFamily' in nextProps) {\n                // @ts-ignore Mapbox specific prop\n                options.localIdeographFontFamily = nextProps.localIdeographFontFamily;\n            }\n            this._map.setStyle((0,_utils_style_utils__WEBPACK_IMPORTED_MODULE_1__.normalizeStyle)(mapStyle), options);\n            return true;\n        }\n        return false;\n    }\n    /* Update fog, light and terrain to match props\n       @param {object} nextProps\n       @param {object} currProps\n       @returns {bool} true if anything is changed\n     */\n    _updateStyleComponents(nextProps, currProps) {\n        const map = this._map;\n        let changed = false;\n        if (map.isStyleLoaded()) {\n            if ('light' in nextProps && map.setLight && !(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(nextProps.light, currProps.light)) {\n                changed = true;\n                map.setLight(nextProps.light);\n            }\n            if ('fog' in nextProps && map.setFog && !(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(nextProps.fog, currProps.fog)) {\n                changed = true;\n                map.setFog(nextProps.fog);\n            }\n            if ('terrain' in nextProps &&\n                map.setTerrain &&\n                !(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(nextProps.terrain, currProps.terrain)) {\n                if (!nextProps.terrain || map.getSource(nextProps.terrain.source)) {\n                    changed = true;\n                    map.setTerrain(nextProps.terrain);\n                }\n            }\n        }\n        return changed;\n    }\n    /* Update interaction handlers to match props\n       @param {object} nextProps\n       @param {object} currProps\n       @returns {bool} true if anything is changed\n     */\n    _updateHandlers(nextProps, currProps) {\n        var _a, _b;\n        const map = this._map;\n        let changed = false;\n        for (const propName of handlerNames) {\n            const newValue = (_a = nextProps[propName]) !== null && _a !== void 0 ? _a : true;\n            const oldValue = (_b = currProps[propName]) !== null && _b !== void 0 ? _b : true;\n            if (!(0,_utils_deep_equal__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(newValue, oldValue)) {\n                changed = true;\n                if (newValue) {\n                    map[propName].enable(newValue);\n                }\n                else {\n                    map[propName].disable();\n                }\n            }\n        }\n        return changed;\n    }\n    _queryRenderedFeatures(point) {\n        const map = this._map;\n        const tr = map.transform;\n        const { interactiveLayerIds = [] } = this.props;\n        try {\n            map.transform = this._renderTransform;\n            return map.queryRenderedFeatures(point, {\n                layers: interactiveLayerIds.filter(map.getLayer.bind(map))\n            });\n        }\n        catch (_a) {\n            // May fail if style is not loaded\n            return [];\n        }\n        finally {\n            map.transform = tr;\n        }\n    }\n    _updateHover(e) {\n        var _a;\n        const { props } = this;\n        const shouldTrackHoveredFeatures = props.interactiveLayerIds && (props.onMouseMove || props.onMouseEnter || props.onMouseLeave);\n        if (shouldTrackHoveredFeatures) {\n            const eventType = e.type;\n            const wasHovering = ((_a = this._hoveredFeatures) === null || _a === void 0 ? void 0 : _a.length) > 0;\n            const features = this._queryRenderedFeatures(e.point);\n            const isHovering = features.length > 0;\n            if (!isHovering && wasHovering) {\n                e.type = 'mouseleave';\n                this._onPointerEvent(e);\n            }\n            this._hoveredFeatures = features;\n            if (isHovering && !wasHovering) {\n                e.type = 'mouseenter';\n                this._onPointerEvent(e);\n            }\n            e.type = eventType;\n        }\n        else {\n            this._hoveredFeatures = null;\n        }\n    }\n    _fireEvent(baseFire, event, properties) {\n        const map = this._map;\n        const tr = map.transform;\n        const eventType = typeof event === 'string' ? event : event.type;\n        if (eventType === 'move') {\n            this._updateViewState(this.props, false);\n        }\n        if (eventType in cameraEvents) {\n            if (typeof event === 'object') {\n                event.viewState = (0,_utils_transform__WEBPACK_IMPORTED_MODULE_0__.transformToViewState)(tr);\n            }\n            if (this._map.isMoving()) {\n                // Replace map.transform with ours during the callbacks\n                map.transform = this._renderTransform;\n                baseFire.call(map, event, properties);\n                map.transform = tr;\n                return map;\n            }\n        }\n        baseFire.call(map, event, properties);\n        return map;\n    }\n    // All camera manipulations are complete, ready to repaint\n    _onBeforeRepaint() {\n        const map = this._map;\n        // If there are camera changes driven by props, invoke camera events so that DOM controls are synced\n        this._internalUpdate = true;\n        for (const eventType in this._deferredEvents) {\n            if (this._deferredEvents[eventType]) {\n                map.fire(eventType);\n            }\n        }\n        this._internalUpdate = false;\n        const tr = this._map.transform;\n        // Make sure camera matches the current props\n        map.transform = this._renderTransform;\n        this._onAfterRepaint = () => {\n            // Mapbox transitions between non-mercator projection and mercator during render time\n            // Copy it back to the other\n            (0,_utils_transform__WEBPACK_IMPORTED_MODULE_0__.syncProjection)(this._renderTransform, tr);\n            // Restores camera state before render/load events are fired\n            map.transform = tr;\n        };\n    }\n}\nMapbox.savedMaps = [];\n/**\n * Access token can be provided via one of:\n *   mapboxAccessToken prop\n *   access_token query parameter\n *   MapboxAccessToken environment variable\n *   REACT_APP_MAPBOX_ACCESS_TOKEN environment variable\n * @returns access token\n */\nfunction getAccessTokenFromEnv() {\n    let accessToken = null;\n    /* global location, process */\n    if (typeof location !== 'undefined') {\n        const match = /access_token=([^&\\/]*)/.exec(location.search);\n        accessToken = match && match[1];\n    }\n    // Note: This depends on bundler plugins (e.g. webpack) importing environment correctly\n    try {\n        accessToken = accessToken || process.env.MapboxAccessToken;\n    }\n    catch (_a) {\n        // ignore\n    }\n    try {\n        accessToken = accessToken || process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;\n    }\n    catch (_b) {\n        // ignore\n    }\n    return accessToken;\n}\n//# sourceMappingURL=mapbox.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/mapbox/mapbox.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/types/public.js":
/*!************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/types/public.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=public.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL3R5cGVzL3B1YmxpYy5qcyIsIm1hcHBpbmdzIjoiO0FBQVU7QUFDViIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtbWFwLWdsXFxkaXN0XFxlc21cXHR5cGVzXFxwdWJsaWMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cHVibGljLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/types/public.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/types/style-spec-mapbox.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/types/style-spec-mapbox.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceMappingURL=style-spec-mapbox.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL3R5cGVzL3N0eWxlLXNwZWMtbWFwYm94LmpzIiwibWFwcGluZ3MiOiI7QUFBVTtBQUNWIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmlzXFxEb2N1bWVudHNcXFByb2plY3RzXFxTcGlkZXhcXHNwaWRleF93ZWJ1aTMuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1tYXAtZ2xcXGRpc3RcXGVzbVxcdHlwZXNcXHN0eWxlLXNwZWMtbWFwYm94LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN0eWxlLXNwZWMtbWFwYm94LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/types/style-spec-mapbox.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyReactStyle: () => (/* binding */ applyReactStyle)\n/* harmony export */ });\n// This is a simplified version of\n// https://github.com/facebook/react/blob/4131af3e4bf52f3a003537ec95a1655147c81270/src/renderers/dom/shared/CSSPropertyOperations.js#L62\nconst unitlessNumber = /box|flex|grid|column|lineHeight|fontWeight|opacity|order|tabSize|zIndex/;\nfunction applyReactStyle(element, styles) {\n    if (!element || !styles) {\n        return;\n    }\n    const style = element.style;\n    for (const key in styles) {\n        const value = styles[key];\n        if (Number.isFinite(value) && !unitlessNumber.test(key)) {\n            style[key] = `${value}px`;\n        }\n        else {\n            style[key] = value;\n        }\n    }\n}\n//# sourceMappingURL=apply-react-style.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL3V0aWxzL2FwcGx5LXJlYWN0LXN0eWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLE1BQU07QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aXNcXERvY3VtZW50c1xcUHJvamVjdHNcXFNwaWRleFxcc3BpZGV4X3dlYnVpMy4wXFxub2RlX21vZHVsZXNcXHJlYWN0LW1hcC1nbFxcZGlzdFxcZXNtXFx1dGlsc1xcYXBwbHktcmVhY3Qtc3R5bGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhpcyBpcyBhIHNpbXBsaWZpZWQgdmVyc2lvbiBvZlxuLy8gaHR0cHM6Ly9naXRodWIuY29tL2ZhY2Vib29rL3JlYWN0L2Jsb2IvNDEzMWFmM2U0YmY1MmYzYTAwMzUzN2VjOTVhMTY1NTE0N2M4MTI3MC9zcmMvcmVuZGVyZXJzL2RvbS9zaGFyZWQvQ1NTUHJvcGVydHlPcGVyYXRpb25zLmpzI0w2MlxuY29uc3QgdW5pdGxlc3NOdW1iZXIgPSAvYm94fGZsZXh8Z3JpZHxjb2x1bW58bGluZUhlaWdodHxmb250V2VpZ2h0fG9wYWNpdHl8b3JkZXJ8dGFiU2l6ZXx6SW5kZXgvO1xuZXhwb3J0IGZ1bmN0aW9uIGFwcGx5UmVhY3RTdHlsZShlbGVtZW50LCBzdHlsZXMpIHtcbiAgICBpZiAoIWVsZW1lbnQgfHwgIXN0eWxlcykge1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGNvbnN0IHN0eWxlID0gZWxlbWVudC5zdHlsZTtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBzdHlsZXMpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSBzdHlsZXNba2V5XTtcbiAgICAgICAgaWYgKE51bWJlci5pc0Zpbml0ZSh2YWx1ZSkgJiYgIXVuaXRsZXNzTnVtYmVyLnRlc3Qoa2V5KSkge1xuICAgICAgICAgICAgc3R5bGVba2V5XSA9IGAke3ZhbHVlfXB4YDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHN0eWxlW2tleV0gPSB2YWx1ZTtcbiAgICAgICAgfVxuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcGx5LXJlYWN0LXN0eWxlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/utils/apply-react-style.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/utils/assert.js":
/*!************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/utils/assert.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ assert)\n/* harmony export */ });\nfunction assert(condition, message) {\n    if (!condition) {\n        throw new Error(message);\n    }\n}\n//# sourceMappingURL=assert.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL3V0aWxzL2Fzc2VydC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmlzXFxEb2N1bWVudHNcXFByb2plY3RzXFxTcGlkZXhcXHNwaWRleF93ZWJ1aTMuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1tYXAtZ2xcXGRpc3RcXGVzbVxcdXRpbHNcXGFzc2VydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBhc3NlcnQoY29uZGl0aW9uLCBtZXNzYWdlKSB7XG4gICAgaWYgKCFjb25kaXRpb24pIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKG1lc3NhZ2UpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFzc2VydC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/utils/assert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/utils/deep-equal.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/utils/deep-equal.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arePointsEqual: () => (/* binding */ arePointsEqual),\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual)\n/* harmony export */ });\n/**\n * Compare two points\n * @param a\n * @param b\n * @returns true if the points are equal\n */\nfunction arePointsEqual(a, b) {\n    const ax = Array.isArray(a) ? a[0] : a ? a.x : 0;\n    const ay = Array.isArray(a) ? a[1] : a ? a.y : 0;\n    const bx = Array.isArray(b) ? b[0] : b ? b.x : 0;\n    const by = Array.isArray(b) ? b[1] : b ? b.y : 0;\n    return ax === bx && ay === by;\n}\n/* eslint-disable complexity */\n/**\n * Compare any two objects\n * @param a\n * @param b\n * @returns true if the objects are deep equal\n */\nfunction deepEqual(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (!a || !b) {\n        return false;\n    }\n    if (Array.isArray(a)) {\n        if (!Array.isArray(b) || a.length !== b.length) {\n            return false;\n        }\n        for (let i = 0; i < a.length; i++) {\n            if (!deepEqual(a[i], b[i])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    else if (Array.isArray(b)) {\n        return false;\n    }\n    if (typeof a === 'object' && typeof b === 'object') {\n        const aKeys = Object.keys(a);\n        const bKeys = Object.keys(b);\n        if (aKeys.length !== bKeys.length) {\n            return false;\n        }\n        for (const key of aKeys) {\n            if (!b.hasOwnProperty(key)) {\n                return false;\n            }\n            if (!deepEqual(a[key], b[key])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    return false;\n}\n//# sourceMappingURL=deep-equal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL3V0aWxzL2RlZXAtZXF1YWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixjQUFjO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmlzXFxEb2N1bWVudHNcXFByb2plY3RzXFxTcGlkZXhcXHNwaWRleF93ZWJ1aTMuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1tYXAtZ2xcXGRpc3RcXGVzbVxcdXRpbHNcXGRlZXAtZXF1YWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb21wYXJlIHR3byBwb2ludHNcbiAqIEBwYXJhbSBhXG4gKiBAcGFyYW0gYlxuICogQHJldHVybnMgdHJ1ZSBpZiB0aGUgcG9pbnRzIGFyZSBlcXVhbFxuICovXG5leHBvcnQgZnVuY3Rpb24gYXJlUG9pbnRzRXF1YWwoYSwgYikge1xuICAgIGNvbnN0IGF4ID0gQXJyYXkuaXNBcnJheShhKSA/IGFbMF0gOiBhID8gYS54IDogMDtcbiAgICBjb25zdCBheSA9IEFycmF5LmlzQXJyYXkoYSkgPyBhWzFdIDogYSA/IGEueSA6IDA7XG4gICAgY29uc3QgYnggPSBBcnJheS5pc0FycmF5KGIpID8gYlswXSA6IGIgPyBiLnggOiAwO1xuICAgIGNvbnN0IGJ5ID0gQXJyYXkuaXNBcnJheShiKSA/IGJbMV0gOiBiID8gYi55IDogMDtcbiAgICByZXR1cm4gYXggPT09IGJ4ICYmIGF5ID09PSBieTtcbn1cbi8qIGVzbGludC1kaXNhYmxlIGNvbXBsZXhpdHkgKi9cbi8qKlxuICogQ29tcGFyZSBhbnkgdHdvIG9iamVjdHNcbiAqIEBwYXJhbSBhXG4gKiBAcGFyYW0gYlxuICogQHJldHVybnMgdHJ1ZSBpZiB0aGUgb2JqZWN0cyBhcmUgZGVlcCBlcXVhbFxuICovXG5leHBvcnQgZnVuY3Rpb24gZGVlcEVxdWFsKGEsIGIpIHtcbiAgICBpZiAoYSA9PT0gYikge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgaWYgKCFhIHx8ICFiKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgaWYgKEFycmF5LmlzQXJyYXkoYSkpIHtcbiAgICAgICAgaWYgKCFBcnJheS5pc0FycmF5KGIpIHx8IGEubGVuZ3RoICE9PSBiLmxlbmd0aCkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYS5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgaWYgKCFkZWVwRXF1YWwoYVtpXSwgYltpXSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkoYikpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIGEgPT09ICdvYmplY3QnICYmIHR5cGVvZiBiID09PSAnb2JqZWN0Jykge1xuICAgICAgICBjb25zdCBhS2V5cyA9IE9iamVjdC5rZXlzKGEpO1xuICAgICAgICBjb25zdCBiS2V5cyA9IE9iamVjdC5rZXlzKGIpO1xuICAgICAgICBpZiAoYUtleXMubGVuZ3RoICE9PSBiS2V5cy5sZW5ndGgpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGNvbnN0IGtleSBvZiBhS2V5cykge1xuICAgICAgICAgICAgaWYgKCFiLmhhc093blByb3BlcnR5KGtleSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIWRlZXBFcXVhbChhW2tleV0sIGJba2V5XSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRlZXAtZXF1YWwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/utils/deep-equal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/utils/set-globals.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/utils/set-globals.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ setGlobals)\n/* harmony export */ });\nconst globalSettings = [\n    'baseApiUrl',\n    'maxParallelImageRequests',\n    'workerClass',\n    'workerCount',\n    'workerUrl'\n];\nfunction setGlobals(mapLib, props) {\n    for (const key of globalSettings) {\n        if (key in props) {\n            mapLib[key] = props[key];\n        }\n    }\n    const { RTLTextPlugin = 'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-rtl-text/v0.2.3/mapbox-gl-rtl-text.js' } = props;\n    if (RTLTextPlugin &&\n        mapLib.getRTLTextPluginStatus &&\n        mapLib.getRTLTextPluginStatus() === 'unavailable') {\n        mapLib.setRTLTextPlugin(RTLTextPlugin, (error) => {\n            if (error) {\n                // eslint-disable-next-line\n                console.error(error);\n            }\n        }, true);\n    }\n}\n//# sourceMappingURL=set-globals.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL3V0aWxzL3NldC1nbG9iYWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksZ0hBQWdIO0FBQzVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmlzXFxEb2N1bWVudHNcXFByb2plY3RzXFxTcGlkZXhcXHNwaWRleF93ZWJ1aTMuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1tYXAtZ2xcXGRpc3RcXGVzbVxcdXRpbHNcXHNldC1nbG9iYWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGdsb2JhbFNldHRpbmdzID0gW1xuICAgICdiYXNlQXBpVXJsJyxcbiAgICAnbWF4UGFyYWxsZWxJbWFnZVJlcXVlc3RzJyxcbiAgICAnd29ya2VyQ2xhc3MnLFxuICAgICd3b3JrZXJDb3VudCcsXG4gICAgJ3dvcmtlclVybCdcbl07XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBzZXRHbG9iYWxzKG1hcExpYiwgcHJvcHMpIHtcbiAgICBmb3IgKGNvbnN0IGtleSBvZiBnbG9iYWxTZXR0aW5ncykge1xuICAgICAgICBpZiAoa2V5IGluIHByb3BzKSB7XG4gICAgICAgICAgICBtYXBMaWJba2V5XSA9IHByb3BzW2tleV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgeyBSVExUZXh0UGx1Z2luID0gJ2h0dHBzOi8vYXBpLm1hcGJveC5jb20vbWFwYm94LWdsLWpzL3BsdWdpbnMvbWFwYm94LWdsLXJ0bC10ZXh0L3YwLjIuMy9tYXBib3gtZ2wtcnRsLXRleHQuanMnIH0gPSBwcm9wcztcbiAgICBpZiAoUlRMVGV4dFBsdWdpbiAmJlxuICAgICAgICBtYXBMaWIuZ2V0UlRMVGV4dFBsdWdpblN0YXR1cyAmJlxuICAgICAgICBtYXBMaWIuZ2V0UlRMVGV4dFBsdWdpblN0YXR1cygpID09PSAndW5hdmFpbGFibGUnKSB7XG4gICAgICAgIG1hcExpYi5zZXRSVExUZXh0UGx1Z2luKFJUTFRleHRQbHVnaW4sIChlcnJvcikgPT4ge1xuICAgICAgICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihlcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sIHRydWUpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNldC1nbG9iYWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/utils/set-globals.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/utils/style-utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/utils/style-utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeStyle: () => (/* binding */ normalizeStyle)\n/* harmony export */ });\nconst refProps = ['type', 'source', 'source-layer', 'minzoom', 'maxzoom', 'filter', 'layout'];\n// Prepare a map style object for diffing\n// If immutable - convert to plain object\n// Work around some issues in older styles that would fail Mapbox's diffing\nfunction normalizeStyle(style) {\n    if (!style) {\n        return null;\n    }\n    if (typeof style === 'string') {\n        return style;\n    }\n    if ('toJS' in style) {\n        style = style.toJS();\n    }\n    if (!style.layers) {\n        return style;\n    }\n    const layerIndex = {};\n    for (const layer of style.layers) {\n        layerIndex[layer.id] = layer;\n    }\n    const layers = style.layers.map(layer => {\n        let normalizedLayer = null;\n        if ('interactive' in layer) {\n            normalizedLayer = Object.assign({}, layer);\n            // Breaks style diffing :(\n            // @ts-ignore legacy field not typed\n            delete normalizedLayer.interactive;\n        }\n        // Style diffing doesn't work with refs so expand them out manually before diffing.\n        // @ts-ignore legacy field not typed\n        const layerRef = layerIndex[layer.ref];\n        if (layerRef) {\n            normalizedLayer = normalizedLayer || Object.assign({}, layer);\n            // @ts-ignore\n            delete normalizedLayer.ref;\n            // https://github.com/mapbox/mapbox-gl-js/blob/master/src/style-spec/deref.js\n            for (const propName of refProps) {\n                if (propName in layerRef) {\n                    normalizedLayer[propName] = layerRef[propName];\n                }\n            }\n        }\n        return normalizedLayer || layer;\n    });\n    // Do not mutate the style object provided by the user\n    return { ...style, layers };\n}\n//# sourceMappingURL=style-utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/utils/style-utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/utils/transform.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/utils/transform.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyViewStateToTransform: () => (/* binding */ applyViewStateToTransform),\n/* harmony export */   cloneTransform: () => (/* binding */ cloneTransform),\n/* harmony export */   syncProjection: () => (/* binding */ syncProjection),\n/* harmony export */   transformToViewState: () => (/* binding */ transformToViewState)\n/* harmony export */ });\n/* harmony import */ var _deep_equal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deep-equal */ \"(ssr)/./node_modules/react-map-gl/dist/esm/utils/deep-equal.js\");\n\n/**\n * Make a copy of a transform\n * @param tr\n */\nfunction cloneTransform(tr) {\n    const newTransform = tr.clone();\n    // Work around mapbox bug - this value is not assigned in clone(), only in resize()\n    newTransform.pixelsToGLUnits = tr.pixelsToGLUnits;\n    return newTransform;\n}\n/**\n * Copy projection from one transform to another. This only applies to mapbox-gl transforms\n * @param src the transform to copy projection settings from\n * @param dest to transform to copy projection settings to\n */\nfunction syncProjection(src, dest) {\n    if (!src.getProjection) {\n        return;\n    }\n    const srcProjection = src.getProjection();\n    const destProjection = dest.getProjection();\n    if (!(0,_deep_equal__WEBPACK_IMPORTED_MODULE_0__.deepEqual)(srcProjection, destProjection)) {\n        dest.setProjection(srcProjection);\n    }\n}\n/**\n * Capture a transform's current state\n * @param transform\n * @returns descriptor of the view state\n */\nfunction transformToViewState(tr) {\n    return {\n        longitude: tr.center.lng,\n        latitude: tr.center.lat,\n        zoom: tr.zoom,\n        pitch: tr.pitch,\n        bearing: tr.bearing,\n        padding: tr.padding\n    };\n}\n/* eslint-disable complexity */\n/**\n * Mutate a transform to match the given view state\n * @param transform\n * @param viewState\n * @returns true if the transform has changed\n */\nfunction applyViewStateToTransform(tr, props) {\n    const v = props.viewState || props;\n    let changed = false;\n    if ('zoom' in v) {\n        const zoom = tr.zoom;\n        tr.zoom = v.zoom;\n        changed = changed || zoom !== tr.zoom;\n    }\n    if ('bearing' in v) {\n        const bearing = tr.bearing;\n        tr.bearing = v.bearing;\n        changed = changed || bearing !== tr.bearing;\n    }\n    if ('pitch' in v) {\n        const pitch = tr.pitch;\n        tr.pitch = v.pitch;\n        changed = changed || pitch !== tr.pitch;\n    }\n    if (v.padding && !tr.isPaddingEqual(v.padding)) {\n        changed = true;\n        tr.padding = v.padding;\n    }\n    if ('longitude' in v && 'latitude' in v) {\n        const center = tr.center;\n        // @ts-ignore\n        tr.center = new center.constructor(v.longitude, v.latitude);\n        changed = changed || center !== tr.center;\n    }\n    return changed;\n}\n//# sourceMappingURL=transform.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/utils/transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/esm/utils/use-isomorphic-layout-effect.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/esm/utils/use-isomorphic-layout-effect.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n// From https://github.com/streamich/react-use/blob/master/src/useIsomorphicLayoutEffect.ts\n// useLayoutEffect but does not trigger warning in server-side rendering\n\nconst useIsomorphicLayoutEffect = typeof document !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useIsomorphicLayoutEffect);\n//# sourceMappingURL=use-isomorphic-layout-effect.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvZXNtL3V0aWxzL3VzZS1pc29tb3JwaGljLWxheW91dC1lZmZlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNtRDtBQUNuRCxvRUFBb0Usa0RBQWUsR0FBRyw0Q0FBUztBQUMvRixpRUFBZSx5QkFBeUIsRUFBQztBQUN6QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtbWFwLWdsXFxkaXN0XFxlc21cXHV0aWxzXFx1c2UtaXNvbW9ycGhpYy1sYXlvdXQtZWZmZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZyb20gaHR0cHM6Ly9naXRodWIuY29tL3N0cmVhbWljaC9yZWFjdC11c2UvYmxvYi9tYXN0ZXIvc3JjL3VzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QudHNcbi8vIHVzZUxheW91dEVmZmVjdCBidXQgZG9lcyBub3QgdHJpZ2dlciB3YXJuaW5nIGluIHNlcnZlci1zaWRlIHJlbmRlcmluZ1xuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5jb25zdCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gdHlwZW9mIGRvY3VtZW50ICE9PSAndW5kZWZpbmVkJyA/IHVzZUxheW91dEVmZmVjdCA6IHVzZUVmZmVjdDtcbmV4cG9ydCBkZWZhdWx0IHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3Q7XG4vLyMgc291cmNlTWFwcGluZ1VSTD11c2UtaXNvbW9ycGhpYy1sYXlvdXQtZWZmZWN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/esm/utils/use-isomorphic-layout-effect.js\n");

/***/ })

};
;