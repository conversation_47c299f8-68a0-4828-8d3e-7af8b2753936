"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { Tenant } from "@/types/tenant";
import {
  CreateTenantSchema,
  UpdateTenantSchema,
  CreateTenantFormData,
  UpdateTenantFormData,
} from "@/lib/schemas";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MapPin, Building, Tag, Globe } from "lucide-react";
import { GoogleMapModal } from "@/components/ui/google-map-modal";
import { MapIcon } from "@/components/ui/map-icon";

interface TenantFormProps {
  tenant?: Tenant;
  isLoading?: boolean;
  onSubmit: (
    data: CreateTenantFormData | UpdateTenantFormData
  ) => Promise<void>;
  onCancel: () => void;
  onClear?: () => void;
}

export function TenantForm({
  tenant,
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: TenantFormProps) {
  const isEditing = !!tenant;
  const schema = isEditing ? UpdateTenantSchema : CreateTenantSchema;
  const [isMapOpen, setIsMapOpen] = useState(false);

  const form = useForm<CreateTenantFormData | UpdateTenantFormData>({
    resolver: zodResolver(schema),
    defaultValues: isEditing
      ? {
          id: tenant.id,
          name: tenant.name,
          type: tenant.type,
          orgName: tenant.orgName,
          enable: tenant.enable,
          latitude: tenant.latitude,
          longitude: tenant.longitude,
        }
      : {
          name: "",
          type: "",
          orgName: "",
          enable: false,
          latitude: undefined,
          longitude: undefined,
        },
  });

  const handleSubmit = async (
    data: CreateTenantFormData | UpdateTenantFormData
  ) => {
    try {
      await onSubmit(data);
      if (!isEditing) {
        form.reset();
      }
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  const handleClear = () => {
    form.reset();
    onClear?.();
  };

  const handleLocationSelect = (location: {
    latitude: number;
    longitude: number;
    geoJson?: string;
  }) => {
    form.setValue("latitude", location.latitude);
    form.setValue("longitude", location.longitude);
    setIsMapOpen(false);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardContent className="space-y-4 pt-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tenant Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter tenant name"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tenant Type</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter tenant type"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    The category or type of this tenant
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="orgName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organization Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter organization name"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  
                  <FormMessage />
                </FormItem>
              )}
            />
        {/* Status and Settings */}
            <FormField
              control={form.control}
              name="enable"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Enabled Status</FormLabel>
                    <FormDescription>
                      Enable or disable this tenant
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* GPS Coordinates */}
              <div className="flex items-center gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1">
                <FormField
                  control={form.control}
                  name="latitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Latitude</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="any"
                          placeholder="(-90 to 90)"
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(
                              value ? parseFloat(value) : undefined
                            );
                          }}
                          disabled={isLoading}
                        />
                      </FormControl>
                      
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="longitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Longitude</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="any"
                          placeholder="(-180 to 180)"
                          {...field}
                          value={field.value || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            field.onChange(
                              value ? parseFloat(value) : undefined
                            );
                          }}
                          disabled={isLoading}
                        />
                      </FormControl>
                      
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Map Icon */}
              <div className="mt-8">
                <MapIcon
                  className="w-8 h-8"
                  onClick={() => setIsMapOpen(true)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-start pt-6 border-t">
          <div className="flex items-center gap-4">
            <Button type="submit" disabled={isLoading}
              className="flex-1 sm:flex-none bg-green-700 text-white hover:bg-green-800 rounded"
            >
              {isLoading
                ? "Saving..."
                : isEditing
                ? "Update Tenant"
                : "Create Tenant"}
            </Button>
            {!isEditing && (
              <Button
                type="button"
                variant="outline"
                onClick={handleClear}
                disabled={isLoading}
                className="border bg-white border-gray-700 text-black hover:bg-gray-20 rounded"
              >
                Clear
              </Button>
            )}
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="border bg-white border-red-700 text-black hover:bg-red-20 rounded"
            >
              Cancel
            </Button>
            
            
          </div>
          
            
          
        </div>
      </form>

      {/* Google Maps Modal */}
      <GoogleMapModal
        isOpen={isMapOpen}
        onClose={() => setIsMapOpen(false)}
        onLocationSelect={handleLocationSelect}
        initialLocation={{
          latitude: form.getValues("latitude"),
          longitude: form.getValues("longitude"),
        }}
      />
    </Form>
  );
}
