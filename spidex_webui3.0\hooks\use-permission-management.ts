"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  Role,
  Page,
  RolePageLink,
  RolePagePermissionDisplay,
  PermissionSearchFilters,
  RbacPaginationParams,
  RbacPageSize,
  DEFAULT_RBAC_PAGE_SIZE,
  RBAC_PAGE_SIZES,
  PermissionMatrix,
  CrudPermission,
} from "@/types/rbac";

interface UsePermissionManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: RbacPageSize;
}

export function usePermissionManagement(
  options: UsePermissionManagementOptions = {}
) {
  const { autoLoad = true, defaultPageSize = DEFAULT_RBAC_PAGE_SIZE } = options;
  const { data: session } = useSession();
  const spidexApi = useSpidexApi();

  // State
  const [roles, setRoles] = useState<Role[]>([]);
  const [pages, setPages] = useState<Page[]>([]);
  const [rolePageLinks, setRolePageLinks] = useState<RolePageLink[]>([]);
  const [permissionDisplayData, setPermissionDisplayData] = useState<
    RolePagePermissionDisplay[]
  >([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchFilters, setSearchFilters] = useState<PermissionSearchFilters>(
    {}
  );
  const [pagination, setPagination] = useState<RbacPaginationParams>({
    page: 1,
    pageSize: defaultPageSize === "all" ? 1000 : defaultPageSize,
  });

  // Available page sizes
  const availablePageSizes = useMemo(() => RBAC_PAGE_SIZES, []);

  // Filter permissions based on search criteria
  const filteredPermissions = useMemo(() => {
    let filtered = permissionDisplayData;

    // Apply search filters
    if (searchFilters.role) {
      filtered = filtered.filter((perm) => perm.role === searchFilters.role);
    }

    if (searchFilters.page) {
      filtered = filtered.filter((perm) => perm.page === searchFilters.page);
    }

    if (searchFilters.permission) {
      filtered = filtered.filter((perm) =>
        perm.permissions.includes(searchFilters.permission!)
      );
    }

    return filtered;
  }, [permissionDisplayData, searchFilters]);

  // Paginated permissions
  const paginatedPermissions = useMemo(() => {
    if (pagination.pageSize === 1000) {
      // Show all
      return filteredPermissions;
    }

    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredPermissions.slice(startIndex, endIndex);
  }, [filteredPermissions, pagination]);

  // Pagination calculations
  const totalRecords = filteredPermissions.length;
  const totalPages = Math.ceil(totalRecords / pagination.pageSize);

  // Transform data whenever roles or rolePageLinks change
  useEffect(() => {
    if (roles.length > 0 && rolePageLinks.length > 0) {
      // Transform data inline to avoid dependency issues
      const displayData: RolePagePermissionDisplay[] = [];
      rolePageLinks.forEach((link, linkIndex) => {
        link.pagePermissions?.forEach((pagePermission, pageIndex) => {
          // Find the role name from the roles array
          const role = roles.find((r) => r.id === (link.roleId || link.id));
          const roleName = role ? role.name : link.roleId || link.id;
          const roleId = link.roleId || link.id;

          displayData.push({
            key: `${roleId}-${pagePermission.pageName}-${linkIndex}-${pageIndex}`,
            role: roleId, // Keep role ID for filtering
            roleName: roleName, // Add role name for display
            page: pagePermission.pageName,
            permissions: pagePermission.permissions.join(", "),
          });
        });
      });

      setPermissionDisplayData(displayData);
    }
  }, [roles, rolePageLinks]);

  // Load all data
  const loadData = useCallback(async () => {
    if (!session?.user?.token) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Load roles, pages, and role-page links in parallel
      const [rolesData, pagesData, linksData] = await Promise.all([
        spidexApi.getAllRolesForRbac(1, 1000),
        spidexApi.getAllPages(),
        spidexApi.getAllPagesRolesLinks(),
      ]);

      setRoles(rolesData || []);
      setPages(pagesData || []);
      setRolePageLinks(linksData || []);
    } catch (err) {
      console.error("Error loading permissions:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load permissions"
      );
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.token, spidexApi]);

  // Create or update role-page permission link
  const updateRolePermissions = useCallback(
    async (
      roleId: string,
      permissionMatrix: PermissionMatrix
    ): Promise<boolean> => {
      if (!session?.user?.token) {
        throw new Error("No authentication token available");
      }

      try {
        // Convert permission matrix to page permissions array
        const selectedPagesArray: any[] = [];
        Object.keys(permissionMatrix).forEach((pageId) => {
          const permissions = Object.keys(permissionMatrix[pageId]).filter(
            (permission) =>
              permissionMatrix[pageId][permission as CrudPermission]
          );
          if (permissions.length > 0) {
            // Find the page name from pages array
            const page = pages.find((p) => p.id === pageId);
            if (page) {
              selectedPagesArray.push({
                pageName: page.pageName,
                permissions: permissions,
              });
            }
          }
        });

        const dateTimeNow = new Date().toISOString();
        const linkData = {
          id: roleId,
          pagePermissions: selectedPagesArray,
          createdTime: dateTimeNow,
          modifiedTime: dateTimeNow,
          createdBy: session.user.userId || "system",
          modifiedBy: session.user.userId || "system",
        };

        await spidexApi.linkPageRole(linkData);
        await loadData();
        return true;
      } catch (err) {
        console.error("Error updating role permissions:", err);
        throw err;
      }
    },
    [session?.user?.token, session?.user?.userId, spidexApi, pages, loadData]
  );

  // Delete role-page permission link
  const deleteRolePermission = useCallback(
    async (linkId: string): Promise<boolean> => {
      if (!session?.user?.token) {
        throw new Error("No authentication token available");
      }

      try {
        await spidexApi.deletePageRoleLink(linkId);
        await loadData();
        return true;
      } catch (err) {
        console.error("Error deleting role permission:", err);
        throw err;
      }
    },
    [session?.user?.token, spidexApi, loadData]
  );

  // Get permissions for a specific role
  const getRolePermissions = useCallback(
    (roleId: string): PermissionMatrix => {
      const roleLink = rolePageLinks.find(
        (link) => link.roleId === roleId || link.id === roleId
      );
      const matrix: PermissionMatrix = {};

      // Initialize matrix with all pages
      pages.forEach((page) => {
        matrix[page.id] = {
          add: false,
          view: false,
          update: false,
          delete: false,
        };
      });

      // Fill in existing permissions
      if (roleLink?.pagePermissions) {
        roleLink.pagePermissions.forEach((pagePermission) => {
          const page = pages.find(
            (p) => p.pageName === pagePermission.pageName
          );
          if (page) {
            pagePermission.permissions.forEach((permission) => {
              if (permission in matrix[page.id]) {
                matrix[page.id][permission as CrudPermission] = true;
              }
            });
          }
        });
      }

      return matrix;
    },
    [rolePageLinks, pages]
  );

  // Pagination functions
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  }, []);

  const changePageSize = useCallback((pageSize: RbacPageSize) => {
    setPagination({
      page: 1,
      pageSize: pageSize === "all" ? 1000 : pageSize,
    });
  }, []);

  // Search functions
  const updateSearchFilters = useCallback(
    (filters: Partial<PermissionSearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({});
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  // Auto-load data on mount
  useEffect(() => {
    if (autoLoad && session?.user?.token) {
      loadData();
    }
  }, [autoLoad, session?.user?.token, loadData]);

  return {
    // Data
    roles,
    pages,
    rolePageLinks,
    permissions: paginatedPermissions,
    filteredPermissions,
    isLoading,
    error,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    availablePageSizes,

    // Actions
    loadData,
    updateRolePermissions,
    deleteRolePermission,
    getRolePermissions,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
  };
}
