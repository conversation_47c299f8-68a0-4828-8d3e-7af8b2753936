import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Select,
  Popconfirm,
  CommonCompactView,
  Input,
  InputNumber,
  Card,
  Checkbox,
  Radio,
  message,
  Row,
  Col,
  Space,
} from '../../../../components';
import { useParams } from 'react-router-dom';
import {
  getAllAreas,
  getAllLocations,
  getAllGateways,
  addGateway,
  updateGateway,
  deleteGateway,
  getAllGatewayConfigs,
} from '../../../../services';
import Context from '../../../../context';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { buildCommonApiValues } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import { get } from 'lodash';
import CommonDrawer from '../../../../components/CommonDrawer';
import { CommunicationTypes, CRUD, InputRegex, LocationTypes, Pages, ModuleNames } from '../../../../constants';

const { TextArea } = Input;
const { Option } = Select;

const GATEWAY_TYPES = {
  TRANSIT: 'transit',
  FIXED: 'fixed',
  LINT: 'lint',
};

const Gateway = () => {
  const params = useParams();
  const [context, setContext] = useContext(Context);
  const [areas, setAreas] = useState([]);
  const [allAreas, setAllAreas] = useState([]);
  const [locations, setLocations] = useState([]);
  const [allLocations, setAllLocations] = useState([]);
  const [models, setModels] = useState([]);
  const [tagModels, setTagModels] = useState([]);
  const [gateway, setGateway] = useState({});
  const [gateways, setGateways] = useState([]);
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');
  const [gatewayType, setGatewayType] = useState('');
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [selectedParentGateway, setSelectedParentGateway] = useState('');
  const [communicationType, setCommunicationType] = useState('');

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setGateways(tableData) : setGateways(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const [areaId, setAreaId] = useState(params.id || null);

  useEffect(() => {
    setAreaId(params.id);
  }, [params]);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const makeActive = (data) => {
    updateGatewayCall({ ...data, deleted: false });
  };

  const closeAdd = () => {
    setVisible(false);
    setGatewayType('');
    form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const loc = allLocations.find((x) => x.id === allAreas.find((y) => y.id === values.areaId)?.locationId);
    values.locId = loc?.id || null;
    values.locName = loc?.name || null;
    values.areaName = allAreas.find((x) => x.id === values.areaId)?.name || '';
    values.ownerTenantId = context.profile.tenantId;
    values.gatewayLint = values.gatewayLint?.map((x) => ({
      ...x,
      areaId: x.areaId,
      areaName: allAreas.find((y) => y.id === x.areaId)?.name || '',
      externalId: x.externalId,
      modelId: x.modelId,
    }));
    const commonValues = buildCommonApiValues(context.profile);
    values.properties = {};
    if (communicationType === CommunicationTypes.RFID_LINT && gatewayType === GATEWAY_TYPES.LINT) {
      values.antenna?.forEach((item) => {
        values.properties[item.name] = item.value;
      });
      delete values.antenna;
    }
    if (action === 'new') {
      await saveGatewayAction({ ...commonValues, ...values });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateGatewayAction(values);
      setVisible(false);
    }
  };

  const saveGatewayAction = async (gateway) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addGateway(gateway)
      .then((res) => {
        setGateways((state) => [res.data, ...state]);
        message.success('Succesfully Added gateway');
        form.resetFields();
        setGatewayType('');
        setCommunicationType('');
      })
      .catch((e) => {
        if (e.response.data) message.error(`Unable to add Gateway, ${e.response.data.debugMessage}`);
        else message.error('Unable to add Gateway, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editGatewayAction = (gateway) => {
    const antenna = [];
    if (gateway.properties)
      Object.keys(gateway.properties).forEach((key) => {
        antenna.push({ name: key, value: gateway.properties[key] });
      });
    const formFields = { ...gateway, antenna };
    if (formFields.communicationType === 'BLE' && formFields.categoryType === 'lint') {
      setCommunicationType('BLE');
      setGatewayType('lint');
    }
    form.setFieldsValue(formFields);
    setAction('edit');
    setGatewayType(gateway.categoryType);
    setGateway(gateway);
    setVisible(true);
  };

  const updateGatewayCall = (values) => {
    const data = { ...gateway, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateGateway(data)
      .then((res) => {
        setGateways((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, { ...i, ...res.data });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated gateway');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Gateway, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateGatewayAction = async (values) => {
    updateGatewayCall(values);
  };

  const setDeleteGatewayAction = (gatewayId, visible) => {
    setGateways((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === gatewayId).visible = visible;
      tempData
        .filter((x) => x.id !== gatewayId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteGatewayAction = (gatewayId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteGateway(gatewayId)
      .then(() => {
        setGateways((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === gatewayId).visible = false;
          tempState.find((x) => x.id === gatewayId).deleted = true;
          return [...state].filter((x) => x.id !== gatewayId);
        });
        message.success('Succesfully Deleted gateway');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Gateway, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  useEffect(() => {
    const init = async () => {
      if (context.profile.tenantId) {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
        let modelRes;
        try {
          modelRes = await getAllGatewayConfigs(context.profile.tenantId);
          setModels(modelRes.data.filter((x) => x.spdxModelConfig?.gatewayConfig));
          setTagModels(modelRes.data.filter((x) => x.spdxModelConfig?.tagConfig));
        } catch (e) {
          console.log(e);
          message.error('Unable to get Device Model details, try again later');
        }
        try {
          let res;
          let locationRes;
          res = await getAllAreas(context.profile.tenantId);
          locationRes = await getAllLocations(context.profile.tenantId);
          setLocations(locationRes.data.filter((x) => x.deleted === false));
          setAllLocations(locationRes.data);
          const gatewayRes = await getAllGateways(context.profile.tenantId);
          // allow only un-used areas
          const usedAreaIds = [...gatewayRes.data.map((x) => x.areaId)];
          setAreas(res.data.filter((x) => x.deleted === false && !usedAreaIds.includes(x.id)));
          setAllAreas(res.data);
          gatewayRes.data.forEach((l) => {
            l.areaName = res.data.find((x) => x.id === l.areaId)?.name;
            l.modelName = modelRes.data.find((x) => x.modelId === l.modelId)?.modelName;
          });
          setTableData(gatewayRes.data);
          setGateways(gatewayRes.data.filter((x) => x.deleted === false));
        } catch (e) {
          console.log(e);
          message.error('Unable to get Gateway details, try again later');
        } finally {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        }
      }
    };
    init();
    // eslint-disable-next-line
  }, []);

  const clearForm = () => {
    form.resetFields();
  };

  const onCategorySelect = (e) => {
    form.setFieldsValue({ ...form.getFieldsValue(), parentId: undefined });
    setGatewayType(e);
  };

  const onParentSelect = (e) => {
    setSelectedParentGateway(e);
  };

  const onCommunicationSelect = (e) => {
    setCommunicationType(e);
  };

  const [isModelTag, setIsModelTag] = useState('');
  const onChangeRadio = (e) => {
    setIsModelTag(e.target.value);
  };

  const [isAntMac, setIsAntMac] = useState('');
  const onChangeRadioAntMac = (e) => {
    setIsAntMac(e.target.value);
  };

  const tableCols = [
    { title: <strong> Name </strong>, key: 'name', dataIndex: 'name' },
    {
      title: <strong> Area </strong>,
      key: 'areaName',
      dataIndex: 'areaName',
      filters: areas.map((x) => ({ text: x.name, value: x.id })) || [],
      defaultFilteredValue: areaId ? [areaId] : [],
      onFilter: (value, record) => {
        return record.areaId === value;
      },
    },
    {
      title: <strong> Parent </strong>,
      key: 'parent',
      render: (record) => <>{gateways.filter((x) => x.id === record.parentId)[0]?.name}</>,
    },
    { title: <strong> MAC ID </strong>, key: 'externalId', dataIndex: 'externalId' },
    { title: <strong> Model </strong>, key: 'modelName', dataIndex: 'modelName' },
    {
      title: <strong> Category Type </strong>,
      key: 'categoryType',
      render: (record) => <>{record.categoryType.toUpperCase()}</>,
    },
    {
      title: <strong> Actions </strong>,
      width: 310,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <PermissionContainer page={Pages.GATEWAY} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                  Activate
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.GATEWAY} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editGatewayAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.GATEWAY} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete gateway ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteGatewayAction(record.id)}
                onCancel={() => setDeleteGatewayAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteGatewayAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const gatewayBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.GATEWAY}].displayName`,
    ModuleNames.GATEWAY
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.CONFIGURE, gatewayBreadcrumbsName]} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.GATEWAY} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Gateway
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="id"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={gateways}
          rowClassName={(record) => record.deleted && 'rowInactive'}
        />
      ) : (
        <CommonCompactView
          parent={true}
          data={gateways}
          onEdit={editGatewayAction}
          onDelete={deleteGatewayAction}
          permissions={[
            { pageName: Pages.GATEWAY, permission: CRUD.UPDATE, label: 'Edit' },
            { pageName: Pages.GATEWAY, permission: CRUD.DELETE, label: 'Delete' },
          ]}
          title="name"
          dataList={[{ label: 'Type', value: 'categoryType' }]}
        />
      )}
      <CommonDrawer title="Gateway" visible={visible} closeAdd={closeAdd}>
        <Form
          scrollToFirstError={true}
          layout="horizontal"
          initialValues={{}}
          form={form}
          wrapperCol={{ span: 16 }}
          labelCol={{ span: 8 }}
        >
          <Form.Item
            hasFeedback
            label="Category"
            name="categoryType"
            rules={[
              {
                required: true,
                message: 'Please select Gateway Category!',
              },
            ]}
          >
            <Select placeholder="Gateway Category" onSelect={onCategorySelect}>
              <Option key={GATEWAY_TYPES.FIXED} value={GATEWAY_TYPES.FIXED}>
                {GATEWAY_TYPES.FIXED.toLocaleUpperCase()}
              </Option>
              <Option key={GATEWAY_TYPES.TRANSIT} value={GATEWAY_TYPES.TRANSIT}>
                {GATEWAY_TYPES.TRANSIT.toLocaleUpperCase()}
              </Option>
              <Option key={GATEWAY_TYPES.LINT} value={GATEWAY_TYPES.LINT}>
                {GATEWAY_TYPES.LINT.toLocaleUpperCase()}
              </Option>
            </Select>
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input gateway Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Gateway Name" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Description"
            name="description"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input gateway description!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <TextArea rows={2} placeholder="Gateway Description" />
          </Form.Item>

          {gatewayType !== '' && (
            <>
              <Form.Item
                hasFeedback
                label="Model ID"
                name="modelId"
                rules={[
                  {
                    required: true,
                    message: 'Please input gateway model ID!',
                  },
                ]}
              >
                <Select placeholder="Gateway Model ID">
                  {models.map((b) => (
                    <Option title={b.modelName} key={b.modelId} value={b.modelId}>
                      {b.modelName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                hasFeedback
                label="MAC ID"
                name="externalId"
                rules={[
                  {
                    required: true,
                    whitespace: true,
                    message: 'Please input gateway MAC ID!',
                    min: 1,
                    max: 24,
                  },
                  { pattern: InputRegex.MAC_ID, message: 'Enter a valid MAC ID' },
                ]}
              >
                <Input placeholder="Gateway MAC ID" />
              </Form.Item>
              <Form.Item
                hasFeedback
                label="Coverage Min"
                name="coverageMin"
                rules={[
                  {
                    required: true,
                    whitespace: true,
                    type: 'number',
                    message: 'Please input Coverage Min!',
                  },
                ]}
              >
                <InputNumber placeholder="Coverage Min" style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item
                hasFeedback
                label="Coverage Max"
                name="coverageMax"
                rules={[
                  {
                    required: true,
                    whitespace: true,
                    type: 'number',
                    message: 'Please input Coverage Max!',
                  },
                ]}
              >
                <InputNumber placeholder="Coverage Max" style={{ width: '100%' }} />
              </Form.Item>
            </>
          )}

          {gatewayType !== '' && (
            <>
              <Form.Item
                hasFeedback
                label="Communication Type"
                name="communicationType"
                rules={[
                  {
                    required: true,
                    message: 'Please select Communication Type!',
                  },
                ]}
              >
                <Select
                  placeholder="Communication Type"
                  onSelect={onCommunicationSelect}
                  onClear={() => setCommunicationType('')}
                >
                  {Object.keys(CommunicationTypes).map((type) => (
                    <Option key={CommunicationTypes[type]} value={CommunicationTypes[type]}>
                      {CommunicationTypes[type]}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              {gatewayType === GATEWAY_TYPES.LINT && (
                <Form.Item label="Parent Gateway" name="parentId">
                  <Select
                    placeholder="Gateway Parent"
                    onSelect={onParentSelect}
                    onClear={() => setSelectedParentGateway('')}
                  >
                    {gateways.map((b) => (
                      <Option title={b.name} key={b.id} value={b.id}>
                        {b.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              )}
              {selectedParentGateway !== '' && (
                <Row>
                  <Col offset={8}>
                    <p> MAC ID: {gateways.find((x) => x.id === selectedParentGateway).externalId} </p>
                  </Col>
                </Row>
              )}
              {communicationType === CommunicationTypes.RFID_LINT && gatewayType === GATEWAY_TYPES.LINT && (
                <Form.Item label="Antenna">
                  <Form.List name="antenna">
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map(({ key, name, fieldKey, ...restField }) => (
                          <Space className="customSpace" key={key} size={0} align="baseline">
                            <Form.Item
                              {...restField}
                              name={[name, 'name']}
                              fieldKey={[fieldKey, 'name']}
                              rules={[
                                {
                                  required: true,
                                  type: 'number',
                                  message: 'Please input Numeric Value',
                                },
                              ]}
                            >
                              <InputNumber placeholder="Numeric Value" style={{ width: 150 }} />
                            </Form.Item>
                            <Form.Item
                              {...restField}
                              name={[name, 'value']}
                              fieldKey={[fieldKey, 'value']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Name',
                                },
                              ]}
                            >
                              <Input placeholder="Name" />
                            </Form.Item>
                            <MinusCircleOutlined style={{ marginLeft: '5px' }} onClick={() => remove(name)} />
                          </Space>
                        ))}
                        <Form.Item>
                          <Button
                            type="dashed"
                            onClick={() => add()}
                            block
                            className="addMoreButton"
                            icon={<PlusOutlined />}
                          >
                            Add Attribute
                          </Button>
                        </Form.Item>
                      </>
                    )}
                  </Form.List>
                </Form.Item>
              )}

              {communicationType === CommunicationTypes.BLE && gatewayType === GATEWAY_TYPES.LINT && (
                <Form.Item label="Gateway Lint">
                  <Form.List
                    name="gatewayLint"
                    initialValue={[{ coverageMin: null, coverageMax: null, antenna: null, xyzCoordinates: null }]}
                  >
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map(({ key, name, fieldKey, ...restField }) => (
                          <Space className="customSpace" key={key} size={0} align="baseline">
                            <Card size="small" style={{ width: 340, marginBottom: 10 }}>
                              <Form.Item
                                {...restField}
                                hasFeedback
                                name={[name, 'coverageMin']}
                                fieldKey={[fieldKey, 'coverageMin']}
                                rules={[
                                  {
                                    required: true,
                                    whitespace: true,
                                    type: 'number',
                                    message: 'Please input Coverage Min',
                                  },
                                ]}
                              >
                                <InputNumber placeholder="Coverage Min" style={{ width: '100%' }} />
                              </Form.Item>
                              <Form.Item
                                {...restField}
                                hasFeedback
                                name={[name, 'coverageMax']}
                                fieldKey={[fieldKey, 'coverageMax']}
                                rules={[
                                  {
                                    required: true,
                                    whitespace: true,
                                    type: 'number',
                                    message: 'Please input Coverage Max',
                                  },
                                ]}
                              >
                                <InputNumber placeholder="Coverage Max" style={{ width: '100%' }} />
                              </Form.Item>

                              <Form.Item>
                                <Radio.Group onChange={onChangeRadioAntMac}>
                                  <Radio value="antenna">Antenna</Radio>
                                  <Radio value="macId">MAC ID</Radio>
                                </Radio.Group>
                              </Form.Item>

                              {isAntMac === 'antenna' && (
                                <Form.Item
                                  label="Antenna"
                                  name={[name, 'antenna']}
                                  rules={[
                                    {
                                      required: true,
                                      message: 'Please input Antenna!',
                                    },
                                  ]}
                                >
                                  <InputNumber placeholder="Antenna" style={{ width: '100%' }} />
                                </Form.Item>
                              )}
                              {isAntMac === 'macId' && (
                                <Form.Item
                                  label="MAC ID"
                                  name={[name, 'externalId']}
                                  rules={[
                                    {
                                      required: true,
                                      whitespace: true,
                                      message: 'Please input gateway MAC ID!',
                                      min: 1,
                                      max: 24,
                                    },
                                    {
                                      pattern: InputRegex.MAC_ID,
                                      message: 'Enter a valid MAC ID',
                                    },
                                  ]}
                                >
                                  <Input placeholder="Gateway MAC ID" />
                                </Form.Item>
                              )}

                              <Form.Item
                                {...restField}
                                hasFeedback
                                name={[name, 'modelType']}
                                fieldKey={[fieldKey, 'modelType']}
                              >
                                <Radio.Group onChange={onChangeRadio}>
                                  <Radio value="gatewayModel">Gateway Model</Radio>
                                  <Radio value="tagModel">Tag Model</Radio>
                                </Radio.Group>
                              </Form.Item>

                              {isModelTag === 'gatewayModel' && (
                                <Form.Item
                                  hasFeedback
                                  label="Gateway Model"
                                  name={[name, 'modelId']}
                                  rules={[
                                    {
                                      required: true,
                                      message: 'Please input gateway model ID!',
                                    },
                                  ]}
                                >
                                  <Select placeholder="Gateway Model">
                                    {models.map((b) => (
                                      <Option title={b.modelName} key={b.modelId} value={b.modelId}>
                                        {b.modelName}
                                      </Option>
                                    ))}
                                  </Select>
                                </Form.Item>
                              )}
                              {isModelTag === 'tagModel' && (
                                <Form.Item
                                  label="Tag Model"
                                  name={[name, 'modelId']}
                                  rules={[
                                    {
                                      required: true,
                                      message: 'Please input Tag Model!',
                                    },
                                  ]}
                                >
                                  <Select placeholder="Tag Model">
                                    {tagModels.map((b) => (
                                      <Option title={b.modelName} key={b.modelId} value={b.modelId}>
                                        {b.modelName}
                                      </Option>
                                    ))}
                                  </Select>
                                </Form.Item>
                              )}
                              <Form.Item
                                hasFeedback
                                label="Bind To"
                                name={[name, 'areaId']}
                                rules={[
                                  {
                                    required: true,
                                    message: 'Please select Area to be bound!',
                                  },
                                ]}
                              >
                                <Select placeholder="Area">
                                  {areas.map((b) => (
                                    <Option title={b.name} key={b.id} value={b.id}>
                                      {b.name}
                                    </Option>
                                  ))}
                                </Select>
                              </Form.Item>

                              <p> XYZ Coordinates:</p>
                              <Row gutter={4}>
                                <Col span={8}>
                                  <Form.Item
                                    {...restField}
                                    hasFeedback
                                    name={[name, 'xyzCoordinates', 'additionalProp1']}
                                    fieldKey={[fieldKey, 'xyzCoordinates', 'additionalProp1']}
                                    rules={[
                                      {
                                        whitespace: true,
                                      },
                                    ]}
                                  >
                                    <Input placeholder="Property 1" />
                                  </Form.Item>
                                </Col>
                                <Col span={8}>
                                  <Form.Item
                                    {...restField}
                                    hasFeedback
                                    name={[name, 'xyzCoordinates', 'additionalProp2']}
                                    fieldKey={[fieldKey, 'xyzCoordinates', 'additionalProp2']}
                                    rules={[
                                      {
                                        whitespace: true,
                                      },
                                    ]}
                                  >
                                    <Input placeholder="Property 2" />
                                  </Form.Item>
                                </Col>
                                <Col span={8}>
                                  <Form.Item
                                    {...restField}
                                    hasFeedback
                                    name={[name, 'xyzCoordinates', 'additionalProp3']}
                                    fieldKey={[fieldKey, 'xyzCoordinates', 'additionalProp3']}
                                    rules={[
                                      {
                                        whitespace: true,
                                      },
                                    ]}
                                  >
                                    <Input placeholder="Property 3" />
                                  </Form.Item>
                                </Col>
                              </Row>
                            </Card>
                            <MinusCircleOutlined style={{ marginLeft: '5px' }} onClick={() => remove(name)} />
                          </Space>
                        ))}
                        <Form.Item>
                          <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                            Add Gateway Lint
                          </Button>
                        </Form.Item>
                      </>
                    )}
                  </Form.List>
                </Form.Item>
              )}

              {gatewayType === GATEWAY_TYPES.LINT && (
                <Form.Item
                  hasFeedback
                  label="Location Type"
                  name="locType"
                  rules={[
                    {
                      required: true,
                      message: 'Please select Location Type!',
                    },
                  ]}
                >
                  <Select placeholder="Location Type">
                    {Object.keys(LocationTypes).map((type) => (
                      <Option key={LocationTypes[type]} value={LocationTypes[type]}>
                        {LocationTypes[type]}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              )}
            </>
          )}

          {gatewayType !== '' && (
            <>
              {gatewayType === GATEWAY_TYPES.TRANSIT && (
                <Form.Item
                  hasFeedback
                  label="Location"
                  name="locId"
                  rules={[
                    {
                      required: true,
                      message: 'Please input Gateway Location!',
                    },
                  ]}
                >
                  <Select placeholder="Gateway Location">
                    {locations.map((b) => (
                      <Option title={b.name} key={b.id} value={b.id}>
                        {b.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              )}
              {(gatewayType === GATEWAY_TYPES.FIXED || gatewayType === GATEWAY_TYPES.LINT) && (
                <Form.Item
                  hasFeedback
                  label="Bind To"
                  name="areaId"
                  rules={[
                    {
                      required: true,
                      message: 'Please select Area to be bound!',
                    },
                  ]}
                >
                  <Select placeholder="Area">
                    {areas.map((b) => (
                      <Option title={b.name} key={b.id} value={b.id}>
                        {b.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              )}

              <Form.Item label="XYZ Coordinates">
                <Row gutter={4}>
                  <Col span={8}>
                    <Form.Item
                      hasFeedback
                      name={['xyzCoordinates', 'additionalProp1']}
                      rules={[
                        {
                          whitespace: true,
                        },
                      ]}
                    >
                      <Input placeholder="Property 1" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      hasFeedback
                      name={['xyzCoordinates', 'additionalProp2']}
                      rules={[
                        {
                          whitespace: true,
                        },
                      ]}
                    >
                      <Input placeholder="Property 2" />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      hasFeedback
                      name={['xyzCoordinates', 'additionalProp3']}
                      rules={[
                        {
                          whitespace: true,
                        },
                      ]}
                    >
                      <Input placeholder="Property 3" />
                    </Form.Item>
                  </Col>
                </Row>
              </Form.Item>
            </>
          )}
          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </>
  );
};

export default Gateway;
