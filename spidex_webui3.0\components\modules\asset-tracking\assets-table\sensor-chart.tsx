import React from "react";
import useSensorSubscription from "./use-sensor-subscription";
import { SensorType } from "@/types/asset-tracking";
import {
  AreaChart as Recharts<PERSON>rea<PERSON>hart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { SENSOR_COLORS, SENSOR_UNITS } from "@/lib/constants/asset-tracking";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

const SENSOR_LABELS: Record<SensorType, string> = {
  [SensorType.TEMPERATURE]: "Temperature",
  [SensorType.HUMIDITY]: "Humidity",
  [SensorType.AMBLIGHT]: "Ambient Light",
  [SensorType.PRESSURE]: "Pressure",
  [SensorType.BATTERY]: "Battery",
  [SensorType.PROXIMITY]: "Proximity",
  [SensorType.CPU]: "CPU Usage",
  [SensorType.MEMORY]: "Memory Usage",
  [SensorType.DISK]: "Disk Usage",
  [SensorType.INBOUND]: "Inbound Traffic",
  [SensorType.OUTBOUND]: "Outbound Traffic",
  [SensorType.RSSI]: "RSSI",
};

type SensorChartProps = {
  sensorType: SensorType;
  deviceLogId: string;
};

const SensorChart: React.FC<SensorChartProps> = ({
  sensorType,
  deviceLogId,
}) => {
  const data = useSensorSubscription({ sensorType, deviceLogId });
  const color =
    (SENSOR_COLORS as Record<string, string>)[sensorType] || "#8884d8";
  const unit = (SENSOR_UNITS as Record<string, string>)[sensorType] || "";
  const label = SENSOR_LABELS[sensorType] || sensorType;
  const latest = data.length > 0 ? data[data.length - 1].value : null;
  const isLoading = data.length === 0;

  return (
    <Card key={sensorType} className="w-full">
      <CardHeader>
        <div className="flex flex-row items-center justify-between gap-2">
          <CardTitle>{label}</CardTitle>
          <div className="text-2xl font-mono font-bold text-primary flex items-center min-w-[80px] justify-end">
            {isLoading ? (
              <Loader2 className="animate-spin w-6 h-6 text-muted-foreground" />
            ) : (
              <>
                {latest}
                <span className="text-base font-normal text-muted-foreground ml-1">
                  {unit}
                </span>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <Loader2 className="animate-spin w-8 h-8 text-muted-foreground mb-2" />
            <div className="text-muted-foreground">Waiting for data...</div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={250}>
            <RechartsAreaChart
              data={data}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient
                  id={`color_${sensorType}`}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor={color} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={color} stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis
                dataKey="time"
                tickFormatter={(t) => t.slice(11, 19)}
                minTickGap={24}
                tick={{
                  fontSize: 12,
                  className: "text-xs text-muted-foreground",
                }}
              />
              <YAxis
                allowDecimals={true}
                domain={["auto", "auto"]}
                tickFormatter={(v) => v + (unit ? ` ${unit}` : "")}
                tick={{
                  fontSize: 12,
                  className: "text-xs text-muted-foreground",
                }}
              />
              <CartesianGrid strokeDasharray="3 3" />
              <Tooltip
                formatter={(value: any) => `${value}${unit}`}
                labelFormatter={(label) => {
                  const d = new Date(label);
                  return d.toLocaleTimeString();
                }}
              />
              <Area
                type="monotone"
                dataKey="value"
                stroke={color}
                fillOpacity={1}
                fill={`url(#color_${sensorType})`}
                isAnimationActive={false}
              />
            </RechartsAreaChart>
          </ResponsiveContainer>
        )}
      </CardContent>
    </Card>
  );
};

export default SensorChart;
