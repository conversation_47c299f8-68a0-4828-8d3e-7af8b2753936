import { useEffect, useState, useContext } from 'react';
import {
  Input,
  Checkbox,
  Table,
  Button,
  Form,
  Popconfirm,
  CommonDrawer,
  CommonCompactView,
  message,
  Row,
  Col,
  Pagination,
  Select,
} from '../../../components';
import { addEcu, updateEcu, deleteEcu, getAllEcuByPagination } from '../../../services';
import Context from '../../../context';
import { PlusOutlined } from '@ant-design/icons';
import { BreadcrumbList, PermissionContainer } from '../../../shared';
import { buildCommonApiValues } from '../../../utils';
import { get } from 'lodash';
import { CRUD, Pages, ModuleNames, MastersPageSizeDefault, VehicleManufacturers } from '../../../constants';

const { Search } = Input;
const { Option } = Select;

const Ecu = () => {
  const [context, setContext] = useContext(Context);
  const [action, setAction] = useState('new');
  const [totalEcuInfo, setTotalEcuInfo] = useState({ items: 0, current: 0, pageSize: MastersPageSizeDefault });
  const [showDeleted, setShowDeleted] = useState(false);
  const [visible, setVisible] = useState(false);
  const [ecus, setEcus] = useState([]);
  const [ecusOriginal, setEcusOriginal] = useState([]);
  const [ecusInfo, setEcusInfo] = useState({});
  const [ecuInfo, setEcuInfo] = useState(false);

  const [form] = Form.useForm();

  const formInitState = {};
  const [formInitValues, setFormInitValues] = useState({ ...formInitState });

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();

    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveEcuAction({
        createdTime: commonValues.createdTime,
        modifiedTime: commonValues.modifiedTime,
        modifiedBy: commonValues.modifiedBy,
        createdBy: commonValues.createdBy,
        deleted: commonValues.deleted,
        ...values,
      });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      updateEcuCall(values);
      if (type === 'add') form.resetFields({});
      setFormInitValues(null);
      setVisible(false);
    }
  };

  //Save ecu data
  const saveEcuAction = async (ecu) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addEcu(ecu)
      .then((res) => {
        setEcus((state) => [res.data, ...state]);
        message.success('Succesfully Added ECU');
        form.resetFields();
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        if (e.response.data.debugMessage) {
          message.error('ECU name is already existed');
          setVisible(true);
        } else {
          message.error('Unable to add ECU, try again later');
        }
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  //update ecu data
  const editEcuAction = (ecu) => {
    form.setFieldsValue({ ...ecu });
    setAction('edit');
    setEcusInfo(ecu);
    setVisible(true);
  };

  const updateEcuCall = (values) => {
    const data = { ...ecusInfo, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateEcu(data)
      .then((res) => {
        setEcus((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, res.data);
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        message.success('Succesfully Updated ECU');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update ECU, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const makeActive = (data) => {
    updateEcuCall({ ...data, deleted: false });
  };

  //delete the ecu data
  const setDeleteEcuAction = (ecuId, visible) => {
    setEcus((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === ecuId).visible = visible;
      tempData
        .filter((x) => x.id !== ecuId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteEcuAction = (ecuId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteEcu(ecuId)
      .then(() => {
        setEcus((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === ecuId).visible = false;
          tempState.find((x) => x.id === ecuId).deleted = true;
          return [...state].filter((x) => x.id !== ecuId);
        });
        message.success('Succesfully Deleted ECU');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete ECU, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const closeAdd = () => {
    setVisible(false);
    setEcuInfo(false);
    if (action === 'edit') form.resetFields();
  };

  const clearForm = () => {
    form.resetFields();
  };

  //view Ecu information
  const viewEcuInfo = (e) => {
    setEcusInfo(e);
    setEcuInfo(true);
  };

  //get all ecu data by pagination
  const onPaginationChange = (e) => {
    init(+e - 1);
  };

  const init = async (page = 0) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const pagination = {
      size: totalEcuInfo.pageSize,
      page: page,
    };

    try {
      const ecuRes = await getAllEcuByPagination(pagination, showDeleted);
      const ecuData = ecuRes.data?.content || [];
      setEcus(ecuData);
      setEcusOriginal(ecuData);
      setTotalEcuInfo((ps) => ({ ...ps, current: page, items: ecuRes.data.totalElements }));
    } catch (e) {
      console.log(e);
      message.error('Unable to get ECU details, try again later');
    } finally {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
    }
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, [showDeleted]);

  //If it is ecu table set the tableModeNormal to true
  //If it is search table set the tableModeNormal to false
  const [tableModeNormal, setTableModeNormal] = useState(true);
  //onSearch Ecu data with ecu name,ecu version,vehicle manufacturer
  const onSearchEcuData = async (e) => {
    if (!e.target.value) {
      setEcus(ecusOriginal);
      setTableModeNormal(true);
    } else {
      try {
        setEcus(() => {
          const tempData = [...ecus];
          return tempData
            .filter(
              (x) =>
                x.ecuName.toLowerCase().includes(e.target.value.toLowerCase()) ||
                x.vehicleManufacturer.toLowerCase().includes(e.target.value.toLowerCase())
            )
            .filter((x) => x.deleted === false);
        });
        setTableModeNormal(false);
      } catch {
        message.error('Could not retrieve ECU');
      }
    }
  };

  const tableCols = [
    { title: <strong> Name </strong>, key: 'ecuName', dataIndex: 'ecuName' },
    { title: <strong> Vehicle Manufacturer </strong>, key: 'vehicleManufacturer', dataIndex: 'vehicleManufacturer' },
    {
      title: <strong> Actions </strong>,
      width: 350,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <Button type="link" className="actionButton" onClick={() => makeActive(record)}>
                Activate
              </Button>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.ECU} permission={CRUD.VIEW}>
              <Button type="link" className="actionButton" onClick={() => viewEcuInfo(record)}>
                View
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.ECU} permission={CRUD.UPDATE}>
              <Button type="link" className="actionButton" onClick={() => editEcuAction(record)}>
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.ECU} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete ECU ${record.ecuName}?`}
                visible={record.visible || false}
                onConfirm={() => deleteEcuAction(record.id)}
                onCancel={() => setDeleteEcuAction(record.id, false)}
              >
                <Button type="link" className="actionButton" onClick={() => setDeleteEcuAction(record.id, true)}>
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const ecuBreadcrumbsName = get(context.tenantProfile, `moduleNames[${Pages.ECU}].displayName`, ModuleNames.ECU);

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.MASTERS, ecuBreadcrumbsName, 'Register ECU']} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.ECU} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add ECU
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <Row className="searchDiv" justify="end">
        <Col xs={24} lg={6}>
          <Search placeholder="Enter Search" allowClear enterButton onChange={onSearchEcuData} />
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <>
            {tableModeNormal ? (
              <>
                <Table
                  size="small"
                  scroll={{ x: true }}
                  rowKey="id"
                  columns={tableCols}
                  dataSource={ecus}
                  rowClassName={(record) => record.deleted && 'rowInactive'}
                  pagination={false}
                />
                <Row justify="end">
                  <Col>
                    <div className="m-2">
                      <Pagination
                        onChange={onPaginationChange}
                        current={totalEcuInfo.current + 1}
                        total={totalEcuInfo.items}
                        defaultPageSize={totalEcuInfo.pageSize}
                        showSizeChanger={false}
                        showQuickJumper={false}
                      />
                    </div>
                  </Col>
                </Row>
              </>
            ) : (
              <Table
                size="small"
                scroll={{ x: true }}
                rowKey="id"
                columns={tableCols}
                dataSource={ecus}
                rowClassName={(record) => record.deleted && 'rowInactive'}
              />
            )}
          </>
        ) : (
          <>
            <CommonCompactView
              data={ecus}
              onEdit={editEcuAction}
              onDelete={deleteEcuAction}
              permissions={[
                { pageName: Pages.ECU, permission: CRUD.UPDATE, label: 'Edit' },
                { pageName: Pages.ECU, permission: CRUD.DELETE, label: 'Delete' },
              ]}
              title="ecuName"
              dataList={[{ label: 'ECU Name', value: 'ecuName' }]}
            />
            <Row justify="end">
              <Col>
                <div className="m-2">
                  <Pagination
                    onChange={onPaginationChange}
                    current={totalEcuInfo.current + 1}
                    total={totalEcuInfo.items}
                    defaultPageSize={totalEcuInfo.pageSize}
                    showSizeChanger={false}
                    showQuickJumper={false}
                  />
                </div>
              </Col>
            </Row>
          </>
        )}
      </>

      <CommonDrawer title="ECU Information" visible={ecuInfo} closeAdd={closeAdd}>
        <div className="infoDrawer">
          {ecusInfo ? (
            <Row className="infoContainer" span={24}>
              <Col className="info" span={24}>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Name :
                  </Col>
                  <Col>{ecusInfo.ecuName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    External ID :
                  </Col>
                  <Col>{ecusInfo.ecuExtId}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Model :
                  </Col>
                  <Col>{ecusInfo.ecuModel}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Make :
                  </Col>
                  <Col>{ecusInfo.ecuMake}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Software Version :
                  </Col>
                  <Col>{ecusInfo.ecuVersion}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    File Name :
                  </Col>
                  <Col>{ecusInfo.fileName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    File Type :
                  </Col>
                  <Col>{ecusInfo.fileType}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    File Location :
                  </Col>
                  <Col>{ecusInfo.fileLocation}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Sftp Url:
                  </Col>
                  <Col>{ecusInfo.sftpUrl}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    User Name:
                  </Col>
                  <Col>{ecusInfo.userName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Password:
                  </Col>
                  <Col>{ecusInfo.password}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Vehicle Manufacturer :
                  </Col>
                  <Col>{ecusInfo.vehicleManufacturer}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Deleted :
                  </Col>
                  <Col>{String(ecusInfo.deleted)}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created By :
                  </Col>
                  <Col>{ecusInfo.createdBy}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created Time :
                  </Col>
                  <Col>{new Date(ecusInfo.createdTime).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified Time :
                  </Col>
                  <Col>{new Date(ecusInfo.modifiedTime).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified By :
                  </Col>
                  <Col>{ecusInfo.modifiedBy}</Col>
                </Row>
                <br />
                <Row justify="center">
                  <Button type="primary" onClick={() => editEcuAction(ecusInfo)}>
                    Edit
                  </Button>
                </Row>
              </Col>
            </Row>
          ) : (
            <></>
          )}
        </div>
      </CommonDrawer>

      <CommonDrawer title="ECU" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          autoComplete={'new-password'}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 16 }}
          labelCol={{ span: 8 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Name"
            name="ecuName"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input name!',
              },
            ]}
          >
            <Input placeholder="Name" disabled={action === 'edit' ? true : false} />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="External ID" name="ecuExtId">
            <Input placeholder="External ID" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="Model" name="ecuModel">
            <Input placeholder="Model" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="Make" name="ecuMake">
            <Input placeholder="Make" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="Software Version" name="ecuVersion">
            <Input placeholder="Software Version" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="File Name" name="fileName">
            <Input placeholder="File Name" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="File Type" name="fileType">
            <Input placeholder="File Tye" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="File Location" name="fileLocation">
            <Input placeholder="File Location" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="Sftp Url" name="sftpUrl">
            <Input placeholder="Sftp Url" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="User Name" name="userName">
            <Input placeholder="User Name" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="Password" name="password">
            <Input placeholder="Password" />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Vehicle Manufacturer"
            name="vehicleManufacturer"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input vehicle manufacturer!',
              },
            ]}
          >
            <Select placeholder="Vehicle Manufacturer">
              {VehicleManufacturers.map((m) => (
                <Option title={m} key={m} value={m}>
                  {m}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </>
  );
};

export default Ecu;
