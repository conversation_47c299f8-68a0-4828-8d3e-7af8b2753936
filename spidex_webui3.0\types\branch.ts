// Branch Management Types

export interface GpsPoint {
  latitude: string;
  longitude: string;
}

export interface Branch {
  id: string;
  name: string;
  address: string;
  gpsPoint: GpsPoint;
  geoJson: string;
  properties: Record<string, any> | null;
  tenantId: string;
  deleted: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
}

export interface CreateBranchFormData {
  name: string;
  address: string;
  gpsPoint: GpsPoint;
  geoJson: string;
}

export interface UpdateBranchFormData {
  id: string;
  name: string;
  address: string;
  gpsPoint: GpsPoint;
  geoJson: string;
}

export interface BranchSearchFilters {
  searchTerm?: string;
  deleted?: boolean;
}

export interface BranchPaginationParams {
  pageNumber: number;
  pageSize: BranchPageSize;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export type BranchPageSize = 10 | 50 | 100 | "all";

export const DEFAULT_BRANCH_PAGE_SIZE: BranchPageSize = 10;
export const BRANCH_PAGE_SIZES: BranchPageSize[] = [10, 50, 100, "all"];
export const BRANCH_PAGE_SIZE_ALL: BranchPageSize = "all";
