## Adding New Module / SubModule

**Action Items**

1. Create entry in menu.js or route.js depending on wether its hierarchal or non hierarchal respectively.
2. Create entry in constants (ModuleNames, Pages, PageRoutes, RolesPages)
3. Add form item in RBAC module (Add Role, entry should be there)
4. Above item needs updating on Pages API endpoint with new entry (this can be performed using standAlone script)
5. Add form item in Application Management Visual module for super admin to give a custom name functionality if needed
6. Add code in visual module (ApplicationManagement/visual)
7. Add code in App.js (for getting names from Tenant Profile API and setting the custom names in a local state)
