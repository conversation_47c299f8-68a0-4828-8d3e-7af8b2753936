"use client";

import { useState } from "react";
import { MoreH<PERSON>zon<PERSON>, Edit, Trash2, Shield } from "lucide-react";
import { Role, RbacPaginationParams, RbacPageSize } from "@/types/rbac";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { RbacTablePagination } from "./rbac-table-pagination";

interface RoleDataTableProps {
  data: Role[];
  pagination: RbacPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: RbacPageSize[];
  onEdit: (role: Role) => void;
  onDelete: (role: Role) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: RbacPageSize) => void;
  isLoading?: boolean;
}

export function RoleDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onEdit,
  onDelete,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
}: RoleDataTableProps) {
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);
  const [forceRender, setForceRender] = useState(0);

  const handleDeleteClick = (role: Role) => {
    // Force a clean state first
    setRoleToDelete(null);
    // Then set the new role after a brief delay
    setTimeout(() => {
      setRoleToDelete(role);
    }, 10);
  };

  const handleDeleteConfirm = () => {
    if (roleToDelete) {
      onDelete(roleToDelete);
    }
    setRoleToDelete(null);
    // Force re-render to ensure clean state
    setForceRender((prev) => prev + 1);
  };

  const handleDeleteCancel = () => {
    setRoleToDelete(null);
    // Force re-render to ensure clean state
    setForceRender((prev) => prev + 1);
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return "N/A";
    }
  };

  const getStatusBadge = (role: Role) => {
    if (role.deleted) {
      return <Badge variant="destructive">Deleted</Badge>;
    }
    if (role.enable) {
      return <Badge variant="default">Active</Badge>;
    }
    return <Badge variant="secondary">Inactive</Badge>;
  };

  if (isLoading) {
    return (
      <>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">Actions</TableHead>
                <TableHead className="w-[100px]">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Role ID
                  </div>
                </TableHead>
                <TableHead className="w-[150px]">Role Name</TableHead>
                <TableHead className="w-[100px]">Status</TableHead>
                <TableHead className="w-[120px]">Created Date</TableHead>
                <TableHead className="w-[120px]">Created By</TableHead>
                <TableHead className="w-[120px]">Modified Date</TableHead>
                <TableHead className="w-[120px]">Modified By</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="h-8 w-8 rounded" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[80px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[120px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-[60px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[100px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[100px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[100px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[100px]" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <RbacTablePagination
          pagination={pagination}
          totalRecords={0}
          currentPageRecords={0}
          totalPages={0}
          availablePageSizes={availablePageSizes}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          entityName="role"
        />
      </>
    );
  }

  return (
    <>
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[100px]">Actions</TableHead>
              <TableHead className="w-[100px]">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Role ID
                </div>
              </TableHead>
              <TableHead className="w-[150px]">Role Name</TableHead>
              <TableHead className="w-[100px]">Status</TableHead>
              <TableHead className="w-[120px]">Created Date</TableHead>
              <TableHead className="w-[120px]">Created By</TableHead>
              <TableHead className="w-[120px]">Modified Date</TableHead>
              <TableHead className="w-[120px]">Modified By</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  No roles found.
                </TableCell>
              </TableRow>
            ) : (
              data.map((role) => (
                <TableRow
                  key={role.id}
                  className={role.deleted ? "opacity-50" : ""}
                >
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {/* <DropdownMenuItem
                          onClick={() => onEdit(role)}
                          className="cursor-pointer"
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Role
                        </DropdownMenuItem> */}
                        {!role.deleted && (
                          <DropdownMenuItem
                            onClick={() => handleDeleteClick(role)}
                            className="cursor-pointer text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Role
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                  <TableCell className="font-medium">{role.id}</TableCell>
                  <TableCell>{role.name}</TableCell>
                  <TableCell>{getStatusBadge(role)}</TableCell>
                  <TableCell>{formatDate(role.createdTime)}</TableCell>
                  <TableCell>{role.createdBy}</TableCell>
                  <TableCell>{formatDate(role.modifiedTime)}</TableCell>
                  <TableCell>{role.modifiedBy}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <RbacTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        entityName="role"
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        key={`delete-dialog-${forceRender}`}
        open={!!roleToDelete}
        onOpenChange={(open) => {
          if (!open) {
            handleDeleteCancel();
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will delete the role "{roleToDelete?.name}". This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
