"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  Branch,
  BranchSearchFilters,
  BranchPaginationParams,
  BranchPageSize,
  DEFAULT_BRANCH_PAGE_SIZE,
  BRANCH_PAGE_SIZES,
  BRANCH_PAGE_SIZE_ALL,
  CreateBranchFormData,
  UpdateBranchFormData,
} from "@/types/branch";

interface UseBranchManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: BranchPageSize;
  initialPageSize?: number;
  availablePageSizes?: BranchPageSize[];
}

export const useBranchManagement = ({
  autoLoad = true,
  defaultPageSize = DEFAULT_BRANCH_PAGE_SIZE,
  initialPageSize,
  availablePageSizes,
}: UseBranchManagementOptions = {}) => {
  // Get authenticated API instance and session
  const spidexApi = useSpidexApi();
  const { data: session, status } = useSession();

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);

  // Data cache - store all branches for client-side pagination
  const [allBranchesCache, setAllBranchesCache] = useState<Branch[]>([]);

  // Search and pagination state
  const [searchFilters, setSearchFilters] = useState<BranchSearchFilters>({
    searchTerm: "",
    deleted: false,
  });

  const [pagination, setPagination] = useState<BranchPaginationParams>({
    pageNumber: 1,
    pageSize: (initialPageSize as BranchPageSize) || defaultPageSize,
    sortBy: "name",
    sortOrder: "asc",
  });

  // Safe search filters with defaults
  const safeSearchFilters = useMemo(
    () => ({
      searchTerm: searchFilters.searchTerm || "",
      deleted: showDeleted,
    }),
    [searchFilters, showDeleted]
  );

  // Filter branches based on search criteria
  const filteredBranches = useMemo(() => {
    let filtered = [...allBranchesCache];

    // Filter by deleted status
    if (!showDeleted) {
      filtered = filtered.filter((branch) => !branch.deleted);
    } else {
      filtered = filtered.filter((branch) => branch.deleted);
    }

    // Filter by search term
    if (safeSearchFilters.searchTerm) {
      const searchTerm = safeSearchFilters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (branch) =>
          branch.name.toLowerCase().includes(searchTerm) ||
          branch.address.toLowerCase().includes(searchTerm)
      );
    }

    // Sort branches
    if (pagination.sortBy) {
      filtered.sort((a, b) => {
        const aValue = a[pagination.sortBy as keyof Branch];
        const bValue = b[pagination.sortBy as keyof Branch];

        if (typeof aValue === "string" && typeof bValue === "string") {
          const comparison = aValue.localeCompare(bValue);
          return pagination.sortOrder === "desc" ? -comparison : comparison;
        }

        if (typeof aValue === "number" && typeof bValue === "number") {
          const comparison = aValue - bValue;
          return pagination.sortOrder === "desc" ? -comparison : comparison;
        }

        return 0;
      });
    }

    return filtered;
  }, [
    allBranchesCache,
    safeSearchFilters,
    showDeleted,
    pagination.sortBy,
    pagination.sortOrder,
  ]);

  // Calculate pagination
  const totalPages = useMemo(() => {
    if (pagination.pageSize === "all") return 1;
    return Math.ceil(filteredBranches.length / pagination.pageSize);
  }, [filteredBranches.length, pagination.pageSize]);

  // Get current page branches
  const paginatedBranches = useMemo(() => {
    if (pagination.pageSize === "all") {
      return filteredBranches;
    }

    const startIndex = (pagination.pageNumber - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredBranches.slice(startIndex, endIndex);
  }, [filteredBranches, pagination.pageNumber, pagination.pageSize]);

  // Count statistics
  const activeRecordsCount = useMemo(
    () => allBranchesCache.filter((branch) => !branch.deleted).length,
    [allBranchesCache]
  );

  const inactiveRecordsCount = useMemo(
    () => allBranchesCache.filter((branch) => branch.deleted).length,
    [allBranchesCache]
  );

  // Available page sizes
  const finalAvailablePageSizes: BranchPageSize[] =
    availablePageSizes || BRANCH_PAGE_SIZES;

  // Load all branches data
  const loadData = useCallback(async () => {
    if (
      status !== "authenticated" ||
      !session?.user?.token ||
      !session?.user?.tenantId
    ) {
      console.log("Waiting for authentication...");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Set authentication in API service
      spidexApi.setAuthToken(session.user.token);
      spidexApi.setTenantId(session.user.tenantId);

      console.log(
        "Loading all branch management data for client-side pagination"
      );

      // Load all branches with large page size for client-side pagination
      const branchesData = await spidexApi.getAllBranches(1, 1000);
      setAllBranchesCache(branchesData);

      console.log(`Loaded ${branchesData.length} branches`);
    } catch (error) {
      console.error("Error loading branch data:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load branch data"
      );
    } finally {
      setIsLoading(false);
    }
  }, [spidexApi, session, status]);

  // Auto-load data when session is ready (only on initial mount)
  useEffect(() => {
    if (autoLoad && status === "authenticated") {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad, status]); // Removed loadData dependency to prevent reloading on session changes

  // CRUD Operations
  const createBranch = useCallback(
    async (branchData: CreateBranchFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        // Transform form data to match API format
        const apiPayload = {
          ...branchData,
          id: null, // Required by API for new branches
          tenantId: session?.user?.tenantId,
          properties: {},
          deleted: false,
          createdBy: session?.user?.userId || "system",
          createdTime: Date.now(),
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        };

        console.log("Creating branch with payload:", apiPayload);
        const newBranch = await spidexApi.createBranch(apiPayload);

        // Reload data to get updated list
        await loadData();

        console.log("Branch created successfully");
        return newBranch;
      } catch (error) {
        console.error("Error creating branch:", error);
        setError(
          error instanceof Error ? error.message : "Failed to create branch"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  const updateBranch = useCallback(
    async (branchData: UpdateBranchFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        // Find the existing branch to get all required fields
        const existingBranch = allBranchesCache.find(b => b.id === branchData.id);
        if (!existingBranch) {
          throw new Error("Branch not found");
        }

        const updatedBranch = await spidexApi.updateBranch({
          ...existingBranch, // Include all existing fields
          ...branchData, // Override with form data
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        });

        // Reload data to get updated list
        await loadData();

        console.log("Branch updated successfully");
        return updatedBranch;
      } catch (error) {
        console.error("Error updating branch:", error);
        setError(
          error instanceof Error ? error.message : "Failed to update branch"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData, allBranchesCache]
  );

  const deleteBranch = useCallback(
    async (branchId: string) => {
      try {
        setIsLoading(true);
        setError(null);

        await spidexApi.deleteBranch(branchId);

        // Reload data to get updated list
        await loadData();

        console.log("Branch deleted successfully");
      } catch (error) {
        console.error("Error deleting branch:", error);
        setError(
          error instanceof Error ? error.message : "Failed to delete branch"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, loadData]
  );

  // Pagination controls
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, pageNumber: page }));
  }, []);

  const changePageSize = useCallback((size: BranchPageSize) => {
    setPagination((prev) => ({ ...prev, pageSize: size, pageNumber: 1 }));
  }, []);

  // Search controls
  const updateSearchFilters = useCallback(
    (filters: Partial<BranchSearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      setPagination((prev) => ({ ...prev, pageNumber: 1 })); // Reset to first page
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({ searchTerm: "", deleted: false });
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
  }, []);

  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
    setPagination((prev) => ({ ...prev, pageNumber: 1 })); // Reset to first page
  }, []);

  return {
    // Data
    branches: paginatedBranches,
    allBranches: allBranchesCache,
    filteredBranches,

    // State
    isLoading,
    error,
    showDeleted,

    // Search and pagination
    searchFilters: safeSearchFilters,
    pagination,
    totalPages,
    totalRecords: filteredBranches.length, // Use filtered count for display
    totalAllRecords: allBranchesCache.length, // Total count including deleted
    activeRecordsCount, // Count of active (not deleted) records
    inactiveRecordsCount, // Count of inactive (deleted) records
    hasNextPage: pagination.pageNumber < totalPages,
    hasPreviousPage: pagination.pageNumber > 1,

    // Actions
    loadData,
    createBranch,
    updateBranch,
    deleteBranch,

    // Pagination controls
    goToPage,
    changePageSize,
    availablePageSizes: finalAvailablePageSizes,

    // Search controls
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
  };
};
