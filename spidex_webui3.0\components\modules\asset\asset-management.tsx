"use client";

import { useState } from "react";
import { Plus, Package, RefreshCw } from "lucide-react";
import { useSession } from "next-auth/react";
import { useAssetManagement } from "@/hooks/use-asset-management";
import { getTableConfig } from "@/config/table-config";
import {
  Asset,
  CreateAssetFormData,
  UpdateAssetFormData,
  AssetPageSize,
} from "@/types/asset";
import { AssetDataTable } from "./asset-data-table";
import { AssetSearchPagination } from "./asset-search-pagination";
import { AssetForm } from "./asset-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  <PERSON><PERSON><PERSON><PERSON>bPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

export default function AssetManagement() {
  const { data: session, status } = useSession();

  // Get table configuration for this page
  const tableConfig = getTableConfig("asset-management");

  const {
    assets: paginatedAssets,
    filteredAssets,
    isLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    totalAllRecords,
    activeRecordsCount,
    inactiveRecordsCount,
    loadData,
    createAsset,
    updateAsset,
    deleteAsset,
    toggleAssetActive,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useAssetManagement({
    initialPageSize: tableConfig.defaultPageSize,
    availablePageSizes: tableConfig.availablePageSizes as AssetPageSize[],
  });

  // Form state
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingAsset, setEditingAsset] = useState<Asset | null>(null);
  const [isFormLoading, setIsFormLoading] = useState(false);

  // Table key for forcing re-render
  const [tableKey, setTableKey] = useState(0);

  const openCreateForm = () => {
    setEditingAsset(null);
    setIsFormOpen(true);
  };

  const openEditForm = (asset: Asset) => {
    console.log("✏️ Opening edit form for asset:", asset);
    setEditingAsset(asset);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setEditingAsset(null);
    setIsFormLoading(false);
  };

  const handleFormSubmit = async (
    data: CreateAssetFormData | UpdateAssetFormData
  ) => {
    try {
      setIsFormLoading(true);

      if (editingAsset) {
        // Update existing asset
        console.log("🔄 Updating asset:", editingAsset.id);
        await updateAsset(data as UpdateAssetFormData);
        toast.success("Asset updated successfully", {
          description: `${data.name} has been updated.`,
        });
      } else {
        // Create new asset
        const result = await createAsset(data as CreateAssetFormData);
        toast.success("Asset created successfully", {
          description: `${data.name} has been added to the system.`,
        });
      }

      closeForm();
      setTableKey((prev) => prev + 1); // Force table re-render
    } catch (error) {
      toast.error(
        editingAsset ? "Failed to update asset" : "Failed to create asset",
        {
          description:
            error instanceof Error
              ? error.message
              : "Please check your input and try again.",
        }
      );
    } finally {
      setIsFormLoading(false);
    }
  };

  const handleDeleteAsset = async (assetId: string) => {
    try {
      await deleteAsset(assetId);
      toast.success("Asset deleted successfully", {
        description: "The asset has been removed from the system.",
      });
      setTableKey((prev) => prev + 1); // Force table re-render
    } catch (error) {
      console.error("Error deleting asset:", error);
      toast.error("Failed to delete asset", {
        description: "Please try again later.",
      });
    }
  };

  const handleToggleActive = async (asset: Asset) => {
    try {
      await toggleAssetActive(asset);
      toast.success(
        `Asset ${asset.deleted ? "activated" : "deactivated"} successfully`,
        {
          description: `${asset.name} has been ${
            asset.deleted ? "activated" : "deactivated"
          }.`,
        }
      );
      setTableKey((prev) => prev + 1); // Force table re-render
    } catch (error) {
      console.error("Error toggling asset status:", error);
      toast.error("Failed to toggle asset status", {
        description: "Please try again later.",
      });
    }
  };

  const handleReloadData = async () => {
    try {
      await loadData();
      toast.success("Data reloaded successfully", {
        description: "Asset data has been refreshed.",
      });
      setTableKey((prev) => prev + 1); // Force table re-render
    } catch (error) {
      console.error("Error reloading data:", error);
      toast.error("Failed to reload data", {
        description: "Please try again later.",
      });
    }
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="text-center">
        <p className="text-muted-foreground">
          Please log in to access asset management.
        </p>
      </div>
    );
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/digital-carpet">
                Digital Carpet
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Asset Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={openCreateForm} className="gap-2 ml-auto">
          <Plus className="h-4 w-4" />
          Add Asset
        </Button>
      </header>
      <main>
        {/* Search and Filters */}
        <div className="p-4">
          <AssetSearchPagination
            searchFilters={searchFilters}
            showDeleted={showDeleted}
            totalRecords={totalRecords}
            totalAllRecords={totalAllRecords}
            activeRecordsCount={activeRecordsCount}
            inactiveRecordsCount={inactiveRecordsCount}
            onSearchChange={updateSearchFilters}
            onClearSearch={clearSearch}
            onToggleShowDeleted={toggleShowDeleted}
          />
        </div>

        {/* Data Table */}
        <div className="p-4 pt-0">
          <AssetDataTable
            key={tableKey}
            data={paginatedAssets}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onEdit={openEditForm}
            onDelete={handleDeleteAsset}
            onToggleActive={handleToggleActive}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
          />
        </div>
      </main>

      {/* Asset Form Sheet */}
      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-1/4 sm:max-w-md overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {editingAsset ? "Edit Asset" : "Create New Asset"}
            </SheetTitle>
            <SheetDescription>
              {editingAsset
                ? "Update the asset information below."
                : "Fill in the details to create a new asset."}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <AssetForm
              key={editingAsset?.id || "new"}
              asset={editingAsset || undefined}
              isLoading={isFormLoading}
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
              onClear={() => {
                // Clear form is handled internally by AssetForm
              }}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
