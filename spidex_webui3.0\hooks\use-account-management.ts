"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  User,
  Role,
  Tenant,
  AccountSearchFilters,
  AccountPaginationParams,
  AccountPageSize,
  DEFAULT_ACCOUNT_PAGE_SIZE,
  ACCOUNT_PAGE_SIZES,
  ACCOUNT_PAGE_SIZE_ALL,
  CreateAccountFormData,
  UpdateAccountFormData,
  PasswordResetFormData,
} from "@/types/account";

interface UseAccountManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: AccountPageSize;
}

export const useAccountManagement = ({
  autoLoad = true,
  defaultPageSize = DEFAULT_ACCOUNT_PAGE_SIZE,
}: UseAccountManagementOptions = {}) => {
  // Get authenticated API instance and session
  const spidexApi = useSpidexApi();
  const { data: session, status } = useSession();

  // State
  const [roles, setRoles] = useState<Role[]>([]);
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);

  // Search and pagination state - ensure all values are properly initialized
  const [searchFilters, setSearchFilters] = useState<AccountSearchFilters>(
    () => ({
      searchTerm: "",
      tenantId: "",
      roleId: "",
      active: undefined,
      deleted: false,
    })
  );

  const [pagination, setPagination] = useState<AccountPaginationParams>({
    pageNumber: 1,
    pageSize: typeof defaultPageSize === "number" ? defaultPageSize : 10,
    sortBy: "name",
    sortOrder: "asc",
  });

  // Client-side pagination - load all data once and paginate in memory
  const [allAccountsCache, setAllAccountsCache] = useState<User[]>([]);

  // Filter accounts based on showDeleted state and search filters
  const filteredAccounts = useMemo(() => {
    console.log("Filtering accounts:", {
      showDeleted,
      totalAccounts: allAccountsCache.length,
      deletedCount: allAccountsCache.filter((acc) => acc.deleted).length,
      activeCount: allAccountsCache.filter((acc) => !acc.deleted).length,
    });

    let filtered = allAccountsCache;

    // First apply showDeleted filter
    if (!showDeleted) {
      // Show only non-deleted accounts
      filtered = filtered.filter((account) => !account.deleted);
      console.log("Filtered out deleted accounts:", filtered.length);
    }

    // Apply search filters
    if (searchFilters.searchTerm) {
      const searchTerm = searchFilters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (account) =>
          account.name.toLowerCase().includes(searchTerm) ||
          account.userId.toLowerCase().includes(searchTerm) ||
          account.email?.toLowerCase().includes(searchTerm) ||
          account.mobileNumber?.includes(searchTerm)
      );
      console.log("Applied search term filter:", filtered.length);
    }

    // Apply active/inactive filter (business logic: ONLY deleted property matters - matches old app)
    if (searchFilters.active !== undefined) {
      if (searchFilters.active) {
        // Show only active accounts (not deleted)
        filtered = filtered.filter((account) => !account.deleted);
      } else {
        // Show only inactive accounts (deleted)
        filtered = filtered.filter((account) => account.deleted);
      }
      console.log(
        `📋 Applied ${searchFilters.active ? "active" : "inactive"} filter:`,
        filtered.length
      );
    }

    // Apply tenant filter
    if (searchFilters.tenantId && searchFilters.tenantId !== "") {
      filtered = filtered.filter(
        (account) => account.tenantId === searchFilters.tenantId
      );
      console.log("Applied tenant filter:", filtered.length);
    }

    // Apply role filter
    if (searchFilters.roleId && searchFilters.roleId !== "") {
      filtered = filtered.filter((account) =>
        account.roles.some((role) => role.id === searchFilters.roleId)
      );
      console.log("Applied role filter:", filtered.length);
    }

    console.log("Final filtered accounts:", filtered.length);
    return filtered;
  }, [allAccountsCache, showDeleted, searchFilters]);

  // Client-side pagination - always use filtered data
  const paginatedAccounts = useMemo(() => {
    console.log("Client-side pagination calculation:", {
      pageSize: pagination.pageSize,
      pageNumber: pagination.pageNumber,
      totalRecords: allAccountsCache.length,
      filteredRecords: filteredAccounts.length,
      showDeleted: showDeleted,
      cacheEmpty: allAccountsCache.length === 0,
    });

    // If filtered accounts is empty, return empty array
    if (filteredAccounts.length === 0) {
      console.log("No accounts to display, returning empty array");
      return [];
    }

    if (pagination.pageSize === ACCOUNT_PAGE_SIZE_ALL) {
      console.log("Showing all filtered accounts:", filteredAccounts.length);
      return filteredAccounts;
    }

    // Client-side pagination on filtered data
    const pageSize = pagination.pageSize as number;
    const startIndex = (pagination.pageNumber - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const result = filteredAccounts.slice(startIndex, endIndex);

    console.log("Client-side pagination result:", {
      startIndex,
      endIndex,
      pageSize,
      totalRecords: allAccountsCache.length,
      filteredRecords: filteredAccounts.length,
      resultCount: result.length,
      showDeleted: showDeleted,
      actualResult: result.map(
        (acc) => `${acc.name}${acc.deleted ? " (deleted)" : ""}`
      ), // Show actual account names with deleted status
    });

    return result;
  }, [
    filteredAccounts,
    pagination.pageNumber,
    pagination.pageSize,
    showDeleted,
  ]);

  // Calculate total pages for client-side pagination using filtered data
  const totalPages = useMemo(() => {
    if (pagination.pageSize === ACCOUNT_PAGE_SIZE_ALL) {
      return 1;
    }

    const currentPageSize = pagination.pageSize as number;
    return Math.ceil(filteredAccounts.length / currentPageSize);
  }, [filteredAccounts.length, pagination.pageSize]);

  // Load all data once for client-side pagination
  const loadData = useCallback(async () => {
    if (status !== "authenticated" || !session?.user?.token) {
      console.log("Account management: User not authenticated", {
        status,
        hasToken: !!session?.user?.token,
      });
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Set authentication in API service
      spidexApi.setAuthToken(session.user.token);
      spidexApi.setTenantId(session.user.tenantId);

      console.log(
        "Loading all account management data for client-side pagination"
      );

      // Load roles and tenants only once (they don't change often)
      let rolesData = roles;
      let tenantsData = tenants;

      if (roles.length === 0 || tenants.length === 0) {
        console.log("Loading roles and tenants...");
        [rolesData, tenantsData] = await Promise.all([
          spidexApi.getAllRoles(1, 1000), // Load all roles once
          spidexApi.getAllTenants(1, 1000), // Load all tenants once
        ]);
        setRoles(rolesData);
        setTenants(tenantsData);
      }

      // Load ALL accounts at once for client-side pagination
      const accountsData = await spidexApi.getAllAccounts(1, 1000);

      console.log(
        `📊 Loaded ${accountsData.length} total accounts for client-side pagination`
      );

      // Enrich accounts with tenant and role names
      const enrichedAccounts = accountsData.map((account: any) => {
        const tenant = tenantsData.find((t: any) => t.id === account.tenantId);
        const accountRoles = account.roles || [];

        return {
          ...account,
          email: account.emailId, // Map emailId to email for consistency
          active: !account.deleted, // Set active based on deleted status (matches business logic)
          tenantName: tenant?.name || "Unknown Tenant",
          roles: accountRoles.map((roleId: string) => {
            const role = rolesData.find((r: any) => r.id === roleId);
            return (
              role || { id: roleId, name: "Unknown Role", permissions: [] }
            );
          }),
        };
      });

      // Store all accounts in cache for client-side pagination (using real API data)
      setAllAccountsCache(enrichedAccounts);

      // Debug: Check actual deleted status from API
      console.log("Real API data - deleted accounts:", {
        total: enrichedAccounts.length,
        deleted: enrichedAccounts.filter((acc) => acc.deleted).length,
        deletedAccounts: enrichedAccounts
          .filter((acc) => acc.deleted)
          .map((acc) => `${acc.name} (${acc.userId})`),
      });

      console.log("Account management data loaded successfully");
      console.log(
        `Cached ${enrichedAccounts.length} accounts for client-side pagination`
      );
    } catch (error) {
      console.error("Error loading account management data:", error);
      setError(error instanceof Error ? error.message : "Failed to load data");
    } finally {
      setIsLoading(false);
    }
  }, [spidexApi, session, status, roles, tenants]);

  // Auto-load data when authenticated (only on initial mount)
  useEffect(() => {
    console.log("Auto-load effect triggered:", {
      autoLoad,
      status,
      hasToken: !!session?.user?.token,
    });
    if (autoLoad && status === "authenticated" && session?.user?.token) {
      console.log("Loading all data for client-side pagination");
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad, status, session?.user?.token]); // Removed loadData dependency to prevent reloading on session changes

  // CRUD Operations
  const createAccount = useCallback(
    async (accountData: CreateAccountFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        // Transform form data to match API format
        const apiPayload = {
          ...accountData,
          id: null, // Required by API for new accounts
          password: accountData.password || "spidex123", // Default password to match old app
          createdBy: session?.user?.userId || "system",
          createdTime: new Date().toISOString(), // ISO string format
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: new Date().toISOString(), // ISO string format
          active: true,
          deleted: false,
        };

        console.log("Creating account with payload:", apiPayload);
        const newAccount = await spidexApi.addAccount(apiPayload);

        // Reload data to get updated list
        await loadData();

        console.log("Account created successfully");
        return newAccount;
      } catch (error) {
        console.error("Error creating account:", error);
        setError(
          error instanceof Error ? error.message : "Failed to create account"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  const updateAccount = useCallback(
    async (accountData: UpdateAccountFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        const updatedAccount = await spidexApi.updateAccount({
          ...accountData,
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        });

        // Reload data to get updated list
        await loadData();

        console.log("Account updated successfully");
        return updatedAccount;
      } catch (error) {
        console.error("Error updating account:", error);
        setError(
          error instanceof Error ? error.message : "Failed to update account"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  const deleteAccount = useCallback(
    async (userId: string) => {
      try {
        setIsLoading(true);
        setError(null);

        await spidexApi.deleteAccount(userId);

        // Reload data to get updated list
        await loadData();

        console.log("Account deleted successfully");
      } catch (error) {
        console.error("Error deleting account:", error);
        setError(
          error instanceof Error ? error.message : "Failed to delete account"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, loadData]
  );

  const resetPassword = useCallback(
    async (passwordData: PasswordResetFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        await spidexApi.passwordReset(passwordData);

        console.log("Password reset successfully");
      } catch (error) {
        console.error("Error resetting password:", error);
        setError(
          error instanceof Error ? error.message : "Failed to reset password"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi]
  );

  // Client-side pagination controls
  const goToPage = useCallback((page: number) => {
    console.log("Going to page:", page);
    setPagination((prev) => ({ ...prev, pageNumber: page }));
  }, []);

  const changePageSize = useCallback(
    (size: AccountPageSize) => {
      console.log(
        "🔄 Changing page size to:",
        size,
        "| Cache:",
        allAccountsCache.length,
        "records"
      );

      // Update pagination state - always reset to page 1
      setPagination((prev) => ({
        ...prev,
        pageSize: size,
        pageNumber: 1,
      }));

      console.log("Page size changed to:", size);
    },
    [allAccountsCache.length]
  );

  const changeSorting = useCallback(
    (sortBy: string, sortOrder: "asc" | "desc") => {
      setPagination((prev) => ({ ...prev, sortBy, sortOrder }));
    },
    []
  );

  // Search controls - Client-side filtering implementation
  const updateSearchFilters = useCallback(
    (filters: Partial<AccountSearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      setPagination((prev) => ({ ...prev, pageNumber: 1 })); // Reset to first page when searching
      console.log("Search filters updated:", filters);
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({
      searchTerm: "",
      tenantId: "",
      roleId: "",
      active: undefined,
      deleted: false,
    });
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
    // Note: No need to reload data for client-side pagination
  }, []);

  // Toggle deleted accounts visibility
  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => {
      const newValue = !prev;
      console.log("Toggling inactive view:", {
        from: prev ? "showing all" : "active only",
        to: newValue ? "showing all" : "active only",
      });
      return newValue;
    });
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
  }, []);

  // Calculate separate counts for active and inactive records (matches old app logic)
  const activeRecordsCount = useMemo(() => {
    return allAccountsCache.filter((account) => !account.deleted).length;
  }, [allAccountsCache]);

  const inactiveRecordsCount = useMemo(() => {
    return allAccountsCache.filter((account) => account.deleted).length;
  }, [allAccountsCache]);

  // Ensure searchFilters is always properly initialized
  const safeSearchFilters = {
    searchTerm: searchFilters.searchTerm || "",
    tenantId: searchFilters.tenantId || "",
    roleId: searchFilters.roleId || "",
    active: searchFilters.active,
    deleted: searchFilters.deleted ?? false,
  };

  return {
    // Data
    accounts: paginatedAccounts,
    allAccounts: allAccountsCache,
    filteredAccounts,
    roles,
    tenants,

    // State
    isLoading,
    error,
    showDeleted,

    // Search and pagination
    searchFilters: safeSearchFilters,
    pagination,
    totalPages,
    totalRecords: filteredAccounts.length, // Use filtered count for display
    totalAllRecords: allAccountsCache.length, // Total count including deleted
    activeRecordsCount, // Count of active (not deleted AND enabled) records
    inactiveRecordsCount, // Count of inactive (deleted OR not enabled) records
    hasNextPage: pagination.pageNumber < totalPages,
    hasPreviousPage: pagination.pageNumber > 1,

    // Actions
    loadData,
    createAccount,
    updateAccount,
    deleteAccount,
    resetPassword,

    // Pagination controls
    goToPage,
    changePageSize,
    changeSorting,

    // Search controls
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,

    // Utilities
    availablePageSizes: [...ACCOUNT_PAGE_SIZES, ACCOUNT_PAGE_SIZE_ALL],
  };
};
