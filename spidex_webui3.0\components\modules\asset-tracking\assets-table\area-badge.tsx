import React from "react";
import CountUp from "react-countup";

export default function AreaBadge({
  label,
  value,
  onClick,
  active,
}: {
  label: string;
  value: number;
  onClick?: () => void;
  active?: boolean;
}) {
  return (
    <button
      className={`rounded-full px-3 py-1 text-xs font-medium border transition mr-2 mb-2
        ${
          active
            ? "bg-blue-600 text-white border-blue-600 font-semibold"
            : "bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200"
        }
      `}
      onClick={onClick}
      type="button"
      aria-label={`Filter by ${label}`}
    >
      {label}: <CountUp end={value} duration={0.8} separator="," />
    </button>
  );
}
