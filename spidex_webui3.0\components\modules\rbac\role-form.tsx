"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Save, X, RotateCcw } from "lucide-react";
import {
  Role,
  Page,
  CreateRoleFormData,
  PermissionMatrix,
  CrudPermission,
  CRUD_PERMISSIONS,
  PERMISSION_ABBREVIATIONS,
} from "@/types/rbac";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

const roleFormSchema = z.object({
  name: z.string().min(1, "Role name is required").max(50, "Role name must be less than 50 characters"),
});

interface RoleFormProps {
  role?: Role;
  pages: Page[];
  isLoading?: boolean;
  onSubmit: (data: CreateRoleFormData) => void;
  onCancel: () => void;
  onClear: () => void;
}

export function RoleForm({
  role,
  pages,
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: RoleFormProps) {
  const [permissionMatrix, setPermissionMatrix] = useState<PermissionMatrix>({});

  const form = useForm<z.infer<typeof roleFormSchema>>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      name: role?.name || "",
    },
  });

  // Initialize permission matrix
  useEffect(() => {
    const matrix: PermissionMatrix = {};
    pages.filter(page => !page.deleted).forEach(page => {
      matrix[page.id] = {
        add: false,
        view: false,
        update: false,
        delete: false,
      };
    });
    setPermissionMatrix(matrix);
  }, [pages]);

  // Update form when role changes
  useEffect(() => {
    if (role) {
      form.reset({
        name: role.name,
      });
    }
  }, [role, form]);

  const handlePermissionChange = (pageId: string, permission: CrudPermission) => {
    setPermissionMatrix(prev => ({
      ...prev,
      [pageId]: {
        ...prev[pageId],
        [permission]: !prev[pageId]?.[permission],
      },
    }));
  };

  const handleSelectAllPermissions = (pageId: string) => {
    setPermissionMatrix(prev => ({
      ...prev,
      [pageId]: {
        add: true,
        view: true,
        update: true,
        delete: true,
      },
    }));
  };

  const handleClearAllPermissions = (pageId: string) => {
    setPermissionMatrix(prev => ({
      ...prev,
      [pageId]: {
        add: false,
        view: false,
        update: false,
        delete: false,
      },
    }));
  };

  const handleFormSubmit = (values: z.infer<typeof roleFormSchema>) => {
    const formData: CreateRoleFormData = {
      name: values.name,
      pagePermissions: permissionMatrix,
    };
    onSubmit(formData);
  };

  const handleClear = () => {
    form.reset();
    const clearedMatrix: PermissionMatrix = {};
    pages.filter(page => !page.deleted).forEach(page => {
      clearedMatrix[page.id] = {
        add: false,
        view: false,
        update: false,
        delete: false,
      };
    });
    setPermissionMatrix(clearedMatrix);
    onClear();
  };

  const getPermissionButtonVariant = (pageId: string, permission: CrudPermission) => {
    return permissionMatrix[pageId]?.[permission] ? "default" : "outline";
  };

  const availablePages = pages.filter(page => !page.deleted);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Role Information</CardTitle>
            <CardDescription>
              Enter the basic information for the role
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter role name"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    A unique name for this role
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Page Permissions */}
        <Card>
          <CardHeader>
            <CardTitle>Page Permissions</CardTitle>
            <CardDescription>
              Select the permissions for each page. C = Create, R = Read, U = Update, D = Delete
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {availablePages.length === 0 ? (
              <p className="text-muted-foreground">No pages available</p>
            ) : (
              <div className="space-y-4">
                {availablePages.map((page) => (
                  <div key={page.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-medium">{page.pageName}</h4>
                        {page.description && (
                          <p className="text-sm text-muted-foreground">{page.description}</p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleSelectAllPermissions(page.id)}
                        >
                          Select All
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleClearAllPermissions(page.id)}
                        >
                          Clear All
                        </Button>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {Object.entries(CRUD_PERMISSIONS).map(([key, permission]) => (
                        <Button
                          key={permission}
                          type="button"
                          variant={getPermissionButtonVariant(page.id, permission)}
                          size="sm"
                          onClick={() => handlePermissionChange(page.id, permission)}
                          className="min-w-[60px]"
                        >
                          {PERMISSION_ABBREVIATIONS[permission]}
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-start gap-4 border-t pt-4">
          <Button type="submit" disabled={isLoading}
            className="flex-1 sm:flex-none bg-green-700 text-white hover:bg-green-800 rounded"
          >
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? "Saving..." : role ? "Update Role" : "Create Role"}
            </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleClear}
            disabled={isLoading}
            className="border bg-white border-gray-700 text-black hover:bg-green-20 rounded"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Clear
          </Button>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="border bg-white border-red-700 text-black hover:bg-red-20 rounded"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            
          </div>
        </div>
      </form>
    </Form>
  );
}
