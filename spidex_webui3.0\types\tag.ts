// Tag Management Types

export interface Tag {
  id: string;
  name: string;
  description: string;
  modelId: string;
  modelName?: string;
  externalId: string;
  status: boolean;
  properties: Record<string, any> | null;
  tenantId: string;
  deleted: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
}

export interface TagModel {
  id: string;
  modelId: string;
  modelName: string | null;
  deviceType: string;
  properties: Record<string, any> | null;
  tenantId: string;
  deleted: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
}

export interface CreateTagFormData {
  name: string;
  description: string;
  modelId: string;
  externalId: string;
  status?: boolean;
}

export interface UpdateTagFormData {
  id: string;
  name: string;
  description: string;
  modelId: string;
  externalId: string;
  status?: boolean;
}

export interface TagSearchFilters {
  searchTerm?: string;
  modelId?: string;
  status?: boolean;
  deleted?: boolean;
  totalRecords?: number;
  activeCount?: number;
  inactiveCount?: number;
}

export interface TagPaginationParams {
  pageNumber: number;
  pageSize: TagPageSize;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export type TagPageSize = 10 | 50 | 100 | "all";

export const DEFAULT_TAG_PAGE_SIZE: TagPageSize = 10;
export const TAG_PAGE_SIZES: TagPageSize[] = [10, 50, 100, "all"];
export const TAG_PAGE_SIZE_ALL: TagPageSize = "all";

// Bulk Tag Upload Types
export interface BulkTagData {
  externalId: string;
  name: string;
  description: string;
  modelId: string;
  status: boolean;
}

export interface BulkTagUploadResult {
  success: boolean;
  message: string;
  createdCount: number;
  failedCount: number;
  errors: string[];
}
