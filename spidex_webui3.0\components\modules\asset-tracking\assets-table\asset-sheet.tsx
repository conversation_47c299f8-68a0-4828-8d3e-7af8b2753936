import React from "react";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>itle,
} from "@/components/ui/sheet";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import AssetTimeline from "./asset-timeline";
import { SensorType } from "@/types/asset-tracking";
import Sensor<PERSON><PERSON> from "./sensor-chart";
import {
  GeolocateControl,
  Map,
  Marker,
  FullscreenControl,
  NavigationControl,
  ScaleControl,
} from "react-map-gl";
import FilledMapPin from "./filled-map-pin";
import { MAPBOX_STYLE } from "@/lib/constants/asset-tracking";

const SENSOR_LIST: SensorType[] = [
  SensorType.TEMPERATURE,
  SensorType.HUMIDITY,
  SensorType.AMBLIGHT,
  SensorType.PRESSURE,
  // SensorType.BATTERY,
  // SensorType.PROXIMITY,
];

export default function AssetSheet({
  open,
  onOpenChange,
  assetName,
  assetId,
  areaGpsPoint,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  assetName: string;
  assetId: string;
  areaGpsPoint?: { latitude: number; longitude: number };
}) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className="w-full sm:w-[70vw] sm:max-w-[70vw] max-w-none overflow-y-auto"
      >
        <SheetHeader className="mr-6">
          <SheetTitle>{assetName}</SheetTitle>
        </SheetHeader>
        <Tabs defaultValue="track" className="mt-4">
          <TabsList className="p-4">
            <TabsTrigger value="track">Track</TabsTrigger>
            <TabsTrigger value="trace">Trace</TabsTrigger>
            <TabsTrigger value="monitor">Monitor</TabsTrigger>
            <TabsTrigger value="command">Command Control</TabsTrigger>
            <TabsTrigger value="configure">Configure</TabsTrigger>
          </TabsList>
          <TabsContent value="track">
            <div className="p-0 h-[60vh] min-h-[320px] flex flex-col">
              {areaGpsPoint &&
              areaGpsPoint.latitude &&
              areaGpsPoint.longitude ? (
                <div className="p-2 mt-4 rounded-lg border bg-card flex flex-col items-center w-full h-full">
                  <div className="mb-2 font-medium text-muted-foreground">
                    Current Location
                  </div>
                  <div className="flex-1 w-full h-full">
                    <TrackMap
                      latitude={areaGpsPoint.latitude}
                      longitude={areaGpsPoint.longitude}
                      assetName={assetName}
                    />
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                  <span>No GPS location available for this asset's area.</span>
                </div>
              )}
            </div>
          </TabsContent>
          <TabsContent value="trace">
            <div className="p-4">
              <AssetTimeline deviceId={assetId} />
            </div>
          </TabsContent>
          <TabsContent value="monitor">
            <div className="p-4 grid grid-cols-1 md:grid-cols-2 gap-6">
              {SENSOR_LIST.map((sensorType: SensorType) => (
                <SensorChart
                  key={sensorType}
                  sensorType={sensorType}
                  deviceLogId={assetId}
                />
              ))}
            </div>
          </TabsContent>
          <TabsContent value="command">
            <div className="p-4 text-muted-foreground">
              Command & Control for {assetName} (coming soon)
            </div>
          </TabsContent>
          <TabsContent value="configure">
            <div className="p-4 text-muted-foreground">
              Configure {assetName} (coming soon)
            </div>
          </TabsContent>
        </Tabs>
      </SheetContent>
    </Sheet>
  );
}

function TrackMap({
  latitude,
  longitude,
  assetName,
}: {
  latitude: number | string;
  longitude: number | string;
  assetName: string;
}) {
  const lat = typeof latitude === "string" ? parseFloat(latitude) : latitude;
  const lng = typeof longitude === "string" ? parseFloat(longitude) : longitude;
  return (
    <div className="w-full h-full rounded overflow-hidden">
      <Map
        initialViewState={{ latitude: lat, longitude: lng, zoom: 14 }}
        mapStyle={MAPBOX_STYLE}
        mapboxAccessToken={process.env.NEXT_PUBLIC_MapboxAccessToken}
        style={{ width: "100%", height: "100%" }}
        minZoom={10}
        maxZoom={20}
      >
        <GeolocateControl position="top-left" />
        <FullscreenControl position="top-left" />
        <NavigationControl position="top-left" />
        <ScaleControl />
        <Marker longitude={lng} latitude={lat} anchor="bottom">
          <div className="relative flex flex-col items-center group cursor-pointer">
            <span className="absolute -top-7 left-1/2 -translate-x-1/2 bg-white text-xs px-2 py-1 rounded shadow border border-gray-200 z-10 whitespace-nowrap">
              {assetName}
            </span>
            <FilledMapPin color="#3b82f6" isHighlighted={true} />
          </div>
        </Marker>
      </Map>
    </div>
  );
}
