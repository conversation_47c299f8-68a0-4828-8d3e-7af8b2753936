export const vendorReportRes = () => {
  return [
    {
      vendorName: 'vendor SWAMI_11',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      shiftName: 'MORNING',
      skillCategory: 'CATEGORY_01',
      workerPackage: 12000,
      inAreaId: 'AREA_123',
      workedMinutes: 680,
    },
    {
      vendorName: 'vendor SWAMI_13',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      shiftName: 'MORNING',
      skillCategory: 'CATEGORY_02',
      workerPackage: 12000,
      inAreaId: 'AREA_123',
      workedMinutes: 680,
    },
    {
      vendorName: 'vendor SWAMI_15',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      shiftName: 'MORNING',
      skillCategory: 'CATEGORY_02',
      workerPackage: 12000,
      inAreaId: 'AREA_123',
      workedMinutes: 680,
    },
    {
      vendorName: 'vendor SWAMI_12',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      shiftName: 'MORNING',
      skillCategory: 'CATEGORY_03',
      workerPackage: 12000,
      inAreaId: 'AREA_123',
      workedMinutes: 680,
    },
    {
      vendorName: 'vendor SWAMI_18',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      shiftName: 'MORNING',
      skillCategory: 'CATEGORY_03',
      workerPackage: 12000,
      inAreaId: 'AREA_123',
      workedMinutes: 680,
    },
  ];
};

export const vendorWorkerReportRes = () => {
  return [
    {
      vendorName: 'vendor SDFC 0122',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      workerName: 'Subodh',
      inTime: '2022-03-07T10:36:17.520Z',
      outTime: '2022-03-07T10:36:17.520Z',
      devicePhyId: 'DEVICE_6756',
      shiftName: 'NIGHT',
      skillCategory: 'CATEGORY_01',
      workerPackage: 30000,
      inAreaId: 'AREA_345678',
      workedHours: 56,
    },
    {
      vendorName: 'vendor SDFC 0123',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      workerName: 'Subodh',
      inTime: '2022-03-07T10:36:17.520Z',
      outTime: '2022-03-07T10:36:17.520Z',
      devicePhyId: 'DEVICE_6756',
      shiftName: 'NIGHT',
      skillCategory: 'CATEGORY_01',
      workerPackage: 30000,
      inAreaId: 'AREA_345678',
      workedHours: 56,
    },
    {
      vendorName: 'vendor SDFC 0124',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      workerName: 'Subodh',
      inTime: '2022-03-07T10:36:17.520Z',
      outTime: '2022-03-07T10:36:17.520Z',
      devicePhyId: 'DEVICE_6756',
      shiftName: 'NIGHT',
      skillCategory: 'CATEGORY_03',
      workerPackage: 30000,
      inAreaId: 'AREA_345678',
      workedHours: 56,
    },
    {
      vendorName: 'vendor SDFC 0126',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      workerName: 'Subodh',
      inTime: '2022-03-07T10:36:17.520Z',
      outTime: '2022-03-07T10:36:17.520Z',
      devicePhyId: 'DEVICE_6756',
      shiftName: 'NIGHT',
      skillCategory: 'CATEGORY_02',
      workerPackage: 30000,
      inAreaId: 'AREA_345678',
      workedHours: 56,
    },
    {
      vendorName: 'vendor SDFC 0177',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      workerName: 'Subodh',
      inTime: '2022-03-07T10:36:17.520Z',
      outTime: '2022-03-07T10:36:17.520Z',
      devicePhyId: 'DEVICE_6756',
      shiftName: 'NIGHT',
      skillCategory: 'CATEGORY_02',
      workerPackage: 30000,
      inAreaId: 'AREA_345678',
      workedHours: 56,
    },
  ];
};

export const areaReportRes = () => {
  return [
    {
      inAreaId: 'AREA_0021',
      vendorName: 'vendor 00BB',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      skillCategory: 'LEVEL_A',
      workedMinutes: 670,
    },
    {
      inAreaId: 'AREA_0022',
      vendorName: 'vendor 00CC',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      skillCategory: 'LEVEL_B',
      workedMinutes: 680,
    },
    {
      inAreaId: 'AREA_0023',
      vendorName: 'vendor 00DD',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      skillCategory: 'LEVEL_C',
      workedMinutes: 690,
    },
    {
      inAreaId: 'AREA_0024',
      vendorName: 'vendor 00EE',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      skillCategory: 'LEVEL_D',
      workedMinutes: 650,
    },
    {
      inAreaId: 'AREA_0025',
      vendorName: 'vendor 00FF',
      eventRawDate: '2022-03-07T10:36:17.520Z',
      skillCategory: 'LEVEL_E',
      workedMinutes: 630,
    },
  ];
};

export const monthlyReportRes = () => {
  return [
    {
      vendorName: 'Vendor Ram',
      billingAmount: 340000,
      scheduledWorkMinutes: 600,
    },
    {
      vendorName: 'Vendor SDF',
      billingAmount: 546789,
      scheduledWorkMinutes: 700,
    },
    {
      vendorName: 'Vendor JKD',
      billingAmount: 300999,
      scheduledWorkMinutes: 650,
    },
    {
      vendorName: 'Vendor GBH',
      billingAmount: 34567,
      scheduledWorkMinutes: 640,
    },
    {
      vendorName: 'Vendor SSD',
      billingAmount: 1000990,
      scheduledWorkMinutes: 500,
    },
  ];
};

export const vehicleReportRes = () => {
  return [
    {
      vendorName: 'Vendor Ram',
      vehicelNumber: 'MH03GG0909',
      devicePhyId: 'DEVICE_AA90',
    },
    {
      vendorName: 'Vendor Shyam',
      vehicelNumber: 'MH04GG0909',
      devicePhyId: 'DEVICE_AA91',
    },
    {
      vendorName: 'Vendor Suman',
      vehicelNumber: 'MH09GG1909',
      devicePhyId: 'DEVICE_AA92',
    },
    {
      vendorName: 'Vendor Raman',
      vehicelNumber: 'MH03GG0919',
      devicePhyId: 'DEVICE_AA93',
    },
    {
      vendorName: 'Vendor OJM',
      vehicelNumber: 'MH03GG0919',
      devicePhyId: 'DEVICE_AA94',
    },
  ];
};
