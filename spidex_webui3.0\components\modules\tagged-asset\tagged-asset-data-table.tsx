"use client";

import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  RotateCcw,
  Package,
  Tag,
} from "lucide-react";
import { TaggedAsset } from "@/types/tagged-asset";
import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

// Import pagination component - we'll create a simple one similar to Branch Management
import { TaggedAssetTablePagination } from "./tagged-asset-table-pagination";

interface TaggedAssetDataTableProps {
  data: TaggedAsset[];
  isLoading?: boolean;
  onEdit: (taggedAsset: TaggedAsset) => void;
  onDelete: (id: string) => void;
  onToggleStatus: (taggedAsset: TaggedAsset) => void;

  // Pagination props
  pagination: { page: number; pageSize: number };
  totalRecords: number;
  totalPages: number;
  availablePageSizes: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
}

export function TaggedAssetDataTable({
  data,
  isLoading = false,
  onEdit,
  onDelete,
  onToggleStatus,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
}: TaggedAssetDataTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [taggedAssetToDelete, setTaggedAssetToDelete] =
    useState<TaggedAsset | null>(null);

  const handleDeleteClick = (taggedAsset: TaggedAsset) => {
    setTaggedAssetToDelete(taggedAsset);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (taggedAssetToDelete) {
      onDelete(taggedAssetToDelete.id);
      setDeleteDialogOpen(false);
      setTaggedAssetToDelete(null);
    }
  };

  const columns: ColumnDef<TaggedAsset>[] = [
    {
      accessorKey: "taggedAssetInfo.assetName",
      header: "Asset Name",
      cell: ({ row }) => {
        const taggedAsset = row.original;
        return (
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 text-muted-foreground" />
            <div className="flex flex-col">
              <span className="font-medium">
                {taggedAsset.taggedAssetInfo.assetName}
              </span>
              <span className="text-sm text-muted-foreground">
                {taggedAsset.taggedAssetInfo.assetExternalId}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "taggedAssetInfo.tagName",
      header: "Tag Name",
      cell: ({ row }) => {
        const taggedAsset = row.original;
        return (
          <div className="flex items-center space-x-2">
            <Tag className="h-4 w-4 text-muted-foreground" />
            <div className="flex flex-col">
              <span className="text-sm text-muted-foreground">
                {taggedAsset.taggedAssetInfo.tagExternalId}
              </span>
            </div>
          </div>
        );
      },
    },

    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const taggedAsset = row.original;
        const isActive =
          taggedAsset.status === "ACTIVE" || taggedAsset.status === "TAGGED";
        return (
          <Badge
            variant={isActive ? "default" : "secondary"}
            className={cn(
              "cursor-pointer hover:opacity-80",
              isActive
                ? "bg-green-600 hover:bg-green-700"
                : "bg-gray-500 hover:bg-gray-600"
            )}
          >
            {taggedAsset.status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "provisioned",
      header: "Provisioned",
      cell: ({ row }) => {
        const provisioned = row.getValue("provisioned") as boolean;
        return (
          <Badge
            variant={provisioned ? "default" : "secondary"}
            className={cn(
              provisioned
                ? "bg-blue-600 hover:bg-blue-700"
                : "bg-gray-500 hover:bg-gray-600"
            )}
          >
            {provisioned ? "Yes" : "No"}
          </Badge>
        );
      },
    },

    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const taggedAsset = row.original;

        return (
          <div className="flex items-center space-x-2">
            {taggedAsset.deleted ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onToggleStatus(taggedAsset)}
                className="h-8 px-2"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Activate
              </Button>
            ) : (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => onEdit(taggedAsset)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => handleDeleteClick(taggedAsset)}
                    className="text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div className="space-y-4">
      {/* Table */}
      <DataTable
        columns={columns}
        data={data}
        isLoading={isLoading}
        rowClassName={() =>
          cn(
            "[&_tr[data-state=selected]]:bg-muted/50",
            "[&_tr:hover]:bg-muted/50"
          )
        }
      />

      {/* Pagination */}
      <TaggedAssetTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              tagged asset "{taggedAssetToDelete?.taggedAssetInfo.assetName}"
              linked to tag "{taggedAssetToDelete?.taggedAssetInfo.tagName}".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
