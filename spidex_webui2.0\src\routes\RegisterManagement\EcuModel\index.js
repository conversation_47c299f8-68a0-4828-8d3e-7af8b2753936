import { useEffect, useState, useContext } from 'react';
import {
  Input,
  Checkbox,
  Table,
  Button,
  Form,
  Popconfirm,
  CommonDrawer,
  CommonCompactView,
  message,
  Row,
  Col,
  Pagination,
  AutoComplete,
  Select,
  Drawer,
  Typography,
  Tooltip,
  Tag,
} from '../../../components';
import {
  getAllModelEcuByPagination,
  addModelEcu,
  getSearchVehicleModel,
  getAllEcu,
  getVehicleModel,
  updateModelEcu,
  deleteModelEcu,
  getAllVehicleModel,
} from '../../../services';
import Context from '../../../context';
import { PlusOutlined } from '@ant-design/icons';
import { BreadcrumbList, PermissionContainer } from '../../../shared';
import { buildCommonApiValues } from '../../../utils';
import { get, debounce } from 'lodash';
import { CRUD, Pages, ModuleNames, MastersPageSizeDefault, VehicleManufacturers } from '../../../constants';
import s from './ecuModel.module.less';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

const EcuModel = () => {
  const [context, setContext] = useContext(Context);
  const [action, setAction] = useState('new');
  const [totalModelEcus, setTotalModelEcus] = useState({
    items: 0,
    current: 0,
    pageSize: MastersPageSizeDefault,
  });
  const [showDeleted, setShowDeleted] = useState(false);
  const [visible, setVisible] = useState(false);
  const [modelEcus, setModelEcus] = useState([]);
  const [modelEcusOriginal, setModelEcusOriginal] = useState([]);
  const [modelEcusInfo, setModelEcusInfo] = useState({});
  const [modelEcuInfo, setModelEcuInfo] = useState(false);
  const [vehicleModels, setVehicleModels] = useState([]);
  const [ecusInfo, setEcusInfo] = useState([]);
  const [selectedEcusInfo, setSelectedEcusInfo] = useState([]);
  const [selectedEcus, setSelectedEcus] = useState([]);
  const [allVehicleModels, setAllVehicleModels] = useState([]);

  const [form] = Form.useForm();
  const [formEcu] = Form.useForm();

  const formInitState = {};
  const [formInitValues, setFormInitValues] = useState({ ...formInitState });

  const openAdd = () => {
    setAction('new');
    setVisible(true);
    setSelectedEcusInfo([]);
    setSelectedEcus([]);
  };

  const finishAdd = async (type) => {
    try {
      const values = await form.validateFields();
      const commonValues = buildCommonApiValues(context.profile);
      const modelFound = allVehicleModels.find((x) => x.id === parseInt(autoSearchId));
      if (action === 'new') {
        await saveModelEcuAction({
          createdTime: commonValues.createdTime,
          modifiedTime: commonValues.modifiedTime,
          modifiedBy: commonValues.modifiedBy,
          createdBy: commonValues.createdBy,
          deleted: commonValues.deleted,
          ...values,
          vehicleModel: parseInt(autoSearchId),
          modelName: modelFound.model + '-' + modelFound.variant,
        });
        if (type === 'save') setVisible(false);
        if (type === 'add') form.resetFields();
      }
      if (action === 'edit') {
        updateModelEcuCall(values);
        if (type === 'add') form.resetFields({});
        setFormInitValues(null);
        setVisible(false);
      }
    } catch (e) {
      console.log(e);
    }
  };

  //Save model ECU data
  const saveModelEcuAction = async (modelEcu) => {
    const ecuObjects = [];
    selectedEcus.forEach((ecuId) => {
      const foundEcu = selectedEcusInfo.find((x) => x.id === ecuId);
      if (foundEcu) ecuObjects.push(foundEcu);
    });

    modelEcu.ecus = ecuObjects;
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addModelEcu(modelEcu)
      .then((res) => {
        setModelEcus((state) => [res.data, ...state]);
        message.success('Succesfully Added Model ECU');
        form.resetFields();
        setSelectedEcusInfo([]);
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        if (e.response.data.debugMessage) {
          message.error('Vehicle model with ECUs are already existed');
          setVisible(true);
        } else {
          message.error('Unable to add  model ECU, try again later');
        }
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const closeAdd = () => {
    setVisible(false);
    setModelEcuInfo(false);
    if (action === 'edit') form.resetFields();
  };

  //Child drawer close
  const closeAddEcu = () => {
    setChildrenDrawer(false);
    if (action === 'edit') form.resetFields();
  };

  const clearForm = () => {
    form.resetFields();
    setSelectedEcusInfo([]);
  };

  const clearEcuForm = () => {
    formEcu.resetFields();
  };

  //update model ECU data
  const editModelEcuAction = async (modelEcuData) => {
    const vehicleModelRes = await getVehicleModel(modelEcuData.vehicleModel);
    form.setFieldsValue({
      ...modelEcuData,
      vehicleModel: vehicleModelRes.data.model + '-' + vehicleModelRes.data.variant,
    });
    setAction('edit');
    setModelEcusInfo(modelEcuData);
    setSelectedEcusInfo(modelEcuData.ecus);
    setVisible(true);
  };

  const updateModelEcuCall = (values) => {
    const data = {
      ...modelEcusInfo,
      ...values,
      ecus: selectedEcusInfo,
      modifiedTime: new Date(),
      vehicleModel: modelEcusInfo.vehicleModel,
    };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateModelEcu(data)
      .then((res) => {
        setModelEcus((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, res.data);
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        setSelectedEcusInfo([]);
        message.success('Succesfully Updated Model ECU');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update model ECU, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const makeActive = (data) => {
    updateModelEcuCall({ ...data, deleted: false });
  };

  //delete the vehicle modal data
  const setDeleteModelEcu = (modelEcuId, visible) => {
    setModelEcus((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === modelEcuId).visible = visible;
      tempData
        .filter((x) => x.id !== modelEcuId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteModelEcuAction = (modelEcuId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteModelEcu(modelEcuId)
      .then(() => {
        setModelEcus((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === modelEcuId).visible = false;
          tempState.find((x) => x.id === modelEcuId).deleted = true;
          return [...state].filter((x) => x.id !== modelEcuId);
        });
        message.success('Succesfully Deleted Model ECU');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete model ECU, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  //view model ECU information
  const viewmodelEcuInfo = async (e) => {
    try {
      const vehicleModelRes = await getVehicleModel(e.vehicleModel);
      setModelEcusInfo({ ...e, vehicleModelName: vehicleModelRes.data.model });
      setModelEcuInfo(true);
    } catch (e) {
      console.log(e);
      message.error('Unable to get model ECU details, try again later');
    }
  };

  //For ecu data edit
  const finishAddForEcus = async (type) => {
    if (type === 'save') {
      const data = await formEcu.validateFields();
      setSelectedEcusInfo((ps) => {
        let temp = [...ps];
        let foundEcu = temp.find((ecu) => ecu.id === data.id);
        foundEcu = { ...foundEcu, ...data };
        temp = [...temp.filter((ecu) => ecu.id !== data.id)];
        temp = [...temp, foundEcu];
        return temp;
      });
      onSelectEcu(data, true);
    }
    setChildrenDrawer(false);
  };
  const [childrenDrawer, setChildrenDrawer] = useState(false);

  const onChildrenDrawerClose = () => {
    setChildrenDrawer(false);
  };

  //Update all ecus
  const editEcuAction = async (ecu) => {
    formEcu.setFieldsValue({ ...ecu });
    setChildrenDrawer(true);
  };

  //get all model ECU data by pagination
  const onPaginationChange = (e) => {
    init(+e - 1);
  };

  const init = async (page = 0) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const pagination = {
      size: totalModelEcus.pageSize,
      page: page,
    };

    try {
      const modelEcuRes = await getAllModelEcuByPagination(pagination, showDeleted);
      const modelEcuData = modelEcuRes.data?.content || [];
      const allEcusRes = await getAllEcu();
      const allEcusData = allEcusRes.data.filter((x) => x.deleted === false) || [];
      const allVehicleModelsRes = await getAllVehicleModel();
      const allVehicleModelsData = allVehicleModelsRes.data;
      setAllVehicleModels(allVehicleModelsData);
      setEcusInfo(allEcusData);
      setModelEcus(modelEcuData);
      setModelEcusOriginal(modelEcuData);
      setTotalModelEcus((ps) => ({ ...ps, current: page, items: modelEcuRes.data.totalElements }));
    } catch (e) {
      console.log(e);
      message.error('Unable to get model ECU details, try again later');
    } finally {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
    }
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, [showDeleted]);

  //If it is ecu model table set the tableModeNormal to true
  //If it is search table set the tableModeNormal to false
  const [tableModeNormal, setTableModeNormal] = useState(true);
  //onSearch model ecu data with vehicle manufacturer
  const onSearchModelEcu = async (e) => {
    if (!e.target.value) {
      setModelEcus(modelEcusOriginal);
      setTableModeNormal(true);
    } else {
      try {
        setModelEcus(() => {
          const tempData = [...modelEcus];
          return tempData
            .filter((x) => x.vehicleManufacturer.toLowerCase().includes(e.target.value.toLowerCase()))
            .filter((x) => x.deleted === false);
        });
        setTableModeNormal(false);
      } catch {
        message.error('Could not retrieve Model ECU');
      }
    }
  };

  //search for vehicle model input field
  const debouncedSearchVehicleModel = debounce(async (value) => {
    try {
      const vehicleModel = await getSearchVehicleModel(value.toLowerCase());
      const vehicleModelData = vehicleModel.data.filter((x) => x.deleted === false);
      setVehicleModels(vehicleModelData);
    } catch (e) {
      console.log(e);
      message.error('Unable to get model ECU details, try again later');
    }
  }, 500);

  //Based on vehicle model selected getting the list of ECUs
  const [autoSearchId, setAutoSearchId] = useState(0);
  const onSelect = (val, option) => {
    setAutoSearchId(option.key.split('-')[0]);
    const vehicleManufacturer = option.key.split('-')[1];
    const formData = form.getFieldsValue();
    form.setFieldsValue({ ...formData, vehicleManufacturer: vehicleManufacturer });
    setSelectedEcusInfo([...ecusInfo.filter((x) => x.vehicleManufacturer === vehicleManufacturer)]);
  };

  //Selecting the ECUs with checkbox
  //validations , if the more than one same vehicle model have the same ECUs
  const onSelectEcu = (ecu, alwaysAdd = false) => {
    setSelectedEcus((ps) => {
      let temp = [...ps];
      if (temp.includes(ecu.id) && !alwaysAdd) temp = [...temp.filter((x) => x !== ecu.id)];
      else temp = [...temp, ecu.id];
      return temp;
    });
  };

  const tableCols = [
    {
      title: <strong>Model</strong>,
      render: (record) => record.modelName || 'NA',
    },
    {
      title: <strong>ECUs</strong>,
      render: (record) => (
        <>
          <Tooltip placement="right" title={record?.ecus?.map((x) => [x.ecuName]).join(',')}>
            {record?.ecus
              ?.map((x) => [x.ecuName])
              .slice(0, 2)
              .join(',')}
            {record.ecus.length > 2 ? (
              <>
                <Tag color="blue" className="ml-1">{`+${record.ecus.length - 2} more`}</Tag>
              </>
            ) : (
              ''
            )}
          </Tooltip>
        </>
      ),
    },
    { title: <strong>Vehicle Manufacturer </strong>, key: 'vehicleManufacturer', dataIndex: 'vehicleManufacturer' },
    {
      title: <strong> Actions </strong>,
      width: 350,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <Button type="link" className="actionButton" onClick={() => makeActive(record)}>
                Activate
              </Button>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.ECU_MODEL} permission={CRUD.VIEW}>
              <Button type="link" className="actionButton" onClick={() => viewmodelEcuInfo(record)}>
                View
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.ECU_MODEL} permission={CRUD.UPDATE}>
              <Button type="link" className="actionButton" onClick={() => editModelEcuAction(record)}>
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.ECU_MODEL} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete model ECU ${record.vehicleManufacturer}?`}
                visible={record.visible || false}
                onConfirm={() => deleteModelEcuAction(record.id)}
                onCancel={() => setDeleteModelEcu(record.id, false)}
              >
                <Button type="link" className="actionButton" onClick={() => setDeleteModelEcu(record.id, true)}>
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const ecuTableCol = [
    {
      title: '',
      render: (ecu) => (
        <Checkbox
          onChange={() => onSelectEcu(ecu)}
          checked={selectedEcus.includes(ecu.id)}
          disabled={action === 'edit' ? true : false}
        ></Checkbox>
      ),
    },
    { title: <strong>Name</strong>, key: 'ecuName', dataIndex: 'ecuName' },
    {
      title: <strong> Actions </strong>,
      key: 'Actions',
      render: (record) => (
        <PermissionContainer page={Pages.ECU_MODEL} permission={CRUD.UPDATE}>
          <Button type="link" className="actionButton" onClick={() => editEcuAction(record)}>
            Edit
          </Button>
        </PermissionContainer>
      ),
    },
  ];

  //ECUs table for view
  const viewEcusCol = [
    { title: <strong>Name</strong>, render: (record) => record.ecuName },
    { title: <strong>Version</strong>, render: (record) => record.ecuVersion },
    { title: <strong>Make </strong>, render: (record) => record.ecuMake },
    { title: <strong>External ID</strong>, render: (record) => record.ecuExtId },
    { title: <strong>Model </strong>, render: (record) => record.ecuModel },
    { title: <strong>File Name</strong>, render: (record) => record.fileName },
    { title: <strong>File Type</strong>, render: (record) => record.fileType },
    { title: <strong>File Location</strong>, render: (record) => record.fileLocation },
    { title: <strong>Sftp Url </strong>, render: (record) => record.sftpUrl },
    { title: <strong>User Name</strong>, render: (record) => record.userName },
    { title: <strong>Password</strong>, render: (record) => record.password },
  ];

  const ecuModelBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.ECU_MODEL}].displayName`,
    ModuleNames.ECU_MODEL
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.MASTERS, ecuModelBreadcrumbsName, 'Register ECU Model']} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.ECU_MODEL} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Model ECU
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <Row className="searchDiv" justify="end">
        <Col xs={24} lg={6}>
          <Search placeholder="Enter Vehicle Manufacturer" allowClear enterButton onChange={onSearchModelEcu} />
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <>
            {tableModeNormal ? (
              <>
                <Table
                  size="small"
                  scroll={{ x: true }}
                  rowKey="id"
                  columns={tableCols}
                  dataSource={modelEcus}
                  rowClassName={(record) => record.deleted && 'rowInactive'}
                  pagination={false}
                />
                <Row justify="end">
                  <Col>
                    <div className="m-2">
                      <Pagination
                        onChange={onPaginationChange}
                        current={totalModelEcus.current + 1}
                        total={totalModelEcus.items}
                        defaultPageSize={totalModelEcus.pageSize}
                        showSizeChanger={false}
                        showQuickJumper={false}
                      />
                    </div>
                  </Col>
                </Row>
              </>
            ) : (
              <Table
                size="small"
                scroll={{ x: true }}
                rowKey="id"
                columns={tableCols}
                dataSource={modelEcus}
                rowClassName={(record) => record.deleted && 'rowInactive'}
              />
            )}
          </>
        ) : (
          <>
            <CommonCompactView
              data={modelEcus}
              onEdit={editModelEcuAction}
              onDelete={deleteModelEcuAction}
              permissions={[
                { pageName: Pages.ECU_MODEL, permission: CRUD.UPDATE, label: 'Edit' },
                { pageName: Pages.ECU_MODEL, permission: CRUD.DELETE, label: 'Delete' },
              ]}
              title="vehicleManufacturer"
              dataList={[{ label: 'Vehicle Manufacturer', value: 'vehicleManufacturer' }]}
            />
            <Row justify="end">
              <Col>
                <div className="m-2">
                  <Pagination
                    onChange={onPaginationChange}
                    current={totalModelEcus.current + 1}
                    total={totalModelEcus.items}
                    defaultPageSize={totalModelEcus.pageSize}
                    showSizeChanger={false}
                    showQuickJumper={false}
                  />
                </div>
              </Col>
            </Row>
          </>
        )}
      </>

      <CommonDrawer title="Model ECU Information" visible={modelEcuInfo} closeAdd={closeAdd}>
        <div className="infoDrawer">
          {modelEcusInfo ? (
            <Row className="infoContainer" span={24}>
              <Col className="info" span={24}>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Vehicle Manufacturer :
                  </Col>
                  <Col>{modelEcusInfo.vehicleManufacturer}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Vehicle Model :
                  </Col>
                  <Col>{modelEcusInfo.vehicleModelName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Model :
                  </Col>
                  <Col>{modelEcusInfo.modelName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Deleted :
                  </Col>
                  <Col>{String(modelEcusInfo.deleted)}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created By :
                  </Col>
                  <Col>{modelEcusInfo.createdBy}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created Time :
                  </Col>
                  <Col>{new Date(modelEcusInfo.createdTime).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified Time :
                  </Col>
                  <Col>{new Date(modelEcusInfo.modifiedTime).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified By :
                  </Col>
                  <Col>{modelEcusInfo.modifiedBy}</Col>
                </Row>
                <Table
                  size="small"
                  scroll={{ x: true }}
                  rowKey="id"
                  columns={viewEcusCol}
                  dataSource={modelEcusInfo.ecus}
                  rowClassName={(record) => record.deleted && 'rowInactive'}
                  pagination={false}
                  className="m-3"
                />
                <br />
                <Row justify="center">
                  <Button type="primary" onClick={() => editModelEcuAction(modelEcusInfo)}>
                    Edit
                  </Button>
                </Row>
              </Col>
            </Row>
          ) : (
            <></>
          )}
        </div>
      </CommonDrawer>

      <CommonDrawer title="Model ECU" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          autoComplete={'new-password'}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 16 }}
          labelCol={{ span: 8 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Vehicle Model"
            name="vehicleModel"
            rules={[
              {
                required: true,
                message: 'Please input vehicle model!',
              },
            ]}
          >
            <AutoComplete
              onSelect={(val, option) => {
                onSelect(val, option);
              }}
              onChange={(e) => (e ? '' : setSelectedEcusInfo([]))}
              onSearch={debouncedSearchVehicleModel}
              placeholder="Vehicle Model"
              disabled={action === 'edit' ? true : false}
              readOnly={true}
            >
              {vehicleModels.map((a) => (
                <AutoComplete.Option key={`${a.id}-${a.vehicleManufacturer}`} value={`${a.model}-${a.variant}`}>
                  {a.model} - {a.variant}
                </AutoComplete.Option>
              ))}
            </AutoComplete>
          </Form.Item>
          <Form.Item hidden={true} name="vehicleManufacturer"></Form.Item>
        </Form>
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="id"
          columns={ecuTableCol}
          dataSource={selectedEcusInfo}
          rowClassName={(record) => record.deleted && 'rowInactive'}
          pagination={false}
          className="m-3"
        />
        <Row gutter={6}>
          <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
            <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
              Save
            </Button>
          </Col>
          <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
            <div>
              {action === 'new' && (
                <Col>
                  <Button block onClick={() => finishAdd('add')}>
                    Save + 1
                  </Button>
                </Col>
              )}
            </div>
          </Col>
        </Row>
        <Row className="footer" gutter={6}>
          <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
            <Button block className={['clearButton', !context.isCompact ? 'clear' : null]} onClick={() => clearForm()}>
              Clear
            </Button>
          </Col>
          <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
            <Button block danger type="primary" onClick={closeAdd}>
              Close
            </Button>
          </Col>
        </Row>
        <Drawer
          title={<Title className={s.title}>Ecu</Title>}
          width={context.isCompact ? 400 : 550}
          visible={childrenDrawer}
          onClose={onChildrenDrawerClose}
          className={s.drawer}
        >
          <Form
            initialValues={formInitValues}
            scrollToFirstError={true}
            autoComplete={'new-password'}
            layout="horizontal"
            form={formEcu}
            wrapperCol={{ span: 16 }}
            labelCol={{ span: 8 }}
          >
            <Form.Item hidden={true} name="id" />
            <Form.Item shouldUpdate={true} hasFeedback label="Name" name="ecuName">
              <Input placeholder="Name" disabled={true} />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="External ID" name="ecuExtID">
              <Input placeholder="External ID" disabled={true} />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="Model" name="ecuModel">
              <Input placeholder="Model" disabled={true} />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="Make" name="ecuMake">
              <Input placeholder="Make" disabled={true} />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="Vehicle Manufacturer" name="vehicleManufacturer">
              <Select placeholder="Vehicle Manufacturer" disabled={true}>
                {VehicleManufacturers.map((m) => (
                  <Option title={m} key={m} value={m}>
                    {m}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="Software Version" name="ecuVersion">
              <Input placeholder="Software Version" />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="File Name" name="fileName">
              <Input placeholder="File Name" />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="File Type" name="fileType">
              <Input placeholder="File Tye" />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="File Location" name="fileLocation">
              <Input placeholder="File Location" />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="User Name" name="userName">
              <Input placeholder="User Name" />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="Password" name="password">
              <Input placeholder="Password" />
            </Form.Item>
            <Form.Item shouldUpdate={true} hasFeedback label="Sftp Url" name="sftpUrl">
              <Input placeholder="Sftp Url" />
            </Form.Item>
            <Row gutter={6}>
              <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
                <Button block className="commonSaveButton formButton" onClick={() => finishAddForEcus('save')}>
                  Save
                </Button>
              </Col>
            </Row>
            <Row className="footer" gutter={6}>
              <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
                <Button
                  block
                  className={['clearButton', !context.isCompact ? 'clear' : null]}
                  onClick={() => clearEcuForm()}
                >
                  Clear
                </Button>
              </Col>
              <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
                <Button block danger type="primary" onClick={closeAddEcu}>
                  Close
                </Button>
              </Col>
            </Row>
          </Form>
        </Drawer>
      </CommonDrawer>
    </>
  );
};

export default EcuModel;
