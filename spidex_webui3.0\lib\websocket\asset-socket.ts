"use client";

import { Client, IMessage, StompSubscription } from "@stomp/stompjs";
import SockJ<PERSON> from "sockjs-client";
import {
  WebSocketMessage,
  SocketSubscription,
  SensorType,
} from "@/types/asset-tracking";
import {
  WEBSOCKET_CONFIG,
  SOCKET_TOPICS,
} from "@/lib/constants/asset-tracking";

export class AssetWebSocketManager {
  private stompClient: Client | null = null;
  private subscriptions: Map<string, SocketSubscription> = new Map();
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private socketUrl: string;
  private onConnectionChange?: (connected: boolean) => void;
  private onError?: (error: string) => void;

  constructor(socketUrl: string) {
    this.socketUrl = socketUrl;
  }

  public setConnectionChangeCallback(callback: (connected: boolean) => void) {
    this.onConnectionChange = callback;
  }

  public setErrorCallback(callback: (error: string) => void) {
    this.onError = callback;
  }

  public async connect(): Promise<boolean> {
    if (this.isConnecting || this.isConnected()) {
      return true;
    }

    this.isConnecting = true;

    try {
      // Create SockJS connection (matches old application exactly)
      const sockJSUrl = `${this.socketUrl}/spdxsocket`;
      console.log("Connecting to SockJS URL:", sockJSUrl);

      this.stompClient = new Client({
        webSocketFactory: () => {
          const sockjs = new SockJS(sockJSUrl);
          console.log("SockJS instance created:", sockjs);
          return sockjs;
        },
        connectHeaders: {},
        debug:
          process.env.NODE_ENV === "development"
            ? (str) => console.log("STOMP Debug:", str)
            : () => {},
        reconnectDelay: WEBSOCKET_CONFIG.RECONNECT_INTERVAL,
        heartbeatIncoming: WEBSOCKET_CONFIG.HEARTBEAT_INTERVAL,
        heartbeatOutgoing: WEBSOCKET_CONFIG.HEARTBEAT_INTERVAL,
        // Allow SockJS to choose the best transport (WebSocket, XHR, etc.)
        forceBinaryWSFrames: false,
        splitLargeFrames: true,
      });

      return new Promise((resolve, reject) => {
        const connectTimeout = setTimeout(() => {
          this.isConnecting = false;
          reject(new Error("Connection timeout"));
        }, WEBSOCKET_CONFIG.CONNECTION_TIMEOUT);

        this.stompClient!.onConnect = (frame) => {
          clearTimeout(connectTimeout);
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          console.log("WebSocket connected:", frame);

          this.startHeartbeat();
          this.onConnectionChange?.(true);
          resolve(true);
        };

        this.stompClient!.onStompError = (frame) => {
          clearTimeout(connectTimeout);
          this.isConnecting = false;
          console.error("WebSocket STOMP error:", frame);

          this.onError?.(frame.headers?.message || "STOMP connection failed");
          this.onConnectionChange?.(false);
          this.scheduleReconnect();
          reject(
            new Error(frame.headers?.message || "STOMP connection failed")
          );
        };

        this.stompClient!.onWebSocketError = (error) => {
          clearTimeout(connectTimeout);
          this.isConnecting = false;
          console.error("WebSocket error:", error);

          this.onError?.("WebSocket connection failed");
          this.onConnectionChange?.(false);
          this.scheduleReconnect();
          reject(new Error("WebSocket connection failed"));
        };

        this.stompClient!.onDisconnect = () => {
          this.onConnectionChange?.(false);
          if (
            this.reconnectAttempts < WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS
          ) {
            this.scheduleReconnect();
          }
        };

        this.stompClient!.activate();
      });
    } catch (error) {
      this.isConnecting = false;
      console.error("Failed to create WebSocket connection:", error);
      this.onError?.("Failed to create connection");
      return false;
    }
  }

  public disconnect(): void {
    this.clearReconnectTimer();
    this.clearHeartbeatTimer();

    // Unsubscribe from all topics
    this.subscriptions.forEach((subscription) => {
      try {
        subscription.unsubscribe();
      } catch (error) {
        console.error("Error unsubscribing:", error);
      }
    });
    this.subscriptions.clear();

    // Disconnect STOMP client
    if (this.stompClient?.connected) {
      try {
        this.stompClient.deactivate();
        console.log("WebSocket disconnected");
      } catch (error) {
        console.error("Error disconnecting:", error);
      }
    }

    this.stompClient = null;
    this.onConnectionChange?.(false);
  }

  public isConnected(): boolean {
    return this.stompClient?.connected === true;
  }

  public subscribeToProximityData(
    gatewayId: string,
    callback: (data: WebSocketMessage) => void
  ): string {
    const topic = SOCKET_TOPICS.PROXIMITY(gatewayId);
    const subscriptionId = `proximity_${gatewayId}`;

    return this.subscribe(subscriptionId, topic, callback);
  }

  public subscribeToGpsData(
    gatewayId: string,
    callback: (data: WebSocketMessage) => void
  ): string {
    const topic = SOCKET_TOPICS.GPS(gatewayId);
    const subscriptionId = `gps_${gatewayId}`;

    return this.subscribe(subscriptionId, topic, callback);
  }

  public subscribeToSensorData(
    sensorType: SensorType,
    deviceId: string,
    callback: (data: WebSocketMessage) => void
  ): string {
    const topic = SOCKET_TOPICS.SENSOR(sensorType, deviceId);
    const subscriptionId = `sensor_${sensorType}_${deviceId}`;

    return this.subscribe(subscriptionId, topic, callback);
  }

  public subscribeToMultipleSensors(
    deviceId: string,
    sensorTypes: SensorType[],
    callback: (data: WebSocketMessage) => void
  ): string[] {
    return sensorTypes.map((sensorType) =>
      this.subscribeToSensorData(sensorType, deviceId, callback)
    );
  }

  private subscribe(
    subscriptionId: string,
    topic: string,
    callback: (data: WebSocketMessage) => void
  ): string {
    if (!this.isConnected()) {
      throw new Error("WebSocket not connected");
    }

    // Unsubscribe existing subscription if any
    this.unsubscribe(subscriptionId);

    try {
      const subscription = this.stompClient!.subscribe(
        topic,
        (message: IMessage) => {
          try {
            const data: WebSocketMessage = JSON.parse(message.body);
            callback(data);
          } catch (error) {
            console.error("Error parsing WebSocket message:", error);
            this.onError?.("Invalid message format received");
          }
        }
      );

      const socketSubscription: SocketSubscription = {
        id: subscriptionId,
        topic,
        callback,
        unsubscribe: () => {
          try {
            subscription.unsubscribe();
          } catch (error) {
            console.error("Error unsubscribing from topic:", topic, error);
          }
        },
      };

      this.subscriptions.set(subscriptionId, socketSubscription);
      console.log(`Subscribed to topic: ${topic}`);

      return subscriptionId;
    } catch (error) {
      console.error("Error subscribing to topic:", topic, error);
      throw error;
    }
  }

  public unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.unsubscribe();
      this.subscriptions.delete(subscriptionId);
      console.log(`Unsubscribed from: ${subscription.topic}`);
    }
  }

  public unsubscribeAll(): void {
    this.subscriptions.forEach((subscription, id) => {
      this.unsubscribe(id);
    });
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS) {
      console.error("Max reconnection attempts reached");
      this.onError?.("Connection lost. Please refresh the page.");
      return;
    }

    this.clearReconnectTimer();

    const delay = Math.min(
      WEBSOCKET_CONFIG.RECONNECT_INTERVAL * Math.pow(2, this.reconnectAttempts),
      30000 // Max 30 seconds
    );

    console.log(
      `Scheduling reconnect in ${delay}ms (attempt ${
        this.reconnectAttempts + 1
      })`
    );

    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect().catch((error) => {
        console.error("Reconnection failed:", error);
      });
    }, delay);
  }

  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private startHeartbeat(): void {
    this.clearHeartbeatTimer();

    this.heartbeatTimer = setInterval(() => {
      if (!this.isConnected()) {
        console.log("Connection lost, attempting to reconnect...");
        this.scheduleReconnect();
      }
    }, WEBSOCKET_CONFIG.HEARTBEAT_INTERVAL);
  }

  private clearHeartbeatTimer(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  public getConnectionStatus(): {
    connected: boolean;
    subscriptions: number;
    reconnectAttempts: number;
  } {
    return {
      connected: this.isConnected(),
      subscriptions: this.subscriptions.size,
      reconnectAttempts: this.reconnectAttempts,
    };
  }
}

// Singleton instance
let assetSocketManager: AssetWebSocketManager | null = null;

export const getAssetSocketManager = (
  socketUrl?: string
): AssetWebSocketManager => {
  if (!assetSocketManager && socketUrl) {
    assetSocketManager = new AssetWebSocketManager(socketUrl);
  }

  if (!assetSocketManager) {
    throw new Error(
      "AssetWebSocketManager not initialized. Provide socketUrl on first call."
    );
  }

  return assetSocketManager;
};

export const initializeAssetSocket = (
  socketUrl: string
): AssetWebSocketManager => {
  if (assetSocketManager) {
    assetSocketManager.disconnect();
  }

  assetSocketManager = new AssetWebSocketManager(socketUrl);
  return assetSocketManager;
};

export default AssetWebSocketManager;
