import getAxiosInstance from '../index';
import { Pages } from '../../constants';

const axios = getAxiosInstance(process.argv[2]);
const PAGE_ACCESS_URI = '/pageAccess/';

export const addPage = (page) => {
  return axios.post(`${PAGE_ACCESS_URI}`, page);
};

export const deletePage = (pageId) => {
  return axios.delete(`${PAGE_ACCESS_URI}${pageId}`);
};

export const getAllPages = () => {
  return axios.get(`${PAGE_ACCESS_URI}all`);
};

const init = async () => {
  const isDelete = process.argv[3] === 'delete';
  try {
    const { data } = await getAllPages();
    if (isDelete) {
      data.forEach(async (page) => {
        try {
          await deletePage(page.id);
          console.log('Deleted page: ', page.pageName, page.id);
        } catch (e) {
          console.log('Unable to delete page', e);
        }
      });
      Object.keys(Pages).forEach(async (page) => {
        const pageName = Pages[page];
        const dateTimeNow = new Date().toISOString();
        console.log('Created Page: ', pageName);
        try {
          await addPage({
            pageName: pageName,
            pageUrl: '',
            imageUrl: '',
            pageAccess: true,
            enable: true,
            deleted: false,
            createdTime: dateTimeNow,
            modifiedTime: dateTimeNow,
            createdBy: 'system',
            modifiedBy: 'system',
          });
        } catch {
          console.log('Unable to create page');
        }
      });
    } else {
      const serverPages = data.filter((x) => x.deleted === false).map((y) => y.name);
      Object.keys(Pages).forEach(async (page) => {
        const pageName = Pages[page];
        if (!serverPages.includes(pageName)) {
          console.log('Created Page: ', pageName);
          const dateTimeNow = new Date().toISOString();
          try {
            await addPage({
              pageName: pageName,
              pageUrl: '',
              imageUrl: '',
              pageAccess: true,
              enable: true,
              deleted: false,
              createdTime: dateTimeNow,
              modifiedTime: dateTimeNow,
              createdBy: 'system',
              modifiedBy: 'system',
            });
          } catch {
            console.log('Unable to create page');
          }
        }
      });
    }
  } catch (err) {
    console.log(err);
  }
};

init();
