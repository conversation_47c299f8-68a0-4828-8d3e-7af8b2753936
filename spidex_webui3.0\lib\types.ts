interface GpsPoint {
  latitude: string;
  longitude: string;
}

//TODO: for below types change properties,level
export interface Branch {
  address: string;
  createdBy: string;
  createdTime: number;
  deleted: boolean;
  geoJson: string;
  gpsPoint: GpsPoint;
  id: string;
  modifiedBy: string;
  modifiedTime: number;
  parentBranchId: string;
  name: string;
  properties: null;
  tenantId: string;
}

export interface Location {
  address: string;
  branchId: string;
  createdBy: string;
  createdTime: number;
  deleted: boolean;
  geoJson: string;
  gpsPoint: GpsPoint;
  id: string;
  modifiedBy: string;
  modifiedTime: number;
  name: string;
  properties: null;
  tenantId: string;
}

export interface Area {
  address: string;
  branchId: string;
  createdBy: string;
  createdTime: number;
  deleted: boolean;
  geoJson: string;
  gpsPoint: GpsPoint;
  id: string;
  level: null;
  locationId: string;
  max: string;
  min: string;
  modifiedBy: string;
  modifiedTime: number;
  name: string;
  properties: null;
  tenantId: string;
}

//TODO: incomplete - add all the fields
export interface Gateway {
  areaId: string;
  areaName: string;
  categoryType: string;
  communicationType: string;
  coverageMax: string;
  coverageMin: string;
  address: string;
  branchId: string;
  createdBy: string;
  createdTime: number;
  deleted: boolean;
  description: string;
  externalId: string;
  gpsPoint: GpsPoint;
  id: string;
  level: null;
  locationId: string;
  max: string;
  min: string;
  modifiedBy: string;
  modifiedTime: number;
  name: string;
  properties: null;
  tenantId: string;
}

export interface BranchItem extends Branch {
  children: LocationItem[];
}

export interface LocationItem extends Location {
  children: AreaItem[];
}

export interface AreaItem extends Area {
  children: GatewayItem[];
}

export interface GatewayItem extends Gateway {
  children: [];
}

export type Item = BranchItem | LocationItem | AreaItem;

// device configure
export interface EventAttr {
  id: number;
  attributeId: number;
  category: string;
  name: string;
  description: string;
  dataType: string;
  eventType: string;
  unitType: string;
  converterClass: string;
  enable: boolean;
  deleted: boolean;
  createdTime: number;
  modifiedTime: number;
  createdBy: string;
  modifiedBy: string;
  epeProperties: EpeProperties;
}

export interface EpeProperties {
  ingress: string;
  egress: string;
  deMultiplexing: string;
  dataConversion: string;
}
