import { useEffect, useState, useContext } from 'react';
import { DownloadOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Table,
  Button,
  Form,
  Switch,
  Select,
  Popconfirm,
  CommonCompactView,
  message,
  Row,
  Col,
  Checkbox,
  Pagination,
} from '../../../../components';
import {
  getAllTaggedAssetsByPagination,
  addTaggedAsset,
  updateTaggedAsset,
  deleteTaggedAsset,
  getAllTgassetAssets,
  getAllTgassetTags,
} from '../../../../services';
import Context from '../../../../context';
import { buildCommonApiValues, csvFile } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import CommonDrawer from '../../../../components/CommonDrawer';
import { get } from 'lodash';
import { CRUD, Pages, ModuleNames, MastersPageSizeDefault, MastersPageSizeOptions } from '../../../../constants';

const { Option } = Select;

const TaggedAsset = () => {
  const [context, setContext] = useContext(Context);
  const [taggedAsset, setTaggedAsset] = useState({});
  const [taggedAssets, setTaggedAssets] = useState([]);
  const [totalTaggedAssets, setTotalTaggedAssets] = useState({
    items: 0,
    current: 1,
    pageSize: MastersPageSizeDefault,
  });
  const [assets, setAssets] = useState([]);
  const [tags, setTags] = useState([]);
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setTaggedAssets(tableData) : setTaggedAssets(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
    tgAssetAndTag();
  };

  const closeAdd = () => {
    setVisible(false);
    form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      saveTaggedAssetAction({ ...commonValues, ...values });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      updateTaggedAssetAction(values);
      setVisible(false);
    }
  };

  const saveTaggedAssetAction = (taggedAsset) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const tempData = { ...taggedAsset };
    tempData.modelId = tags.find((x) => x.id === taggedAsset.deviceId).modelId;
    tempData.taggedAssetInfo = {};
    tempData.taggedAssetInfo.tagName = tags.find((x) => x.id === taggedAsset.deviceId).name;
    tempData.taggedAssetInfo.tagExternalId = tags.find((x) => x.id === taggedAsset.deviceId).externalId;
    tempData.taggedAssetInfo.assetName = assets.find((x) => x.id === taggedAsset.assetId).name;
    tempData.taggedAssetInfo.assetExternalId = assets.find((x) => x.id === taggedAsset.assetId).externalId;
    addTaggedAsset(tempData)
      .then((res) => {
        setTaggedAssets((state) => [res.data, ...state]);
        message.success('Succesfully Added tagged asset');
        form.resetFields();
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Tagged Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editTaggedAssetAction = (taggedAsset) => {
    form.setFieldsValue({ ...taggedAsset });
    setAction('edit');
    setTaggedAsset(taggedAsset);
    setVisible(true);
  };

  const updateTaggedAssetCall = (values) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const tempData = { ...taggedAsset };
    tempData.modelId = tags.find((x) => x.id === taggedAsset.deviceId).modelId;
    tempData.taggedAssetInfo = {};
    tempData.taggedAssetInfo.tagName = tags.find((x) => x.id === taggedAsset.deviceId).name;
    tempData.taggedAssetInfo.tagExternalId = tags.find((x) => x.id === taggedAsset.deviceId).externalId;
    tempData.taggedAssetInfo.assetName = assets.find((x) => x.id === taggedAsset.assetId).name;
    tempData.taggedAssetInfo.assetExternalId = assets.find((x) => x.id === taggedAsset.assetId).externalId;
    updateTaggedAsset({ ...tempData, ...values, modifiedTime: new Date() })
      .then((res) => {
        setTaggedAssets((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, {
                ...res.data,
              });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated tagged asset');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Tagged Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateTaggedAssetAction = (values) => {
    updateTaggedAssetCall(values);
  };

  const makeActive = (values) => {
    updateTaggedAssetCall({ ...values, deleted: false });
  };

  const setDeleteTaggedAssetAction = (taggedAssetId, visible) => {
    setTaggedAssets((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === taggedAssetId).visible = visible;
      tempData
        .filter((x) => x.id !== taggedAssetId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteTaggedAssetaction = (taggedAssetId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteTaggedAsset(taggedAssetId)
      .then(() => {
        setTaggedAssets((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === taggedAssetId).visible = false;
          tempState.find((x) => x.id === taggedAssetId).deleted = true;
          return [...state].filter((x) => x.id !== taggedAssetId);
        });
        message.success('Succesfully Deleted tagged asset');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Tagged Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const tgAssetAndTag = async () => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      try {
        const tgtagRes = await getAllTgassetTags(context.profile.tenantId);
        setTags(tgtagRes.data);
      } catch (e) {
        console.log(e);
        message.error('Unable to get Tagged Assets Tags, try again later');
      }
      try {
        const tgassetRes = await getAllTgassetAssets(context.profile.tenantId);
        setAssets(tgassetRes.data.filter((x) => x.deleted === false));
      } catch (e) {
        console.log(e);
        message.error('Unable to get Tagged Assests details, try again later');
      }
    }
  };

  const onPaginationChange = (e, size) => {
    setTotalTaggedAssets((ps) => ({ ...ps, current: +e, pageSize: size }));
    init(+e - 1, size);
  };

  const init = (page = 0, pageSize = 0) => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      getAllTaggedAssetsByPagination(context.profile.tenantId, {
        page: page,
        size: pageSize || totalTaggedAssets.pageSize,
      })
        .then((res) => {
          setTableData(res.data.content);
          setTaggedAssets(res.data.content.filter((x) => x.deleted === false));
          setTotalTaggedAssets((ps) => ({ ...ps, items: res.data.totalElements }));
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to get Tagged Asset details, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
    }
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, []);

  const clearForm = () => {
    form.resetFields();
  };

  const csvFileDownload = () => {
    const exportData = [];
    tableData.map((x) => {
      exportData.push({
        name: x.taggedAssetInfo.assetName,
        externalId: x.taggedAssetInfo.assetExternalId,
        status: x.status,
        provisioned: x.provisioned,
      });
      return exportData;
    });
    csvFile({
      data: exportData,
      fileName: `tagged_asset_${new Date().toDateString()}`.replaceAll(' ', '_').toLowerCase(),
    });
  };

  const tableCols = [
    {
      title: <strong> Asset Name </strong>,
      key: 'Name',
      render: (record) => <>{record?.taggedAssetInfo?.assetName}</>,
    },
    {
      title: <strong> Tag External ID </strong>,
      key: 'Tag External ID',
      render: (record) => <>{record?.taggedAssetInfo?.tagExternalId}</>,
    },
    {
      title: <strong> Status </strong>,
      key: 'status',
      render: (record) => <>{record.status ? 'True' : 'False'}</>,
    },
    {
      title: <strong> Provisioned </strong>,
      key: 'provisioned',
      render: (record) => <>{record.provisioned ? 'True' : 'False'}</>,
    },
    {
      title: <strong> Actions </strong>,
      width: 320,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <PermissionContainer page={Pages.TAGGED_ASSEST} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                  Activate
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified by {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.TAGGED_ASSEST} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editTaggedAssetAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.TAGGED_ASSEST} permission={CRUD.UPDATE}>
              <Popconfirm
                title={`Are you sure to delete tagged asset ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteTaggedAssetaction(record.id)}
                onCancel={() => setDeleteTaggedAssetAction(record.id, false)}
              >
                <Button
                  type="link"
                  onClick={() => setDeleteTaggedAssetAction(record.id, true)}
                  className="actionButton"
                >
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];
  const onBoardBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.ONBOARD}].displayName`,
    ModuleNames.ONBOARD
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.CONFIGURE, onBoardBreadcrumbsName]} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.ONBOARD} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Tagged Asset
            </Button>
          </PermissionContainer>
          <Button size="small" onClick={csvFileDownload} type="primary" className="downloadButton">
            Export <DownloadOutlined />
          </Button>
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="id"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={taggedAssets}
          rowClassName={(record) => record.deleted && 'rowInactive'}
          pagination={{
            showSizeChanger: true,
            defaultPageSize: MastersPageSizeDefault,
            pageSizeOptions: MastersPageSizeOptions,
            onChange: onPaginationChange,
            current: totalTaggedAssets.current,
            total: totalTaggedAssets.items,
          }}
        />
      ) : (
        <>
          <CommonCompactView
            data={taggedAssets}
            onEdit={editTaggedAssetAction}
            onDelete={deleteTaggedAssetaction}
            permissions={[
              { pageName: Pages.TAGGED_ASSEST, permission: CRUD.UPDATE, label: 'Edit' },
              { pageName: Pages.TAGGED_ASSEST, permission: CRUD.DELETE, label: 'Delete' },
            ]}
            title="id"
            dataList={[
              { label: 'Status', value: 'status', type: 'boolean' },
              { label: 'Provisioned', value: 'provisioned', type: 'boolean' },
            ]}
          />
          <Row justify="end">
            <Col>
              <div className="m-2">
                <Pagination
                  onChange={onPaginationChange}
                  current={totalTaggedAssets.current}
                  total={totalTaggedAssets.items}
                  showSizeChanger={false}
                />
              </div>
            </Col>
          </Row>
        </>
      )}
      <CommonDrawer title="Tagged Asset" visible={visible} closeAdd={closeAdd}>
        <Form
          scrollToFirstError={true}
          layout="horizontal"
          initialValues={{}}
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          <Form.Item
            label="Asset ID"
            name="assetId"
            rules={[
              {
                required: true,
                message: 'Please select Asset ID!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Asset ID"
            >
              {assets.map((b) => (
                <Option title={b.name} key={b.id} value={b.id}>
                  {b.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Device ID"
            name="deviceId"
            rules={[
              {
                required: true,
                message: 'Please select Device ID!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Device ID"
            >
              {tags.map((b) => (
                <Option title={`${b.name} ${b.externalId}`} key={b.id} value={b.id}>
                  {`${b.name} (ExtId: ${b.externalId})`}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="status" valuePropName="checked" label="Status" initialValue={false}>
            <Switch />
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </>
  );
};

export default TaggedAsset;
