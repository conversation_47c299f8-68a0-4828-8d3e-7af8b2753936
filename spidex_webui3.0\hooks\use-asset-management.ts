"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  Asset,
  AssetSearchFilters,
  AssetPaginationParams,
  AssetPageSize,
  CreateAssetFormData,
  UpdateAssetFormData,
} from "@/types/asset";

// Default values
const DEFAULT_ASSET_PAGE_SIZE: AssetPageSize = 10;
const ASSET_PAGE_SIZES: AssetPageSize[] = [10, 25, 50, 100];

interface UseAssetManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: AssetPageSize;
  initialPageSize?: number;
  availablePageSizes?: AssetPageSize[];
}

export const useAssetManagement = ({
  autoLoad = true,
  defaultPageSize = DEFAULT_ASSET_PAGE_SIZE,
  initialPageSize,
  availablePageSizes,
}: UseAssetManagementOptions = {}) => {
  // Get authenticated API instance and session
  const spidexApi = useSpidexApi();
  const { data: session, status } = useSession();

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);

  // Data cache - store all assets for client-side pagination
  const [allAssetsCache, setAllAssetsCache] = useState<Asset[]>([]);

  // Search and pagination state
  const [searchFilters, setSearchFilters] = useState<AssetSearchFilters>({
    searchTerm: "",
  });

  const [pagination, setPagination] = useState<AssetPaginationParams>({
    page: 1,
    size: (initialPageSize as AssetPageSize) || defaultPageSize,
  });

  // Safe search filters with defaults
  const safeSearchFilters = useMemo(
    () => ({
      searchTerm: searchFilters.searchTerm || "",
      assetType: searchFilters.assetType,
    }),
    [searchFilters]
  );

  // Filter assets based on search criteria
  const filteredAssets = useMemo(() => {
    let filtered = [...allAssetsCache];

    // Filter by deleted status
    if (!showDeleted) {
      filtered = filtered.filter((asset) => !asset.deleted);
    } else {
      filtered = filtered.filter((asset) => asset.deleted);
    }

    // Filter by search term
    if (safeSearchFilters.searchTerm) {
      const searchTerm = safeSearchFilters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (asset) =>
          asset.name.toLowerCase().includes(searchTerm) ||
          asset.externalId.toLowerCase().includes(searchTerm) ||
          asset.assetType.toLowerCase().includes(searchTerm)
      );
    }

    // Filter by asset type
    if (safeSearchFilters.assetType) {
      filtered = filtered.filter(
        (asset) => asset.assetType === safeSearchFilters.assetType
      );
    }

    // Sort assets by name
    filtered.sort((a, b) => a.name.localeCompare(b.name));

    return filtered;
  }, [allAssetsCache, safeSearchFilters, showDeleted]);

  // Calculate pagination
  const totalPages = useMemo(() => {
    return Math.ceil(filteredAssets.length / pagination.size);
  }, [filteredAssets.length, pagination.size]);

  // Get current page assets
  const paginatedAssets = useMemo(() => {
    const startIndex = (pagination.page - 1) * pagination.size;
    const endIndex = startIndex + pagination.size;
    return filteredAssets.slice(startIndex, endIndex);
  }, [filteredAssets, pagination.page, pagination.size]);

  // Count statistics
  const activeRecordsCount = useMemo(
    () => allAssetsCache.filter((asset) => !asset.deleted).length,
    [allAssetsCache]
  );

  const inactiveRecordsCount = useMemo(
    () => allAssetsCache.filter((asset) => asset.deleted).length,
    [allAssetsCache]
  );

  // Available page sizes
  const finalAvailablePageSizes: AssetPageSize[] =
    availablePageSizes || ASSET_PAGE_SIZES;

  // Load all assets data
  const loadData = useCallback(async () => {
    if (
      status !== "authenticated" ||
      !session?.user?.token ||
      !session?.user?.tenantId
    ) {
      console.log("Waiting for authentication...");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Set authentication in API service
      spidexApi.setAuthToken(session.user.token);
      spidexApi.setTenantId(session.user.tenantId);

      console.log(
        "Loading all asset management data for client-side pagination"
      );

      // Load all assets with large page size for client-side pagination
      const assetsData = await spidexApi.getAllAssets(1, 1000);

      setAllAssetsCache(assetsData || []);

      console.log(`Loaded ${assetsData?.length || 0} assets`);
    } catch (error) {
      console.error("Error loading asset data:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load asset data"
      );
    } finally {
      setIsLoading(false);
    }
  }, [spidexApi, session, status]);

  // Auto-load data when session is ready (only on initial mount)
  useEffect(() => {
    if (autoLoad && status === "authenticated") {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad, status]); // Removed loadData dependency to prevent reloading on session changes

  // CRUD Operations
  const createAsset = useCallback(
    async (assetData: CreateAssetFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        // Validate session
        if (!session?.user?.token) {
          throw new Error("No authentication token available");
        }

        if (!session?.user?.tenantId) {
          throw new Error("No tenant ID available");
        }

        // Set authentication in API service
        spidexApi.setAuthToken(session.user.token);
        spidexApi.setTenantId(session.user.tenantId);

        // Transform form data to match API format
        const apiPayload = {
          ...assetData,
          status: assetData.status.toString(),
          id: null, // Required by API for new assets
          tenantId: session.user.tenantId,
          deleted: false,
          createdBy: session.user.userId || "system",
          createdDate: new Date().toISOString(),
          modifiedBy: session.user.userId || "system",
          modifiedDate: new Date().toISOString(),
        };

        const newAsset = await spidexApi.createAsset(apiPayload);

        // Reload data to get updated list
        await loadData();

        console.log("✅ Asset created successfully in useAssetManagement");
        return newAsset;
      } catch (error) {
        setError(
          error instanceof Error ? error.message : "Failed to create asset"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  const updateAsset = useCallback(
    async (assetData: UpdateAssetFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        // Find the existing asset to get all required fields
        const existingAsset = allAssetsCache.find((a) => a.id === assetData.id);
        if (!existingAsset) {
          throw new Error("Asset not found");
        }

        const apiPayload = {
          tenantId: existingAsset.tenantId,
          id: existingAsset.id,
          name: assetData.name,
          externalId: assetData.externalId,
          status: assetData.status.toString(),
          assetType: assetData.assetType,
          parentAssetId: existingAsset.parentAssetId || "",
          deleted: existingAsset.deleted,
          createdTime: existingAsset.createdTime,
          modifiedTime: new Date().toISOString(),
          createdBy: existingAsset.createdBy,
          modifiedBy: session?.user?.userId || "system",
          properties: existingAsset.properties,
        };

        const updatedAsset = await spidexApi.updateAsset(apiPayload);

        // Reload data to get updated list
        await loadData();

        console.log("Asset updated successfully");
        return updatedAsset;
      } catch (error) {
        console.error("Error updating asset:", error);
        setError(
          error instanceof Error ? error.message : "Failed to update asset"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData, allAssetsCache]
  );

  const deleteAsset = useCallback(
    async (assetId: string) => {
      try {
        setIsLoading(true);
        setError(null);

        await spidexApi.deleteAsset(assetId);

        // Reload data to get updated list
        await loadData();

        console.log("Asset deleted successfully");
      } catch (error) {
        console.error("Error deleting asset:", error);
        setError(
          error instanceof Error ? error.message : "Failed to delete asset"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, loadData]
  );

  const toggleAssetActive = useCallback(
    async (asset: Asset) => {
      try {
        setIsLoading(true);
        setError(null);

        // Toggle the deleted status
        const updatedAsset = {
          tenantId: asset.tenantId,
          id: asset.id,
          name: asset.name,
          externalId: asset.externalId,
          status: asset.status,
          assetType: asset.assetType,
          parentAssetId: asset.parentAssetId || "",
          deleted: !asset.deleted,
          createdTime: asset.createdTime,
          modifiedTime: new Date().toISOString(),
          createdBy: asset.createdBy,
          modifiedBy: session?.user?.userId || "system",
          properties: asset.properties,
        };

        await spidexApi.updateAsset(updatedAsset);

        // Reload data to get updated list
        await loadData();

        console.log("Asset status toggled successfully");
      } catch (error) {
        console.error("Error toggling asset status:", error);
        setError(
          error instanceof Error
            ? error.message
            : "Failed to toggle asset status"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  // Pagination controls
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  }, []);

  const changePageSize = useCallback((size: AssetPageSize) => {
    setPagination((prev) => ({ ...prev, size, page: 1 }));
  }, []);

  // Search controls
  const updateSearchFilters = useCallback(
    (filters: Partial<AssetSearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({ searchTerm: "", assetType: undefined });
    setShowDeleted(false);
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
    setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
  }, []);

  return {
    // Data
    assets: paginatedAssets,
    allAssets: allAssetsCache,
    filteredAssets,

    // State
    isLoading,
    error,
    showDeleted,

    // Search and pagination
    searchFilters: safeSearchFilters,
    pagination,
    totalPages,
    totalRecords: filteredAssets.length, // Use filtered count for display
    totalAllRecords: allAssetsCache.length, // Total count including deleted
    activeRecordsCount, // Count of active (not deleted) records
    inactiveRecordsCount, // Count of inactive (deleted) records
    hasNextPage: pagination.page < totalPages,
    hasPreviousPage: pagination.page > 1,

    // Actions
    loadData,
    createAsset,
    updateAsset,
    deleteAsset,
    toggleAssetActive,

    // Pagination controls
    goToPage,
    changePageSize,
    availablePageSizes: finalAvailablePageSizes,

    // Search controls
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
  };
};
