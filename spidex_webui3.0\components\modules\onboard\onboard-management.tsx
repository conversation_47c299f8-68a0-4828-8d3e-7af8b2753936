"use client";

import { useState } from "react";
import { Package, Router } from "lucide-react";
import { useSession } from "next-auth/react";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";
import { OnboardAssetManagement } from "./onboard-asset-management";
import { OnboardGatewayManagement } from "./onboard-gateway-management";
import { ErrorBoundary } from "./error-boundary";

export default function OnboardManagement() {
  const { data: session, status } = useSession();
  const [activeTab, setActiveTab] = useState("assets");

  // Loading state
  if (status === "loading") {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Unauthorized state
  if (status === "unauthenticated") {
    return (
      <div className="">
        <div className="text-center">
          <p className="text-muted-foreground">
            Please log in to access onboard management.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/digital-carpet">
                Digital Carpet
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Onboard Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main>
        {/* Tabs */}
        <div className="p-4">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-6"
          >
            <TabsList className="grid w-full grid-cols-2 max-w-md">
              <TabsTrigger value="assets" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Asset Onboarding
              </TabsTrigger>
              <TabsTrigger value="gateways" className="flex items-center gap-2">
                <Router className="h-4 w-4" />
                Gateway Onboarding
              </TabsTrigger>
            </TabsList>

            <TabsContent value="assets">
              <ErrorBoundary>
                <OnboardAssetManagement />
              </ErrorBoundary>
            </TabsContent>

            <TabsContent value="gateways">
              <ErrorBoundary>
                <OnboardGatewayManagement />
              </ErrorBoundary>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </>
  );
}
