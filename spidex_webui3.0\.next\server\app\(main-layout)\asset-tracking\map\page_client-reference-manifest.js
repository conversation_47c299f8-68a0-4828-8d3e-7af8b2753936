globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(main-layout)/asset-tracking/map/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(ssr)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/forms/login-form.tsx":{"*":{"id":"(ssr)/./components/forms/login-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/particles.tsx":{"*":{"id":"(ssr)/./components/particles.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js":{"*":{"id":"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/app-sidebar.tsx":{"*":{"id":"(ssr)/./components/app-sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/apollo-provider.tsx":{"*":{"id":"(ssr)/./components/providers/apollo-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/query-provider.tsx":{"*":{"id":"(ssr)/./components/providers/query-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/session-provider.tsx":{"*":{"id":"(ssr)/./components/providers/session-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/sidebar.tsx":{"*":{"id":"(ssr)/./components/ui/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/what3words-script.tsx":{"*":{"id":"(ssr)/./components/what3words-script.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/contexts/auth-context.tsx":{"*":{"id":"(ssr)/./lib/contexts/auth-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(ssr)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/modules/asset-tracking/map.tsx":{"*":{"id":"(ssr)/./components/modules/asset-tracking/map.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/modules/branch/branch-management.tsx":{"*":{"id":"(ssr)/./components/modules/branch/branch-management.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/table-skeleton.tsx":{"*":{"id":"(ssr)/./components/ui/table-skeleton.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/modules/location/location-management.tsx":{"*":{"id":"(ssr)/./components/modules/location/location-management.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\globals.css":{"id":"(app-pages-browser)/./globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\theme-provider.tsx":{"id":"(app-pages-browser)/./components/theme-provider.tsx","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\forms\\login-form.tsx":{"id":"(app-pages-browser)/./components/forms/login-form.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\particles.tsx":{"id":"(app-pages-browser)/./components/particles.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-chunks.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\shared\\lib\\lazy-dynamic\\preload-chunks.js":{"id":"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\app-sidebar.tsx":{"id":"(app-pages-browser)/./components/app-sidebar.tsx","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\providers\\apollo-provider.tsx":{"id":"(app-pages-browser)/./components/providers/apollo-provider.tsx","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\providers\\query-provider.tsx":{"id":"(app-pages-browser)/./components/providers/query-provider.tsx","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\providers\\session-provider.tsx":{"id":"(app-pages-browser)/./components/providers/session-provider.tsx","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\ui\\sidebar.tsx":{"id":"(app-pages-browser)/./components/ui/sidebar.tsx","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\what3words-script.tsx":{"id":"(app-pages-browser)/./components/what3words-script.tsx","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\lib\\contexts\\auth-context.tsx":{"id":"(app-pages-browser)/./lib/contexts/auth-context.tsx","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\(main-layout)\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\(main-layout)\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\mapbox-gl\\dist\\mapbox-gl.css":{"id":"(app-pages-browser)/./node_modules/mapbox-gl/dist/mapbox-gl.css","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\nextjs-toploader\\dist\\index.js":{"id":"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\sonner\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/(main-layout)/layout","static/chunks/app/(main-layout)/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\modules\\asset-tracking\\map.tsx":{"id":"(app-pages-browser)/./components/modules/asset-tracking/map.tsx","name":"*","chunks":["app/(main-layout)/asset-tracking/map/page","static/chunks/app/(main-layout)/asset-tracking/map/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\modules\\branch\\branch-management.tsx":{"id":"(app-pages-browser)/./components/modules/branch/branch-management.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\ui\\table-skeleton.tsx":{"id":"(app-pages-browser)/./components/ui/table-skeleton.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\components\\modules\\location\\location-management.tsx":{"id":"(app-pages-browser)/./components/modules/location/location-management.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\":[],"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\app\\(login-layout)\\layout":[],"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\app\\(main-layout)\\layout":[{"inlined":false,"path":"static/css/app/(main-layout)/layout.css"}],"C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\app\\(main-layout)\\asset-tracking\\map\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./globals.css":{"*":{"id":"(rsc)/./globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/theme-provider.tsx":{"*":{"id":"(rsc)/./components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/forms/login-form.tsx":{"*":{"id":"(rsc)/./components/forms/login-form.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/particles.tsx":{"*":{"id":"(rsc)/./components/particles.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":{"*":{"id":"(rsc)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js":{"*":{"id":"(rsc)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/app-sidebar.tsx":{"*":{"id":"(rsc)/./components/app-sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/apollo-provider.tsx":{"*":{"id":"(rsc)/./components/providers/apollo-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/query-provider.tsx":{"*":{"id":"(rsc)/./components/providers/query-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/session-provider.tsx":{"*":{"id":"(rsc)/./components/providers/session-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/sidebar.tsx":{"*":{"id":"(rsc)/./components/ui/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/what3words-script.tsx":{"*":{"id":"(rsc)/./components/what3words-script.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/contexts/auth-context.tsx":{"*":{"id":"(rsc)/./lib/contexts/auth-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/mapbox-gl/dist/mapbox-gl.css":{"*":{"id":"(rsc)/./node_modules/mapbox-gl/dist/mapbox-gl.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(rsc)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/modules/asset-tracking/map.tsx":{"*":{"id":"(rsc)/./components/modules/asset-tracking/map.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/modules/branch/branch-management.tsx":{"*":{"id":"(rsc)/./components/modules/branch/branch-management.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/table-skeleton.tsx":{"*":{"id":"(rsc)/./components/ui/table-skeleton.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/modules/location/location-management.tsx":{"*":{"id":"(rsc)/./components/modules/location/location-management.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}