"use client";

import React, {
  useState,
  useEffect,
  useRef,
  Suspense,
  useCallback,
} from "react";
import { useSearchParams } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Search,
  Wifi,
  WifiOff,
  Activity,
  ChevronLeft,
  ChevronRight,
  Eye,
  BarChart3,
  Map as MapIcon,
  MapPin,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  AlertTriangle,
  Thermometer,
} from "lucide-react";
import Map, { Marker, NavigationControl, ScaleControl } from "react-map-gl";
import { useSpidexAssetTracking } from "@/hooks/use-spidex-asset-tracking";
import { Gateway, Asset, SensorType } from "@/types/asset-tracking";
import { SENSOR_UNITS, MAP_CONFIG } from "@/lib/constants/asset-tracking";
import { ZoneTree } from "@/components/ui/zone-tree";

// MapBox configuration
const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MapboxAccessToken || "";
// Debug: Check if MapBox token is available
if (!MAPBOX_TOKEN) {
  console.warn(
    "MapBox token not found. Set NEXT_PUBLIC_MapboxAccessToken environment variable."
  );
}

const SOCKET_URL = process.env.NEXT_PUBLIC_SPIDEX_SOCKET_URI;

function AssetTrackingDashboardContent() {
  const searchParams = useSearchParams();
  const deviceParam = searchParams.get("device");

  // Add mounted state to prevent hydration issues
  const [isMounted, setIsMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"asset" | "zone">("asset"); // Default to asset view
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const [selectedLocationId, setSelectedLocationId] = useState<string>("");

  // Modal state for device details
  const [isDeviceModalOpen, setIsDeviceModalOpen] = useState(false);
  const [selectedAssetForModal, setSelectedAssetForModal] =
    useState<Asset | null>(null);

  // Device map state
  const [deviceMapViewState, setDeviceMapViewState] = useState<any>({
    longitude: MAP_CONFIG.DEFAULT_CENTER.longitude,
    latitude: MAP_CONFIG.DEFAULT_CENTER.latitude,
    zoom: 16,
  });
  const [isLocationUpdating, setIsLocationUpdating] = useState(false);
  const deviceMapRef = useRef<any>(null);
  const processedDeviceParam = useRef<string | null>(null);

  // Set mounted state on client side only
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const {
    gateways,
    locations,
    zones,
    currentTableData,
    sensorData,
    selectedGateway,
    isLoading,
    isSocketConnected,
    socketError,
    selectGateway,
    selectAsset,
    setSearchFilters,
    getGatewayCardsForLocation,
    loadProximityDataForLocation,
  } = useSpidexAssetTracking({
    socketUrl: SOCKET_URL,
    autoConnect: true,
  });

  useEffect(() => {
    setSearchFilters({ searchTerm });
  }, [searchTerm, setSearchFilters]);

  // Auto-select first location when locations are loaded
  useEffect(() => {
    const autoSelectLocation = async () => {
      if (locations.length > 0 && !selectedLocationId) {
        // Try to find "AME_STFloor" location to match old application
        const ameLocation = locations.find((loc) => loc.name === "AME_STFloor");
        const firstLocationId = ameLocation ? ameLocation.id : locations[0].id;

        setSelectedLocationId(firstLocationId);

        // Load proximity data for the auto-selected location (matches old implementation)
        await loadProximityDataForLocation(firstLocationId);

        // Auto-select first gateway in the location
        const locationGateways = gateways.filter(
          (g) => g.locId === firstLocationId
        );
        if (locationGateways.length > 0) {
          selectGateway(locationGateways[0]);
        }
      }
    };

    autoSelectLocation();
  }, [
    locations,
    gateways,
    selectedLocationId,
    selectGateway,
    loadProximityDataForLocation,
  ]);

  const handleGatewaySelect = (gateway: Gateway) => {
    selectGateway(gateway);
  };

  const handleDeviceClick = (asset: Asset) => {
    selectAsset(asset);
    setSelectedAssetForModal(asset);
    setIsDeviceModalOpen(true);

    // Set device map view state based on asset location
    const location = getAssetLocation(asset);
    if (location) {
      setDeviceMapViewState({
        longitude: location.longitude,
        latitude: location.latitude,
        zoom: 16,
      });
    }
  };

  const handleCloseDeviceModal = () => {
    setIsDeviceModalOpen(false);
    setSelectedAssetForModal(null);
    processedDeviceParam.current = null; // Reset to allow reopening
  };

  // Device map helper functions
  const getAssetLocation = React.useCallback(
    (asset: Asset) => {
      console.log("getAssetLocation: Processing asset:", asset.deviceId);
      console.log("getAssetLocation: Asset lintDtos:", asset.lintDtos);
      console.log("getAssetLocation: Asset sourceId:", asset.sourceId);
      console.log("getAssetLocation: Available gateways:", gateways.length);
      console.log("getAssetLocation: selectedLocationId:", selectedLocationId);

      // Priority 1: Try to get location from lintDtos attributeValues array
      if (asset.lintDtos && asset.lintDtos.length > 0) {
        console.log(
          "getAssetLocation: Found lintDtos, count:",
          asset.lintDtos.length
        );

        const lintWithLocation = asset.lintDtos
          .filter(
            (lint) => lint.attributeValues && lint.attributeValues.length >= 2
          )
          .sort((a, b) => b.lintTime - a.lintTime)[0]; // Most recent first

        console.log(
          "getAssetLocation: Filtered lintWithLocation:",
          lintWithLocation
        );

        if (lintWithLocation) {
          try {
            const lat = parseFloat(lintWithLocation.attributeValues![0]);
            const lng = parseFloat(lintWithLocation.attributeValues![1]);

            console.log(
              "getAssetLocation: Parsed coordinates - Lat:",
              lat,
              "Lng:",
              lng
            );

            // Validate coordinates
            if (
              !isNaN(lat) &&
              !isNaN(lng) &&
              lat >= -90 &&
              lat <= 90 &&
              lng >= -180 &&
              lng <= 180
            ) {
              console.log(
                "getAssetLocation: Valid coordinates found from lintDtos!"
              );
              return { latitude: lat, longitude: lng };
            } else {
              console.log("getAssetLocation: Coordinates failed validation");
            }
          } catch (error) {
            console.warn("Error parsing lintDtos coordinates:", error);
          }
        } else {
          console.log(
            "getAssetLocation: No lintWithLocation found after filtering"
          );
        }
      } else {
        console.log("getAssetLocation: No lintDtos found or empty array");
      }

      // Priority 2: Try to get location from the asset's source gateway
      const gateway = gateways.find((g) => g.id === asset.sourceId);
      console.log("getAssetLocation: Found gateway for sourceId:", gateway);
      if (gateway && gateway.xyzCoordinates) {
        const coords = gateway.xyzCoordinates;
        console.log("getAssetLocation: Gateway coordinates:", coords);
        if (coords.latitude && coords.longitude) {
          console.log(
            "getAssetLocation: Valid coordinates found from gateway!"
          );
          return { latitude: coords.latitude, longitude: coords.longitude };
        }
      }

      // Priority 3: Use location-based positioning with some spread
      if (selectedLocationId) {
        const locationGateways = gateways.filter(
          (g) => g.locId === selectedLocationId
        );
        console.log(
          "getAssetLocation: Location gateways:",
          locationGateways.length
        );
        if (locationGateways.length > 0) {
          const gatewaysWithCoords = locationGateways.filter(
            (g) =>
              g.xyzCoordinates &&
              g.xyzCoordinates.latitude &&
              g.xyzCoordinates.longitude
          );
          console.log(
            "getAssetLocation: Gateways with coords:",
            gatewaysWithCoords.length
          );

          if (gatewaysWithCoords.length > 0) {
            const avgLat =
              gatewaysWithCoords.reduce(
                (sum, g) => sum + g.xyzCoordinates!.latitude,
                0
              ) / gatewaysWithCoords.length;
            const avgLng =
              gatewaysWithCoords.reduce(
                (sum, g) => sum + g.xyzCoordinates!.longitude,
                0
              ) / gatewaysWithCoords.length;

            // Add some deterministic spread around the location center to avoid hydration issues
            const assetHash = asset.deviceId.split("").reduce((a, b) => {
              a = (a << 5) - a + b.charCodeAt(0);
              return a & a;
            }, 0);
            const latOffset = ((assetHash % 1000) / 1000 - 0.5) * 0.005; // ~500m radius
            const lngOffset = (((assetHash * 7) % 1000) / 1000 - 0.5) * 0.005;

            const fallbackLocation = {
              latitude: avgLat + latOffset,
              longitude: avgLng + lngOffset,
            };
            console.log(
              "getAssetLocation: Using location-based fallback:",
              fallbackLocation
            );
            return fallbackLocation;
          }
        }
      }

      // Fallback: Use default center with deterministic spread to avoid hydration issues
      console.log("getAssetLocation: Using default center fallback");
      const assetHash = asset.deviceId.split("").reduce((a, b) => {
        a = (a << 5) - a + b.charCodeAt(0);
        return a & a;
      }, 0);
      const latOffset = ((assetHash % 2000) / 2000 - 0.5) * 0.01; // ~1km radius
      const lngOffset = (((assetHash * 13) % 2000) / 2000 - 0.5) * 0.01;
      const defaultLocation = {
        latitude: MAP_CONFIG.DEFAULT_CENTER.latitude + latOffset,
        longitude: MAP_CONFIG.DEFAULT_CENTER.longitude + lngOffset,
      };
      console.log(
        "getAssetLocation: Default fallback location:",
        defaultLocation
      );
      return defaultLocation;
    },
    [gateways, selectedLocationId]
  ); // Close useCallback with dependencies

  const hasValidCoordinates = React.useCallback(
    (asset: Asset | null) => {
      if (!asset) return false;
      const location = getAssetLocation(asset);
      console.log(
        `hasValidCoordinates for ${asset.deviceId}:`,
        !!location,
        location
      );
      return location !== null;
    },
    [gateways, selectedLocationId]
  );

  const handleDeviceMapZoomIn = () => {
    setDeviceMapViewState((prev: any) => ({
      ...prev,
      zoom: Math.min(prev.zoom + 1, MAP_CONFIG.MAX_ZOOM),
    }));
  };

  const handleDeviceMapZoomOut = () => {
    setDeviceMapViewState((prev: any) => ({
      ...prev,
      zoom: Math.max(prev.zoom - 1, MAP_CONFIG.MIN_ZOOM),
    }));
  };

  const handleReturnToMarker = () => {
    if (selectedAssetForModal) {
      const location = getAssetLocation(selectedAssetForModal);
      if (location) {
        setDeviceMapViewState({
          longitude: location.longitude,
          latitude: location.latitude,
          zoom: 16,
        });
      }
    }
  };

  const getAssetStatus = useCallback(
    (asset: Asset) => {
      if (!isMounted) return "unknown"; // Avoid hydration issues

      const lastSeen = asset.lastSeen ? new Date(asset.lastSeen) : null;
      const now = new Date();
      const timeDiff = lastSeen ? now.getTime() - lastSeen.getTime() : Infinity;
      const minutesDiff = timeDiff / (1000 * 60);

      if (minutesDiff < 5) return "online";
      if (minutesDiff < 30) return "warning";
      return "offline";
    },
    [isMounted]
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-green-500";
      case "warning":
        return "bg-yellow-500";
      case "offline":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const renderSensorValue = (deviceLogId: string, sensorType: SensorType) => {
    const sensor = sensorData[deviceLogId]?.[sensorType];
    if (!sensor) return "N/A";
    if (sensor.loading) return "Loading...";
    const unit = SENSOR_UNITS[sensorType as keyof typeof SENSOR_UNITS] || "";
    return `${sensor.value}${unit}`;
  };

  // Get all assets (matches old implementation: Object.values(currentTableData).flat())
  const allAssets = isMounted ? Object.values(currentTableData).flat() : [];

  // Get current assets for selected gateway (matches old implementation)
  const currentAssets =
    isMounted && selectedGateway?.id
      ? currentTableData[selectedGateway.id] || []
      : [];

  // Handle device parameter from URL to auto-open modal (only after mounting)
  useEffect(() => {
    if (
      isMounted &&
      deviceParam &&
      allAssets.length > 0 &&
      processedDeviceParam.current !== deviceParam
    ) {
      const targetAsset = allAssets.find(
        (asset) => asset.deviceId === deviceParam
      );
      if (targetAsset) {
        console.log("Auto-opening modal for device:", deviceParam);
        processedDeviceParam.current = deviceParam;

        // Use setTimeout to break the synchronous update cycle
        setTimeout(() => {
          selectAsset(targetAsset);
          setSelectedAssetForModal(targetAsset);
          setIsDeviceModalOpen(true);

          // Set device map view state based on asset location
          const location = getAssetLocation(targetAsset);
          if (location) {
            setDeviceMapViewState({
              longitude: location.longitude,
              latitude: location.latitude,
              zoom: 16,
            });
          }
        }, 0);
      }
    }
  }, [isMounted, deviceParam, allAssets.length]);

  // Update selected asset with real-time data when modal is open
  useEffect(() => {
    if (selectedAssetForModal && isDeviceModalOpen) {
      // Find the updated asset data from all assets (not just current gateway)
      const updatedAsset = allAssets.find(
        (asset) => asset.deviceId === selectedAssetForModal.deviceId
      );

      if (updatedAsset) {
        console.log("Real-time update for asset:", updatedAsset.deviceId);

        // Only update if the asset data has actually changed (prevent infinite loops)
        const hasDataChanged =
          JSON.stringify(updatedAsset.lintDtos) !==
          JSON.stringify(selectedAssetForModal.lintDtos);

        if (hasDataChanged) {
          // Check if location actually changed
          const oldLocation = getAssetLocation(selectedAssetForModal);
          const newLocation = getAssetLocation(updatedAsset);

          const locationChanged =
            oldLocation &&
            newLocation &&
            (Math.abs(oldLocation.latitude - newLocation.latitude) > 0.000001 ||
              Math.abs(oldLocation.longitude - newLocation.longitude) >
                0.000001);

          setSelectedAssetForModal(updatedAsset);

          // Update map position if coordinates changed
          if (newLocation && locationChanged) {
            console.log(
              "Location changed! Updating map position to:",
              newLocation
            );
            setIsLocationUpdating(true);

            setDeviceMapViewState((prev: any) => ({
              ...prev,
              longitude: newLocation.longitude,
              latitude: newLocation.latitude,
            }));

            // Reset animation after 2 seconds
            setTimeout(() => setIsLocationUpdating(false), 2000);
          }
        }
      }
    }
  }, [allAssets.length, selectedAssetForModal?.deviceId, isDeviceModalOpen]);

  // Get gateway cards for selected location (matches old implementation)
  const gatewayCards = selectedLocationId
    ? getGatewayCardsForLocation(selectedLocationId)
    : [];

  // Get zones with asset counts for zone view
  const zonesWithCounts = zones;

  // Filter assets based on search term (only after mounting)
  const filteredAssets = isMounted
    ? currentAssets.filter((asset) => {
        const searchLower = searchTerm.toLowerCase();
        return (
          asset.name.toLowerCase().includes(searchLower) ||
          asset.deviceId.toLowerCase().includes(searchLower)
        );
      })
    : [];

  // Pagination (currentPage and itemsPerPage already declared above)
  const currentItems = isMounted
    ? viewMode === "asset"
      ? filteredAssets
      : zonesWithCounts
    : [];
  const totalPages = isMounted
    ? Math.ceil(currentItems.length / itemsPerPage)
    : 1;
  const startIndex = isMounted ? (currentPage - 1) * itemsPerPage : 0;
  const paginatedAssets =
    isMounted && viewMode === "asset"
      ? filteredAssets.slice(startIndex, startIndex + itemsPerPage)
      : [];

  // For zone view, we don't paginate the tree structure to maintain hierarchy
  const displayZones = isMounted ? zonesWithCounts : [];

  // Prevent hydration mismatch by not rendering until mounted
  if (!isMounted) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Initializing dashboard...</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">
            Loading asset tracking data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Similar to old UI */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium">
                Asset Tracking Dashboard
              </div>
              <div className="flex items-center space-x-2">
                {isSocketConnected ? (
                  <Badge variant="default" className="bg-green-500">
                    <Wifi className="h-3 w-3 mr-1" />
                    Connected
                  </Badge>
                ) : (
                  <Badge variant="destructive">
                    <WifiOff className="h-3 w-3 mr-1" />
                    Disconnected
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar - Gateway Selection */}
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-4">Gateways</h3>
                <div className="space-y-2">
                  {getGatewayCardsForLocation(selectedLocationId).map(
                    (gatewayCard) => (
                      <div
                        key={gatewayCard.id}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedGateway?.id === gatewayCard.gateway.id
                            ? "bg-blue-50 border-blue-200"
                            : "hover:bg-gray-50"
                        }`}
                        onClick={() => handleGatewaySelect(gatewayCard.gateway)}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-sm">
                              {gatewayCard.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {gatewayCard.area}
                            </p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {gatewayCard.assetCount} assets
                          </Badge>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            {/* Controls */}
            <div className="mb-6">
              <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium">Location:</label>
                  <select
                    value={selectedLocationId}
                    onChange={(e) => {
                      setSelectedLocationId(e.target.value);
                      loadProximityDataForLocation(e.target.value);
                    }}
                    className="px-3 py-1 border rounded text-sm"
                  >
                    <option value="">Select Location</option>
                    {locations.map((location) => (
                      <option key={location.id} value={location.id}>
                        {location.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">All Areas</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant={viewMode === "asset" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("asset")}
                  >
                    Asset View
                  </Button>
                  <Button
                    variant={viewMode === "zone" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("zone")}
                  >
                    Zone View
                  </Button>
                </div>
                <div className="flex-1">
                  <div className="relative max-w-md">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by Asset Name or Device ID"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Content based on view mode */}
            {viewMode === "asset" ? (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">Assets</h3>
                    <div className="text-sm text-gray-600">
                      {filteredAssets.length} assets found
                    </div>
                  </div>

                  {/* Asset Table */}
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3 font-medium">
                            Asset Name
                          </th>
                          <th className="text-left p-3 font-medium">
                            Device ID
                          </th>
                          <th className="text-left p-3 font-medium">Area</th>
                          <th className="text-left p-3 font-medium">Zone</th>
                          <th className="text-left p-3 font-medium">Status</th>
                          <th className="text-left p-3 font-medium">Battery</th>
                          <th className="text-left p-3 font-medium">
                            Temperature
                          </th>
                          <th className="text-left p-3 font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {paginatedAssets.map((asset) => (
                          <tr
                            key={asset.deviceId}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="p-3">
                              <Button
                                variant="link"
                                className="p-0 h-auto font-normal"
                                onClick={() => handleDeviceClick(asset)}
                              >
                                {asset.name}
                              </Button>
                            </td>
                            <td className="p-3 font-mono text-sm">
                              {asset.deviceId}
                            </td>
                            <td className="p-3">{asset.areaName}</td>
                            <td className="p-3">{asset.zone || "N/A"}</td>
                            <td className="p-3">
                              <div className="flex items-center space-x-2">
                                <div
                                  className={`w-2 h-2 rounded-full ${getStatusColor(
                                    getAssetStatus(asset)
                                  )}`}
                                />
                                <span className="capitalize text-sm">
                                  {getAssetStatus(asset)}
                                </span>
                              </div>
                            </td>
                            <td className="p-3">
                              {renderSensorValue(
                                asset.deviceLogId,
                                SensorType.BATTERY
                              )}
                            </td>
                            <td className="p-3">
                              {renderSensorValue(
                                asset.deviceLogId,
                                SensorType.TEMPERATURE
                              )}
                            </td>
                            <td className="p-3">
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDeviceClick(asset)}
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    window.open(
                                      `/asset-tracking/analytics?device=${asset.deviceId}`,
                                      "_blank"
                                    );
                                  }}
                                >
                                  <BarChart3 className="h-3 w-3 mr-1" />
                                  Analytics
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-sm text-gray-600">
                        Showing {startIndex + 1} to{" "}
                        {Math.min(
                          startIndex + itemsPerPage,
                          filteredAssets.length
                        )}{" "}
                        of {filteredAssets.length} assets
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(currentPage - 1)}
                          disabled={currentPage === 1}
                        >
                          <ChevronLeft className="h-4 w-4" />
                        </Button>
                        <span className="text-sm">
                          Page {currentPage} of {totalPages}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(currentPage + 1)}
                          disabled={currentPage === totalPages}
                        >
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">Zone View</h3>
                    <div className="text-sm text-gray-600">
                      {displayZones.length} zones found
                    </div>
                  </div>

                  {/* Zone Tree */}
                  <ZoneTree data={displayZones} />
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* Device Details Modal */}
      <Dialog open={isDeviceModalOpen} onOpenChange={setIsDeviceModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedAssetForModal?.name} ({selectedAssetForModal?.deviceId})
            </DialogTitle>
            <DialogDescription>
              Real-time device information and location tracking
            </DialogDescription>
          </DialogHeader>

          {selectedAssetForModal && (
            <div className="space-y-6">
              {/* Device Info Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Activity className="h-5 w-5 text-blue-500" />
                      <div>
                        <p className="text-sm font-medium">Status</p>
                        <div className="flex items-center space-x-2">
                          <div
                            className={`w-2 h-2 rounded-full ${getStatusColor(
                              getAssetStatus(selectedAssetForModal)
                            )}`}
                          />
                          <span className="capitalize">
                            {getAssetStatus(selectedAssetForModal)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="text-sm font-medium">Location</p>
                        <p className="text-sm text-gray-600">
                          {selectedAssetForModal.areaName}
                        </p>
                        <p className="text-xs text-gray-500">
                          Zone: {selectedAssetForModal.zone || "N/A"}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Thermometer className="h-5 w-5 text-orange-500" />
                      <div>
                        <p className="text-sm font-medium">Temperature</p>
                        <p className="text-lg font-semibold">
                          {renderSensorValue(
                            selectedAssetForModal.deviceLogId,
                            SensorType.TEMPERATURE
                          )}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Map Section */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">Live Location</h3>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleDeviceMapZoomIn}
                      >
                        <ZoomIn className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleDeviceMapZoomOut}
                      >
                        <ZoomOut className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleReturnToMarker}
                      >
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {hasValidCoordinates(selectedAssetForModal) ? (
                    <div className="h-64 rounded-lg overflow-hidden border">
                      {MAPBOX_TOKEN ? (
                        <Map
                          ref={deviceMapRef}
                          {...deviceMapViewState}
                          onMove={(evt) => setDeviceMapViewState(evt.viewState)}
                          style={{ width: "100%", height: "100%" }}
                          mapStyle="mapbox://styles/mapbox/streets-v12"
                          mapboxAccessToken={MAPBOX_TOKEN}
                        >
                          <Marker
                            longitude={
                              getAssetLocation(selectedAssetForModal)!.longitude
                            }
                            latitude={
                              getAssetLocation(selectedAssetForModal)!.latitude
                            }
                            anchor="bottom"
                          >
                            <div
                              className={`w-6 h-6 rounded-full border-2 ${
                                isLocationUpdating
                                  ? "animate-ping bg-blue-500 border-blue-600"
                                  : "bg-red-500 border-red-600"
                              }`}
                            />
                          </Marker>
                          <NavigationControl position="top-right" />
                        </Map>
                      ) : (
                        <div className="flex items-center justify-center h-full bg-gray-100">
                          <div className="text-center">
                            <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-600">
                              MapBox token required for map display
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="h-64 rounded-lg border flex items-center justify-center bg-gray-50">
                      <div className="text-center">
                        <AlertTriangle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          Coordinates not found for this device
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    window.open(
                      `/asset-tracking/analytics?device=${selectedAssetForModal.deviceId}`,
                      "_blank"
                    );
                  }}
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </Button>
                <Button onClick={handleCloseDeviceModal}>Close</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default function AssetTrackingDashboard() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">
              Loading asset tracking dashboard...
            </p>
          </div>
        </div>
      }
    >
      <AssetTrackingDashboardContent />
    </Suspense>
  );
}
