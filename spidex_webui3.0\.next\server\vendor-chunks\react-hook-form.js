"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\nvar isCheckBoxInput = (element) => element.type === 'checkbox';\n\nvar isDateObject = (value) => value instanceof Date;\n\nvar isNullOrUndefined = (value) => value == null;\n\nconst isObjectType = (value) => typeof value === 'object';\nvar isObject = (value) => !isNullOrUndefined(value) &&\n    !Array.isArray(value) &&\n    isObjectType(value) &&\n    !isDateObject(value);\n\nvar getEventValue = (event) => isObject(event) && event.target\n    ? isCheckBoxInput(event.target)\n        ? event.target.checked\n        : event.target.value\n    : event;\n\nvar getNodeParentName = (name) => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\n\nvar isPlainObject = (tempObject) => {\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return (isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf'));\n};\n\nvar isWeb = typeof window !== 'undefined' &&\n    typeof window.HTMLElement !== 'undefined' &&\n    typeof document !== 'undefined';\n\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== 'undefined' ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    }\n    else if (data instanceof Set) {\n        copy = new Set(data);\n    }\n    else if (!(isWeb && (data instanceof Blob || isFileListInstance)) &&\n        (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        }\n        else {\n            for (const key in data) {\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    }\n    else {\n        return data;\n    }\n    return copy;\n}\n\nvar compact = (value) => Array.isArray(value) ? value.filter(Boolean) : [];\n\nvar isUndefined = (val) => val === undefined;\n\nvar get = (object, path, defaultValue) => {\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object\n        ? isUndefined(object[path])\n            ? defaultValue\n            : object[path]\n        : result;\n};\n\nvar isBoolean = (value) => typeof value === 'boolean';\n\nvar isKey = (value) => /^\\w*$/.test(value);\n\nvar stringToPath = (input) => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n\nvar set = (object, path, value) => {\n    let index = -1;\n    const tempPath = isKey(path) ? [path] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while (++index < length) {\n        const key = tempPath[index];\n        let newValue = value;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue =\n                isObject(objValue) || Array.isArray(objValue)\n                    ? objValue\n                    : !isNaN(+tempPath[index + 1])\n                        ? []\n                        : {};\n        }\n        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n    return object;\n};\n\nconst EVENTS = {\n    BLUR: 'blur',\n    FOCUS_OUT: 'focusout',\n    CHANGE: 'change',\n};\nconst VALIDATION_MODE = {\n    onBlur: 'onBlur',\n    onChange: 'onChange',\n    onSubmit: 'onSubmit',\n    onTouched: 'onTouched',\n    all: 'all',\n};\nconst INPUT_VALIDATION_RULES = {\n    max: 'max',\n    min: 'min',\n    maxLength: 'maxLength',\n    minLength: 'minLength',\n    pattern: 'pattern',\n    required: 'required',\n    validate: 'validate',\n};\n\nconst HookFormContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = (props) => {\n    const { children, ...data } = props;\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, { value: data }, children));\n};\n\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true) => {\n    const result = {\n        defaultValues: control._defaultValues,\n    };\n    for (const key in formState) {\n        Object.defineProperty(result, key, {\n            get: () => {\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            },\n        });\n    }\n    return result;\n};\n\nvar isEmptyObject = (value) => isObject(value) && !Object.keys(value).length;\n\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return (isEmptyObject(formState) ||\n        Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n        Object.keys(formState).find((key) => _proxyFormState[key] ===\n            (!isRoot || VALIDATION_MODE.all)));\n};\n\nvar convertToArrayPayload = (value) => (Array.isArray(value) ? value : [value]);\n\nvar shouldSubscribeByName = (name, signalName, exact) => !name ||\n    !signalName ||\n    name === signalName ||\n    convertToArrayPayload(name).some((currentName) => currentName &&\n        (exact\n            ? currentName === signalName\n            : currentName.startsWith(signalName) ||\n                signalName.startsWith(currentName)));\n\nfunction useSubscribe(props) {\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    _props.current = props;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        const subscription = !props.disabled &&\n            _props.current.subject &&\n            _props.current.subject.subscribe({\n                next: _props.current.next,\n            });\n        return () => {\n            subscription && subscription.unsubscribe();\n        };\n    }, [props.disabled]);\n}\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _mounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    });\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        next: (value) => _mounted.current &&\n            shouldSubscribeByName(_name.current, value.name, exact) &&\n            shouldRenderFormState(value, _localProxyFormState.current, control._updateFormState) &&\n            updateFormState({\n                ...control._formState,\n                ...value,\n            }),\n        subject: control._subjects.state,\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        _mounted.current = true;\n        _localProxyFormState.current.isValid && control._updateValid(true);\n        return () => {\n            _mounted.current = false;\n        };\n    }, [control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => getProxyFormState(formState, control, _localProxyFormState.current, false), [formState, control]);\n}\n\nvar isString = (value) => typeof value === 'string';\n\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName) => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact, } = props || {};\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        subject: control._subjects.values,\n        next: (formState) => {\n            if (shouldSubscribeByName(_name.current, formState.name, exact)) {\n                updateValue(cloneObject(generateWatchOutput(_name.current, control._names, formState.values || control._formValues, false, defaultValue)));\n            }\n        },\n    });\n    const [value, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, defaultValue));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._removeUnmounted());\n    return value;\n}\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true,\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true,\n    });\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value,\n        ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => Object.defineProperties({}, {\n        invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n        },\n    }), [formState, name]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        name,\n        value,\n        ...(isBoolean(disabled) || formState.disabled\n            ? { disabled: formState.disabled || disabled }\n            : {}),\n        onChange: (event) => _registerProps.current.onChange({\n            target: {\n                value: getEventValue(event),\n                name: name,\n            },\n            type: EVENTS.CHANGE,\n        }),\n        onBlur: () => _registerProps.current.onBlur({\n            target: {\n                value: get(control._formValues, name),\n                name: name,\n            },\n            type: EVENTS.BLUR,\n        }),\n        ref: (elm) => {\n            const field = get(control._fields, name);\n            if (field && elm) {\n                field._f.ref = {\n                    focus: () => elm.focus(),\n                    select: () => elm.select(),\n                    setCustomValidity: (message) => elm.setCustomValidity(message),\n                    reportValidity: () => elm.reportValidity(),\n                };\n            }\n        },\n    }), [\n        name,\n        control._formValues,\n        disabled,\n        formState.disabled,\n        value,\n        control._fields,\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        const updateMounted = (name, value) => {\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value);\n            }\n        }\n        !isArrayField && control.register(name);\n        return () => {\n            (isArrayField\n                ? _shouldUnregisterField && !control._state.action\n                : _shouldUnregisterField)\n                ? control.unregister(name)\n                : updateMounted(name, false);\n        };\n    }, [name, control, isArrayField, shouldUnregister]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._updateDisabledField({\n            disabled,\n            fields: control._fields,\n            name,\n        });\n    }, [disabled, name, control]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        field,\n        formState,\n        fieldState,\n    }), [field, formState, fieldState]);\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = (props) => props.render(useController(props));\n\nconst flatten = (obj) => {\n    const output = {};\n    for (const key of Object.keys(obj)) {\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)) {\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        }\n        else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\n\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event) => {\n        let hasError = false;\n        let type = '';\n        await control.handleSubmit(async (data) => {\n            const formData = new FormData();\n            let formDataJson = '';\n            try {\n                formDataJson = JSON.stringify(data);\n            }\n            catch (_a) { }\n            const flattenFormValues = flatten(control._formValues);\n            for (const key in flattenFormValues) {\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson,\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers['Content-Type'],\n                        encType,\n                    ].some((value) => value && value.includes('json'));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...(encType ? { 'Content-Type': encType } : {}),\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData,\n                    });\n                    if (response &&\n                        (validateStatus\n                            ? !validateStatus(response.status)\n                            : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({ response });\n                        type = String(response.status);\n                    }\n                    else {\n                        onSuccess && onSuccess({ response });\n                    }\n                }\n                catch (error) {\n                    hasError = true;\n                    onError && onError({ error });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false,\n            });\n            props.control.setError('root.server', {\n                type,\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        setMounted(true);\n    }, []);\n    return render ? (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit,\n    }))) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", { noValidate: mounted, action: action, method: method, encType: encType, onSubmit: submit, ...rest }, children));\n}\n\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n            ...(errors[name] && errors[name].types ? errors[name].types : {}),\n            [type]: message || true,\n        },\n    }\n    : {};\n\nvar generateId = () => {\n    const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n    });\n};\n\nvar getFocusFieldName = (name, index, options = {}) => options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n        `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n\nvar getValidationModes = (mode) => ({\n    isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n    isOnBlur: mode === VALIDATION_MODE.onBlur,\n    isOnChange: mode === VALIDATION_MODE.onChange,\n    isOnAll: mode === VALIDATION_MODE.all,\n    isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent &&\n    (_names.watchAll ||\n        _names.watch.has(name) ||\n        [..._names.watch].some((watchName) => name.startsWith(watchName) &&\n            /^\\.\\w+/.test(name.slice(watchName.length))));\n\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n    for (const key of fieldsNames || Object.keys(fields)) {\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                }\n                else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                }\n                else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            }\n            else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\n\nvar updateFieldArrayRootError = (errors, error, name) => {\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, 'root', error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\n\nvar isFileInput = (element) => element.type === 'file';\n\nvar isFunction = (value) => typeof value === 'function';\n\nvar isHTMLElement = (value) => {\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value ? value.ownerDocument : 0;\n    return (value instanceof\n        (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement));\n};\n\nvar isMessage = (value) => isString(value);\n\nvar isRadioInput = (element) => element.type === 'radio';\n\nvar isRegex = (value) => value instanceof RegExp;\n\nconst defaultResult = {\n    value: false,\n    isValid: false,\n};\nconst validResult = { value: true, isValid: true };\nvar getCheckboxValue = (options) => {\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options\n                .filter((option) => option && option.checked && !option.disabled)\n                .map((option) => option.value);\n            return { value: values, isValid: !!values.length };\n        }\n        return options[0].checked && !options[0].disabled\n            ? // @ts-expect-error expected to work in the browser\n                options[0].attributes && !isUndefined(options[0].attributes.value)\n                    ? isUndefined(options[0].value) || options[0].value === ''\n                        ? validResult\n                        : { value: options[0].value, isValid: true }\n                    : validResult\n            : defaultResult;\n    }\n    return defaultResult;\n};\n\nconst defaultReturn = {\n    isValid: false,\n    value: null,\n};\nvar getRadioValue = (options) => Array.isArray(options)\n    ? options.reduce((previous, option) => option && option.checked && !option.disabled\n        ? {\n            isValid: true,\n            value: option.value,\n        }\n        : previous, defaultReturn)\n    : defaultReturn;\n\nfunction getValidateError(result, ref, type = 'validate') {\n    if (isMessage(result) ||\n        (Array.isArray(result) && result.every(isMessage)) ||\n        (isBoolean(result) && !result)) {\n        return {\n            type,\n            message: isMessage(result) ? result : '',\n            ref,\n        };\n    }\n}\n\nvar getValueAndMessage = (validationData) => isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n    };\n\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message) => {\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = ((valueAsNumber || isFileInput(ref)) &&\n        isUndefined(ref.value) &&\n        isUndefined(inputValue)) ||\n        (isHTMLElement(ref) && ref.value === '') ||\n        inputValue === '' ||\n        (Array.isArray(inputValue) && !inputValue.length);\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength) => {\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n        };\n    };\n    if (isFieldArray\n        ? !Array.isArray(inputValue) || !inputValue.length\n        : required &&\n            ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n                (isBoolean(inputValue) && !inputValue) ||\n                (isCheckBox && !getCheckboxValue(refs).isValid) ||\n                (isRadio && !getRadioValue(refs).isValid))) {\n        const { value, message } = isMessage(required)\n            ? { value: !!required, message: required }\n            : getValueAndMessage(required);\n        if (value) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber ||\n                (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        }\n        else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time) => new Date(new Date().toDateString() + ' ' + time);\n            const isTime = ref.type == 'time';\n            const isWeek = ref.type == 'week';\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime\n                    ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n                    : isWeek\n                        ? inputValue > maxOutput.value\n                        : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime\n                    ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n                    : isWeek\n                        ? inputValue < minOutput.value\n                        : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) &&\n        !isEmpty &&\n        (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) &&\n            inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) &&\n            inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message),\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        }\n        else if (isObject(validate)) {\n            let validationResult = {};\n            for (const key in validate) {\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message),\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult,\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\n\nvar appendAt = (data, value) => [\n    ...data,\n    ...convertToArrayPayload(value),\n];\n\nvar fillEmptyArray = (value) => Array.isArray(value) ? value.map(() => undefined) : undefined;\n\nfunction insert(data, index, value) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value),\n        ...data.slice(index),\n    ];\n}\n\nvar moveArrayAt = (data, from, to) => {\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\n\nvar prependAt = (data, value) => [\n    ...convertToArrayPayload(value),\n    ...convertToArrayPayload(data),\n];\n\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [...data];\n    for (const index of indexes) {\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index)\n    ? []\n    : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\n\nvar swapArrayAt = (data, indexA, indexB) => {\n    [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while (index < length) {\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for (const key in obj) {\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path)\n        ? path\n        : isKey(path)\n            ? [path]\n            : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 &&\n        ((isObject(childObject) && isEmptyObject(childObject)) ||\n            (Array.isArray(childObject) && isEmptyArray(childObject)))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\n\nvar updateAt = (fieldValues, index, value) => {\n    fieldValues[index] = value;\n    return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = 'id', shouldUnregister, rules, } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules &&\n        control.register(name, rules);\n    useSubscribe({\n        next: ({ values, name: fieldArrayName, }) => {\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n        subject: control._subjects.array,\n    });\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues) => {\n        _actioned.current = true;\n        control._updateFieldArray(name, updatedFieldArrayValues);\n    }, [control, name]);\n    const append = (value, options) => {\n        const appendValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const prepend = (value, options) => {\n        const prependValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value),\n        });\n    };\n    const remove = (index) => {\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) &&\n            set(control._fields, name, undefined);\n        control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index,\n        });\n    };\n    const insert$1 = (index, value, options) => {\n        const insertValue = convertToArrayPayload(cloneObject(value));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value),\n        });\n    };\n    const swap = (indexA, indexB) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB,\n        }, false);\n    };\n    const move = (from, to) => {\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to,\n        }, false);\n    };\n    const update = (index, value) => {\n        const updateValue = cloneObject(value);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([...updatedFieldArrayValues]);\n        control._updateFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue,\n        }, true, false);\n    };\n    const replace = (value) => {\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([...updatedFieldArrayValues]);\n        setFields([...updatedFieldArrayValues]);\n        control._updateFieldArray(name, [...updatedFieldArrayValues], (data) => data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        control._state.action = false;\n        isWatched(name, control._names) &&\n            control._subjects.state.next({\n                ...control._formState,\n            });\n        if (_actioned.current &&\n            (!getValidationModes(control._options.mode).isOnSubmit ||\n                control._formState.isSubmitted)) {\n            if (control._options.resolver) {\n                control._executeSchema([name]).then((result) => {\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError\n                        ? (!error && existingError.type) ||\n                            (error &&\n                                (existingError.type !== error.type ||\n                                    existingError.message !== error.message))\n                        : error && error.type) {\n                        error\n                            ? set(control._formState.errors, name, error)\n                            : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors,\n                        });\n                    }\n                });\n            }\n            else {\n                const field = get(control._fields, name);\n                if (field &&\n                    field._f &&\n                    !(getValidationModes(control._options.reValidateMode).isOnSubmit &&\n                        getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error) => !isEmptyObject(error) &&\n                        control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name),\n                        }));\n                }\n            }\n        }\n        control._subjects.values.next({\n            name,\n            values: { ...control._formValues },\n        });\n        control._names.focus &&\n            iterateFieldsByAction(control._fields, (ref, key) => {\n                if (control._names.focus &&\n                    key.startsWith(control._names.focus) &&\n                    ref.focus) {\n                    ref.focus();\n                    return 1;\n                }\n                return;\n            });\n        control._names.focus = '';\n        control._updateValid();\n        _actioned.current = false;\n    }, [fields, name, control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        !get(control._formValues, name) && control._updateFieldArray(name);\n        return () => {\n            (control._options.shouldUnregister || shouldUnregister) &&\n                control.unregister(name);\n        };\n    }, [name, control, keyName, shouldUnregister]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [updateValues, name, control]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [updateValues, name, control]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [updateValues, name, control]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [updateValues, name, control]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [updateValues, name, control]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [updateValues, name, control]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [updateValues, name, control]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [updateValues, name, control]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => fields.map((field, index) => ({\n            ...field,\n            [keyName]: ids.current[index] || generateId(),\n        })), [fields, keyName]),\n    };\n}\n\nvar createSubject = () => {\n    let _observers = [];\n    const next = (value) => {\n        for (const observer of _observers) {\n            observer.next && observer.next(value);\n        }\n    };\n    const subscribe = (observer) => {\n        _observers.push(observer);\n        return {\n            unsubscribe: () => {\n                _observers = _observers.filter((o) => o !== observer);\n            },\n        };\n    };\n    const unsubscribe = () => {\n        _observers = [];\n    };\n    return {\n        get observers() {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe,\n    };\n};\n\nvar isPrimitive = (value) => isNullOrUndefined(value) || !isObjectType(value);\n\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1) {\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== 'ref') {\n            const val2 = object2[key];\n            if ((isDateObject(val1) && isDateObject(val2)) ||\n                (isObject(val1) && isObject(val2)) ||\n                (Array.isArray(val1) && Array.isArray(val2))\n                ? !deepEqual(val1, val2)\n                : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\n\nvar isMultipleSelect = (element) => element.type === `select-multiple`;\n\nvar isRadioOrCheckbox = (ref) => isRadioInput(ref) || isCheckBoxInput(ref);\n\nvar live = (ref) => isHTMLElement(ref) && ref.isConnected;\n\nvar objectHasFunction = (data) => {\n    for (const key in data) {\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\n\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            }\n            else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for (const key in data) {\n            if (Array.isArray(data[key]) ||\n                (isObject(data[key]) && !objectHasFunction(data[key]))) {\n                if (isUndefined(formValues) ||\n                    isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key])\n                        ? markFieldsDirty(data[key], [])\n                        : { ...markFieldsDirty(data[key]) };\n                }\n                else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            }\n            else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\n\nvar getFieldValueAs = (value, { valueAsNumber, valueAsDate, setValueAs }) => isUndefined(value)\n    ? value\n    : valueAsNumber\n        ? value === ''\n            ? NaN\n            : value\n                ? +value\n                : value\n        : valueAsDate && isString(value)\n            ? new Date(value)\n            : setValueAs\n                ? setValueAs(value)\n                : value;\n\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [...ref.selectedOptions].map(({ value }) => value);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n    const fields = {};\n    for (const name of fieldsNames) {\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [...fieldsNames],\n        fields,\n        shouldUseNativeValidation,\n    };\n};\n\nvar getRuleValue = (rule) => isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n        ? rule.source\n        : isObject(rule)\n            ? isRegex(rule.value)\n                ? rule.value.source\n                : rule.value\n            : rule;\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\nvar hasPromiseValidation = (fieldReference) => !!fieldReference &&\n    !!fieldReference.validate &&\n    !!((isFunction(fieldReference.validate) &&\n        fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n        (isObject(fieldReference.validate) &&\n            Object.values(fieldReference.validate).find((validateFunction) => validateFunction.constructor.name === ASYNC_FUNCTION)));\n\nvar hasValidation = (options) => options.mount &&\n    (options.required ||\n        options.min ||\n        options.max ||\n        options.maxLength ||\n        options.minLength ||\n        options.pattern ||\n        options.validate);\n\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name,\n        };\n    }\n    const names = name.split('.');\n    while (names.length) {\n        const fieldName = names.join('.');\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return { name };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError,\n            };\n        }\n        names.pop();\n    }\n    return {\n        name,\n    };\n}\n\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n    if (mode.isOnAll) {\n        return false;\n    }\n    else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    }\n    else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    }\n    else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\n\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\n\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true,\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props,\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false,\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values)\n        ? cloneObject(_options.defaultValues || _options.values) || {}\n        : {};\n    let _formValues = _options.shouldUnregister\n        ? {}\n        : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false,\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set(),\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false,\n    };\n    const _subjects = {\n        values: createSubject(),\n        array: createSubject(),\n        state: createSubject(),\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback) => (wait) => {\n        clearTimeout(timer);\n        timer = setTimeout(callback, wait);\n    };\n    const _updateValid = async (shouldUpdateValid) => {\n        if (!_options.disabled && (_proxyFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver\n                ? isEmptyObject((await _executeSchema()).errors)\n                : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid,\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating) => {\n        if (!_options.disabled &&\n            (_proxyFormState.isValidating || _proxyFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name) => {\n                if (name) {\n                    isValidating\n                        ? set(_formState.validatingFields, name, isValidating)\n                        : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields),\n            });\n        }\n    };\n    const _updateFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true) => {\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if (_proxyFormState.touchedFields &&\n                shouldUpdateFieldsAndState &&\n                Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid,\n            });\n        }\n        else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error) => {\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors,\n        });\n    };\n    const _setErrors = (errors) => {\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false,\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n            isUndefined(defaultValue) ||\n                (ref && ref.defaultChecked) ||\n                shouldSkipSetValueAs\n                ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f))\n                : setFieldValue(name, defaultValue);\n            _state.mount && _updateValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name,\n        };\n        if (!_options.disabled) {\n            const disabledField = !!(get(_fields, name) &&\n                get(_fields, name)._f &&\n                get(_fields, name)._f.disabled);\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = disabledField || deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!(!disabledField && get(_formState.dirtyFields, name));\n                isCurrentFieldPristine || disabledField\n                    ? unset(_formState.dirtyFields, name)\n                    : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField =\n                    shouldUpdateField ||\n                        (_proxyFormState.dirtyFields &&\n                            isPreviousDirty !== !isCurrentFieldPristine);\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField =\n                        shouldUpdateField ||\n                            (_proxyFormState.touchedFields &&\n                                isPreviousFieldTouched !== isBlurEvent);\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState) => {\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = _proxyFormState.isValid &&\n            isBoolean(isValid) &&\n            _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(() => updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        }\n        else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error\n                ? set(_formState.errors, name, error)\n                : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n            !isEmptyObject(fieldState) ||\n            shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n                errors: _formState.errors,\n                name,\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState,\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _executeSchema = async (name) => {\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names) => {\n        const { errors } = await _executeSchema(names);\n        if (names) {\n            for (const name of names) {\n                const error = get(errors, name);\n                error\n                    ? set(_formState.errors, name, error)\n                    : unset(_formState.errors, name);\n            }\n        }\n        else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true,\n    }) => {\n        for (const name in fields) {\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([name]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid &&\n                        (get(fieldError, _f.name)\n                            ? isFieldArrayRoot\n                                ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name)\n                                : set(_formState.errors, _f.name, fieldError[_f.name])\n                            : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) &&\n                    (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = () => {\n        for (const name of _names.unMount) {\n            const field = get(_fields, name);\n            field &&\n                (field._f.refs\n                    ? field._f.refs.every((ref) => !live(ref))\n                    : !live(field._f.ref)) &&\n                unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data) => !_options.disabled &&\n        (name && data && set(_formValues, name, data),\n            !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n        ...(_state.mount\n            ? _formValues\n            : isUndefined(defaultValue)\n                ? _defaultValues\n                : isString(names)\n                    ? { [names]: defaultValue }\n                    : defaultValue),\n    }, isGlobal, defaultValue);\n    const _getFieldArray = (name) => compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        let fieldValue = value;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled &&\n                    set(_formValues, name, getFieldValueAs(value, fieldReference));\n                fieldValue =\n                    isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n                        ? ''\n                        : value;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [...fieldReference.ref.options].forEach((optionRef) => (optionRef.selected = fieldValue.includes(optionRef.value)));\n                }\n                else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.length > 1\n                            ? fieldReference.refs.forEach((checkboxRef) => (!checkboxRef.defaultChecked || !checkboxRef.disabled) &&\n                                (checkboxRef.checked = Array.isArray(fieldValue)\n                                    ? !!fieldValue.find((data) => data === checkboxRef.value)\n                                    : fieldValue === checkboxRef.value))\n                            : fieldReference.refs[0] &&\n                                (fieldReference.refs[0].checked = !!fieldValue);\n                    }\n                    else {\n                        fieldReference.refs.forEach((radioRef) => (radioRef.checked = radioRef.value === fieldValue));\n                    }\n                }\n                else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = '';\n                }\n                else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.values.next({\n                            name,\n                            values: { ..._formValues },\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) &&\n            updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value, options) => {\n        for (const fieldKey in value) {\n            const fieldValue = value[fieldKey];\n            const fieldName = `${name}.${fieldKey}`;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) ||\n                isObject(fieldValue) ||\n                (field && !field._f)) &&\n                !isDateObject(fieldValue)\n                ? setValues(fieldName, fieldValue, options)\n                : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value, options = {}) => {\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: { ..._formValues },\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields) &&\n                options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue),\n                });\n            }\n        }\n        else {\n            field && !field._f && !isNullOrUndefined(cloneValue)\n                ? setValues(name, cloneValue, options)\n                : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({ ..._formState });\n        _subjects.values.next({\n            name: _state.mount ? name : undefined,\n            values: { ..._formValues },\n        });\n    };\n    const onChange = async (event) => {\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const getCurrentFieldValue = () => target.type ? getFieldValue(field._f) : getEventValue(event);\n        const _updateIsFieldValueUpdated = (fieldValue) => {\n            isFieldValueUpdated =\n                Number.isNaN(fieldValue) ||\n                    (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n                    deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = getCurrentFieldValue();\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = (!hasValidation(field._f) &&\n                !_options.resolver &&\n                !get(_formState.errors, name) &&\n                !field._f.deps) ||\n                skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            }\n            else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent, false);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent &&\n                _subjects.values.next({\n                    name,\n                    type: event.type,\n                    values: { ..._formValues },\n                });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid) {\n                    if (_options.mode === 'onBlur' && isBlurEvent) {\n                        _updateValid();\n                    }\n                    else if (!isBlurEvent) {\n                        _updateValid();\n                    }\n                }\n                return (shouldRender &&\n                    _subjects.state.next({ name, ...(watched ? {} : fieldState) }));\n            }\n            !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n            if (_options.resolver) {\n                const { errors } = await _executeSchema([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            }\n            else {\n                _updateIsValidating([name], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([name]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    }\n                    else if (_proxyFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps &&\n                    trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key) => {\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {}) => {\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name\n                ? !fieldNames.some((name) => get(errors, name))\n                : isValid;\n        }\n        else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName) => {\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? { [fieldName]: field } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _updateValid();\n        }\n        else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...(!isString(name) ||\n                (_proxyFormState.isValid && isValid !== _formState.isValid)\n                ? {}\n                : { name }),\n            ...(_options.resolver || !name ? { isValid } : {}),\n            errors: _formState.errors,\n        });\n        options.shouldFocus &&\n            !validationResult &&\n            iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames) => {\n        const values = {\n            ...(_state.mount ? _formValues : _defaultValues),\n        };\n        return isUndefined(fieldNames)\n            ? values\n            : isString(fieldNames)\n                ? get(values, fieldNames)\n                : fieldNames.map((name) => get(values, name));\n    };\n    const getFieldState = (name, formState) => ({\n        invalid: !!get((formState || _formState).errors, name),\n        isDirty: !!get((formState || _formState).dirtyFields, name),\n        error: get((formState || _formState).errors, name),\n        isValidating: !!get(_formState.validatingFields, name),\n        isTouched: !!get((formState || _formState).touchedFields, name),\n    });\n    const clearErrors = (name) => {\n        name &&\n            convertToArrayPayload(name).forEach((inputName) => unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {},\n        });\n    };\n    const setError = (name, error, options) => {\n        const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref,\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false,\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue) => isFunction(name)\n        ? _subjects.values.subscribe({\n            next: (payload) => name(_getWatch(undefined, defaultValue), payload),\n        })\n        : _getWatch(name, defaultValue, true);\n    const unregister = (name, options = {}) => {\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating &&\n                unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister &&\n                !options.keepDefaultValue &&\n                unset(_defaultValues, fieldName);\n        }\n        _subjects.values.next({\n            values: { ..._formValues },\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n        });\n        !options.keepIsValid && _updateValid();\n    };\n    const _updateDisabledField = ({ disabled, name, field, fields, }) => {\n        if ((isBoolean(disabled) && _state.mount) ||\n            !!disabled ||\n            _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n            updateTouchAndDirty(name, getFieldValue(field ? field._f : get(fields, name)._f), false, false, true);\n        }\n    };\n    const register = (name, options = {}) => {\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...(field || {}),\n            _f: {\n                ...(field && field._f ? field._f : { ref: { name } }),\n                name,\n                mount: true,\n                ...options,\n            },\n        });\n        _names.mount.add(name);\n        if (field) {\n            _updateDisabledField({\n                field,\n                disabled: isBoolean(options.disabled)\n                    ? options.disabled\n                    : _options.disabled,\n                name,\n            });\n        }\n        else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...(disabledIsDefined\n                ? { disabled: options.disabled || _options.disabled }\n                : {}),\n            ...(_options.progressive\n                ? {\n                    required: !!options.required,\n                    min: getRuleValue(options.min),\n                    max: getRuleValue(options.max),\n                    minLength: getRuleValue(options.minLength),\n                    maxLength: getRuleValue(options.maxLength),\n                    pattern: getRuleValue(options.pattern),\n                }\n                : {}),\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref) => {\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value)\n                        ? ref.querySelectorAll\n                            ? ref.querySelectorAll('input,select,textarea')[0] || ref\n                            : ref\n                        : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox\n                        ? refs.find((option) => option === fieldRef)\n                        : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...(radioOrCheckbox\n                                ? {\n                                    refs: [\n                                        ...refs.filter(live),\n                                        fieldRef,\n                                        ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                                    ],\n                                    ref: { type: fieldRef.type, name },\n                                }\n                                : { ref: fieldRef }),\n                        },\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                }\n                else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) &&\n                        !(isNameInFieldArray(_names.array, name) && _state.action) &&\n                        _names.unMount.add(name);\n                }\n            },\n        };\n    };\n    const _focusError = () => _options.shouldFocusError &&\n        iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled) => {\n        if (isBoolean(disabled)) {\n            _subjects.state.next({ disabled });\n            iterateFieldsByAction(_fields, (ref, name) => {\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef) => {\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid) => async (e) => {\n        let onValidError = undefined;\n        if (e) {\n            e.preventDefault && e.preventDefault();\n            e.persist && e.persist();\n        }\n        let fieldValues = cloneObject(_formValues);\n        if (_names.disabled.size) {\n            for (const name of _names.disabled) {\n                set(fieldValues, name, undefined);\n            }\n        }\n        _subjects.state.next({\n            isSubmitting: true,\n        });\n        if (_options.resolver) {\n            const { errors, values } = await _executeSchema();\n            _formState.errors = errors;\n            fieldValues = values;\n        }\n        else {\n            await executeBuiltInValidation(_fields);\n        }\n        unset(_formState.errors, 'root');\n        if (isEmptyObject(_formState.errors)) {\n            _subjects.state.next({\n                errors: {},\n            });\n            try {\n                await onValid(fieldValues, e);\n            }\n            catch (error) {\n                onValidError = error;\n            }\n        }\n        else {\n            if (onInvalid) {\n                await onInvalid({ ..._formState.errors }, e);\n            }\n            _focusError();\n            setTimeout(_focusError);\n        }\n        _subjects.state.next({\n            isSubmitted: true,\n            isSubmitting: false,\n            isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n            submitCount: _formState.submitCount + 1,\n            errors: _formState.errors,\n        });\n        if (onValidError) {\n            throw onValidError;\n        }\n    };\n    const resetField = (name, options = {}) => {\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            }\n            else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue\n                    ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n                    : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _updateValid();\n            }\n            _subjects.state.next({ ..._formState });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {}) => {\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)) {\n                    get(_formState.dirtyFields, fieldName)\n                        ? set(values, fieldName, get(_formValues, fieldName))\n                        : setValue(fieldName, get(values, fieldName));\n                }\n            }\n            else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount) {\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs)\n                                ? field._f.refs[0]\n                                : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest('form');\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                _fields = {};\n            }\n            _formValues = _options.shouldUnregister\n                ? keepStateOptions.keepDefaultValues\n                    ? cloneObject(_defaultValues)\n                    : {}\n                : cloneObject(values);\n            _subjects.array.next({\n                values: { ...values },\n            });\n            _subjects.values.next({\n                values: { ...values },\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: '',\n        };\n        _state.mount =\n            !_proxyFormState.isValid ||\n                !!keepStateOptions.keepIsValid ||\n                !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount\n                ? _formState.submitCount\n                : 0,\n            isDirty: isEmptyResetValues\n                ? false\n                : keepStateOptions.keepDirty\n                    ? _formState.isDirty\n                    : !!(keepStateOptions.keepDefaultValues &&\n                        !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted\n                ? _formState.isSubmitted\n                : false,\n            dirtyFields: isEmptyResetValues\n                ? {}\n                : keepStateOptions.keepDirtyValues\n                    ? keepStateOptions.keepDefaultValues && _formValues\n                        ? getDirtyFields(_defaultValues, _formValues)\n                        : _formState.dirtyFields\n                    : keepStateOptions.keepDefaultValues && formValues\n                        ? getDirtyFields(_defaultValues, formValues)\n                        : keepStateOptions.keepDirty\n                            ? _formState.dirtyFields\n                            : {},\n            touchedFields: keepStateOptions.keepTouched\n                ? _formState.touchedFields\n                : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n                ? _formState.isSubmitSuccessful\n                : false,\n            isSubmitting: false,\n        });\n    };\n    const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues)\n        ? formValues(_formValues)\n        : formValues, keepStateOptions);\n    const setFocus = (name, options = {}) => {\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs\n                ? fieldReference.refs[0]\n                : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect &&\n                    isFunction(fieldRef.select) &&\n                    fieldRef.select();\n            }\n        }\n    };\n    const _updateFormState = (updatedFormState) => {\n        _formState = {\n            ..._formState,\n            ...updatedFormState,\n        };\n    };\n    const _resetDefaultValues = () => isFunction(_options.defaultValues) &&\n        _options.defaultValues().then((values) => {\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false,\n            });\n        });\n    return {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _executeSchema,\n            _getWatch,\n            _getDirty,\n            _updateValid,\n            _removeUnmounted,\n            _updateFieldArray,\n            _updateDisabledField,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _updateFormState,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            _setErrors,\n            get _fields() {\n                return _fields;\n            },\n            get _formValues() {\n                return _formValues;\n            },\n            get _state() {\n                return _state;\n            },\n            set _state(value) {\n                _state = value;\n            },\n            get _defaultValues() {\n                return _defaultValues;\n            },\n            get _names() {\n                return _names;\n            },\n            set _names(value) {\n                _names = value;\n            },\n            get _formState() {\n                return _formState;\n            },\n            set _formState(value) {\n                _formState = value;\n            },\n            get _options() {\n                return _options;\n            },\n            set _options(value) {\n                _options = {\n                    ..._options,\n                    ...value,\n                };\n            },\n        },\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState,\n    };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        defaultValues: isFunction(props.defaultValues)\n            ? undefined\n            : props.defaultValues,\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...createFormControl(props),\n            formState,\n        };\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useSubscribe({\n        subject: control._subjects.state,\n        next: (value) => {\n            if (shouldRenderFormState(value, control._proxyFormState, control._updateFormState, true)) {\n                updateFormState({ ...control._formState });\n            }\n        },\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty,\n                });\n            }\n        }\n    }, [control, formState.isDirty]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state) => ({ ...state }));\n        }\n        else {\n            control._resetDefaultValues();\n        }\n    }, [props.values, control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (props.errors) {\n            control._setErrors(props.errors);\n        }\n    }, [props.errors, control]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        if (!control._state.mount) {\n            control._updateValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({ ...control._formState });\n        }\n        control._removeUnmounted();\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        props.shouldUnregister &&\n            control._subjects.values.next({\n                values: control._getWatch(),\n            });\n    }, [props.shouldUnregister, control]);\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n\n\n//# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;