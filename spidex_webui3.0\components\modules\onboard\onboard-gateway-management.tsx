"use client";

import { useEffect } from "react";
import { RefreshCw, Router, Search } from "lucide-react";
import { useSession } from "next-auth/react";
import { useGatewayManagement } from "@/hooks/use-gateway-management";
import { getTableConfig } from "@/config/table-config";
import {
  Gateway,
  UpdateGatewayFormData,
  GatewayPageSize,
} from "@/types/gateway";
import { OnboardGatewayDataTable } from "./onboard-gateway-data-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { toast } from "sonner";

export function OnboardGatewayManagement() {
  const { data: session } = useSession();

  // Get table configuration for this page
  const tableConfig = getTableConfig("onboard-gateway-management");

  const {
    gateways: paginatedGateways,
    isLoading,
    error,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    loadData,
    updateGateway,
    goToPage,
    changePageSize,
    updateSearchFilters,
    availablePageSizes,
  } = useGatewayManagement({
    initialPageSize: tableConfig.defaultPageSize,
    availablePageSizes: tableConfig.availablePageSizes as GatewayPageSize[],
  });

  // Load data on component mount
  useEffect(() => {
    const initializeData = async () => {
      try {
        await loadData();
      } catch (error) {
        console.error("Error initializing data:", error);
      }
    };

    initializeData();
  }, [loadData]); // Include loadData dependency

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await loadData();
      toast.success("Data refreshed successfully");
    } catch (error) {
      console.error("Refresh error:", error);
      toast.error("Failed to refresh data");
    }
  };

  // Handle provision all
  const handleProvisionAll = async () => {
    try {
      const unprovisionedGateways = paginatedGateways.filter(
        (gateway: Gateway) => !gateway.provisioned
      );

      if (unprovisionedGateways.length === 0) {
        toast.info("No gateways to provision");
        return;
      }

      for (const gateway of unprovisionedGateways) {
        await updateGateway({
          ...gateway,
          provisioned: true,
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        } as UpdateGatewayFormData);
      }

      await loadData();
      toast.success(
        `Successfully provisioned ${unprovisionedGateways.length} gateways`
      );
    } catch (error) {
      console.error("Provision all error:", error);
      toast.error("Failed to provision all gateways");
    }
  };

  // Handle provision single gateway
  const handleProvisionGateway = async (
    gateway: Gateway,
    provisioned: boolean
  ) => {
    try {
      await updateGateway({
        ...gateway,
        provisioned,
        modifiedBy: session?.user?.userId || "system",
        modifiedTime: Date.now(),
      } as UpdateGatewayFormData);

      await loadData();
      toast.success(
        provisioned
          ? "Gateway provisioned successfully"
          : "Gateway unprovisioned successfully"
      );
    } catch (error) {
      console.error("Provision gateway error:", error);
      toast.error("Failed to update gateway provision status");
    }
  };

  return (
    <>
      {/* Search and Actions */}
      <div className="p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Label htmlFor="search" className="sr-only">
              Search gateways
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                type="text"
                placeholder="Search by gateway name, external ID, type..."
                value={searchFilters.searchTerm || ""}
                onChange={(e) =>
                  updateSearchFilters({ searchTerm: e.target.value })
                }
                className="pl-10"
                disabled={isLoading}
              />
            </div>
          </div>
          <Button
            onClick={handleProvisionAll}
            disabled={isLoading || paginatedGateways.length === 0}
            className="whitespace-nowrap"
          >
            Provision All
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <div className="p-4 pt-0">
        <OnboardGatewayDataTable
          data={paginatedGateways}
          isLoading={isLoading}
          onProvision={handleProvisionGateway}
          pagination={pagination}
          totalRecords={totalRecords}
          totalPages={totalPages}
          availablePageSizes={availablePageSizes}
          onPageChange={goToPage}
          onPageSizeChange={(size: number) => changePageSize(size as any)}
        />
      </div>
    </>
  );
}
