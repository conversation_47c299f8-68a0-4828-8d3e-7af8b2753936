"use server";

import fetchWithAuth from "@/lib/auth/fetchWithAuth";
import {
  Location,
  CreateLocationFormData,
  UpdateLocationFormData,
} from "@/types/location";

/**
 * Fetch all locations for a tenant
 */
export async function getAllLocations(tenantId: string): Promise<Location[]> {
  const res = await fetchWithAuth(`/location/tId/?tenantId=${tenantId}`);
  return res.json();
}

/**
 * Create a new location
 */
export async function createLocation(
  locationData: CreateLocationFormData
): Promise<Location> {
  const res = await fetchWithAuth("/location/", {
    method: "POST",
    body: JSON.stringify(locationData),
  });
  return res.json();
}

/**
 * Update an existing location
 */
export async function updateLocation(
  locationData: UpdateLocationFormData
): Promise<Location> {
  const res = await fetchWithAuth("/location/", {
    method: "PUT",
    body: JSON.stringify(locationData),
  });
  return res.json();
}

/**
 * Delete a location (soft delete)
 */
export async function deleteLocation(locationId: string): Promise<void> {
  await fetchWithAuth(`/location/${locationId}`, {
    method: "DELETE",
  });
}

/**
 * Get a single location by ID
 */
export async function getLocationById(locationId: string): Promise<Location> {
  const res = await fetchWithAuth(`/location/${locationId}`);
  return res.json();
}
