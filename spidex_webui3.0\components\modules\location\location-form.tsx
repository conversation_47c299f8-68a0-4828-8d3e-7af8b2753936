"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useState, useEffect } from "react";
import { Location } from "@/types/location";
import {
  CreateLocationSchema,
  UpdateLocationSchema,
  CreateLocationFormData,
  UpdateLocationFormData,
} from "@/lib/schemas";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MapPin, Loader2, Building2 } from "lucide-react";
import { GoogleMapModal } from "@/components/ui/google-map-modal";
import { MapIcon } from "@/components/ui/map-icon";

interface LocationFormProps {
  location?: Location;
  branches?: Array<{ id: string; name: string }>;
  isLoading?: boolean;
  onSubmit: (
    data: CreateLocationFormData | UpdateLocationFormData
  ) => Promise<void>;
  onCancel?: () => void;
  onClear?: () => void;
}

export function LocationForm({
  location,
  branches = [],
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: LocationFormProps) {
  const isEditing = !!location;
  const schema = isEditing ? UpdateLocationSchema : CreateLocationSchema;
  const [isMapOpen, setIsMapOpen] = useState(false);

  const form = useForm<CreateLocationFormData | UpdateLocationFormData>({
    resolver: zodResolver(schema),
    defaultValues: isEditing
      ? {
          id: location.id,
          name: location.name,
          address: location.address,
          branchId: location.branchId,
          geoJson: location.geoJson || "",
          gpsPoint: {
            latitude: location.gpsPoint.latitude,
            longitude: location.gpsPoint.longitude,
          },
        }
      : {
          name: "",
          address: "",
          branchId: "",
          geoJson: "",
          gpsPoint: {
            latitude: "",
            longitude: "",
          },
        },
  });

  // Reset form when location changes
  useEffect(() => {
    if (isEditing && location) {
      form.reset({
        id: location.id,
        name: location.name,
        address: location.address,
        branchId: location.branchId,
        geoJson: location.geoJson || "",
        gpsPoint: {
          latitude: location.gpsPoint.latitude,
          longitude: location.gpsPoint.longitude,
        },
      });
    }
  }, [location, isEditing, form]);

  const handleSubmit = async (
    data: CreateLocationFormData | UpdateLocationFormData
  ) => {
    try {
      await onSubmit(data);
      if (!isEditing) {
        form.reset();
      }
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  const handleClear = () => {
    form.reset();
    onClear?.();
  };

  const handleLocationSelect = (location: {
    latitude: number;
    longitude: number;
  }) => {
    form.setValue("gpsPoint.latitude", location.latitude.toString());
    form.setValue("gpsPoint.longitude", location.longitude.toString());
    setIsMapOpen(false);
  };

  return (
    <React.Fragment>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardContent className="space-y-4 pt-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="(1-200 characters)"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="branchId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Branch</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select the branch" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {branches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id}>
                            {branch.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="(1-200 characters)"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    
                    <FormMessage />
                  </FormItem>
                )}
                
              />
              <div className="space-y-4">
              <FormField
                control={form.control}
                name="geoJson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Geo</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="(1-2000 characters)"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    
                    <FormMessage />
                  </FormItem>
                )}
              />
              {/* GPS Coordinates */}
              <div className="flex items-end gap-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1">
                  <FormField
                    control={form.control}
                    name="gpsPoint.latitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Latitude</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="(-90 to 90)"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="gpsPoint.longitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Longitude</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="(-180 to 180)"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Map Icon Button */}
                <div className="flex-shrink-0">
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setIsMapOpen(true)}
                    disabled={isLoading}
                    className="h-10 w-10"
                  >
                    <MapIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
            </CardContent>
          </Card>
          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex items-center gap-2 border bg-green-700 text-white hover:bg-green-800 rounded"
            >
              {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
              {isEditing ? "Update Location" : "Create Location"}
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={handleClear}
              disabled={isLoading}
              className="border bg-white border-gray-700 text-black hover:bg-green-20 rounded"
            >
              Clear
            </Button>

            {onCancel && (
              <Button
                type="button"
                variant="secondary"
                onClick={onCancel}
                disabled={isLoading}
                className="border bg-white border-red-700 text-black hover:bg-green-20 rounded"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </Form>

      {/* Google Map Modal for location selection */}
      {isMapOpen && (
        <GoogleMapModal
          isOpen={isMapOpen}
          onClose={() => setIsMapOpen(false)}
          onLocationSelect={handleLocationSelect}
        />
      )}
    </React.Fragment>
  );
}
