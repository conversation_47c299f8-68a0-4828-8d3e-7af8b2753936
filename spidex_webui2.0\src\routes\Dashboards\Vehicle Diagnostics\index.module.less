@import '../../../Colors.less';

.title {
  font-size: 1.5rem !important;
  font-weight: bold !important;
  color: @primary-color-hex !important;
  padding: 20px !important;
}

.drawer {
  .title {
    font-size: 1.5rem;
    font-weight: bold;
    background-color: @white-hex !important;
    color: @primary-color-hex;
  }
  .uploadContainer {
    padding: 0 5px 10px 20px;
  }
  .tableContainer {
    padding: 0 20px 0 20px !important;
  }
  :global {
    .ant-drawer-header {
      padding-top: 0px !important;
      padding-right: 24px !important;
      padding-bottom: 0px !important;
      padding-left: 24px !important;
      border-bottom: 5px solid #f0f2f5 !important;
    }
    .ant-drawer-close {
      .anticon-close {
        color: @primary-color-hex !important;
      }
    }
    .ant-drawer-content {
      background-color: transparent !important;
    }
    .ant-drawer-body {
      padding: 20px;
      background-color: @white-hex !important;
    }
    .ant-spin-nested-loading {
      width: 100%;
    }
  }
}
.warningInfo {
  padding-top: 4px;
  color: red;
}
