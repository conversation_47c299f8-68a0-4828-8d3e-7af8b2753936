// Area Management Types

export interface GpsPoint {
  latitude: string;
  longitude: string;
}

export interface Area {
  id: string;
  name: string;
  address: string;
  branchId: string;
  branchName?: string; // This will be populated from branch data
  locationId: string;
  locationName?: string; // This will be populated from location data
  gpsPoint: GpsPoint;
  geoJson: string;
  level?: string | null;
  min?: string;
  max?: string;
  properties: Record<string, any> | null;
  tenantId: string;
  deleted: boolean;
  createdBy: string;
  createdTime: number;
  modifiedBy: string;
  modifiedTime: number;
}

export interface CreateAreaFormData {
  name: string;
  address: string;
  branchId: string;
  locationId: string;
  gpsPoint: GpsPoint;
  geoJson?: string;
  level?: string;
  min?: string;
  max?: string;
}

export interface UpdateAreaFormData {
  id: string;
  name: string;
  address: string;
  branchId: string;
  locationId: string;
  gpsPoint: GpsPoint;
  geoJson?: string;
  level?: string;
  min?: string;
  max?: string;
}

export interface AreaSearchFilters {
  searchTerm?: string;
  branchId?: string;
  locationId?: string;
  deleted?: boolean;
}

export interface AreaPaginationParams {
  pageNumber: number;
  pageSize: AreaPageSize;
}

// Page size options for area management
export type AreaPageSize = 10 | 25 | 50 | 100 | 1000 | "all";

export const DEFAULT_AREA_PAGE_SIZE: AreaPageSize = 25;
export const AREA_PAGE_SIZES: AreaPageSize[] = [10, 25, 50, 100];
export const AREA_PAGE_SIZE_ALL: AreaPageSize = "all";

// Form validation types
export interface AreaFormErrors {
  name?: string;
  address?: string;
  branchId?: string;
  locationId?: string;
  gpsPoint?: {
    latitude?: string;
    longitude?: string;
  };
  geoJson?: string;
  level?: string;
  min?: string;
  max?: string;
}

// Table column types
export interface AreaTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  responsive?: string[];
  render?: (value: any, record: Area) => React.ReactNode;
}

// Action types for area management
export type AreaAction = "create" | "update" | "delete" | "view";

// Area status for UI display
export type AreaStatus = "active" | "inactive";

export interface AreaStats {
  total: number;
  active: number;
  inactive: number;
  byBranch: Record<string, number>;
  byLocation: Record<string, number>;
}

// Location data for area form
export interface LocationOption {
  id: string;
  name: string;
  branchId: string;
  branchName: string;
}

// Branch data for area form
export interface BranchOption {
  id: string;
  name: string;
}

// Map location data for What3Words integration
export interface MapLocationData {
  latitude: number;
  longitude: number;
  geoJson?: string;
}

// Area form state
export interface AreaFormState {
  isLoading: boolean;
  errors: AreaFormErrors;
  isDirty: boolean;
  isValid: boolean;
}

// Area management context
export interface AreaManagementContext {
  areas: Area[];
  branches: BranchOption[];
  locations: LocationOption[];
  isLoading: boolean;
  error: string | null;
  selectedArea: Area | null;
  formState: AreaFormState;
}
