"use client";

import { useState } from "react";
import { Plus } from "lucide-react";
import { useSession } from "next-auth/react";
import { useRoleManagement } from "@/hooks/use-role-management";
import { usePageManagement } from "@/hooks/use-page-management";
import { Role, CreateRoleFormData, PermissionMatrix } from "@/types/rbac";
import { RoleDataTable } from "./role-data-table";
import { RoleSearchPagination } from "./role-search-pagination";
import { RoleForm } from "./role-form";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";
import { toast } from "sonner";

export default function RoleManagement() {
  const { data: session, status } = useSession();
  const {
    roles: paginatedRoles,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    createRole,
    deleteRole,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useRoleManagement();

  const { pages, isLoading: pagesLoading } = usePageManagement();

  // Form state
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // Form handlers
  const openCreateForm = () => {
    setEditingRole(null);
    setIsFormOpen(true);
  };

  const openEditForm = (role: Role) => {
    setEditingRole(role);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setEditingRole(null);
    setFormLoading(false);
  };

  const handleFormSubmit = async (formData: CreateRoleFormData) => {
    setFormLoading(true);
    try {
      if (editingRole) {
        // Update role - not implemented in this version
        console.log("Role update not implemented");
        toast.error("Role update not implemented", {
          description:
            "Role update functionality is not available in this version.",
        });
      } else {
        // Create role
        await createRole(formData);
        toast.success("Role created successfully", {
          description: `${formData.name} has been added to the system.`,
          style: {
            background: "#22c55e",
            color: "#000000",
            border: "1px solid #16a34a",
          },
        });
      }
      closeForm();
    } catch (error) {
      console.error("Error submitting role form:", error);
      toast.error("Failed to create role", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while creating the role.",
      });
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteRole = async (role: Role) => {
    try {
      await deleteRole(role.id);
      toast.success("Role deleted successfully", {
        description: `${role.name} has been deleted from the system.`,
        style: {
          background: "#22c55e",
          color: "#000000",
          border: "1px solid #16a34a",
        },
      });
    } catch (error) {
      console.error("Error deleting role:", error);
      toast.error("Failed to delete role", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while deleting the role.",
      });
    }
  };

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  if (!session) {
    return <div>Please log in to access role management.</div>;
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/user-management">
                User Management
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Role Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={openCreateForm} className="gap-2 ml-auto">
          <Plus className="h-4 w-4" />
          Add Role
        </Button>
      </header>
      <main className="p-4">
        {/* Search and Pagination Controls */}
        <RoleSearchPagination
          searchFilters={searchFilters}
          showDeleted={showDeleted}
          totalRecords={totalRecords}
          onUpdateSearchFilters={updateSearchFilters}
          onClearSearch={clearSearch}
          onToggleShowDeleted={toggleShowDeleted}
        />

        {/* Data Table */}
        <div className="mt-6">
          <RoleDataTable
            data={paginatedRoles}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onEdit={openEditForm}
            onDelete={handleDeleteRole}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
          />
        </div>
      </main>

      {/* Role Form Sheet */}
      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-1/2 sm:max-w-4xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle>
              {editingRole ? "Edit Role" : "Create New Role"}
            </SheetTitle>
            <SheetDescription>
              {editingRole
                ? "Update the role information and permissions."
                : "Create a new role with appropriate permissions."}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <RoleForm
              role={editingRole || undefined}
              pages={pages}
              isLoading={formLoading || pagesLoading}
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
              onClear={() => console.log("Form cleared")}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
