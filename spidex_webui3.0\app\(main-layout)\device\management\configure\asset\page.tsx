import { Suspense } from "react";
import { Metadata } from "next";
import AssetManagement from "@/components/modules/asset/asset-management";
import { Skeleton } from "@/components/ui/skeleton";
import {
  TableSkeleton,
  ASSET_TABLE_COLUMNS,
} from "@/components/ui/table-skeleton";
import { Card, CardContent } from "@/components/ui/card";

export const metadata: Metadata = {
  title: "Asset Management - Spidex",
  description: "Create and manage assets for your organization",
};

function AssetPageSkeleton() {
  return (
    <div className="space-y-6">
      {/* Breadcrumb skeleton */}
      <div className="flex items-center space-x-2">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-28" />
      </div>

      {/* Header skeleton */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-28" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      {/* Search and filters skeleton */}
      <div className="rounded-lg border">
        <div className="p-6 space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-96" />
          </div>
          <div className="flex flex-col sm:flex-row gap-4">
            <Skeleton className="h-10 flex-1" />
            <Skeleton className="h-10 w-48" />
          </div>
          <div className="flex gap-4">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-8 w-24" />
          </div>
          <div className="flex gap-4 pt-2 border-t">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-18" />
          </div>
        </div>
      </div>

      {/* Data Table Skeleton */}
      <Card>
        <CardContent className="p-0">
          <TableSkeleton
            columns={ASSET_TABLE_COLUMNS}
            rows={10}
            showPagination={true}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default function AssetPage() {
  return (
    <Suspense fallback={<AssetPageSkeleton />}>
      <AssetManagement />
    </Suspense>
  );
}
