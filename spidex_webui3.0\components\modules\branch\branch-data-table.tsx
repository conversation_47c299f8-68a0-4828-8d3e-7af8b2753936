"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Branch, BranchPaginationParams, BranchPageSize } from "@/types/branch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { BranchTablePagination } from "./branch-table-pagination";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  RotateCcw,
  MapPin,
  Calendar,
} from "lucide-react";

interface BranchDataTableProps {
  data: Branch[];
  pagination: BranchPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: BranchPageSize[];
  onEdit: (branch: Branch) => void;
  onDelete: (branchId: string) => void;
  onToggleActive?: (branch: Branch) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: BranchPageSize) => void;
}

export function BranchDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onEdit,
  onDelete,
  onToggleActive,
  onPageChange,
  onPageSizeChange,
}: BranchDataTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [branchToDelete, setBranchToDelete] = useState<Branch | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Reset dropdown state when data changes
  useEffect(() => {
    setOpenDropdownId(null);
  }, [data]);

  const handleDeleteClick = (branch: Branch) => {
    setBranchToDelete(branch);
    setDeleteDialogOpen(true);
    setOpenDropdownId(null); // Close dropdown
  };

  const handleDeleteConfirm = () => {
    if (branchToDelete) {
      onDelete(branchToDelete.id);
      setBranchToDelete(null);
      setDeleteDialogOpen(false);
    }
  };

  const handleEditClick = (branch: Branch) => {
    setOpenDropdownId(null); // Close dropdown first
    // Small delay to ensure dropdown closes before opening modal
    setTimeout(() => {
      onEdit(branch);
    }, 50);
  };

  const handleDropdownOpenChange = (open: boolean, branchId: string) => {
    if (open) {
      setOpenDropdownId(branchId);
    } else {
      setOpenDropdownId(null);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatGpsPoint = (gpsPoint: {
    latitude: string;
    longitude: string;
  }) => {
    return `${parseFloat(gpsPoint.latitude).toFixed(4)}, ${parseFloat(
      gpsPoint.longitude
    ).toFixed(4)}`;
  };

  return (
    <div>
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-left">Actions</TableHead>
              <TableHead className="w-[200px]">Name</TableHead>
              <TableHead className="hidden md:table-cell">Address</TableHead>
              <TableHead className="hidden lg:table-cell">
                GPS Coordinates
              </TableHead>
              <TableHead className="hidden xl:table-cell">Status</TableHead>
              <TableHead className="hidden xl:table-cell">Created</TableHead>
              <TableHead className="hidden xl:table-cell">Modified</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <MapPin className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">No branches found</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              data.map((branch) => (
                <TableRow
                  key={branch.id}
                  className={branch.deleted ? "opacity-50" : ""}
                >
                  <TableCell className="text-left">
                    <DropdownMenu
                      open={openDropdownId === branch.id}
                      onOpenChange={(open) =>
                        handleDropdownOpenChange(open, branch.id)
                      }
                    >
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {branch.deleted ? (
                          onToggleActive && (
                            <DropdownMenuItem
                              onClick={() => {
                                onToggleActive(branch);
                                setOpenDropdownId(null);
                              }}
                              className="text-green-600"
                            >
                              <RotateCcw className="mr-2 h-4 w-4" />
                              Activate
                            </DropdownMenuItem>
                          )
                        ) : (
                          <>
                            <DropdownMenuItem
                              onClick={() => handleEditClick(branch)}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteClick(branch)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <Link
                        href={`/device/management/configure/location/${branch.id}`}
                        className="font-semibold text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {branch.name}
                      </Link>
                      <span className="text-sm text-muted-foreground md:hidden">
                        {branch.address}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <div
                      className="max-w-[200px] truncate"
                      title={branch.address}
                    >
                      {branch.address}
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span className="text-sm font-mono">
                        {formatGpsPoint(branch.gpsPoint)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <Badge variant={branch.deleted ? "destructive" : "default"}>
                      {branch.deleted ? "Inactive" : "Active"}
                    </Badge>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {formatDate(branch.createdTime)}
                    </div>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {formatDate(branch.modifiedTime)}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <BranchTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Branch</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the branch &quot;
              {branchToDelete?.name}&quot;? This action cannot be undone and
              will mark the branch as inactive.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
