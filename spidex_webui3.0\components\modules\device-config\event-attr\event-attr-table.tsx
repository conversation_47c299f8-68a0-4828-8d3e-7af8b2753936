"use client";
import { DataTable } from "@/components/ui/data-table";
import { EventAttr } from "@/lib/types";
import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const columns: ColumnDef<EventAttr>[] = [
  {
    id: "actions",
    cell: ({ row }) => {
      const eventAttr = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() =>
                navigator.clipboard.writeText(String(eventAttr.attributeId))
              }
            >
              Copy attribute ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Edit</DropdownMenuItem>
            <DropdownMenuItem>Delete</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "category",
    header: "Amount",
  },
  {
    accessorKey: "unitType",
    header: "Unit Type",
  },
  {
    accessorKey: "dataType",
    header: "Data Type",
  },
  {
    header: "EPE",
    accessorFn: (row) => {
      if (row.epeProperties.egress) {
        return `${row.epeProperties.ingress}, ${row.epeProperties.egress}`;
      }
      return `${row.epeProperties.ingress}`;
    },
  },
  {
    accessorKey: "epeProperties.deMultiplexing",
    header: "Multiplexing",
  },
  {
    accessorKey: "epeProperties.dataConversion",
    header: "Data Conversion",
  },
  {
    accessorKey: "enable",
    header: "Enable",
  },
];

export default function EventAttrTable({ data }: { data: EventAttr[] }) {
  return <DataTable columns={columns} data={data} />;
}
