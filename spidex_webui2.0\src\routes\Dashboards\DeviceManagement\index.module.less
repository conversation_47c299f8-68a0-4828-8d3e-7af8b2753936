@import '../../../Colors.less';

.dashboardCheckbox {
  margin-right: 20px;
}

.burstDiv {
  div {
    svg {
      width: 100% !important;
      height: 75vh !important;
    }
  }
}

.burstDivCompact {
  div {
    svg {
      height: 294px !important;
      width: 100% !important;
    }
  }
}

.burstArea {
  margin-top: -10px;
  padding: 20px !important;
}

.statImage {
  height: 300px !important;
  width: 300px !important;
}

.cardCustomHeader {
  animation: fadeInLeft;
  animation-duration: 2s;
  :global {
    .ant-card-body {
      div {
        table {
          margin: auto !important;
        }
      }
    }
  }
}
.statRow {
  margin-bottom: 10px;
}
.sensorTab {
  margin-top: 20px;
}

:global {
  .CircularProgressbar {
    height: 80px !important;
  }
  div[data-test-id='CircularProgressbarWithChildren__children'] {
    position: absolute !important;
    width: 80px !important;
    height: 80px !important;
    margin-top: -70% !important;
    margin-left: 15% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
  }
}

.mapView {
  border-radius: 4px;
  border: 1px solid @solitude-hex;
  min-height: 300px;
  margin-bottom: 20px;
  .data {
    padding: 20px;
  }
  .map {
    height: 300px;
  }
  .gatewaySelection {
    width: 150px;
    margin: 10px 0 10px 0;
  }
}

.radialSeparators {
  background: @white-hex;
  width: 2px;
  height: 10%;
}
