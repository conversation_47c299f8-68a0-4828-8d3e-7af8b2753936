"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { SpidexApiService } from "@/lib/api/spidex-api";
import {
  Tenant,
  TenantSearchFilters,
  TenantPaginationParams,
  TenantPageSize,
  CreateTenantFormData,
  UpdateTenantFormData,
} from "@/types/tenant";

export function useTenantManagement() {
  const { data: session } = useSession();
  const spidexApi = useMemo(() => SpidexApiService.getInstance(), []);

  // State management
  const [allTenants, setAllTenants] = useState<Tenant[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);

  // Search and pagination state
  const [searchFilters, setSearchFilters] = useState<TenantSearchFilters>({
    searchTerm: "",
  });
  const [pagination, setPagination] = useState<TenantPaginationParams>({
    pageNumber: 1,
    pageSize: 10,
  });

  // Available page sizes
  const availablePageSizes: TenantPageSize[] = [10, 50, 100, "all"];

  // Filter tenants based on search criteria and deleted status
  const filteredTenants = useMemo(() => {
    let filtered = allTenants;

    // Filter by deleted status
    if (!showDeleted) {
      filtered = filtered.filter((tenant) => !tenant.deleted);
    }

    // Apply search filters
    if (searchFilters.searchTerm) {
      const searchTerm = searchFilters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (tenant) =>
          tenant.name.toLowerCase().includes(searchTerm) ||
          tenant.type.toLowerCase().includes(searchTerm) ||
          tenant.orgName.toLowerCase().includes(searchTerm)
      );
    }

    if (searchFilters.type) {
      filtered = filtered.filter((tenant) =>
        tenant.type.toLowerCase().includes(searchFilters.type!.toLowerCase())
      );
    }

    if (searchFilters.orgName) {
      filtered = filtered.filter((tenant) =>
        tenant.orgName
          .toLowerCase()
          .includes(searchFilters.orgName!.toLowerCase())
      );
    }

    if (searchFilters.enable !== undefined) {
      filtered = filtered.filter(
        (tenant) => tenant.enable === searchFilters.enable
      );
    }

    return filtered;
  }, [allTenants, searchFilters, showDeleted]);

  // Paginate filtered tenants
  const { tenants: paginatedTenants, totalPages } = useMemo(() => {
    if (pagination.pageSize === "all") {
      return {
        tenants: filteredTenants,
        totalPages: 1,
      };
    }

    const startIndex = (pagination.pageNumber - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    const paginatedData = filteredTenants.slice(startIndex, endIndex);

    return {
      tenants: paginatedData,
      totalPages: Math.ceil(filteredTenants.length / pagination.pageSize),
    };
  }, [filteredTenants, pagination]);

  // Calculate statistics
  const totalRecords = filteredTenants.length;
  const totalAllRecords = allTenants.length;
  const activeRecordsCount = allTenants.filter(
    (tenant) => !tenant.deleted
  ).length;
  const inactiveRecordsCount = allTenants.filter(
    (tenant) => tenant.deleted
  ).length;

  // Load all tenant data
  const loadData = useCallback(async () => {
    if (!session?.user?.token) {
      console.log("No session token available");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Set authentication in API service
      spidexApi.setAuthToken(session.user.token);
      spidexApi.setTenantId(session.user.tenantId);

      console.log("Loading all tenant data for client-side pagination");

      // Load all tenants with large page size for client-side pagination
      const tenantsData = await spidexApi.getAllTenants(1, 1000);
      setAllTenants(tenantsData);

      console.log(`Loaded ${tenantsData.length} tenants`);
    } catch (error) {
      console.error("Error loading tenant data:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load tenant data"
      );
    } finally {
      setIsLoading(false);
    }
  }, [session, spidexApi]);

  // Load data on mount (only on initial mount)
  useEffect(() => {
    if (session?.user?.token) {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user?.token]); // Removed loadData dependency to prevent reloading on session changes

  // Create tenant
  const createTenant = useCallback(
    async (tenantData: CreateTenantFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        const newTenant = await spidexApi.createTenant({
          ...tenantData,
          properties: {},
          createdBy: session?.user?.userId || "system",
          createdTime: Date.now(),
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        });

        // Reload data to get updated list
        await loadData();

        console.log("Tenant created successfully");
        return newTenant;
      } catch (error) {
        console.error("Error creating tenant:", error);
        setError(
          error instanceof Error ? error.message : "Failed to create tenant"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  // Update tenant
  const updateTenant = useCallback(
    async (tenantData: UpdateTenantFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        const updatedTenant = await spidexApi.updateTenant({
          ...tenantData,
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        });

        // Reload data to get updated list
        await loadData();

        console.log("Tenant updated successfully");
        return updatedTenant;
      } catch (error) {
        console.error("Error updating tenant:", error);
        setError(
          error instanceof Error ? error.message : "Failed to update tenant"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  // Delete tenant
  const deleteTenant = useCallback(
    async (tenantId: string) => {
      try {
        setIsLoading(true);
        setError(null);

        await spidexApi.deleteTenant(tenantId);

        // Reload data to get updated list
        await loadData();

        console.log("Tenant deleted successfully");
      } catch (error) {
        console.error("Error deleting tenant:", error);
        setError(
          error instanceof Error ? error.message : "Failed to delete tenant"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, loadData]
  );

  // Pagination controls
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, pageNumber: page }));
  }, []);

  const changePageSize = useCallback((size: TenantPageSize) => {
    setPagination({ pageNumber: 1, pageSize: size });
  }, []);

  // Search controls
  const updateSearchFilters = useCallback(
    (filters: Partial<TenantSearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      setPagination((prev) => ({ ...prev, pageNumber: 1 })); // Reset to first page
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({ searchTerm: "" });
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
  }, []);

  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
    setPagination((prev) => ({ ...prev, pageNumber: 1 })); // Reset to first page
  }, []);

  return {
    // Data
    tenants: paginatedTenants,
    filteredTenants,
    allTenants,

    // State
    isLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,

    // Statistics
    totalPages,
    totalRecords,
    totalAllRecords,
    activeRecordsCount,
    inactiveRecordsCount,
    availablePageSizes,

    // Actions
    loadData,
    createTenant,
    updateTenant,
    deleteTenant,

    // Controls
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
  };
}
