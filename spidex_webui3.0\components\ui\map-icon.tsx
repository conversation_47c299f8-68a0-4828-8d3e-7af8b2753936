import React from "react";

interface MapIconProps {
  className?: string;
  onClick?: () => void;
}

export function MapIcon({ className = "w-6 h-6", onClick }: MapIconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      aria-label="Google Maps"
      role="img"
      viewBox="0 0 512 512"
      className={`cursor-pointer hover:opacity-80 transition-opacity ${className}`}
      onClick={onClick}
    >
      <rect
        id="a"
        width="512"
        height="512"
        x="0"
        y="0"
        rx="15%"
        fill="#fff"
      />
      <clipPath id="b">
        <use xlinkHref="#a" />
      </clipPath>
      <g clipPath="url(#b)">
        <path fill="#35a85b" d="M0 512V0h512z" />
        <path fill="#5881ca" d="M256 288L32 512h448z" />
        <path fill="#c1c0be" d="M288 256L512 32v448z" />
        <path stroke="#fadb2a" strokeWidth="71" d="M0 512L512 0" />
        <path
          fill="none"
          stroke="#f2f2f2"
          strokeWidth="22"
          d="M175 173h50a50 54 0 1 1-15-41"
        />
        <path
          fill="#de3738"
          d="M353 85a70 70 0 0 1 140 0c0 70-70 70-70 157 0-87-70-87-70-157"
        />
        <circle cx="423" cy="89" r="25" fill="#7d2426" />
      </g>
    </svg>
  );
}
