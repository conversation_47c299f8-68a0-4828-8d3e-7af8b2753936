{"name": "spidex_webui3.0", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.8", "@apollo/client-integration-nextjs": "^0.12.2", "@googlemaps/js-api-loader": "^1.16.8", "@hookform/resolvers": "^4.0.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@stomp/stompjs": "^7.0.0", "@tanstack/react-query": "^5.59.0", "@tanstack/react-table": "^8.20.6", "@tsparticles/engine": "^3.5.0", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.5.0", "axios": "^1.7.7", "chart.js": "^4.4.6", "chartjs-adapter-date-fns": "^3.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.0", "graphql": "^16.11.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.399.0", "mapbox-gl": "^3.4.0", "next": "^15.3.3", "next-auth": "^5.0.0-beta.19", "next-themes": "^0.4.6", "nextjs-toploader": "^3.7.15", "react": "^19.1.0", "react-chartjs-2": "^5.2.0", "react-countup": "^6.5.3", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-map-gl": "^7.1.7", "react-toastify": "^11.0.5", "recharts": "^2.15.4", "sockjs-client": "^1.6.1", "sonner": "^2.0.5", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ws": "^8.18.0", "zod": "^3.24.2"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.6", "@types/mapbox-gl": "^3.1.0", "@types/node": "^20", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.6", "@types/react-map-gl": "^6.1.7", "@types/sockjs-client": "^1.5.4", "@types/ws": "^8.5.13", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "^5"}}