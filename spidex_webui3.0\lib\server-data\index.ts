import { getAllAreas } from "@/lib/server-data/area";
import { getAllBranches } from "@/lib/server-data/branch";
import { getAllgateway } from "@/lib/server-data/gateway";
import { getAllLocations } from "@/lib/server-data/location";
import { BranchItem } from "@/lib/types";

export default async function getMapHierarchyData(
  tenantId: string
): Promise<BranchItem[]> {
  const branchesData = getAllBranches(tenantId);
  const locationsData = getAllLocations(tenantId);
  const areasData = getAllAreas(tenantId);
  const gatewayData = getAllgateway(tenantId);
  //   const tenantData = getTenant(tenantId);

  let [branches, locations, areas, gateways] = await Promise.all([
    branchesData,
    locationsData,
    areasData,
    gatewayData,
    // tenantData,
  ]);
  console.log({ branches });

  //TODO
  branches = branches.filter(({ gpsPoint }) => {
    if (gpsPoint.latitude.includes(" ") || gpsPoint.longitude.includes(" "))
      return false;
    return true;
  });

  //TODO This logic should be moved to API layer
  return branches.map((branch) => {
    return {
      ...branch,
      children: locations
        .filter((loc) => loc.branchId == branch.id)
        .map((location) => {
          return {
            ...location,
            children: areas
              .filter((area) => area.locationId == location.id)
              .map((area) => {
                return {
                  ...area,
                  children: gateways
                    .filter((gw) => gw.areaId == area.id)
                    .map((gw) => ({ ...gw, children: [] })),
                };
              }),
          };
        }),
    };
  }) as BranchItem[];
}
