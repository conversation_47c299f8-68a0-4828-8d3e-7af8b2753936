"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TenantPaginationParams, TenantPageSize } from "@/types/tenant";

interface TenantTablePaginationProps {
  pagination: TenantPaginationParams;
  totalRecords: number;
  currentPageRecords: number; // Number of records on current page
  totalPages?: number;
  availablePageSizes: TenantPageSize[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: TenantPageSize) => void;
}

export function TenantTablePagination({
  pagination,
  totalRecords,
  currentPageRecords,
  totalPages = 1,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
}: TenantTablePaginationProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center justify-between p-4 border-t">
        <div className="text-sm text-muted-foreground">Loading...</div>
        <div className="flex items-center gap-2">
          <div className="w-20 h-8 bg-muted rounded animate-pulse" />
        </div>
      </div>
    );
  }

  const startRecord =
    totalRecords === 0
      ? 0
      : (pagination.pageNumber - 1) *
          (pagination.pageSize === "all" ? totalRecords : pagination.pageSize) +
        1;
  const endRecord =
    pagination.pageSize === "all"
      ? totalRecords
      : Math.min(startRecord + currentPageRecords - 1, totalRecords);

  return (
    <div className="flex items-center justify-between p-4 border-t">
      <div className="text-sm text-muted-foreground">
        {totalRecords === 0 ? (
          "No tenants found"
        ) : (
          <>
            Showing {startRecord} to {endRecord} of {totalRecords} tenant
            {totalRecords !== 1 ? "s" : ""}
          </>
        )}
      </div>

      <div className="flex items-center gap-4">
        {/* Page Size Selector */}
        <div className="flex items-center gap-2">
          <Label htmlFor="page-size">Show:</Label>
          <Select
            value={pagination.pageSize.toString()}
            onValueChange={(value) => {
              console.log("Page size select changed to:", value);
              const newSize =
                value === "all" ? "all" : (parseInt(value) as TenantPageSize);
              console.log("Calling onPageSizeChange with:", newSize);
              onPageSizeChange(newSize);
            }}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availablePageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size === "all" ? "All" : size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pagination Controls - only show if not showing all */}
        {pagination.pageSize !== "all" && totalPages > 1 && (
          <div className="flex items-center gap-2">
            {/* First Page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={pagination.pageNumber === 1}
            >
              First
            </Button>

            {/* Page Numbers */}
            <div className="flex items-center gap-1">
              {(() => {
                const currentPage = pagination.pageNumber;
                const pages = [];
                const maxVisiblePages = 5;

                let startPage = Math.max(
                  1,
                  currentPage - Math.floor(maxVisiblePages / 2)
                );
                let endPage = Math.min(
                  totalPages,
                  startPage + maxVisiblePages - 1
                );

                // Adjust start if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                for (let i = startPage; i <= endPage; i++) {
                  pages.push(
                    <Button
                      key={i}
                      variant={i === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => onPageChange(i)}
                      className="w-8 h-8 p-0"
                    >
                      {i}
                    </Button>
                  );
                }

                return pages;
              })()}
            </div>

            {/* Last Page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={pagination.pageNumber === totalPages}
            >
              Last
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
