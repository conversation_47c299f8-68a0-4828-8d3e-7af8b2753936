"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/oauth4webapi";
exports.ids = ["vendor-chunks/oauth4webapi"];
exports.modules = {

/***/ "(action-browser)/./node_modules/oauth4webapi/build/index.js":
/*!**************************************************!*\
  !*** ./node_modules/oauth4webapi/build/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZATION_RESPONSE_ERROR: () => (/* binding */ AUTHORIZATION_RESPONSE_ERROR),\n/* harmony export */   AuthorizationResponseError: () => (/* binding */ AuthorizationResponseError),\n/* harmony export */   ClientSecretBasic: () => (/* binding */ ClientSecretBasic),\n/* harmony export */   ClientSecretJwt: () => (/* binding */ ClientSecretJwt),\n/* harmony export */   ClientSecretPost: () => (/* binding */ ClientSecretPost),\n/* harmony export */   DPoP: () => (/* binding */ DPoP),\n/* harmony export */   HTTP_REQUEST_FORBIDDEN: () => (/* binding */ HTTP_REQUEST_FORBIDDEN),\n/* harmony export */   INVALID_REQUEST: () => (/* binding */ INVALID_REQUEST),\n/* harmony export */   INVALID_RESPONSE: () => (/* binding */ INVALID_RESPONSE),\n/* harmony export */   INVALID_SERVER_METADATA: () => (/* binding */ INVALID_SERVER_METADATA),\n/* harmony export */   JSON_ATTRIBUTE_COMPARISON: () => (/* binding */ JSON_ATTRIBUTE_COMPARISON),\n/* harmony export */   JWT_CLAIM_COMPARISON: () => (/* binding */ JWT_CLAIM_COMPARISON),\n/* harmony export */   JWT_TIMESTAMP_CHECK: () => (/* binding */ JWT_TIMESTAMP_CHECK),\n/* harmony export */   JWT_USERINFO_EXPECTED: () => (/* binding */ JWT_USERINFO_EXPECTED),\n/* harmony export */   KEY_SELECTION: () => (/* binding */ KEY_SELECTION),\n/* harmony export */   MISSING_SERVER_METADATA: () => (/* binding */ MISSING_SERVER_METADATA),\n/* harmony export */   None: () => (/* binding */ None),\n/* harmony export */   OperationProcessingError: () => (/* binding */ OperationProcessingError),\n/* harmony export */   PARSE_ERROR: () => (/* binding */ PARSE_ERROR),\n/* harmony export */   PrivateKeyJwt: () => (/* binding */ PrivateKeyJwt),\n/* harmony export */   REQUEST_PROTOCOL_FORBIDDEN: () => (/* binding */ REQUEST_PROTOCOL_FORBIDDEN),\n/* harmony export */   RESPONSE_BODY_ERROR: () => (/* binding */ RESPONSE_BODY_ERROR),\n/* harmony export */   RESPONSE_IS_NOT_CONFORM: () => (/* binding */ RESPONSE_IS_NOT_CONFORM),\n/* harmony export */   RESPONSE_IS_NOT_JSON: () => (/* binding */ RESPONSE_IS_NOT_JSON),\n/* harmony export */   ResponseBodyError: () => (/* binding */ ResponseBodyError),\n/* harmony export */   TlsClientAuth: () => (/* binding */ TlsClientAuth),\n/* harmony export */   UNSUPPORTED_OPERATION: () => (/* binding */ UNSUPPORTED_OPERATION),\n/* harmony export */   UnsupportedOperationError: () => (/* binding */ UnsupportedOperationError),\n/* harmony export */   WWWAuthenticateChallengeError: () => (/* binding */ WWWAuthenticateChallengeError),\n/* harmony export */   WWW_AUTHENTICATE_CHALLENGE: () => (/* binding */ WWW_AUTHENTICATE_CHALLENGE),\n/* harmony export */   _expectedIssuer: () => (/* binding */ _expectedIssuer),\n/* harmony export */   _nodiscoverycheck: () => (/* binding */ _nodiscoverycheck),\n/* harmony export */   _nopkce: () => (/* binding */ _nopkce),\n/* harmony export */   allowInsecureRequests: () => (/* binding */ allowInsecureRequests),\n/* harmony export */   authorizationCodeGrantRequest: () => (/* binding */ authorizationCodeGrantRequest),\n/* harmony export */   calculatePKCECodeChallenge: () => (/* binding */ calculatePKCECodeChallenge),\n/* harmony export */   checkProtocol: () => (/* binding */ checkProtocol),\n/* harmony export */   clientCredentialsGrantRequest: () => (/* binding */ clientCredentialsGrantRequest),\n/* harmony export */   clockSkew: () => (/* binding */ clockSkew),\n/* harmony export */   clockTolerance: () => (/* binding */ clockTolerance),\n/* harmony export */   customFetch: () => (/* binding */ customFetch),\n/* harmony export */   deviceAuthorizationRequest: () => (/* binding */ deviceAuthorizationRequest),\n/* harmony export */   deviceCodeGrantRequest: () => (/* binding */ deviceCodeGrantRequest),\n/* harmony export */   discoveryRequest: () => (/* binding */ discoveryRequest),\n/* harmony export */   expectNoNonce: () => (/* binding */ expectNoNonce),\n/* harmony export */   expectNoState: () => (/* binding */ expectNoState),\n/* harmony export */   generateKeyPair: () => (/* binding */ generateKeyPair),\n/* harmony export */   generateRandomCodeVerifier: () => (/* binding */ generateRandomCodeVerifier),\n/* harmony export */   generateRandomNonce: () => (/* binding */ generateRandomNonce),\n/* harmony export */   generateRandomState: () => (/* binding */ generateRandomState),\n/* harmony export */   genericTokenEndpointRequest: () => (/* binding */ genericTokenEndpointRequest),\n/* harmony export */   getValidatedIdTokenClaims: () => (/* binding */ getValidatedIdTokenClaims),\n/* harmony export */   introspectionRequest: () => (/* binding */ introspectionRequest),\n/* harmony export */   isDPoPNonceError: () => (/* binding */ isDPoPNonceError),\n/* harmony export */   issueRequestObject: () => (/* binding */ issueRequestObject),\n/* harmony export */   jweDecrypt: () => (/* binding */ jweDecrypt),\n/* harmony export */   jwksCache: () => (/* binding */ jwksCache),\n/* harmony export */   modifyAssertion: () => (/* binding */ modifyAssertion),\n/* harmony export */   processAuthorizationCodeResponse: () => (/* binding */ processAuthorizationCodeResponse),\n/* harmony export */   processClientCredentialsResponse: () => (/* binding */ processClientCredentialsResponse),\n/* harmony export */   processDeviceAuthorizationResponse: () => (/* binding */ processDeviceAuthorizationResponse),\n/* harmony export */   processDeviceCodeResponse: () => (/* binding */ processDeviceCodeResponse),\n/* harmony export */   processDiscoveryResponse: () => (/* binding */ processDiscoveryResponse),\n/* harmony export */   processGenericTokenEndpointResponse: () => (/* binding */ processGenericTokenEndpointResponse),\n/* harmony export */   processIntrospectionResponse: () => (/* binding */ processIntrospectionResponse),\n/* harmony export */   processPushedAuthorizationResponse: () => (/* binding */ processPushedAuthorizationResponse),\n/* harmony export */   processRefreshTokenResponse: () => (/* binding */ processRefreshTokenResponse),\n/* harmony export */   processRevocationResponse: () => (/* binding */ processRevocationResponse),\n/* harmony export */   processUserInfoResponse: () => (/* binding */ processUserInfoResponse),\n/* harmony export */   protectedResourceRequest: () => (/* binding */ protectedResourceRequest),\n/* harmony export */   pushedAuthorizationRequest: () => (/* binding */ pushedAuthorizationRequest),\n/* harmony export */   refreshTokenGrantRequest: () => (/* binding */ refreshTokenGrantRequest),\n/* harmony export */   resolveEndpoint: () => (/* binding */ resolveEndpoint),\n/* harmony export */   revocationRequest: () => (/* binding */ revocationRequest),\n/* harmony export */   skipAuthTimeCheck: () => (/* binding */ skipAuthTimeCheck),\n/* harmony export */   skipStateCheck: () => (/* binding */ skipStateCheck),\n/* harmony export */   skipSubjectCheck: () => (/* binding */ skipSubjectCheck),\n/* harmony export */   userInfoRequest: () => (/* binding */ userInfoRequest),\n/* harmony export */   validateApplicationLevelSignature: () => (/* binding */ validateApplicationLevelSignature),\n/* harmony export */   validateAuthResponse: () => (/* binding */ validateAuthResponse),\n/* harmony export */   validateCodeIdTokenResponse: () => (/* binding */ validateCodeIdTokenResponse),\n/* harmony export */   validateDetachedSignatureResponse: () => (/* binding */ validateDetachedSignatureResponse),\n/* harmony export */   validateJwtAccessToken: () => (/* binding */ validateJwtAccessToken),\n/* harmony export */   validateJwtAuthResponse: () => (/* binding */ validateJwtAuthResponse)\n/* harmony export */ });\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'oauth4webapi';\n    const VERSION = 'v3.1.4';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nfunction looseInstanceOf(input, expected) {\n    if (input == null) {\n        return false;\n    }\n    try {\n        return (input instanceof expected ||\n            Object.getPrototypeOf(input)[Symbol.toStringTag] === expected.prototype[Symbol.toStringTag]);\n    }\n    catch {\n        return false;\n    }\n}\nconst ERR_INVALID_ARG_VALUE = 'ERR_INVALID_ARG_VALUE';\nconst ERR_INVALID_ARG_TYPE = 'ERR_INVALID_ARG_TYPE';\nfunction CodedTypeError(message, code, cause) {\n    const err = new TypeError(message, { cause });\n    Object.assign(err, { code });\n    return err;\n}\nconst allowInsecureRequests = Symbol();\nconst clockSkew = Symbol();\nconst clockTolerance = Symbol();\nconst customFetch = Symbol();\nconst modifyAssertion = Symbol();\nconst jweDecrypt = Symbol();\nconst jwksCache = Symbol();\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nfunction buf(input) {\n    if (typeof input === 'string') {\n        return encoder.encode(input);\n    }\n    return decoder.decode(input);\n}\nconst CHUNK_SIZE = 0x8000;\nfunction encodeBase64Url(input) {\n    if (input instanceof ArrayBuffer) {\n        input = new Uint8Array(input);\n    }\n    const arr = [];\n    for (let i = 0; i < input.byteLength; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join('')).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\nfunction decodeBase64Url(input) {\n    try {\n        const binary = atob(input.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, ''));\n        const bytes = new Uint8Array(binary.length);\n        for (let i = 0; i < binary.length; i++) {\n            bytes[i] = binary.charCodeAt(i);\n        }\n        return bytes;\n    }\n    catch (cause) {\n        throw CodedTypeError('The input to be decoded is not correctly encoded.', ERR_INVALID_ARG_VALUE, cause);\n    }\n}\nfunction b64u(input) {\n    if (typeof input === 'string') {\n        return decodeBase64Url(input);\n    }\n    return encodeBase64Url(input);\n}\nclass UnsupportedOperationError extends Error {\n    code;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = UNSUPPORTED_OPERATION;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass OperationProcessingError extends Error {\n    code;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        if (options?.code) {\n            this.code = options?.code;\n        }\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nfunction OPE(message, code, cause) {\n    return new OperationProcessingError(message, { code, cause });\n}\nfunction assertCryptoKey(key, it) {\n    if (!(key instanceof CryptoKey)) {\n        throw CodedTypeError(`${it} must be a CryptoKey`, ERR_INVALID_ARG_TYPE);\n    }\n}\nfunction assertPrivateKey(key, it) {\n    assertCryptoKey(key, it);\n    if (key.type !== 'private') {\n        throw CodedTypeError(`${it} must be a private CryptoKey`, ERR_INVALID_ARG_VALUE);\n    }\n}\nfunction assertPublicKey(key, it) {\n    assertCryptoKey(key, it);\n    if (key.type !== 'public') {\n        throw CodedTypeError(`${it} must be a public CryptoKey`, ERR_INVALID_ARG_VALUE);\n    }\n}\nfunction normalizeTyp(value) {\n    return value.toLowerCase().replace(/^application\\//, '');\n}\nfunction isJsonObject(input) {\n    if (input === null || typeof input !== 'object' || Array.isArray(input)) {\n        return false;\n    }\n    return true;\n}\nfunction prepareHeaders(input) {\n    if (looseInstanceOf(input, Headers)) {\n        input = Object.fromEntries(input.entries());\n    }\n    const headers = new Headers(input);\n    if (USER_AGENT && !headers.has('user-agent')) {\n        headers.set('user-agent', USER_AGENT);\n    }\n    if (headers.has('authorization')) {\n        throw CodedTypeError('\"options.headers\" must not include the \"authorization\" header name', ERR_INVALID_ARG_VALUE);\n    }\n    if (headers.has('dpop')) {\n        throw CodedTypeError('\"options.headers\" must not include the \"dpop\" header name', ERR_INVALID_ARG_VALUE);\n    }\n    return headers;\n}\nfunction signal(value) {\n    if (typeof value === 'function') {\n        value = value();\n    }\n    if (!(value instanceof AbortSignal)) {\n        throw CodedTypeError('\"options.signal\" must return or be an instance of AbortSignal', ERR_INVALID_ARG_TYPE);\n    }\n    return value;\n}\nasync function discoveryRequest(issuerIdentifier, options) {\n    if (!(issuerIdentifier instanceof URL)) {\n        throw CodedTypeError('\"issuerIdentifier\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    checkProtocol(issuerIdentifier, options?.[allowInsecureRequests] !== true);\n    const url = new URL(issuerIdentifier.href);\n    switch (options?.algorithm) {\n        case undefined:\n        case 'oidc':\n            url.pathname = `${url.pathname}/.well-known/openid-configuration`.replace('//', '/');\n            break;\n        case 'oauth2':\n            if (url.pathname === '/') {\n                url.pathname = '.well-known/oauth-authorization-server';\n            }\n            else {\n                url.pathname = `.well-known/oauth-authorization-server/${url.pathname}`.replace('//', '/');\n            }\n            break;\n        default:\n            throw CodedTypeError('\"options.algorithm\" must be \"oidc\" (default), or \"oauth2\"', ERR_INVALID_ARG_VALUE);\n    }\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body: undefined,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : undefined,\n    });\n}\nfunction assertNumber(input, allow0, it, code, cause) {\n    try {\n        if (typeof input !== 'number' || !Number.isFinite(input)) {\n            throw CodedTypeError(`${it} must be a number`, ERR_INVALID_ARG_TYPE, cause);\n        }\n        if (input > 0)\n            return;\n        if (allow0 && input !== 0) {\n            throw CodedTypeError(`${it} must be a non-negative number`, ERR_INVALID_ARG_VALUE, cause);\n        }\n        throw CodedTypeError(`${it} must be a positive number`, ERR_INVALID_ARG_VALUE, cause);\n    }\n    catch (err) {\n        if (code) {\n            throw OPE(err.message, code, cause);\n        }\n        throw err;\n    }\n}\nfunction assertString(input, it, code, cause) {\n    try {\n        if (typeof input !== 'string') {\n            throw CodedTypeError(`${it} must be a string`, ERR_INVALID_ARG_TYPE, cause);\n        }\n        if (input.length === 0) {\n            throw CodedTypeError(`${it} must not be empty`, ERR_INVALID_ARG_VALUE, cause);\n        }\n    }\n    catch (err) {\n        if (code) {\n            throw OPE(err.message, code, cause);\n        }\n        throw err;\n    }\n}\nasync function processDiscoveryResponse(expectedIssuerIdentifier, response) {\n    if (!(expectedIssuerIdentifier instanceof URL) &&\n        expectedIssuerIdentifier !== _nodiscoverycheck) {\n        throw CodedTypeError('\"expectedIssuer\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform Authorization Server Metadata response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    assertApplicationJson(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(json)) {\n        throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n    }\n    assertString(json.issuer, '\"response\" body \"issuer\" property', INVALID_RESPONSE, { body: json });\n    if (new URL(json.issuer).href !== expectedIssuerIdentifier.href &&\n        expectedIssuerIdentifier !== _nodiscoverycheck) {\n        throw OPE('\"response\" body \"issuer\" property does not match the expected value', JSON_ATTRIBUTE_COMPARISON, { expected: expectedIssuerIdentifier.href, body: json, attribute: 'issuer' });\n    }\n    return json;\n}\nfunction assertApplicationJson(response) {\n    assertContentType(response, 'application/json');\n}\nfunction notJson(response, ...types) {\n    let msg = '\"response\" content-type must be ';\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `${types.join(', ')}, or ${last}`;\n    }\n    else if (types.length === 2) {\n        msg += `${types[0]} or ${types[1]}`;\n    }\n    else {\n        msg += types[0];\n    }\n    return OPE(msg, RESPONSE_IS_NOT_JSON, response);\n}\nfunction assertContentTypes(response, ...types) {\n    if (!types.includes(getContentType(response))) {\n        throw notJson(response, ...types);\n    }\n}\nfunction assertContentType(response, contentType) {\n    if (getContentType(response) !== contentType) {\n        throw notJson(response, contentType);\n    }\n}\nfunction randomBytes() {\n    return b64u(crypto.getRandomValues(new Uint8Array(32)));\n}\nfunction generateRandomCodeVerifier() {\n    return randomBytes();\n}\nfunction generateRandomState() {\n    return randomBytes();\n}\nfunction generateRandomNonce() {\n    return randomBytes();\n}\nasync function calculatePKCECodeChallenge(codeVerifier) {\n    assertString(codeVerifier, 'codeVerifier');\n    return b64u(await crypto.subtle.digest('SHA-256', buf(codeVerifier)));\n}\nfunction getKeyAndKid(input) {\n    if (input instanceof CryptoKey) {\n        return { key: input };\n    }\n    if (!(input?.key instanceof CryptoKey)) {\n        return {};\n    }\n    if (input.kid !== undefined) {\n        assertString(input.kid, '\"kid\"');\n    }\n    return {\n        key: input.key,\n        kid: input.kid,\n    };\n}\nfunction psAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'PS256';\n        case 'SHA-384':\n            return 'PS384';\n        case 'SHA-512':\n            return 'PS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name', {\n                cause: key,\n            });\n    }\n}\nfunction rsAlg(key) {\n    switch (key.algorithm.hash.name) {\n        case 'SHA-256':\n            return 'RS256';\n        case 'SHA-384':\n            return 'RS384';\n        case 'SHA-512':\n            return 'RS512';\n        default:\n            throw new UnsupportedOperationError('unsupported RsaHashedKeyAlgorithm hash name', {\n                cause: key,\n            });\n    }\n}\nfunction esAlg(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n            return 'ES256';\n        case 'P-384':\n            return 'ES384';\n        case 'P-521':\n            return 'ES512';\n        default:\n            throw new UnsupportedOperationError('unsupported EcKeyAlgorithm namedCurve', { cause: key });\n    }\n}\nfunction keyToJws(key) {\n    switch (key.algorithm.name) {\n        case 'RSA-PSS':\n            return psAlg(key);\n        case 'RSASSA-PKCS1-v1_5':\n            return rsAlg(key);\n        case 'ECDSA':\n            return esAlg(key);\n        case 'Ed25519':\n        case 'EdDSA':\n            return 'Ed25519';\n        default:\n            throw new UnsupportedOperationError('unsupported CryptoKey algorithm name', { cause: key });\n    }\n}\nfunction getClockSkew(client) {\n    const skew = client?.[clockSkew];\n    return typeof skew === 'number' && Number.isFinite(skew) ? skew : 0;\n}\nfunction getClockTolerance(client) {\n    const tolerance = client?.[clockTolerance];\n    return typeof tolerance === 'number' && Number.isFinite(tolerance) && Math.sign(tolerance) !== -1\n        ? tolerance\n        : 30;\n}\nfunction epochTime() {\n    return Math.floor(Date.now() / 1000);\n}\nfunction assertAs(as) {\n    if (typeof as !== 'object' || as === null) {\n        throw CodedTypeError('\"as\" must be an object', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(as.issuer, '\"as.issuer\"');\n}\nfunction assertClient(client) {\n    if (typeof client !== 'object' || client === null) {\n        throw CodedTypeError('\"client\" must be an object', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(client.client_id, '\"client.client_id\"');\n}\nfunction formUrlEncode(token) {\n    return encodeURIComponent(token).replace(/(?:[-_.!~*'()]|%20)/g, (substring) => {\n        switch (substring) {\n            case '-':\n            case '_':\n            case '.':\n            case '!':\n            case '~':\n            case '*':\n            case \"'\":\n            case '(':\n            case ')':\n                return `%${substring.charCodeAt(0).toString(16).toUpperCase()}`;\n            case '%20':\n                return '+';\n            default:\n                throw new Error();\n        }\n    });\n}\nfunction ClientSecretPost(clientSecret) {\n    assertString(clientSecret, '\"clientSecret\"');\n    return (_as, client, body, _headers) => {\n        body.set('client_id', client.client_id);\n        body.set('client_secret', clientSecret);\n    };\n}\nfunction ClientSecretBasic(clientSecret) {\n    assertString(clientSecret, '\"clientSecret\"');\n    return (_as, client, _body, headers) => {\n        const username = formUrlEncode(client.client_id);\n        const password = formUrlEncode(clientSecret);\n        const credentials = btoa(`${username}:${password}`);\n        headers.set('authorization', `Basic ${credentials}`);\n    };\n}\nfunction clientAssertionPayload(as, client) {\n    const now = epochTime() + getClockSkew(client);\n    return {\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n        sub: client.client_id,\n    };\n}\nfunction PrivateKeyJwt(clientPrivateKey, options) {\n    const { key, kid } = getKeyAndKid(clientPrivateKey);\n    assertPrivateKey(key, '\"clientPrivateKey.key\"');\n    return async (as, client, body, _headers) => {\n        const header = { alg: keyToJws(key), kid };\n        const payload = clientAssertionPayload(as, client);\n        options?.[modifyAssertion]?.(header, payload);\n        body.set('client_id', client.client_id);\n        body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n        body.set('client_assertion', await signJwt(header, payload, key));\n    };\n}\nfunction ClientSecretJwt(clientSecret, options) {\n    assertString(clientSecret, '\"clientSecret\"');\n    const modify = options?.[modifyAssertion];\n    let key;\n    return async (as, client, body, _headers) => {\n        key ||= await crypto.subtle.importKey('raw', buf(clientSecret), { hash: 'SHA-256', name: 'HMAC' }, false, ['sign']);\n        const header = { alg: 'HS256' };\n        const payload = clientAssertionPayload(as, client);\n        modify?.(header, payload);\n        const data = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(payload)))}`;\n        const hmac = await crypto.subtle.sign(key.algorithm, key, buf(data));\n        body.set('client_id', client.client_id);\n        body.set('client_assertion_type', 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer');\n        body.set('client_assertion', `${data}.${b64u(new Uint8Array(hmac))}`);\n    };\n}\nfunction None() {\n    return (_as, client, body, _headers) => {\n        body.set('client_id', client.client_id);\n    };\n}\nfunction TlsClientAuth() {\n    return None();\n}\nasync function signJwt(header, payload, key) {\n    if (!key.usages.includes('sign')) {\n        throw CodedTypeError('CryptoKey instances used for signing assertions must include \"sign\" in their \"usages\"', ERR_INVALID_ARG_VALUE);\n    }\n    const input = `${b64u(buf(JSON.stringify(header)))}.${b64u(buf(JSON.stringify(payload)))}`;\n    const signature = b64u(await crypto.subtle.sign(keyToSubtle(key), key, buf(input)));\n    return `${input}.${signature}`;\n}\nasync function issueRequestObject(as, client, parameters, privateKey, options) {\n    assertAs(as);\n    assertClient(client);\n    parameters = new URLSearchParams(parameters);\n    const { key, kid } = getKeyAndKid(privateKey);\n    assertPrivateKey(key, '\"privateKey.key\"');\n    parameters.set('client_id', client.client_id);\n    const now = epochTime() + getClockSkew(client);\n    const claims = {\n        ...Object.fromEntries(parameters.entries()),\n        jti: randomBytes(),\n        aud: as.issuer,\n        exp: now + 60,\n        iat: now,\n        nbf: now,\n        iss: client.client_id,\n    };\n    let resource;\n    if (parameters.has('resource') &&\n        (resource = parameters.getAll('resource')) &&\n        resource.length > 1) {\n        claims.resource = resource;\n    }\n    {\n        let value = parameters.get('max_age');\n        if (value !== null) {\n            claims.max_age = parseInt(value, 10);\n            assertNumber(claims.max_age, true, '\"max_age\" parameter');\n        }\n    }\n    {\n        let value = parameters.get('claims');\n        if (value !== null) {\n            try {\n                claims.claims = JSON.parse(value);\n            }\n            catch (cause) {\n                throw OPE('failed to parse the \"claims\" parameter as JSON', PARSE_ERROR, cause);\n            }\n            if (!isJsonObject(claims.claims)) {\n                throw CodedTypeError('\"claims\" parameter must be a JSON with a top level object', ERR_INVALID_ARG_VALUE);\n            }\n        }\n    }\n    {\n        let value = parameters.get('authorization_details');\n        if (value !== null) {\n            try {\n                claims.authorization_details = JSON.parse(value);\n            }\n            catch (cause) {\n                throw OPE('failed to parse the \"authorization_details\" parameter as JSON', PARSE_ERROR, cause);\n            }\n            if (!Array.isArray(claims.authorization_details)) {\n                throw CodedTypeError('\"authorization_details\" parameter must be a JSON with a top level array', ERR_INVALID_ARG_VALUE);\n            }\n        }\n    }\n    const header = {\n        alg: keyToJws(key),\n        typ: 'oauth-authz-req+jwt',\n        kid,\n    };\n    options?.[modifyAssertion]?.(header, claims);\n    return signJwt(header, claims, key);\n}\nlet jwkCache;\nasync function getSetPublicJwkCache(key) {\n    const { kty, e, n, x, y, crv } = await crypto.subtle.exportKey('jwk', key);\n    const jwk = { kty, e, n, x, y, crv };\n    jwkCache.set(key, jwk);\n    return jwk;\n}\nasync function publicJwk(key) {\n    jwkCache ||= new WeakMap();\n    return jwkCache.get(key) || getSetPublicJwkCache(key);\n}\nconst URLParse = URL.parse\n    ?\n        (url, base) => URL.parse(url, base)\n    : (url, base) => {\n        try {\n            return new URL(url, base);\n        }\n        catch {\n            return null;\n        }\n    };\nfunction checkProtocol(url, enforceHttps) {\n    if (enforceHttps && url.protocol !== 'https:') {\n        throw OPE('only requests to HTTPS are allowed', HTTP_REQUEST_FORBIDDEN, url);\n    }\n    if (url.protocol !== 'https:' && url.protocol !== 'http:') {\n        throw OPE('only HTTP and HTTPS requests are allowed', REQUEST_PROTOCOL_FORBIDDEN, url);\n    }\n}\nfunction validateEndpoint(value, endpoint, useMtlsAlias, enforceHttps) {\n    let url;\n    if (typeof value !== 'string' || !(url = URLParse(value))) {\n        throw OPE(`authorization server metadata does not contain a valid ${useMtlsAlias ? `\"as.mtls_endpoint_aliases.${endpoint}\"` : `\"as.${endpoint}\"`}`, value === undefined ? MISSING_SERVER_METADATA : INVALID_SERVER_METADATA, { attribute: useMtlsAlias ? `mtls_endpoint_aliases.${endpoint}` : endpoint });\n    }\n    checkProtocol(url, enforceHttps);\n    return url;\n}\nfunction resolveEndpoint(as, endpoint, useMtlsAlias, enforceHttps) {\n    if (useMtlsAlias && as.mtls_endpoint_aliases && endpoint in as.mtls_endpoint_aliases) {\n        return validateEndpoint(as.mtls_endpoint_aliases[endpoint], endpoint, useMtlsAlias, enforceHttps);\n    }\n    return validateEndpoint(as[endpoint], endpoint, useMtlsAlias, enforceHttps);\n}\nasync function pushedAuthorizationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'pushed_authorization_request_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, 'POST');\n    }\n    const response = await authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nclass DPoPHandler {\n    #header;\n    #privateKey;\n    #publicKey;\n    #clockSkew;\n    #modifyAssertion;\n    #map;\n    constructor(client, keyPair, options) {\n        assertPrivateKey(keyPair?.privateKey, '\"DPoP.privateKey\"');\n        assertPublicKey(keyPair?.publicKey, '\"DPoP.publicKey\"');\n        if (!keyPair.publicKey.extractable) {\n            throw CodedTypeError('\"DPoP.publicKey.extractable\" must be true', ERR_INVALID_ARG_VALUE);\n        }\n        this.#modifyAssertion = options?.[modifyAssertion];\n        this.#clockSkew = getClockSkew(client);\n        this.#privateKey = keyPair.privateKey;\n        this.#publicKey = keyPair.publicKey;\n        branded.add(this);\n    }\n    #get(key) {\n        this.#map ||= new Map();\n        let item = this.#map.get(key);\n        if (item) {\n            this.#map.delete(key);\n            this.#map.set(key, item);\n        }\n        return item;\n    }\n    #set(key, val) {\n        this.#map ||= new Map();\n        this.#map.delete(key);\n        if (this.#map.size === 100) {\n            this.#map.delete(this.#map.keys().next().value);\n        }\n        this.#map.set(key, val);\n    }\n    async addProof(url, headers, htm, accessToken) {\n        this.#header ||= {\n            alg: keyToJws(this.#privateKey),\n            typ: 'dpop+jwt',\n            jwk: await publicJwk(this.#publicKey),\n        };\n        const nonce = this.#get(url.origin);\n        const now = epochTime() + this.#clockSkew;\n        const payload = {\n            iat: now,\n            jti: randomBytes(),\n            htm,\n            nonce,\n            htu: `${url.origin}${url.pathname}`,\n            ath: accessToken ? b64u(await crypto.subtle.digest('SHA-256', buf(accessToken))) : undefined,\n        };\n        this.#modifyAssertion?.(this.#header, payload);\n        headers.set('dpop', await signJwt(this.#header, payload, this.#privateKey));\n    }\n    cacheNonce(response) {\n        try {\n            const nonce = response.headers.get('dpop-nonce');\n            if (nonce) {\n                this.#set(new URL(response.url).origin, nonce);\n            }\n        }\n        catch { }\n    }\n}\nfunction isDPoPNonceError(err) {\n    if (err instanceof WWWAuthenticateChallengeError) {\n        const { 0: challenge, length } = err.cause;\n        return (length === 1 && challenge.scheme === 'dpop' && challenge.parameters.error === 'use_dpop_nonce');\n    }\n    if (err instanceof ResponseBodyError) {\n        return err.error === 'use_dpop_nonce';\n    }\n    return false;\n}\nfunction DPoP(client, keyPair, options) {\n    return new DPoPHandler(client, keyPair, options);\n}\nclass ResponseBodyError extends Error {\n    cause;\n    code;\n    error;\n    status;\n    error_description;\n    response;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = RESPONSE_BODY_ERROR;\n        this.cause = options.cause;\n        this.error = options.cause.error;\n        this.status = options.response.status;\n        this.error_description = options.cause.error_description;\n        Object.defineProperty(this, 'response', { enumerable: false, value: options.response });\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass AuthorizationResponseError extends Error {\n    cause;\n    code;\n    error;\n    error_description;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = AUTHORIZATION_RESPONSE_ERROR;\n        this.cause = options.cause;\n        this.error = options.cause.get('error');\n        this.error_description = options.cause.get('error_description') ?? undefined;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass WWWAuthenticateChallengeError extends Error {\n    cause;\n    code;\n    response;\n    status;\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        this.code = WWW_AUTHENTICATE_CHALLENGE;\n        this.cause = options.cause;\n        this.status = options.response.status;\n        this.response = options.response;\n        Object.defineProperty(this, 'response', { enumerable: false });\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nfunction unquote(value) {\n    if (value.length >= 2 && value[0] === '\"' && value[value.length - 1] === '\"') {\n        return value.slice(1, -1);\n    }\n    return value;\n}\nconst SPLIT_REGEXP = /((?:,|, )?[0-9a-zA-Z!#$%&'*+-.^_`|~]+=)/;\nconst SCHEMES_REGEXP = /(?:^|, ?)([0-9a-zA-Z!#$%&'*+\\-.^_`|~]+)(?=$|[ ,])/g;\nfunction wwwAuth(scheme, params) {\n    const arr = params.split(SPLIT_REGEXP).slice(1);\n    if (!arr.length) {\n        return { scheme: scheme.toLowerCase(), parameters: {} };\n    }\n    arr[arr.length - 1] = arr[arr.length - 1].replace(/,$/, '');\n    const parameters = {};\n    for (let i = 1; i < arr.length; i += 2) {\n        const idx = i;\n        if (arr[idx][0] === '\"') {\n            while (arr[idx].slice(-1) !== '\"' && ++i < arr.length) {\n                arr[idx] += arr[i];\n            }\n        }\n        const key = arr[idx - 1].replace(/^(?:, ?)|=$/g, '').toLowerCase();\n        parameters[key] = unquote(arr[idx]);\n    }\n    return {\n        scheme: scheme.toLowerCase(),\n        parameters,\n    };\n}\nfunction parseWwwAuthenticateChallenges(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    const header = response.headers.get('www-authenticate');\n    if (header === null) {\n        return undefined;\n    }\n    const result = [];\n    for (const { 1: scheme, index } of header.matchAll(SCHEMES_REGEXP)) {\n        result.push([scheme, index]);\n    }\n    if (!result.length) {\n        return undefined;\n    }\n    const challenges = result.map(([scheme, indexOf], i, others) => {\n        const next = others[i + 1];\n        let parameters;\n        if (next) {\n            parameters = header.slice(indexOf, next[1]);\n        }\n        else {\n            parameters = header.slice(indexOf);\n        }\n        return wwwAuth(scheme, parameters);\n    });\n    return challenges;\n}\nasync function processPushedAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    let challenges;\n    if ((challenges = parseWwwAuthenticateChallenges(response))) {\n        throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n    }\n    if (response.status !== 201) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            await response.body?.cancel();\n            throw new ResponseBodyError('server responded with an error in the response body', {\n                cause: err,\n                response,\n            });\n        }\n        throw OPE('\"response\" is not a conform Pushed Authorization Request Endpoint response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    assertApplicationJson(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(json)) {\n        throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n    }\n    assertString(json.request_uri, '\"response\" body \"request_uri\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, false, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    return json;\n}\nfunction assertDPoP(option) {\n    if (!branded.has(option)) {\n        throw CodedTypeError('\"options.DPoP\" is not a valid DPoPHandle', ERR_INVALID_ARG_VALUE);\n    }\n}\nasync function resourceRequest(accessToken, method, url, headers, body, options) {\n    assertString(accessToken, '\"accessToken\"');\n    if (!(url instanceof URL)) {\n        throw CodedTypeError('\"url\" must be an instance of URL', ERR_INVALID_ARG_TYPE);\n    }\n    checkProtocol(url, options?.[allowInsecureRequests] !== true);\n    headers = prepareHeaders(headers);\n    if (options?.DPoP) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, method.toUpperCase(), accessToken);\n        headers.set('authorization', `DPoP ${accessToken}`);\n    }\n    else {\n        headers.set('authorization', `Bearer ${accessToken}`);\n    }\n    const response = await (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method,\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : undefined,\n    });\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function protectedResourceRequest(accessToken, method, url, headers, body, options) {\n    return resourceRequest(accessToken, method, url, headers, body, options).then((response) => {\n        let challenges;\n        if ((challenges = parseWwwAuthenticateChallenges(response))) {\n            throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n        }\n        return response;\n    });\n}\nasync function userInfoRequest(as, client, accessToken, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'userinfo_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    if (client.userinfo_signed_response_alg) {\n        headers.set('accept', 'application/jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n        headers.append('accept', 'application/jwt');\n    }\n    return resourceRequest(accessToken, 'GET', url, headers, null, {\n        ...options,\n        [clockSkew]: getClockSkew(client),\n    });\n}\nlet jwksMap;\nfunction setJwksCache(as, jwks, uat, cache) {\n    jwksMap ||= new WeakMap();\n    jwksMap.set(as, {\n        jwks,\n        uat,\n        get age() {\n            return epochTime() - this.uat;\n        },\n    });\n    if (cache) {\n        Object.assign(cache, { jwks: structuredClone(jwks), uat });\n    }\n}\nfunction isFreshJwksCache(input) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || epochTime() - input.uat >= 300) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !isJsonObject(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, isJsonObject)) {\n        return false;\n    }\n    return true;\n}\nfunction clearJwksCache(as, cache) {\n    jwksMap?.delete(as);\n    delete cache?.jwks;\n    delete cache?.uat;\n}\nasync function getPublicSigKeyFromIssuerJwksUri(as, options, header) {\n    const { alg, kid } = header;\n    checkSupportedJwsAlg(header);\n    if (!jwksMap?.has(as) && isFreshJwksCache(options?.[jwksCache])) {\n        setJwksCache(as, options?.[jwksCache].jwks, options?.[jwksCache].uat);\n    }\n    let jwks;\n    let age;\n    if (jwksMap?.has(as)) {\n        ;\n        ({ jwks, age } = jwksMap.get(as));\n        if (age >= 300) {\n            clearJwksCache(as, options?.[jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n    }\n    else {\n        jwks = await jwksRequest(as, options).then(processJwksResponse);\n        age = 0;\n        setJwksCache(as, jwks, epochTime(), options?.[jwksCache]);\n    }\n    let kty;\n    switch (alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            kty = 'RSA';\n            break;\n        case 'ES':\n            kty = 'EC';\n            break;\n        case 'Ed':\n            kty = 'OKP';\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg } });\n    }\n    const candidates = jwks.keys.filter((jwk) => {\n        if (jwk.kty !== kty) {\n            return false;\n        }\n        if (kid !== undefined && kid !== jwk.kid) {\n            return false;\n        }\n        if (jwk.alg !== undefined && alg !== jwk.alg) {\n            return false;\n        }\n        if (jwk.use !== undefined && jwk.use !== 'sig') {\n            return false;\n        }\n        if (jwk.key_ops?.includes('verify') === false) {\n            return false;\n        }\n        switch (true) {\n            case alg === 'ES256' && jwk.crv !== 'P-256':\n            case alg === 'ES384' && jwk.crv !== 'P-384':\n            case alg === 'ES512' && jwk.crv !== 'P-521':\n            case alg === 'Ed25519' && jwk.crv !== 'Ed25519':\n            case alg === 'EdDSA' && jwk.crv !== 'Ed25519':\n                return false;\n        }\n        return true;\n    });\n    const { 0: jwk, length } = candidates;\n    if (!length) {\n        if (age >= 60) {\n            clearJwksCache(as, options?.[jwksCache]);\n            return getPublicSigKeyFromIssuerJwksUri(as, options, header);\n        }\n        throw OPE('error when selecting a JWT verification key, no applicable keys found', KEY_SELECTION, { header, candidates, jwks_uri: new URL(as.jwks_uri) });\n    }\n    if (length !== 1) {\n        throw OPE('error when selecting a JWT verification key, multiple applicable keys found, a \"kid\" JWT Header Parameter is required', KEY_SELECTION, { header, candidates, jwks_uri: new URL(as.jwks_uri) });\n    }\n    return importJwk(alg, jwk);\n}\nconst skipSubjectCheck = Symbol();\nfunction getContentType(input) {\n    return input.headers.get('content-type')?.split(';')[0];\n}\nasync function processUserInfoResponse(as, client, expectedSubject, response, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    let challenges;\n    if ((challenges = parseWwwAuthenticateChallenges(response))) {\n        throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform UserInfo Endpoint response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    let json;\n    if (getContentType(response) === 'application/jwt') {\n        const { claims, jwt } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.userinfo_signed_response_alg, as.userinfo_signing_alg_values_supported, undefined), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(validateOptionalAudience.bind(undefined, client.client_id))\n            .then(validateOptionalIssuer.bind(undefined, as));\n        jwtRefs.set(response, jwt);\n        json = claims;\n    }\n    else {\n        if (client.userinfo_signed_response_alg) {\n            throw OPE('JWT UserInfo Response expected', JWT_USERINFO_EXPECTED, response);\n        }\n        assertApplicationJson(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n        }\n    }\n    if (!isJsonObject(json)) {\n        throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n    }\n    assertString(json.sub, '\"response\" body \"sub\" property', INVALID_RESPONSE, { body: json });\n    switch (expectedSubject) {\n        case skipSubjectCheck:\n            break;\n        default:\n            assertString(expectedSubject, '\"expectedSubject\"');\n            if (json.sub !== expectedSubject) {\n                throw OPE('unexpected \"response\" body \"sub\" property value', JSON_ATTRIBUTE_COMPARISON, {\n                    expected: expectedSubject,\n                    body: json,\n                    attribute: 'sub',\n                });\n            }\n    }\n    return json;\n}\nasync function authenticatedRequest(as, client, clientAuthentication, url, body, headers, options) {\n    await clientAuthentication(as, client, body, headers);\n    headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'POST',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : undefined,\n    });\n}\nasync function tokenEndpointRequest(as, client, clientAuthentication, grantType, parameters, options) {\n    const url = resolveEndpoint(as, 'token_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    parameters.set('grant_type', grantType);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    if (options?.DPoP !== undefined) {\n        assertDPoP(options.DPoP);\n        await options.DPoP.addProof(url, headers, 'POST');\n    }\n    const response = await authenticatedRequest(as, client, clientAuthentication, url, parameters, headers, options);\n    options?.DPoP?.cacheNonce(response);\n    return response;\n}\nasync function refreshTokenGrantRequest(as, client, clientAuthentication, refreshToken, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(refreshToken, '\"refreshToken\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('refresh_token', refreshToken);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'refresh_token', parameters, options);\n}\nconst idTokenClaims = new WeakMap();\nconst jwtRefs = new WeakMap();\nfunction getValidatedIdTokenClaims(ref) {\n    if (!ref.id_token) {\n        return undefined;\n    }\n    const claims = idTokenClaims.get(ref);\n    if (!claims) {\n        throw CodedTypeError('\"ref\" was already garbage collected or did not resolve from the proper sources', ERR_INVALID_ARG_VALUE);\n    }\n    return claims;\n}\nasync function validateApplicationLevelSignature(as, ref, options) {\n    assertAs(as);\n    if (!jwtRefs.has(ref)) {\n        throw CodedTypeError('\"ref\" does not contain a processed JWT Response to verify the signature of', ERR_INVALID_ARG_VALUE);\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwtRefs.get(ref).split('.');\n    const header = JSON.parse(buf(b64u(protectedHeader)));\n    if (header.alg.startsWith('HS')) {\n        throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg: header.alg } });\n    }\n    let key;\n    key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, b64u(encodedSignature));\n}\nasync function processGenericAccessTokenResponse(as, client, response, additionalRequiredIdTokenClaims, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    let challenges;\n    if ((challenges = parseWwwAuthenticateChallenges(response))) {\n        throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            await response.body?.cancel();\n            throw new ResponseBodyError('server responded with an error in the response body', {\n                cause: err,\n                response,\n            });\n        }\n        throw OPE('\"response\" is not a conform Token Endpoint response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    assertApplicationJson(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(json)) {\n        throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n    }\n    assertString(json.access_token, '\"response\" body \"access_token\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.token_type, '\"response\" body \"token_type\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.token_type = json.token_type.toLowerCase();\n    if (json.token_type !== 'dpop' && json.token_type !== 'bearer') {\n        throw new UnsupportedOperationError('unsupported `token_type` value', { cause: { body: json } });\n    }\n    if (json.expires_in !== undefined) {\n        let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n        assertNumber(expiresIn, false, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n        json.expires_in = expiresIn;\n    }\n    if (json.refresh_token !== undefined) {\n        assertString(json.refresh_token, '\"response\" body \"refresh_token\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    if (json.scope !== undefined && typeof json.scope !== 'string') {\n        throw OPE('\"response\" body \"scope\" property must be a string', INVALID_RESPONSE, { body: json });\n    }\n    if (json.id_token !== undefined) {\n        assertString(json.id_token, '\"response\" body \"id_token\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n        const requiredClaims = ['aud', 'exp', 'iat', 'iss', 'sub'];\n        if (client.require_auth_time === true) {\n            requiredClaims.push('auth_time');\n        }\n        if (client.default_max_age !== undefined) {\n            assertNumber(client.default_max_age, false, '\"client.default_max_age\"');\n            requiredClaims.push('auth_time');\n        }\n        if (additionalRequiredIdTokenClaims?.length) {\n            requiredClaims.push(...additionalRequiredIdTokenClaims);\n        }\n        const { claims, jwt } = await validateJwt(json.id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(validatePresence.bind(undefined, requiredClaims))\n            .then(validateIssuer.bind(undefined, as))\n            .then(validateAudience.bind(undefined, client.client_id));\n        if (Array.isArray(claims.aud) && claims.aud.length !== 1) {\n            if (claims.azp === undefined) {\n                throw OPE('ID Token \"aud\" (audience) claim includes additional untrusted audiences', JWT_CLAIM_COMPARISON, { claims, claim: 'aud' });\n            }\n            if (claims.azp !== client.client_id) {\n                throw OPE('unexpected ID Token \"azp\" (authorized party) claim value', JWT_CLAIM_COMPARISON, { expected: client.client_id, claims, claim: 'azp' });\n            }\n        }\n        if (claims.auth_time !== undefined) {\n            assertNumber(claims.auth_time, false, 'ID Token \"auth_time\" (authentication time)', INVALID_RESPONSE, { claims });\n        }\n        jwtRefs.set(response, jwt);\n        idTokenClaims.set(json, claims);\n    }\n    return json;\n}\nasync function processRefreshTokenResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nfunction validateOptionalAudience(expected, result) {\n    if (result.claims.aud !== undefined) {\n        return validateAudience(expected, result);\n    }\n    return result;\n}\nfunction validateAudience(expected, result) {\n    if (Array.isArray(result.claims.aud)) {\n        if (!result.claims.aud.includes(expected)) {\n            throw OPE('unexpected JWT \"aud\" (audience) claim value', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: result.claims,\n                claim: 'aud',\n            });\n        }\n    }\n    else if (result.claims.aud !== expected) {\n        throw OPE('unexpected JWT \"aud\" (audience) claim value', JWT_CLAIM_COMPARISON, {\n            expected,\n            claims: result.claims,\n            claim: 'aud',\n        });\n    }\n    return result;\n}\nfunction validateOptionalIssuer(as, result) {\n    if (result.claims.iss !== undefined) {\n        return validateIssuer(as, result);\n    }\n    return result;\n}\nfunction validateIssuer(as, result) {\n    const expected = as[_expectedIssuer]?.(result) ?? as.issuer;\n    if (result.claims.iss !== expected) {\n        throw OPE('unexpected JWT \"iss\" (issuer) claim value', JWT_CLAIM_COMPARISON, {\n            expected,\n            claims: result.claims,\n            claim: 'iss',\n        });\n    }\n    return result;\n}\nconst branded = new WeakSet();\nfunction brand(searchParams) {\n    branded.add(searchParams);\n    return searchParams;\n}\nasync function authorizationCodeGrantRequest(as, client, clientAuthentication, callbackParameters, redirectUri, codeVerifier, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!branded.has(callbackParameters)) {\n        throw CodedTypeError('\"callbackParameters\" must be an instance of URLSearchParams obtained from \"validateAuthResponse()\", or \"validateJwtAuthResponse()', ERR_INVALID_ARG_VALUE);\n    }\n    assertString(redirectUri, '\"redirectUri\"');\n    const code = getURLSearchParameter(callbackParameters, 'code');\n    if (!code) {\n        throw OPE('no authorization code in \"callbackParameters\"', INVALID_RESPONSE);\n    }\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('redirect_uri', redirectUri);\n    parameters.set('code', code);\n    if (codeVerifier !== _nopkce) {\n        assertString(codeVerifier, '\"codeVerifier\"');\n        parameters.set('code_verifier', codeVerifier);\n    }\n    return tokenEndpointRequest(as, client, clientAuthentication, 'authorization_code', parameters, options);\n}\nconst jwtClaimNames = {\n    aud: 'audience',\n    c_hash: 'code hash',\n    client_id: 'client id',\n    exp: 'expiration time',\n    iat: 'issued at',\n    iss: 'issuer',\n    jti: 'jwt id',\n    nonce: 'nonce',\n    s_hash: 'state hash',\n    sub: 'subject',\n    ath: 'access token hash',\n    htm: 'http method',\n    htu: 'http uri',\n    cnf: 'confirmation',\n    auth_time: 'authentication time',\n};\nfunction validatePresence(required, result) {\n    for (const claim of required) {\n        if (result.claims[claim] === undefined) {\n            throw OPE(`JWT \"${claim}\" (${jwtClaimNames[claim]}) claim missing`, INVALID_RESPONSE, {\n                claims: result.claims,\n            });\n        }\n    }\n    return result;\n}\nconst expectNoNonce = Symbol();\nconst skipAuthTimeCheck = Symbol();\nasync function processAuthorizationCodeResponse(as, client, response, options) {\n    if (typeof options?.expectedNonce === 'string' ||\n        typeof options?.maxAge === 'number' ||\n        options?.requireIdToken) {\n        return processAuthorizationCodeOpenIDResponse(as, client, response, options.expectedNonce, options.maxAge, {\n            [jweDecrypt]: options[jweDecrypt],\n        });\n    }\n    return processAuthorizationCodeOAuth2Response(as, client, response, options);\n}\nasync function processAuthorizationCodeOpenIDResponse(as, client, response, expectedNonce, maxAge, options) {\n    const additionalRequiredClaims = [];\n    switch (expectedNonce) {\n        case undefined:\n            expectedNonce = expectNoNonce;\n            break;\n        case expectNoNonce:\n            break;\n        default:\n            assertString(expectedNonce, '\"expectedNonce\" argument');\n            additionalRequiredClaims.push('nonce');\n    }\n    maxAge ??= client.default_max_age;\n    switch (maxAge) {\n        case undefined:\n            maxAge = skipAuthTimeCheck;\n            break;\n        case skipAuthTimeCheck:\n            break;\n        default:\n            assertNumber(maxAge, false, '\"maxAge\" argument');\n            additionalRequiredClaims.push('auth_time');\n    }\n    const result = await processGenericAccessTokenResponse(as, client, response, additionalRequiredClaims, options);\n    assertString(result.id_token, '\"response\" body \"id_token\" property', INVALID_RESPONSE, {\n        body: result,\n    });\n    const claims = getValidatedIdTokenClaims(result);\n    if (maxAge !== skipAuthTimeCheck) {\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n        }\n    }\n    if (expectedNonce === expectNoNonce) {\n        if (claims.nonce !== undefined) {\n            throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n                expected: undefined,\n                claims,\n                claim: 'nonce',\n            });\n        }\n    }\n    else if (claims.nonce !== expectedNonce) {\n        throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n            expected: expectedNonce,\n            claims,\n            claim: 'nonce',\n        });\n    }\n    return result;\n}\nasync function processAuthorizationCodeOAuth2Response(as, client, response, options) {\n    const result = await processGenericAccessTokenResponse(as, client, response, undefined, options);\n    const claims = getValidatedIdTokenClaims(result);\n    if (claims) {\n        if (client.default_max_age !== undefined) {\n            assertNumber(client.default_max_age, false, '\"client.default_max_age\"');\n            const now = epochTime() + getClockSkew(client);\n            const tolerance = getClockTolerance(client);\n            if (claims.auth_time + client.default_max_age < now - tolerance) {\n                throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n            }\n        }\n        if (claims.nonce !== undefined) {\n            throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n                expected: undefined,\n                claims,\n                claim: 'nonce',\n            });\n        }\n    }\n    return result;\n}\nconst WWW_AUTHENTICATE_CHALLENGE = 'OAUTH_WWW_AUTHENTICATE_CHALLENGE';\nconst RESPONSE_BODY_ERROR = 'OAUTH_RESPONSE_BODY_ERROR';\nconst UNSUPPORTED_OPERATION = 'OAUTH_UNSUPPORTED_OPERATION';\nconst AUTHORIZATION_RESPONSE_ERROR = 'OAUTH_AUTHORIZATION_RESPONSE_ERROR';\nconst JWT_USERINFO_EXPECTED = 'OAUTH_JWT_USERINFO_EXPECTED';\nconst PARSE_ERROR = 'OAUTH_PARSE_ERROR';\nconst INVALID_RESPONSE = 'OAUTH_INVALID_RESPONSE';\nconst INVALID_REQUEST = 'OAUTH_INVALID_REQUEST';\nconst RESPONSE_IS_NOT_JSON = 'OAUTH_RESPONSE_IS_NOT_JSON';\nconst RESPONSE_IS_NOT_CONFORM = 'OAUTH_RESPONSE_IS_NOT_CONFORM';\nconst HTTP_REQUEST_FORBIDDEN = 'OAUTH_HTTP_REQUEST_FORBIDDEN';\nconst REQUEST_PROTOCOL_FORBIDDEN = 'OAUTH_REQUEST_PROTOCOL_FORBIDDEN';\nconst JWT_TIMESTAMP_CHECK = 'OAUTH_JWT_TIMESTAMP_CHECK_FAILED';\nconst JWT_CLAIM_COMPARISON = 'OAUTH_JWT_CLAIM_COMPARISON_FAILED';\nconst JSON_ATTRIBUTE_COMPARISON = 'OAUTH_JSON_ATTRIBUTE_COMPARISON_FAILED';\nconst KEY_SELECTION = 'OAUTH_KEY_SELECTION_FAILED';\nconst MISSING_SERVER_METADATA = 'OAUTH_MISSING_SERVER_METADATA';\nconst INVALID_SERVER_METADATA = 'OAUTH_INVALID_SERVER_METADATA';\nfunction checkJwtType(expected, result) {\n    if (typeof result.header.typ !== 'string' || normalizeTyp(result.header.typ) !== expected) {\n        throw OPE('unexpected JWT \"typ\" header parameter value', INVALID_RESPONSE, {\n            header: result.header,\n        });\n    }\n    return result;\n}\nasync function clientCredentialsGrantRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'client_credentials', new URLSearchParams(parameters), options);\n}\nasync function genericTokenEndpointRequest(as, client, clientAuthentication, grantType, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(grantType, '\"grantType\"');\n    return tokenEndpointRequest(as, client, clientAuthentication, grantType, new URLSearchParams(parameters), options);\n}\nasync function processGenericTokenEndpointResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function processClientCredentialsResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function revocationRequest(as, client, clientAuthentication, token, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(token, '\"token\"');\n    const url = resolveEndpoint(as, 'revocation_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    headers.delete('accept');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processRevocationResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    let challenges;\n    if ((challenges = parseWwwAuthenticateChallenges(response))) {\n        throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            await response.body?.cancel();\n            throw new ResponseBodyError('server responded with an error in the response body', {\n                cause: err,\n                response,\n            });\n        }\n        throw OPE('\"response\" is not a conform Revocation Endpoint response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    return undefined;\n}\nfunction assertReadableResponse(response) {\n    if (response.bodyUsed) {\n        throw CodedTypeError('\"response\" body has been used already', ERR_INVALID_ARG_VALUE);\n    }\n}\nasync function introspectionRequest(as, client, clientAuthentication, token, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(token, '\"token\"');\n    const url = resolveEndpoint(as, 'introspection_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(options?.additionalParameters);\n    body.set('token', token);\n    const headers = prepareHeaders(options?.headers);\n    if (options?.requestJwtResponse ?? client.introspection_signed_response_alg) {\n        headers.set('accept', 'application/token-introspection+jwt');\n    }\n    else {\n        headers.set('accept', 'application/json');\n    }\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processIntrospectionResponse(as, client, response, options) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    let challenges;\n    if ((challenges = parseWwwAuthenticateChallenges(response))) {\n        throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            await response.body?.cancel();\n            throw new ResponseBodyError('server responded with an error in the response body', {\n                cause: err,\n                response,\n            });\n        }\n        throw OPE('\"response\" is not a conform Introspection Endpoint response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    let json;\n    if (getContentType(response) === 'application/token-introspection+jwt') {\n        assertReadableResponse(response);\n        const { claims, jwt } = await validateJwt(await response.text(), checkSigningAlgorithm.bind(undefined, client.introspection_signed_response_alg, as.introspection_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n            .then(checkJwtType.bind(undefined, 'token-introspection+jwt'))\n            .then(validatePresence.bind(undefined, ['aud', 'iat', 'iss']))\n            .then(validateIssuer.bind(undefined, as))\n            .then(validateAudience.bind(undefined, client.client_id));\n        jwtRefs.set(response, jwt);\n        json = claims.token_introspection;\n        if (!isJsonObject(json)) {\n            throw OPE('JWT \"token_introspection\" claim must be a JSON object', INVALID_RESPONSE, {\n                claims,\n            });\n        }\n    }\n    else {\n        assertReadableResponse(response);\n        assertApplicationJson(response);\n        try {\n            json = await response.json();\n        }\n        catch (cause) {\n            throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n        }\n        if (!isJsonObject(json)) {\n            throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n        }\n    }\n    if (typeof json.active !== 'boolean') {\n        throw OPE('\"response\" body \"active\" property must be a boolean', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function jwksRequest(as, options) {\n    assertAs(as);\n    const url = resolveEndpoint(as, 'jwks_uri', false, options?.[allowInsecureRequests] !== true);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    headers.append('accept', 'application/jwk-set+json');\n    return (options?.[customFetch] || fetch)(url.href, {\n        body: undefined,\n        headers: Object.fromEntries(headers.entries()),\n        method: 'GET',\n        redirect: 'manual',\n        signal: options?.signal ? signal(options.signal) : undefined,\n    });\n}\nasync function processJwksResponse(response) {\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    if (response.status !== 200) {\n        throw OPE('\"response\" is not a conform JSON Web Key Set response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    assertContentTypes(response, 'application/json', 'application/jwk-set+json');\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(json)) {\n        throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n    }\n    if (!Array.isArray(json.keys)) {\n        throw OPE('\"response\" body \"keys\" property must be an array', INVALID_RESPONSE, { body: json });\n    }\n    if (!Array.prototype.every.call(json.keys, isJsonObject)) {\n        throw OPE('\"response\" body \"keys\" property members must be JWK formatted objects', INVALID_RESPONSE, { body: json });\n    }\n    return json;\n}\nasync function handleOAuthBodyError(response) {\n    if (response.status > 399 && response.status < 500) {\n        assertReadableResponse(response);\n        assertApplicationJson(response);\n        try {\n            const json = await response.clone().json();\n            if (isJsonObject(json) && typeof json.error === 'string' && json.error.length) {\n                return json;\n            }\n        }\n        catch { }\n    }\n    return undefined;\n}\nfunction supported(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'ES256':\n        case 'RS256':\n        case 'PS384':\n        case 'ES384':\n        case 'RS384':\n        case 'PS512':\n        case 'ES512':\n        case 'RS512':\n        case 'Ed25519':\n        case 'EdDSA':\n            return true;\n        default:\n            return false;\n    }\n}\nfunction checkSupportedJwsAlg(header) {\n    if (!supported(header.alg)) {\n        throw new UnsupportedOperationError('unsupported JWS \"alg\" identifier', {\n            cause: { alg: header.alg },\n        });\n    }\n}\nfunction checkRsaKeyAlgorithm(key) {\n    const { algorithm } = key;\n    if (typeof algorithm.modulusLength !== 'number' || algorithm.modulusLength < 2048) {\n        throw new UnsupportedOperationError(`unsupported ${algorithm.name} modulusLength`, {\n            cause: key,\n        });\n    }\n}\nfunction ecdsaHashName(key) {\n    const { algorithm } = key;\n    switch (algorithm.namedCurve) {\n        case 'P-256':\n            return 'SHA-256';\n        case 'P-384':\n            return 'SHA-384';\n        case 'P-521':\n            return 'SHA-512';\n        default:\n            throw new UnsupportedOperationError('unsupported ECDSA namedCurve', { cause: key });\n    }\n}\nfunction keyToSubtle(key) {\n    switch (key.algorithm.name) {\n        case 'ECDSA':\n            return {\n                name: key.algorithm.name,\n                hash: ecdsaHashName(key),\n            };\n        case 'RSA-PSS': {\n            checkRsaKeyAlgorithm(key);\n            switch (key.algorithm.hash.name) {\n                case 'SHA-256':\n                case 'SHA-384':\n                case 'SHA-512':\n                    return {\n                        name: key.algorithm.name,\n                        saltLength: parseInt(key.algorithm.hash.name.slice(-3), 10) >> 3,\n                    };\n                default:\n                    throw new UnsupportedOperationError('unsupported RSA-PSS hash name', { cause: key });\n            }\n        }\n        case 'RSASSA-PKCS1-v1_5':\n            checkRsaKeyAlgorithm(key);\n            return key.algorithm.name;\n        case 'Ed25519':\n        case 'EdDSA':\n            return key.algorithm.name;\n    }\n    throw new UnsupportedOperationError('unsupported CryptoKey algorithm name', { cause: key });\n}\nasync function validateJwsSignature(protectedHeader, payload, key, signature) {\n    const data = buf(`${protectedHeader}.${payload}`);\n    const algorithm = keyToSubtle(key);\n    const verified = await crypto.subtle.verify(algorithm, key, signature, data);\n    if (!verified) {\n        throw OPE('JWT signature verification failed', INVALID_RESPONSE, {\n            key,\n            data,\n            signature,\n            algorithm,\n        });\n    }\n}\nasync function validateJwt(jws, checkAlg, clockSkew, clockTolerance, decryptJwt) {\n    let { 0: protectedHeader, 1: payload, length } = jws.split('.');\n    if (length === 5) {\n        if (decryptJwt !== undefined) {\n            jws = await decryptJwt(jws);\n            ({ 0: protectedHeader, 1: payload, length } = jws.split('.'));\n        }\n        else {\n            throw new UnsupportedOperationError('JWE decryption is not configured', { cause: jws });\n        }\n    }\n    if (length !== 3) {\n        throw OPE('Invalid JWT', INVALID_RESPONSE, jws);\n    }\n    let header;\n    try {\n        header = JSON.parse(buf(b64u(protectedHeader)));\n    }\n    catch (cause) {\n        throw OPE('failed to parse JWT Header body as base64url encoded JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(header)) {\n        throw OPE('JWT Header must be a top level object', INVALID_RESPONSE, jws);\n    }\n    checkAlg(header);\n    if (header.crit !== undefined) {\n        throw new UnsupportedOperationError('no JWT \"crit\" header parameter extensions are supported', {\n            cause: { header },\n        });\n    }\n    let claims;\n    try {\n        claims = JSON.parse(buf(b64u(payload)));\n    }\n    catch (cause) {\n        throw OPE('failed to parse JWT Payload body as base64url encoded JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(claims)) {\n        throw OPE('JWT Payload must be a top level object', INVALID_RESPONSE, jws);\n    }\n    const now = epochTime() + clockSkew;\n    if (claims.exp !== undefined) {\n        if (typeof claims.exp !== 'number') {\n            throw OPE('unexpected JWT \"exp\" (expiration time) claim type', INVALID_RESPONSE, { claims });\n        }\n        if (claims.exp <= now - clockTolerance) {\n            throw OPE('unexpected JWT \"exp\" (expiration time) claim value, expiration is past current timestamp', JWT_TIMESTAMP_CHECK, { claims, now, tolerance: clockTolerance, claim: 'exp' });\n        }\n    }\n    if (claims.iat !== undefined) {\n        if (typeof claims.iat !== 'number') {\n            throw OPE('unexpected JWT \"iat\" (issued at) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    if (claims.iss !== undefined) {\n        if (typeof claims.iss !== 'string') {\n            throw OPE('unexpected JWT \"iss\" (issuer) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    if (claims.nbf !== undefined) {\n        if (typeof claims.nbf !== 'number') {\n            throw OPE('unexpected JWT \"nbf\" (not before) claim type', INVALID_RESPONSE, { claims });\n        }\n        if (claims.nbf > now + clockTolerance) {\n            throw OPE('unexpected JWT \"nbf\" (not before) claim value', JWT_TIMESTAMP_CHECK, {\n                claims,\n                now,\n                tolerance: clockTolerance,\n                claim: 'nbf',\n            });\n        }\n    }\n    if (claims.aud !== undefined) {\n        if (typeof claims.aud !== 'string' && !Array.isArray(claims.aud)) {\n            throw OPE('unexpected JWT \"aud\" (audience) claim type', INVALID_RESPONSE, { claims });\n        }\n    }\n    return { header, claims, jwt: jws };\n}\nasync function validateJwtAuthResponse(as, client, parameters, expectedState, options) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, or URL', ERR_INVALID_ARG_TYPE);\n    }\n    const response = getURLSearchParameter(parameters, 'response');\n    if (!response) {\n        throw OPE('\"parameters\" does not contain a JARM response', INVALID_RESPONSE);\n    }\n    const { claims, header, jwt } = await validateJwt(response, checkSigningAlgorithm.bind(undefined, client.authorization_signed_response_alg, as.authorization_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n        .then(validatePresence.bind(undefined, ['aud', 'exp', 'iss']))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwt.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    const result = new URLSearchParams();\n    for (const [key, value] of Object.entries(claims)) {\n        if (typeof value === 'string' && key !== 'aud') {\n            result.set(key, value);\n        }\n    }\n    return validateAuthResponse(as, client, result, expectedState);\n}\nasync function idTokenHash(data, header, claimName) {\n    let algorithm;\n    switch (header.alg) {\n        case 'RS256':\n        case 'PS256':\n        case 'ES256':\n            algorithm = 'SHA-256';\n            break;\n        case 'RS384':\n        case 'PS384':\n        case 'ES384':\n            algorithm = 'SHA-384';\n            break;\n        case 'RS512':\n        case 'PS512':\n        case 'ES512':\n        case 'Ed25519':\n        case 'EdDSA':\n            algorithm = 'SHA-512';\n            break;\n        default:\n            throw new UnsupportedOperationError(`unsupported JWS algorithm for ${claimName} calculation`, { cause: { alg: header.alg } });\n    }\n    const digest = await crypto.subtle.digest(algorithm, buf(data));\n    return b64u(digest.slice(0, digest.byteLength / 2));\n}\nasync function idTokenHashMatches(data, actual, header, claimName) {\n    const expected = await idTokenHash(data, header, claimName);\n    return actual === expected;\n}\nasync function validateDetachedSignatureResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    return validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, true);\n}\nasync function validateCodeIdTokenResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options) {\n    return validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, false);\n}\nasync function consumeStream(request) {\n    if (request.bodyUsed) {\n        throw CodedTypeError('form_post Request instances must contain a readable body', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    return request.text();\n}\nasync function formPostResponse(request) {\n    if (request.method !== 'POST') {\n        throw CodedTypeError('form_post responses are expected to use the POST method', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    if (getContentType(request) !== 'application/x-www-form-urlencoded') {\n        throw CodedTypeError('form_post responses are expected to use the application/x-www-form-urlencoded content-type', ERR_INVALID_ARG_VALUE, { cause: request });\n    }\n    return consumeStream(request);\n}\nasync function validateHybridResponse(as, client, parameters, expectedNonce, expectedState, maxAge, options, fapi) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        if (!parameters.hash.length) {\n            throw CodedTypeError('\"parameters\" as an instance of URL must contain a hash (fragment) with the Authorization Response parameters', ERR_INVALID_ARG_VALUE);\n        }\n        parameters = new URLSearchParams(parameters.hash.slice(1));\n    }\n    else if (looseInstanceOf(parameters, Request)) {\n        parameters = new URLSearchParams(await formPostResponse(parameters));\n    }\n    else if (parameters instanceof URLSearchParams) {\n        parameters = new URLSearchParams(parameters);\n    }\n    else {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, URL, or Response', ERR_INVALID_ARG_TYPE);\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    parameters.delete('id_token');\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            break;\n        default:\n            assertString(expectedState, '\"expectedState\" argument');\n    }\n    const result = validateAuthResponse({\n        ...as,\n        authorization_response_iss_parameter_supported: false,\n    }, client, parameters, expectedState);\n    if (!id_token) {\n        throw OPE('\"parameters\" does not contain an ID Token', INVALID_RESPONSE);\n    }\n    const code = getURLSearchParameter(parameters, 'code');\n    if (!code) {\n        throw OPE('\"parameters\" does not contain an Authorization Code', INVALID_RESPONSE);\n    }\n    const requiredClaims = [\n        'aud',\n        'exp',\n        'iat',\n        'iss',\n        'sub',\n        'nonce',\n        'c_hash',\n    ];\n    const state = parameters.get('state');\n    if (fapi && (typeof expectedState === 'string' || state !== null)) {\n        requiredClaims.push('s_hash');\n    }\n    if (maxAge !== undefined) {\n        assertNumber(maxAge, false, '\"maxAge\" argument');\n    }\n    else if (client.default_max_age !== undefined) {\n        assertNumber(client.default_max_age, false, '\"client.default_max_age\"');\n    }\n    maxAge ??= client.default_max_age ?? skipAuthTimeCheck;\n    if (client.require_auth_time || maxAge !== skipAuthTimeCheck) {\n        requiredClaims.push('auth_time');\n    }\n    const { claims, header, jwt } = await validateJwt(id_token, checkSigningAlgorithm.bind(undefined, client.id_token_signed_response_alg, as.id_token_signing_alg_values_supported, 'RS256'), getClockSkew(client), getClockTolerance(client), options?.[jweDecrypt])\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, client.client_id));\n    const clockSkew = getClockSkew(client);\n    const now = epochTime() + clockSkew;\n    if (claims.iat < now - 3600) {\n        throw OPE('unexpected JWT \"iat\" (issued at) claim value, it is too far in the past', JWT_TIMESTAMP_CHECK, { now, claims, claim: 'iat' });\n    }\n    assertString(claims.c_hash, 'ID Token \"c_hash\" (code hash) claim value', INVALID_RESPONSE, {\n        claims,\n    });\n    if (claims.auth_time !== undefined) {\n        assertNumber(claims.auth_time, false, 'ID Token \"auth_time\" (authentication time)', INVALID_RESPONSE, { claims });\n    }\n    if (maxAge !== skipAuthTimeCheck) {\n        const now = epochTime() + getClockSkew(client);\n        const tolerance = getClockTolerance(client);\n        if (claims.auth_time + maxAge < now - tolerance) {\n            throw OPE('too much time has elapsed since the last End-User authentication', JWT_TIMESTAMP_CHECK, { claims, now, tolerance, claim: 'auth_time' });\n        }\n    }\n    assertString(expectedNonce, '\"expectedNonce\" argument');\n    if (claims.nonce !== expectedNonce) {\n        throw OPE('unexpected ID Token \"nonce\" claim value', JWT_CLAIM_COMPARISON, {\n            expected: expectedNonce,\n            claims,\n            claim: 'nonce',\n        });\n    }\n    if (Array.isArray(claims.aud) && claims.aud.length !== 1) {\n        if (claims.azp === undefined) {\n            throw OPE('ID Token \"aud\" (audience) claim includes additional untrusted audiences', JWT_CLAIM_COMPARISON, { claims, claim: 'aud' });\n        }\n        if (claims.azp !== client.client_id) {\n            throw OPE('unexpected ID Token \"azp\" (authorized party) claim value', JWT_CLAIM_COMPARISON, {\n                expected: client.client_id,\n                claims,\n                claim: 'azp',\n            });\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = jwt.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    if ((await idTokenHashMatches(code, claims.c_hash, header, 'c_hash')) !== true) {\n        throw OPE('invalid ID Token \"c_hash\" (code hash) claim value', JWT_CLAIM_COMPARISON, {\n            code,\n            alg: header.alg,\n            claim: 'c_hash',\n            claims,\n        });\n    }\n    if ((fapi && state !== null) || claims.s_hash !== undefined) {\n        assertString(claims.s_hash, 'ID Token \"s_hash\" (state hash) claim value', INVALID_RESPONSE, {\n            claims,\n        });\n        assertString(state, '\"state\" response parameter', INVALID_RESPONSE, { parameters });\n        if ((await idTokenHashMatches(state, claims.s_hash, header, 's_hash')) !== true) {\n            throw OPE('invalid ID Token \"s_hash\" (state hash) claim value', JWT_CLAIM_COMPARISON, {\n                state,\n                alg: header.alg,\n                claim: 's_hash',\n                claims,\n            });\n        }\n    }\n    return result;\n}\nfunction checkSigningAlgorithm(client, issuer, fallback, header) {\n    if (client !== undefined) {\n        if (typeof client === 'string' ? header.alg !== client : !client.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: client,\n                reason: 'client configuration',\n            });\n        }\n        return;\n    }\n    if (Array.isArray(issuer)) {\n        if (!issuer.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: issuer,\n                reason: 'authorization server metadata',\n            });\n        }\n        return;\n    }\n    if (fallback !== undefined) {\n        if (typeof fallback === 'string'\n            ? header.alg !== fallback\n            : typeof fallback === 'function'\n                ? !fallback(header.alg)\n                : !fallback.includes(header.alg)) {\n            throw OPE('unexpected JWT \"alg\" header parameter', INVALID_RESPONSE, {\n                header,\n                expected: fallback,\n                reason: 'default value',\n            });\n        }\n        return;\n    }\n    throw OPE('missing client or server configuration to verify used JWT \"alg\" header parameter', undefined, { client, issuer, fallback });\n}\nfunction getURLSearchParameter(parameters, name) {\n    const { 0: value, length } = parameters.getAll(name);\n    if (length > 1) {\n        throw OPE(`\"${name}\" parameter must be provided only once`, INVALID_RESPONSE);\n    }\n    return value;\n}\nconst skipStateCheck = Symbol();\nconst expectNoState = Symbol();\nfunction validateAuthResponse(as, client, parameters, expectedState) {\n    assertAs(as);\n    assertClient(client);\n    if (parameters instanceof URL) {\n        parameters = parameters.searchParams;\n    }\n    if (!(parameters instanceof URLSearchParams)) {\n        throw CodedTypeError('\"parameters\" must be an instance of URLSearchParams, or URL', ERR_INVALID_ARG_TYPE);\n    }\n    if (getURLSearchParameter(parameters, 'response')) {\n        throw OPE('\"parameters\" contains a JARM response, use validateJwtAuthResponse() instead of validateAuthResponse()', INVALID_RESPONSE, { parameters });\n    }\n    const iss = getURLSearchParameter(parameters, 'iss');\n    const state = getURLSearchParameter(parameters, 'state');\n    if (!iss && as.authorization_response_iss_parameter_supported) {\n        throw OPE('response parameter \"iss\" (issuer) missing', INVALID_RESPONSE, { parameters });\n    }\n    if (iss && iss !== as.issuer) {\n        throw OPE('unexpected \"iss\" (issuer) response parameter value', INVALID_RESPONSE, {\n            expected: as.issuer,\n            parameters,\n        });\n    }\n    switch (expectedState) {\n        case undefined:\n        case expectNoState:\n            if (state !== undefined) {\n                throw OPE('unexpected \"state\" response parameter encountered', INVALID_RESPONSE, {\n                    expected: undefined,\n                    parameters,\n                });\n            }\n            break;\n        case skipStateCheck:\n            break;\n        default:\n            assertString(expectedState, '\"expectedState\" argument');\n            if (state !== expectedState) {\n                throw OPE(state === undefined\n                    ? 'response parameter \"state\" missing'\n                    : 'unexpected \"state\" response parameter value', INVALID_RESPONSE, { expected: expectedState, parameters });\n            }\n    }\n    const error = getURLSearchParameter(parameters, 'error');\n    if (error) {\n        throw new AuthorizationResponseError('authorization response from the server is an error', {\n            cause: parameters,\n        });\n    }\n    const id_token = getURLSearchParameter(parameters, 'id_token');\n    const token = getURLSearchParameter(parameters, 'token');\n    if (id_token !== undefined || token !== undefined) {\n        throw new UnsupportedOperationError('implicit and hybrid flows are not supported');\n    }\n    return brand(new URLSearchParams(parameters));\n}\nfunction algToSubtle(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n        case 'ES256':\n        case 'ES384':\n            return { name: 'ECDSA', namedCurve: `P-${alg.slice(-3)}` };\n        case 'ES512':\n            return { name: 'ECDSA', namedCurve: 'P-521' };\n        case 'Ed25519':\n        case 'EdDSA':\n            return 'Ed25519';\n        default:\n            throw new UnsupportedOperationError('unsupported JWS algorithm', { cause: { alg } });\n    }\n}\nasync function importJwk(alg, jwk) {\n    const { ext, key_ops, use, ...key } = jwk;\n    return crypto.subtle.importKey('jwk', key, algToSubtle(alg), true, ['verify']);\n}\nasync function deviceAuthorizationRequest(as, client, clientAuthentication, parameters, options) {\n    assertAs(as);\n    assertClient(client);\n    const url = resolveEndpoint(as, 'device_authorization_endpoint', client.use_mtls_endpoint_aliases, options?.[allowInsecureRequests] !== true);\n    const body = new URLSearchParams(parameters);\n    body.set('client_id', client.client_id);\n    const headers = prepareHeaders(options?.headers);\n    headers.set('accept', 'application/json');\n    return authenticatedRequest(as, client, clientAuthentication, url, body, headers, options);\n}\nasync function processDeviceAuthorizationResponse(as, client, response) {\n    assertAs(as);\n    assertClient(client);\n    if (!looseInstanceOf(response, Response)) {\n        throw CodedTypeError('\"response\" must be an instance of Response', ERR_INVALID_ARG_TYPE);\n    }\n    let challenges;\n    if ((challenges = parseWwwAuthenticateChallenges(response))) {\n        throw new WWWAuthenticateChallengeError('server responded with a challenge in the WWW-Authenticate HTTP Header', { cause: challenges, response });\n    }\n    if (response.status !== 200) {\n        let err;\n        if ((err = await handleOAuthBodyError(response))) {\n            await response.body?.cancel();\n            throw new ResponseBodyError('server responded with an error in the response body', {\n                cause: err,\n                response,\n            });\n        }\n        throw OPE('\"response\" is not a conform Device Authorization Endpoint response (unexpected HTTP status code)', RESPONSE_IS_NOT_CONFORM, response);\n    }\n    assertReadableResponse(response);\n    assertApplicationJson(response);\n    let json;\n    try {\n        json = await response.json();\n    }\n    catch (cause) {\n        throw OPE('failed to parse \"response\" body as JSON', PARSE_ERROR, cause);\n    }\n    if (!isJsonObject(json)) {\n        throw OPE('\"response\" body must be a top level object', INVALID_RESPONSE, { body: json });\n    }\n    assertString(json.device_code, '\"response\" body \"device_code\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.user_code, '\"response\" body \"user_code\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    assertString(json.verification_uri, '\"response\" body \"verification_uri\" property', INVALID_RESPONSE, { body: json });\n    let expiresIn = typeof json.expires_in !== 'number' ? parseFloat(json.expires_in) : json.expires_in;\n    assertNumber(expiresIn, false, '\"response\" body \"expires_in\" property', INVALID_RESPONSE, {\n        body: json,\n    });\n    json.expires_in = expiresIn;\n    if (json.verification_uri_complete !== undefined) {\n        assertString(json.verification_uri_complete, '\"response\" body \"verification_uri_complete\" property', INVALID_RESPONSE, { body: json });\n    }\n    if (json.interval !== undefined) {\n        assertNumber(json.interval, false, '\"response\" body \"interval\" property', INVALID_RESPONSE, {\n            body: json,\n        });\n    }\n    return json;\n}\nasync function deviceCodeGrantRequest(as, client, clientAuthentication, deviceCode, options) {\n    assertAs(as);\n    assertClient(client);\n    assertString(deviceCode, '\"deviceCode\"');\n    const parameters = new URLSearchParams(options?.additionalParameters);\n    parameters.set('device_code', deviceCode);\n    return tokenEndpointRequest(as, client, clientAuthentication, 'urn:ietf:params:oauth:grant-type:device_code', parameters, options);\n}\nasync function processDeviceCodeResponse(as, client, response, options) {\n    return processGenericAccessTokenResponse(as, client, response, undefined, options);\n}\nasync function generateKeyPair(alg, options) {\n    assertString(alg, '\"alg\"');\n    const algorithm = algToSubtle(alg);\n    if (alg.startsWith('PS') || alg.startsWith('RS')) {\n        Object.assign(algorithm, {\n            modulusLength: options?.modulusLength ?? 2048,\n            publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n        });\n    }\n    return crypto.subtle.generateKey(algorithm, options?.extractable ?? false, [\n        'sign',\n        'verify',\n    ]);\n}\nfunction normalizeHtu(htu) {\n    const url = new URL(htu);\n    url.search = '';\n    url.hash = '';\n    return url.href;\n}\nasync function validateDPoP(request, accessToken, accessTokenClaims, options) {\n    const headerValue = request.headers.get('dpop');\n    if (headerValue === null) {\n        throw OPE('operation indicated DPoP use but the request has no DPoP HTTP Header', INVALID_REQUEST, { headers: request.headers });\n    }\n    if (request.headers.get('authorization')?.toLowerCase().startsWith('dpop ') === false) {\n        throw OPE(`operation indicated DPoP use but the request's Authorization HTTP Header scheme is not DPoP`, INVALID_REQUEST, { headers: request.headers });\n    }\n    if (typeof accessTokenClaims.cnf?.jkt !== 'string') {\n        throw OPE('operation indicated DPoP use but the JWT Access Token has no jkt confirmation claim', INVALID_REQUEST, { claims: accessTokenClaims });\n    }\n    const clockSkew = getClockSkew(options);\n    const proof = await validateJwt(headerValue, checkSigningAlgorithm.bind(undefined, options?.signingAlgorithms, undefined, supported), clockSkew, getClockTolerance(options), undefined)\n        .then(checkJwtType.bind(undefined, 'dpop+jwt'))\n        .then(validatePresence.bind(undefined, ['iat', 'jti', 'ath', 'htm', 'htu']));\n    const now = epochTime() + clockSkew;\n    const diff = Math.abs(now - proof.claims.iat);\n    if (diff > 300) {\n        throw OPE('DPoP Proof iat is not recent enough', JWT_TIMESTAMP_CHECK, {\n            now,\n            claims: proof.claims,\n            claim: 'iat',\n        });\n    }\n    if (proof.claims.htm !== request.method) {\n        throw OPE('DPoP Proof htm mismatch', JWT_CLAIM_COMPARISON, {\n            expected: request.method,\n            claims: proof.claims,\n            claim: 'htm',\n        });\n    }\n    if (typeof proof.claims.htu !== 'string' ||\n        normalizeHtu(proof.claims.htu) !== normalizeHtu(request.url)) {\n        throw OPE('DPoP Proof htu mismatch', JWT_CLAIM_COMPARISON, {\n            expected: normalizeHtu(request.url),\n            claims: proof.claims,\n            claim: 'htu',\n        });\n    }\n    {\n        const expected = b64u(await crypto.subtle.digest('SHA-256', buf(accessToken)));\n        if (proof.claims.ath !== expected) {\n            throw OPE('DPoP Proof ath mismatch', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: proof.claims,\n                claim: 'ath',\n            });\n        }\n    }\n    {\n        let components;\n        switch (proof.header.jwk.kty) {\n            case 'EC':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                    y: proof.header.jwk.y,\n                };\n                break;\n            case 'OKP':\n                components = {\n                    crv: proof.header.jwk.crv,\n                    kty: proof.header.jwk.kty,\n                    x: proof.header.jwk.x,\n                };\n                break;\n            case 'RSA':\n                components = {\n                    e: proof.header.jwk.e,\n                    kty: proof.header.jwk.kty,\n                    n: proof.header.jwk.n,\n                };\n                break;\n            default:\n                throw new UnsupportedOperationError('unsupported JWK key type', { cause: proof.header.jwk });\n        }\n        const expected = b64u(await crypto.subtle.digest('SHA-256', buf(JSON.stringify(components))));\n        if (accessTokenClaims.cnf.jkt !== expected) {\n            throw OPE('JWT Access Token confirmation mismatch', JWT_CLAIM_COMPARISON, {\n                expected,\n                claims: accessTokenClaims,\n                claim: 'cnf.jkt',\n            });\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = headerValue.split('.');\n    const signature = b64u(encodedSignature);\n    const { jwk, alg } = proof.header;\n    if (!jwk) {\n        throw OPE('DPoP Proof is missing the jwk header parameter', INVALID_REQUEST, {\n            header: proof.header,\n        });\n    }\n    const key = await importJwk(alg, jwk);\n    if (key.type !== 'public') {\n        throw OPE('DPoP Proof jwk header parameter must contain a public key', INVALID_REQUEST, {\n            header: proof.header,\n        });\n    }\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n}\nasync function validateJwtAccessToken(as, request, expectedAudience, options) {\n    assertAs(as);\n    if (!looseInstanceOf(request, Request)) {\n        throw CodedTypeError('\"request\" must be an instance of Request', ERR_INVALID_ARG_TYPE);\n    }\n    assertString(expectedAudience, '\"expectedAudience\"');\n    const authorization = request.headers.get('authorization');\n    if (authorization === null) {\n        throw OPE('\"request\" is missing an Authorization HTTP Header', INVALID_REQUEST, {\n            headers: request.headers,\n        });\n    }\n    let { 0: scheme, 1: accessToken, length } = authorization.split(' ');\n    scheme = scheme.toLowerCase();\n    switch (scheme) {\n        case 'dpop':\n        case 'bearer':\n            break;\n        default:\n            throw new UnsupportedOperationError('unsupported Authorization HTTP Header scheme', {\n                cause: { headers: request.headers },\n            });\n    }\n    if (length !== 2) {\n        throw OPE('invalid Authorization HTTP Header format', INVALID_REQUEST, {\n            headers: request.headers,\n        });\n    }\n    const requiredClaims = [\n        'iss',\n        'exp',\n        'aud',\n        'sub',\n        'iat',\n        'jti',\n        'client_id',\n    ];\n    if (options?.requireDPoP || scheme === 'dpop' || request.headers.has('dpop')) {\n        requiredClaims.push('cnf');\n    }\n    const { claims, header } = await validateJwt(accessToken, checkSigningAlgorithm.bind(undefined, options?.signingAlgorithms, undefined, supported), getClockSkew(options), getClockTolerance(options), undefined)\n        .then(checkJwtType.bind(undefined, 'at+jwt'))\n        .then(validatePresence.bind(undefined, requiredClaims))\n        .then(validateIssuer.bind(undefined, as))\n        .then(validateAudience.bind(undefined, expectedAudience))\n        .catch(reassignRSCode);\n    for (const claim of ['client_id', 'jti', 'sub']) {\n        if (typeof claims[claim] !== 'string') {\n            throw OPE(`unexpected JWT \"${claim}\" claim type`, INVALID_REQUEST, { claims });\n        }\n    }\n    if ('cnf' in claims) {\n        if (!isJsonObject(claims.cnf)) {\n            throw OPE('unexpected JWT \"cnf\" (confirmation) claim value', INVALID_REQUEST, { claims });\n        }\n        const { 0: cnf, length } = Object.keys(claims.cnf);\n        if (length) {\n            if (length !== 1) {\n                throw new UnsupportedOperationError('multiple confirmation claims are not supported', {\n                    cause: { claims },\n                });\n            }\n            if (cnf !== 'jkt') {\n                throw new UnsupportedOperationError('unsupported JWT Confirmation method', {\n                    cause: { claims },\n                });\n            }\n        }\n    }\n    const { 0: protectedHeader, 1: payload, 2: encodedSignature } = accessToken.split('.');\n    const signature = b64u(encodedSignature);\n    const key = await getPublicSigKeyFromIssuerJwksUri(as, options, header);\n    await validateJwsSignature(protectedHeader, payload, key, signature);\n    if (options?.requireDPoP ||\n        scheme === 'dpop' ||\n        claims.cnf?.jkt !== undefined ||\n        request.headers.has('dpop')) {\n        await validateDPoP(request, accessToken, claims, options).catch(reassignRSCode);\n    }\n    return claims;\n}\nfunction reassignRSCode(err) {\n    if (err instanceof OperationProcessingError && err?.code === INVALID_REQUEST) {\n        err.code = INVALID_RESPONSE;\n    }\n    throw err;\n}\nconst _nopkce = Symbol();\nconst _nodiscoverycheck = Symbol();\nconst _expectedIssuer = Symbol();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/oauth4webapi/build/index.js\n");

/***/ })

};
;