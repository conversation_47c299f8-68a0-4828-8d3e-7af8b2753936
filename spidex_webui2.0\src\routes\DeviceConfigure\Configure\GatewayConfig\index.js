import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Card,
  Tag,
  Select,
  CommonCompactView,
  Drawer,
  Typography,
  message,
  Row,
  Col,
  Input,
  Space,
  Tooltip,
} from '../../../../components';
import { getAllEventAttrs, getAllGatewayConfigs, deleteGatewayConfig, addGatewayConfig } from '../../../../services';
import Context from '../../../../context';
import { PlusOutlined, MinusCircleOutlined, CodeOutlined, InfoCircleOutlined, CopyFilled } from '@ant-design/icons';
import { calcDrawerWidth, buildCommonApiValues } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import { CRUD, Pages, ModuleNames } from '../../../../constants';
import { get } from 'lodash';
import s from './index.module.less';

const { Option } = Select;
const { Paragraph, Title } = Typography;

const GatewayConfig = () => {
  const [context, setContext] = useContext(Context);
  const [gatewayConfigs, setGatewayConfigs] = useState([]);
  const [eventAttrs, setEventAttrs] = useState([]);
  const [visible, setVisible] = useState(false);
  const [selectedAttrs, setSelectedAttrs] = useState([]);

  const [form] = Form.useForm();

  const openAdd = () => {
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    setSelectedAttrs([]);
    form.resetFields();
  };

  const onAttributeChange = (e) => {
    setSelectedAttrs(e);
  };

  const clearForm = () => {
    setSelectedAttrs([]);
    form.resetFields();
  };

  const buildDataPayload = (values) => {
    const dataPayload = { ...values };
    const { eventAttrConfigs } = dataPayload.gatewayConfig;
    dataPayload.spdxModelConfig = {};
    dataPayload.gatewayConfig.eventAttrConfigs = [];
    dataPayload.spdxModelConfig.tagConfig = null;
    dataPayload.spdxModelConfig.gatewayConfig = { ...dataPayload.gatewayConfig };
    delete dataPayload.gatewayConfig;

    for (const [key, value] of Object.entries(eventAttrConfigs)) {
      const attrSelected = eventAttrs.find((x) => x.id === parseInt(key));
      if (!key.includes('characteristics')) {
        dataPayload.spdxModelConfig.gatewayConfig.eventAttrConfigs.push({
          ...value,
          ...attrSelected,
          attId: attrSelected.attributeId,
          attrName: attrSelected.name,
          category: attrSelected.category,
          eventType: attrSelected.eventType,
          id: key,
        });
      } else {
        dataPayload.spdxModelConfig.gatewayConfig.eventAttrConfigs.forEach((x) => {
          if (x.id === key.split('-')[1]) {
            x.characteristics = value;
          }
        });
      }
    }
    return dataPayload;
  };

  const finishAdd = async () => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    const dataPayload = buildDataPayload({ ...values, ...commonValues });
    await saveGatewayConfigAction(dataPayload);
    setVisible(false);
    setSelectedAttrs([]);
    form.resetFields();
  };

  const saveGatewayConfigAction = async (tag) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addGatewayConfig(tag)
      .then((res) => {
        setGatewayConfigs((state) => [res.data, ...state]);
        message.success('Succesfully Added gateway config');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Gateway Config, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const deleteGatewayConfigAction = (tagConfigId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteGatewayConfig(tagConfigId)
      .then(() => {
        setGatewayConfigs((state) => {
          return [...state].filter((x) => x.id !== tagConfigId);
        });
        message.success('Succesfully Deleted gateway config');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Gateway Config, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const copyRecord = (record) => {
    if (window.isSecureContext || window.location.hostname === 'localhost') {
      navigator.clipboard.writeText(JSON.stringify(record));
      message.success('Copied to clipboard.');
    } else {
      message.error('Protocol is not a secure context.');
    }
  };

  useEffect(() => {
    const init = async () => {
      if (context.profile.tenantId) {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
        try {
          const eventAttrRes = await getAllEventAttrs(context.profile.tenantId);
          setEventAttrs(eventAttrRes.data.filter((x) => x.deleted === false));
        } catch (e) {
          console.log(e);
          message.error('Unable to get Event Attribute details, try again later');
        }
        getAllGatewayConfigs(context.profile.tenantId)
          .then((res) => {
            setGatewayConfigs(res.data.filter((x) => x.spdxModelConfig?.gatewayConfig));
          })
          .catch((e) => {
            console.log(e);
            message.error('Unable to get Gateway Config details, try again later');
          })
          .finally(() => {
            setContext((state) => {
              return {
                ...state,
                isLoading: false,
              };
            });
          });
      }
    };
    init();
    // eslint-disable-next-line
  }, []);

  const tableCols = [
    { title: <strong> ID </strong>, key: 'modelId', dataIndex: 'modelId' },
    { title: <strong> Model Name </strong>, key: 'modelName', dataIndex: 'modelName' },
    {
      title: <strong> Data </strong>,
      key: 'data',
      render: (record) => (
        <span>
          <Tooltip className={s.customTooltip} title={JSON.stringify(record)} placement="bottom">
            <span>
              <CodeOutlined />
            </span>
          </Tooltip>
          <Button type="link" icon={<CopyFilled />} onClick={() => copyRecord(record)} />
        </span>
      ),
    },
    {
      title: <strong> Info </strong>,
      key: 'info',
      render: (record) => (
        <Tooltip
          title={
            <>
              <p>{`Created By: ${record.createdBy}`}</p>
              <p>{`Created At: ${new Date(record.createdTime * 1000).toDateString()}`}</p>
            </>
          }
          placement="bottom"
        >
          <span>
            <InfoCircleOutlined />
          </span>
        </Tooltip>
      ),
    },
    {
      title: <strong> Actions </strong>,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <p className="lastModifiedDate">
            Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
          </p>
        ) : (
          <PermissionContainer page={Pages.GATEWAY_CONFIG} permission={CRUD.DELETE}>
            <Button type="link" onClick={() => deleteGatewayConfigAction(record.id)} className="actionButton">
              Delete
            </Button>
          </PermissionContainer>
        ),
    },
  ];
  const gatewayconfigBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.GATEWAY_CONFIG}].displayName`,
    ModuleNames.GATEWAY_CONFIG
  );

  return (
    <div className={s.gatewayConfigContainer}>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList
            list={['Home', ModuleNames.DEVICE_CONFIGURE, ModuleNames.CONFIGURE, gatewayconfigBreadcrumbsName]}
          />
        </Col>
        <Col>
          {!context.isCompact && (
            <PermissionContainer page={Pages.GATEWAY_CONFIG} permission={CRUD.ADD}>
              <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
                Add Gateway Config
              </Button>
            </PermissionContainer>
          )}
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="modelId"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={gatewayConfigs}
        />
      ) : (
        <CommonCompactView
          data={gatewayConfigs}
          onDelete={deleteGatewayConfigAction}
          permissions={[{ pageName: Pages.GATEWAY_CONFIG, permission: CRUD.DELETE, label: 'Delete' }]}
          title="modelName"
          dataList={[
            { label: 'Created By', value: 'createdBy' },
            { label: 'Created At', value: 'createdTime', type: 'date' },
          ]}
        />
      )}

      <Drawer
        getContainer={false}
        width={calcDrawerWidth(true)}
        visible={visible}
        className="commonDrawer"
        onClose={closeAdd}
        bodyStyle={{ padding: 0 }}
      >
        <Title className="title">Gateway Config</Title>
        <div className="content">
          <Form scrollToFirstError={true} layout="vertical" initialValues={{ deviceType: 'gateway' }} form={form}>
            <Row gutter={[4, 4]}>
              <Col span={8}>
                <Card className="cardCustomHeader" title="Basic Details" size="small">
                  <Form.Item
                    hasFeedback
                    label="Model Name"
                    name="modelName"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Model Name!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Model Name" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Model Series"
                    name="modelSeries"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Model Series!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Model Series" />
                  </Form.Item>
                  <Form.Item hasFeedback name="deviceType" hidden>
                    <Input />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Vendor"
                    name="vendor"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Vendor!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Vendor" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="OTA Supported"
                    name="otaSupported"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input OTA Supported!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="OTA Supported" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Warranty Period"
                    name="warrantyPeriod"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Warranty Period!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Warranty Period" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Firmware Version"
                    name="firmwareVersion"
                    rules={[
                      {
                        required: true,
                        whitespace: true,
                        message: 'Please input Firmware Version!',
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input placeholder="Firmware Version" />
                  </Form.Item>
                  <Form.Item
                    hasFeedback
                    label="Event Attributes"
                    name="eventAttributes"
                    rules={[
                      {
                        required: true,
                        message: 'Please input Event Attribute(s) !',
                      },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                      placeholder="Event Attribute"
                      onChange={onAttributeChange}
                    >
                      {eventAttrs.map((b) => (
                        <Option title={b.name} key={b.id} value={b.id}>
                          {b.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Card>
              </Col>
              <Col span={16}>
                <Card title="Gateway Details" size="small" className="cardCustomHeader">
                  <Row gutter={[4, 4]}>
                    <Col span={12}>
                      <Form.Item hasFeedback name="deviceType" hidden>
                        <Input />
                      </Form.Item>
                      <Form.Item
                        hasFeedback
                        label="Mobilitiy"
                        name={['gatewayConfig', 'mobility']}
                        rules={[
                          {
                            required: true,
                            whitespace: true,
                            message: 'Please input Mobility!',
                            min: 1,
                            max: 200,
                          },
                        ]}
                      >
                        <Input placeholder="Mobility" />
                      </Form.Item>
                      <Form.Item
                        hasFeedback
                        label="Tag Type ID"
                        name={['gatewayConfig', 'tagTypeId']}
                        rules={[
                          {
                            required: true,
                            whitespace: true,
                            message: 'Please input Tag Type ID!',
                            min: 1,
                            max: 200,
                          },
                        ]}
                      >
                        <Input placeholder="Tag Type ID" />
                      </Form.Item>
                      <Form.Item
                        hasFeedback
                        label="Util Config Keys"
                        name={['gatewayConfig', 'utilConfigKeys']}
                        rules={[
                          {
                            required: true,
                            whitespace: true,
                            message: 'Please input Util Config Keys!',
                            min: 1,
                            max: 200,
                          },
                        ]}
                      >
                        <Input placeholder="Util Config Keys" />
                      </Form.Item>
                    </Col>

                    <Col span={12}>
                      <Row gutter={[4, 4]}>
                        <Col span={24}>
                          <Card title="Power Source" size="small" className="cardCustomHeader">
                            <Form.List
                              initialValue={[{ name: null, input: null, currRate: null }]}
                              name={['gatewayConfig', 'powerSource']}
                            >
                              {(fields, { add, remove }) => (
                                <>
                                  {fields.map(({ key, name, fieldKey, ...restField }) => (
                                    <Space className={s.customSpace} key={key} size={0} align="baseline">
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'name']}
                                        fieldKey={[fieldKey, 'name']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Power Name',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="Name" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'input']}
                                        fieldKey={[fieldKey, 'input']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Input Value',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="Input" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'currRate']}
                                        fieldKey={[fieldKey, 'currRate']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Curr Rate',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="Curr Rate" />
                                      </Form.Item>
                                      <MinusCircleOutlined style={{ marginLeft: '5px' }} onClick={() => remove(name)} />
                                    </Space>
                                  ))}
                                  <Form.Item>
                                    <Button
                                      type="dashed"
                                      onClick={() => add()}
                                      block
                                      className="addMoreButton"
                                      icon={<PlusOutlined />}
                                    >
                                      Add Power Source
                                    </Button>
                                  </Form.Item>
                                </>
                              )}
                            </Form.List>
                          </Card>
                        </Col>
                        <Col span={24}>
                          <Card title="BackhaulConfig" size="small" className="cardCustomHeader">
                            <Form.List
                              initialValue={[
                                { id: null, name: null, speedRange: null, version: null, paramsConfigKeys: null },
                              ]}
                              name={['gatewayConfig', 'backhaulConfig']}
                            >
                              {(fields, { add, remove }) => (
                                <>
                                  {fields.map(({ key, name, fieldKey, ...restField }) => (
                                    <Space className="customSpace" key={key} size={0} align="baseline">
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'id']}
                                        fieldKey={[fieldKey, 'id']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Backhaul ID',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="ID" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'name']}
                                        fieldKey={[fieldKey, 'name']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Backhaul Name',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="Name" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'speedRange']}
                                        fieldKey={[fieldKey, 'speedRange']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Speed Range',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="Speed Range" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'version']}
                                        fieldKey={[fieldKey, 'version']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Version',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="Version" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'paramsConfigKeys']}
                                        fieldKey={[fieldKey, 'paramsConfigKeys']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Param Config Keys',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="Param Config Keys" />
                                      </Form.Item>
                                      <MinusCircleOutlined style={{ marginLeft: '5px' }} onClick={() => remove(name)} />
                                    </Space>
                                  ))}
                                  <Form.Item>
                                    <Button
                                      type="dashed"
                                      onClick={() => add()}
                                      block
                                      className="addMoreButton"
                                      icon={<PlusOutlined />}
                                    >
                                      Add Backhaul Config
                                    </Button>
                                  </Form.Item>
                                </>
                              )}
                            </Form.List>
                          </Card>
                        </Col>
                        <Col span={24}>
                          <Card title="Util Params" size="small" className="cardCustomHeader">
                            <Form.List
                              initialValue={[{ params: null, name: null, id: null }]}
                              name={['gatewayConfig', 'utilsParams']}
                            >
                              {(fields, { add, remove }) => (
                                <>
                                  {fields.map(({ key, name, fieldKey, ...restField }) => (
                                    <Space key={key} align="baseline">
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'params']}
                                        fieldKey={[fieldKey, 'params']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Input Param',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="Param" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'name']}
                                        fieldKey={[fieldKey, 'name']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Input Name',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="Name" />
                                      </Form.Item>
                                      <Form.Item
                                        {...restField}
                                        name={[name, 'id']}
                                        fieldKey={[fieldKey, 'id']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: 'Please input Input ID',
                                          },
                                        ]}
                                      >
                                        <Input placeholder="ID" />
                                      </Form.Item>
                                      <MinusCircleOutlined
                                        style={{ marginLeft: '5px', marginRight: '15px' }}
                                        onClick={() => remove(name)}
                                      />
                                    </Space>
                                  ))}
                                  <Form.Item>
                                    <Button
                                      type="dashed"
                                      onClick={() => add()}
                                      block
                                      className="addMoreButton"
                                      icon={<PlusOutlined />}
                                    >
                                      Add Param
                                    </Button>
                                  </Form.Item>
                                </>
                              )}
                            </Form.List>
                          </Card>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </Card>
              </Col>

              <Col span={24}>
                <Card className="cardCustomHeader" title="Event Attributes" size="small">
                  {selectedAttrs.length < 1 ? (
                    <Paragraph type="danger">Select Attributes to configure this section</Paragraph>
                  ) : (
                    <Row gutter={[4, 4]}>
                      {selectedAttrs.map((i) => (
                        <Col key={i} span={12}>
                          <Card
                            className="cardCustomHeader"
                            title={eventAttrs.filter((x) => x.id === i)[0]?.name}
                            size="small"
                          >
                            <Form.Item
                              hasFeedback
                              isListField={true}
                              fieldKey={[`${i}`]}
                              label="Enable"
                              name={['gatewayConfig', 'eventAttrConfigs', `${i}`, 'enabled']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Enabled !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Enable" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              isListField={true}
                              fieldKey={[`${i}`]}
                              label="Read"
                              name={['gatewayConfig', 'eventAttrConfigs', `${i}`, 'read']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Read !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Read" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              label="Report"
                              isListField={true}
                              fieldKey={[`${i}`]}
                              name={['gatewayConfig', 'eventAttrConfigs', `${i}`, 'report']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Report !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Report" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              label="Upper Threshold"
                              isListField={true}
                              fieldKey={[`${i}`]}
                              name={['gatewayConfig', 'eventAttrConfigs', `${i}`, 'upperThreshold']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Upper Threshold !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Upper Threshold" />
                            </Form.Item>
                            <Form.Item
                              hasFeedback
                              label="Lower Threshold"
                              isListField={true}
                              fieldKey={[`${i}`]}
                              name={['gatewayConfig', 'eventAttrConfigs', `${i}`, 'lowerThreshold']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: 'Please input Event Attribute Lower Threshold !',
                                  min: 1,
                                  max: 200,
                                },
                              ]}
                            >
                              <Input placeholder="Lower Threshold" />
                            </Form.Item>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  )}
                </Card>
              </Col>

              <Col span={24}>
                <Card className="cardCustomHeader" title="Media Adaptor Type" size="small">
                  <Form.List
                    initialValue={[{ id: null, name: null, configKeys: null, chip: null }]}
                    name={['gatewayConfig', 'mediaAdaptorType']}
                  >
                    {(fields, { add, remove }) => (
                      <Row gutter={[4, 4]}>
                        {fields.map(({ key, name, fieldKey, ...restField }) => (
                          <Col key={key} span={12}>
                            <Card
                              size="small"
                              className={s.sameColCard}
                              title={
                                <Row justify="space-between">
                                  <Col>
                                    Basic Details <Tag className={s.mediaTypeTag}>{fieldKey + 1}</Tag>
                                  </Col>
                                  <Col>
                                    <MinusCircleOutlined style={{ marginLeft: '5px' }} onClick={() => remove(name)} />
                                  </Col>
                                </Row>
                              }
                            >
                              <Row gutter={[4, 4]}>
                                <Col span={12}>
                                  <Form.Item
                                    {...restField}
                                    hasFeedback
                                    label="ID"
                                    name={[name, 'id']}
                                    fieldKey={[fieldKey, 'id']}
                                    rules={[
                                      {
                                        required: true,
                                        whitespace: true,
                                        message: 'Please input Media Type ID',
                                      },
                                    ]}
                                  >
                                    <Input placeholder="ID" />
                                  </Form.Item>
                                  <Form.Item
                                    {...restField}
                                    hasFeedback
                                    label="Name"
                                    name={[name, 'name']}
                                    fieldKey={[fieldKey, 'name']}
                                    rules={[
                                      {
                                        required: true,
                                        whitespace: true,
                                        message: 'Please input Media Type Name',
                                      },
                                    ]}
                                  >
                                    <Input placeholder="Name" />
                                  </Form.Item>
                                  <Form.Item
                                    {...restField}
                                    hasFeedback
                                    label="SDK Version"
                                    name={[name, 'sdkVersion']}
                                    fieldKey={[fieldKey, 'sdkVersion']}
                                    rules={[
                                      {
                                        required: true,
                                        whitespace: true,
                                        message: 'Please input SDK Version',
                                      },
                                    ]}
                                  >
                                    <Input placeholder="SDK Version" />
                                  </Form.Item>
                                </Col>
                                <Col span={12}>
                                  <Form.Item
                                    {...restField}
                                    hasFeedback
                                    label="Config Keys"
                                    name={[name, 'configKeys']}
                                    fieldKey={[fieldKey, 'configKeys']}
                                    rules={[
                                      {
                                        required: true,
                                        whitespace: true,
                                        message: 'Please input Config Keys',
                                      },
                                    ]}
                                  >
                                    <Input placeholder="Config Keys" />
                                  </Form.Item>
                                  <Form.Item
                                    {...restField}
                                    hasFeedback
                                    label="Chip"
                                    name={[name, 'chip']}
                                    fieldKey={[fieldKey, 'chip']}
                                    rules={[
                                      {
                                        required: true,
                                        whitespace: true,
                                        message: 'Please input Chip',
                                      },
                                    ]}
                                  >
                                    <Input placeholder="Chip" />
                                  </Form.Item>
                                </Col>

                                <Col span={12}>
                                  <Card size="small" title="Chip Vendor Config" className="cardCustomHeader">
                                    <Form.Item
                                      hasFeedback
                                      label="Vendor ID"
                                      name={[name, 'chipVendorConfig', 'vendorId']}
                                      fieldKey={[fieldKey, 'chipVendorConfig', 'vendorId']}
                                      rules={[
                                        {
                                          required: true,
                                          whitespace: true,
                                          message: 'Please input Chip Vendor ID',
                                        },
                                      ]}
                                    >
                                      <Input placeholder="Chip Vendor ID" />
                                    </Form.Item>
                                    <Form.Item
                                      hasFeedback
                                      label="Name"
                                      name={[name, 'chipVendorConfig', 'name']}
                                      fieldKey={[fieldKey, 'chipVendorConfig', 'name']}
                                      rules={[
                                        {
                                          required: true,
                                          whitespace: true,
                                          message: 'Please input Chip Vendor Name',
                                        },
                                      ]}
                                    >
                                      <Input placeholder="Name" />
                                    </Form.Item>
                                    <Form.Item
                                      hasFeedback
                                      labelCol={{ span: 12 }}
                                      label="Interface Protocol"
                                      name={[name, 'chipVendorConfig', 'interfaceProtocol']}
                                      fieldKey={[fieldKey, 'chipVendorConfig', 'interfaceProtocol']}
                                      rules={[
                                        {
                                          required: true,
                                          whitespace: true,
                                          message: 'Please input Chip Interface Protocol',
                                        },
                                      ]}
                                    >
                                      <Input placeholder="Interface Protocol" />
                                    </Form.Item>
                                  </Card>
                                </Col>
                                <Col span={12}>
                                  <Card size="small" title="Tag Comm Medium" className="cardCustomHeader">
                                    <Form.Item
                                      hasFeedback
                                      label="Vendor ID"
                                      name={[name, 'tagCommMedium', 'tagCommMedId']}
                                      fieldKey={[fieldKey, 'tagCommMedium', 'tagCommMedId']}
                                      rules={[
                                        {
                                          required: true,
                                          whitespace: true,
                                          message: 'Please input ID',
                                        },
                                      ]}
                                    >
                                      <Input placeholder="ID" />
                                    </Form.Item>
                                    <Form.Item
                                      hasFeedback
                                      label="Short Name"
                                      name={[name, 'tagCommMedium', 'shortName']}
                                      fieldKey={[fieldKey, 'tagCommMedium', 'shortName']}
                                      rules={[
                                        {
                                          required: true,
                                          whitespace: true,
                                          message: 'Please input Short Name',
                                        },
                                      ]}
                                    >
                                      <Input placeholder="Short Name" />
                                    </Form.Item>
                                    <Form.Item
                                      hasFeedback
                                      label="Full Name"
                                      name={[name, 'tagCommMedium', 'fullName']}
                                      fieldKey={[fieldKey, 'tagCommMedium', 'fullName']}
                                      rules={[
                                        {
                                          required: true,
                                          whitespace: true,
                                          message: 'Please input Full Name',
                                        },
                                      ]}
                                    >
                                      <Input placeholder="Full Name" />
                                    </Form.Item>
                                    <Form.Item
                                      hasFeedback
                                      labelCol={{ span: 12 }}
                                      label="Additional Config"
                                      name={[name, 'tagCommMedium', 'additionalConfig']}
                                      fieldKey={[fieldKey, 'tagCommMedium', 'additionalConfig']}
                                      rules={[
                                        {
                                          required: true,
                                          whitespace: true,
                                          message: 'Please input Additional Config',
                                        },
                                      ]}
                                    >
                                      <Input placeholder="Additional Config" />
                                    </Form.Item>
                                  </Card>
                                </Col>
                                <Col span={24}>
                                  <Form.List
                                    initialValue={[
                                      {
                                        params: null,
                                        mediaAdaptorCommType: null,
                                        mediaAdaptorRole: null,
                                      },
                                    ]}
                                    name={[name, 'mediaAdaptors']}
                                  >
                                    {(fields, { add, remove }) => (
                                      <Row gutter={[4, 4]}>
                                        {fields.map(({ key, name, fieldKey, ...restField }) => (
                                          <Col key={key} span={12}>
                                            <Card
                                              size="small"
                                              className={s.sameColCard}
                                              title={
                                                <Row justify="space-between">
                                                  <Col>
                                                    Media Adaptors <Tag className={s.mediaTypeTag}>{fieldKey + 1}</Tag>
                                                  </Col>
                                                  <Col>
                                                    <MinusCircleOutlined
                                                      style={{ marginLeft: '5px' }}
                                                      onClick={() => remove(name)}
                                                    />
                                                  </Col>
                                                </Row>
                                              }
                                            >
                                              <Row gutter={[4, 4]}>
                                                <Col span={24}>
                                                  <Form.Item
                                                    {...restField}
                                                    hasFeedback
                                                    label="ID"
                                                    name={[name, 'id']}
                                                    fieldKey={[fieldKey, 'id']}
                                                    rules={[
                                                      {
                                                        required: true,
                                                        whitespace: true,
                                                        message: 'Please input ID',
                                                      },
                                                    ]}
                                                  >
                                                    <Input placeholder="Params" />
                                                  </Form.Item>
                                                  <Form.Item
                                                    {...restField}
                                                    hasFeedback
                                                    label="Name"
                                                    name={[name, 'name']}
                                                    fieldKey={[fieldKey, 'name']}
                                                    rules={[
                                                      {
                                                        required: true,
                                                        whitespace: true,
                                                        message: 'Please input Name',
                                                      },
                                                    ]}
                                                  >
                                                    <Input placeholder="Name" />
                                                  </Form.Item>
                                                  <Form.Item
                                                    {...restField}
                                                    hasFeedback
                                                    label="Params"
                                                    name={[name, 'params']}
                                                    fieldKey={[fieldKey, 'params']}
                                                    rules={[
                                                      {
                                                        required: true,
                                                        whitespace: true,
                                                        message: 'Please input Params',
                                                      },
                                                    ]}
                                                  >
                                                    <Input placeholder="Params" />
                                                  </Form.Item>
                                                </Col>

                                                <Col span={24}>
                                                  <Card title="Communication Type" size="small">
                                                    <Form.Item
                                                      {...restField}
                                                      hasFeedback
                                                      label="ID"
                                                      name={[name, 'mediaAdaptorCommType', 'id']}
                                                      fieldKey={[fieldKey, 'mediaAdaptorCommType', 'id']}
                                                      rules={[
                                                        {
                                                          required: true,
                                                          whitespace: true,
                                                          message: 'Please input ID',
                                                        },
                                                      ]}
                                                    >
                                                      <Input placeholder="ID" />
                                                    </Form.Item>
                                                    <Form.Item
                                                      {...restField}
                                                      hasFeedback
                                                      label="Name"
                                                      name={[name, 'mediaAdaptorCommType', 'name']}
                                                      fieldKey={[fieldKey, 'mediaAdaptorCommType', 'name']}
                                                      rules={[
                                                        {
                                                          required: true,
                                                          whitespace: true,
                                                          message: 'Please input Name',
                                                        },
                                                      ]}
                                                    >
                                                      <Input placeholder="Name" />
                                                    </Form.Item>
                                                    <Form.Item
                                                      {...restField}
                                                      hasFeedback
                                                      label="Format String"
                                                      name={[name, 'mediaAdaptorCommType', 'formatString']}
                                                      fieldKey={[fieldKey, 'mediaAdaptorCommType', 'formatString']}
                                                      rules={[
                                                        {
                                                          required: true,
                                                          whitespace: true,
                                                          message: 'Please input Format String',
                                                        },
                                                      ]}
                                                    >
                                                      <Input placeholder="Format String" />
                                                    </Form.Item>
                                                    <Form.Item
                                                      {...restField}
                                                      hasFeedback
                                                      label="Params"
                                                      name={[name, 'mediaAdaptorCommType', 'params']}
                                                      fieldKey={[fieldKey, 'mediaAdaptorCommType', 'params']}
                                                      rules={[
                                                        {
                                                          required: true,
                                                          whitespace: true,
                                                          message: 'Please input Params',
                                                        },
                                                      ]}
                                                    >
                                                      <Input placeholder="Params" />
                                                    </Form.Item>
                                                  </Card>
                                                </Col>

                                                <Col span={24}>
                                                  <Card title="Role" size="small">
                                                    <Form.Item
                                                      {...restField}
                                                      hasFeedback
                                                      label="ID"
                                                      name={[name, 'mediaAdaptorRole', 'id']}
                                                      fieldKey={[fieldKey, 'mediaAdaptorRole', 'id']}
                                                      rules={[
                                                        {
                                                          required: true,
                                                          whitespace: true,
                                                          message: 'Please input ID',
                                                        },
                                                      ]}
                                                    >
                                                      <Input placeholder="ID" />
                                                    </Form.Item>
                                                    <Form.Item
                                                      {...restField}
                                                      hasFeedback
                                                      label="Name"
                                                      name={[name, 'mediaAdaptorRole', 'name']}
                                                      fieldKey={[fieldKey, 'mediaAdaptorRole', 'name']}
                                                      rules={[
                                                        {
                                                          required: true,
                                                          whitespace: true,
                                                          message: 'Please input Name',
                                                        },
                                                      ]}
                                                    >
                                                      <Input placeholder="Name" />
                                                    </Form.Item>

                                                    <Form.List
                                                      initialValue={[
                                                        {
                                                          id: null,
                                                          name: null,
                                                          dataType: null,
                                                          value: null,
                                                        },
                                                      ]}
                                                      name={[name, 'mediaAdaptorRole', 'mediaRoleParams']}
                                                    >
                                                      {(fields, { add, remove }) => (
                                                        <>
                                                          {fields.map(({ key, name, fieldKey, ...restField }) => (
                                                            <Card
                                                              size="small"
                                                              key={key}
                                                              title={
                                                                <Row justify="space-between">
                                                                  <Col>
                                                                    Params
                                                                    <Tag
                                                                      className={s.mediaTypeTag}
                                                                      style={{ marginLeft: 5 }}
                                                                    >
                                                                      {fieldKey + 1}
                                                                    </Tag>
                                                                  </Col>
                                                                  <Col>
                                                                    <MinusCircleOutlined onClick={() => remove(name)} />
                                                                  </Col>
                                                                </Row>
                                                              }
                                                            >
                                                              <Form.Item
                                                                {...restField}
                                                                hasFeedback
                                                                label="ID"
                                                                name={[name, 'id']}
                                                                fieldKey={[fieldKey, 'id']}
                                                                rules={[
                                                                  {
                                                                    required: true,
                                                                    whitespace: true,
                                                                    message: 'Please input ID',
                                                                  },
                                                                ]}
                                                              >
                                                                <Input placeholder="ID" />
                                                              </Form.Item>
                                                              <Form.Item
                                                                {...restField}
                                                                hasFeedback
                                                                label="Name"
                                                                name={[name, 'name']}
                                                                fieldKey={[fieldKey, 'name']}
                                                                rules={[
                                                                  {
                                                                    required: true,
                                                                    whitespace: true,
                                                                    message: 'Please input Name',
                                                                  },
                                                                ]}
                                                              >
                                                                <Input placeholder="Name" />
                                                              </Form.Item>
                                                              <Form.Item
                                                                {...restField}
                                                                hasFeedback
                                                                label="Data Type"
                                                                name={[name, 'dataType']}
                                                                fieldKey={[fieldKey, 'dataType']}
                                                                rules={[
                                                                  {
                                                                    required: true,
                                                                    whitespace: true,
                                                                    message: 'Please input Data Type',
                                                                  },
                                                                ]}
                                                              >
                                                                <Input placeholder="Data Type" />
                                                              </Form.Item>
                                                              <Form.Item
                                                                {...restField}
                                                                hasFeedback
                                                                label="Value"
                                                                name={[name, 'value']}
                                                                fieldKey={[fieldKey, 'value']}
                                                                rules={[
                                                                  {
                                                                    required: true,
                                                                    whitespace: true,
                                                                    message: 'Please input Value',
                                                                  },
                                                                ]}
                                                              >
                                                                <Input placeholder="Value" />
                                                              </Form.Item>
                                                            </Card>
                                                          ))}
                                                          <Form.Item>
                                                            <Button
                                                              type="dashed"
                                                              onClick={() => add()}
                                                              block
                                                              icon={<PlusOutlined />}
                                                            >
                                                              Add Params
                                                            </Button>
                                                          </Form.Item>
                                                        </>
                                                      )}
                                                    </Form.List>
                                                  </Card>
                                                </Col>
                                              </Row>
                                            </Card>
                                          </Col>
                                        ))}
                                        <Form.Item>
                                          <Button
                                            type="dashed"
                                            onClick={() => add()}
                                            className="addMoreButton"
                                            icon={<PlusOutlined />}
                                          >
                                            Add Media Adaptor
                                          </Button>
                                        </Form.Item>
                                      </Row>
                                    )}
                                  </Form.List>
                                </Col>
                              </Row>
                            </Card>
                          </Col>
                        ))}
                        <Form.Item>
                          <Button type="dashed" onClick={() => add()} className="addMoreButton" icon={<PlusOutlined />}>
                            Add Media Adaptor Type
                          </Button>
                        </Form.Item>
                      </Row>
                    )}
                  </Form.List>
                </Card>
              </Col>
            </Row>
            <div className="footer">
              <Row gutter={[4, 4]}>
                <Col span={2}>
                  <Button block className="commonSaveButton formButton" type="primary" onClick={() => finishAdd()}>
                    Save
                  </Button>
                </Col>
                <Col span={2}>
                  <Button block className="clearButton" onClick={() => clearForm()}>
                    Clear
                  </Button>
                </Col>
                <Col span={2}>
                  <Button block danger type="primary" onClick={closeAdd}>
                    Close
                  </Button>
                </Col>
              </Row>
            </div>
          </Form>
        </div>
      </Drawer>
    </div>
  );
};

export default GatewayConfig;
