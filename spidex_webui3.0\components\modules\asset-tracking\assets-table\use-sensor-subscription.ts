import { useEffect, useRef, useState } from "react";
import { getAssetSocketManager } from "@/lib/websocket/asset-socket";
import {
  ChartDataPoint,
  SensorType,
  WebSocketMessage,
} from "@/types/asset-tracking";

interface UseSensorSubscriptionOptions {
  sensorType: SensorType;
  deviceLogId: string;
  socketUrl?: string;
  maxPoints?: number;
}

export default function useSensorSubscription({
  sensorType,
  deviceLogId,
  socketUrl,
  maxPoints = 50,
}: UseSensorSubscriptionOptions) {
  const socketManagerRef = useRef<any>(null);
  const subscriptionIdRef = useRef<string | null>(null);
  const [data, setData] = useState<ChartDataPoint[]>([]);

  useEffect(() => {
    if (!sensorType || !deviceLogId) return;
    if (!socketManagerRef.current) {
      socketManagerRef.current = getAssetSocketManager(socketUrl);
    }
    const socketManager = socketManagerRef.current;
    let unsubscribed = false;

    function handleSensorEvent(event: WebSocketMessage) {
      if (unsubscribed) return;
      const { attributeType, attributeValue, eventTime } = event;
      let value: number;
      if (attributeType === "float") {
        value = parseFloat(attributeValue as string);
      } else {
        value = Number(attributeValue);
      }
      if (isNaN(value)) return;
      setData((prev) => {
        const next = [
          ...prev.slice(-maxPoints + 1),
          {
            value,
            time: new Date(eventTime * 1000).toISOString(),
            timestamp: eventTime * 1000,
          },
        ];
        return next;
      });
    }

    let subId: string | null = null;
    if (socketManager.isConnected()) {
      subId = socketManager.subscribeToSensorData(
        sensorType,
        deviceLogId,
        handleSensorEvent
      );
      subscriptionIdRef.current = subId;
    } else {
      socketManager.connect().then(() => {
        if (!unsubscribed) {
          subId = socketManager.subscribeToSensorData(
            sensorType,
            deviceLogId,
            handleSensorEvent
          );
          subscriptionIdRef.current = subId;
        }
      });
    }

    return () => {
      unsubscribed = true;
      if (subscriptionIdRef.current && socketManager) {
        try {
          socketManager.unsubscribe(subscriptionIdRef.current);
        } catch {}
        subscriptionIdRef.current = null;
      }
    };
  }, [sensorType, deviceLogId, socketUrl, maxPoints]);

  return data;
}
