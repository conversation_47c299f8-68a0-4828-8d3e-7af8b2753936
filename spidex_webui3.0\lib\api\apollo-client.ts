import { HttpLink, ApolloLink } from "@apollo/client";
import {
  registerApollo<PERSON>lient,
  ApolloClient,
  InMemoryCache,
} from "@apollo/client-integration-nextjs";
import { RetryLink } from "@apollo/client/link/retry";
import { cache } from "react";

const retryLink = new RetryLink({
  attempts: {
    max: 3,
    retryIf: (error, _operation) => !!error,
  },
  delay: {
    initial: 300,
    max: 1000,
    jitter: true,
  },
});

export const { getClient, query, PreloadQuery } = registerApolloClient(() => {
  return new ApolloClient({
    cache: new InMemoryCache(),
    link: ApolloLink.from([
      retryLink,
      new HttpLink({
        // this needs to be an absolute url, as relative urls cannot be used in SSR
        // Set NEXT_PUBLIC_GRAPHQL_API_URL in your .env.local file
        uri: process.env.NEXT_PUBLIC_GRAPHQL_API_URL || "http://localhost:4000",
        fetchOptions: {
          // you can pass additional options that should be passed to `fetch` here,
          // e.g. Next.js-related `fetch` options regarding caching and revalidation
          // see https://nextjs.org/docs/app/api-reference/functions/fetch#fetchurl-options
          cache: "force-cache",
          next: { revalidate: 30 },
        },
      }),
    ]),
  });
});
