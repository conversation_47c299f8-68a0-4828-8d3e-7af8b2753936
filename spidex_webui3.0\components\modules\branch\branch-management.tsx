"use client";

import { useState } from "react";
import { Plus, MapPin } from "lucide-react";
import { useSession } from "next-auth/react";
import { useBranchManagement } from "@/hooks/use-branch-management";
import { getTableConfig } from "@/config/table-config";
import {
  Branch,
  CreateBranchFormData,
  UpdateBranchFormData,
  BranchPageSize,
} from "@/types/branch";
import { BranchDataTable } from "./branch-data-table";
import { BranchSearchPagination } from "./branch-search-pagination";
import { BranchForm } from "./branch-form";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";
import { toast } from "sonner";

export default function BranchManagement() {
  const { data: session, status } = useSession();

  // Get table configuration for this page
  const tableConfig = getTableConfig("branch-management");

  const {
    branches: paginatedBranches,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    createBranch,
    updateBranch,
    deleteBranch,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useBranchManagement({
    initialPageSize: tableConfig.defaultPageSize,
    availablePageSizes: tableConfig.availablePageSizes as BranchPageSize[],
  });

  // Form state
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingBranch, setEditingBranch] = useState<Branch | null>(null);
  const [isFormLoading, setIsFormLoading] = useState(false);
  const [tableKey, setTableKey] = useState(0);

  // Form handlers
  const openCreateForm = () => {
    setEditingBranch(null);
    setIsFormOpen(true);
  };

  const openEditForm = (branch: Branch) => {
    console.log("✏️ Opening edit form for branch:", branch);
    setEditingBranch(branch);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    // Small delay to ensure proper state cleanup
    setTimeout(() => {
      setEditingBranch(null);
      setIsFormLoading(false);
      setTableKey((prev) => prev + 1); // Force table re-render
    }, 100);
  };

  const handleFormSubmit = async (
    data: CreateBranchFormData | UpdateBranchFormData
  ) => {
    try {
      setIsFormLoading(true);
      console.log("🚀 Form submission started:", {
        editingBranch: !!editingBranch,
        data,
      });

      if (editingBranch) {
        // Update existing branch
        console.log("🔄 Updating branch:", editingBranch.id);
        await updateBranch(data as UpdateBranchFormData);
        toast.success("Branch updated successfully");
      } else {
        // Create new branch
        console.log("➕ Creating new branch");
        await createBranch(data as CreateBranchFormData);
        toast.success("Branch created successfully");
      }

      closeForm();
    } catch (error) {
      console.error("❌ Form submission error:", error);
      toast.error(
        editingBranch
          ? "Failed to update branch. Please try again."
          : "Failed to create branch. Please try again."
      );
    } finally {
      setIsFormLoading(false);
    }
  };

  const handleDeleteBranch = async (branchId: string) => {
    try {
      await deleteBranch(branchId);
      toast.success("Branch deleted successfully");
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Failed to delete branch. Please try again.");
    }
  };

  const handleToggleActive = async (branch: Branch) => {
    try {
      const updateData = {
        ...branch,
        deleted: false,
        modifiedBy: session?.user?.userId || "system",
        modifiedTime: Date.now(),
      } as UpdateBranchFormData;

      await updateBranch(updateData);
      toast.success("Branch activated successfully");
    } catch (error) {
      console.error("Activate error:", error);
      toast.error("Failed to activate branch. Please try again.");
    }
  };

  if (status === "loading") {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p className="text-muted-foreground">
            Please log in to access branch management.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/digital-carpet">
                Digital Carpet
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Branch Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={openCreateForm} className="gap-2 ml-auto">
          <Plus className="h-4 w-4" />
          Add Branch
        </Button>
      </header>
      <main>
        {/* Search and Filters */}
        <div className="p-4">
          <BranchSearchPagination
            searchFilters={searchFilters}
            showDeleted={showDeleted}
            totalRecords={totalRecords}
            onSearchChange={updateSearchFilters}
            onClearSearch={clearSearch}
            onToggleShowDeleted={toggleShowDeleted}
          />
        </div>

        {/* Data Table */}
        <div className="p-4 pt-0">
          <BranchDataTable
            key={tableKey}
            data={paginatedBranches}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onEdit={openEditForm}
            onDelete={handleDeleteBranch}
            onToggleActive={handleToggleActive}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
          />
        </div>
      </main>

      {/* Form Sheet */}
      <Sheet
        open={isFormOpen}
        onOpenChange={(open) => {
          if (!open) {
            closeForm();
          } else {
            setIsFormOpen(true);
          }
        }}
      >
        <SheetContent className="w-1/4 sm:max-w-4xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              {editingBranch ? "Edit Branch" : "Create New Branch"}
            </SheetTitle>
            <SheetDescription>
              {editingBranch
                ? "Update the branch information below."
                : "Fill in the details to create a new branch."}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <BranchForm
              key={editingBranch?.id || "new"}
              branch={editingBranch || undefined}
              isLoading={isFormLoading}
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
