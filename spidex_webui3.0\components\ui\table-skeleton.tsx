"use client";

import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface TableSkeletonProps {
  /** Number of rows to show in skeleton */
  rows?: number;
  /** Column configurations for the skeleton */
  columns: {
    /** Header text for the column */
    header: string;
    /** Width class for the column */
    width?: string;
    /** Whether column is hidden on certain screen sizes */
    className?: string;
  }[];
  /** Whether to show pagination skeleton */
  showPagination?: boolean;
}

export function TableSkeleton({ 
  rows = 10, 
  columns, 
  showPagination = true 
}: TableSkeletonProps) {
  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column, index) => (
                <TableHead 
                  key={index} 
                  className={`${column.width || ''} ${column.className || ''}`}
                >
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: rows }).map((_, rowIndex) => (
              <TableRow key={rowIndex}>
                {columns.map((column, colIndex) => (
                  <TableCell 
                    key={colIndex}
                    className={column.className || ''}
                  >
                    {colIndex === 0 ? (
                      // First column often has more complex content
                      <div className="flex flex-col space-y-1">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24 md:hidden" />
                      </div>
                    ) : colIndex === columns.length - 1 ? (
                      // Last column is usually actions
                      <div className="flex justify-end">
                        <Skeleton className="h-8 w-8" />
                      </div>
                    ) : (
                      // Regular content columns
                      <Skeleton className="h-4 w-full max-w-[200px]" />
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Skeleton */}
      {showPagination && (
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-48" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>
      )}
    </div>
  );
}

// Predefined column configurations for common Digital Carpet tables

export const BRANCH_TABLE_COLUMNS = [
  { header: "Name", width: "w-[200px]" },
  { header: "Address", className: "hidden md:table-cell" },
  { header: "GPS Coordinates", className: "hidden lg:table-cell" },
  { header: "Status", className: "hidden xl:table-cell" },
  { header: "Created", className: "hidden xl:table-cell" },
  { header: "Modified", className: "hidden xl:table-cell" },
  { header: "Actions", className: "text-right" },
];

export const LOCATION_TABLE_COLUMNS = [
  { header: "Name", width: "w-[200px]" },
  { header: "Branch", className: "hidden md:table-cell" },
  { header: "Address", className: "hidden lg:table-cell" },
  { header: "GPS Coordinates", className: "hidden lg:table-cell" },
  { header: "Status", className: "hidden xl:table-cell" },
  { header: "Created", className: "hidden xl:table-cell" },
  { header: "Actions", className: "text-right" },
];

export const AREA_TABLE_COLUMNS = [
  { header: "Name", width: "w-[200px]" },
  { header: "Location", className: "hidden md:table-cell" },
  { header: "Level", className: "hidden lg:table-cell" },
  { header: "Min/Max", className: "hidden lg:table-cell" },
  { header: "Coordinates", className: "hidden xl:table-cell" },
  { header: "Status", className: "hidden xl:table-cell" },
  { header: "Gateways", className: "hidden xl:table-cell" },
  { header: "Actions", className: "text-right" },
];

export const GATEWAY_TABLE_COLUMNS = [
  { header: "Name", width: "w-[200px]" },
  { header: "Type", className: "hidden md:table-cell" },
  { header: "Model", className: "hidden md:table-cell" },
  { header: "Area", className: "hidden lg:table-cell" },
  { header: "Location", className: "hidden lg:table-cell" },
  { header: "Status", className: "hidden xl:table-cell" },
  { header: "Actions", className: "text-right" },
];

export const TAG_TABLE_COLUMNS = [
  { header: "Tag ID", width: "w-[150px]" },
  { header: "Model", className: "hidden md:table-cell" },
  { header: "Status", className: "hidden md:table-cell" },
  { header: "Battery", className: "hidden lg:table-cell" },
  { header: "Last Seen", className: "hidden lg:table-cell" },
  { header: "Created", className: "hidden xl:table-cell" },
  { header: "Actions", className: "text-right" },
];

export const ASSET_TABLE_COLUMNS = [
  { header: "Name", width: "w-[200px]" },
  { header: "Type", className: "hidden md:table-cell" },
  { header: "Description", className: "hidden lg:table-cell" },
  { header: "Status", className: "hidden lg:table-cell" },
  { header: "Created", className: "hidden xl:table-cell" },
  { header: "Modified", className: "hidden xl:table-cell" },
  { header: "Actions", className: "text-right" },
];

export const TAGGED_ASSET_TABLE_COLUMNS = [
  { header: "Asset", width: "w-[200px]" },
  { header: "Tag", className: "hidden md:table-cell" },
  { header: "Status", className: "hidden md:table-cell" },
  { header: "Location", className: "hidden lg:table-cell" },
  { header: "Last Update", className: "hidden lg:table-cell" },
  { header: "Created", className: "hidden xl:table-cell" },
  { header: "Actions", className: "text-right" },
];
