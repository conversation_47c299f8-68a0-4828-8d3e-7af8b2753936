"use client";

import { useState, useEffect } from "react";
import { Search, Filter, X } from "lucide-react";
import { TagSearchFilters, TagModel } from "@/types/tag";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TagSearchPaginationProps {
  searchFilters: TagSearchFilters;
  showDeleted: boolean;
  tagModels: TagModel[];
  totalRecords: number;
  totalAllRecords: number;
  activeRecordsCount: number;
  inactiveRecordsCount: number;
  onSearchChange: (filters: Partial<TagSearchFilters>) => void;
  onClearSearch: () => void;
  onToggleShowDeleted: () => void;
}

export function TagSearchPagination({
  searchFilters,
  showDeleted,
  tagModels,
  totalRecords,
  totalAllRecords,
  activeRecordsCount,
  inactiveRecordsCount,
  onSearchChange,
  onClearSearch,
  onToggleShowDeleted,
}: TagSearchPaginationProps) {
  const [localSearchTerm, setLocalSearchTerm] = useState(
    searchFilters.searchTerm || ""
  );

  // Update local search term when filters change
  useEffect(() => {
    setLocalSearchTerm(searchFilters.searchTerm || "");
  }, [searchFilters.searchTerm]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange({ searchTerm: localSearchTerm });
  };

  const handleSearchChange = (value: string) => {
    setLocalSearchTerm(value);
    // Debounced search - trigger search after user stops typing
    const timeoutId = setTimeout(() => {
      onSearchChange({ searchTerm: value });
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  const handleClearSearch = () => {
    setLocalSearchTerm("");
    onClearSearch();
  };

  const hasActiveFilters = searchFilters.searchTerm || showDeleted;

  return (
    <div className="space-y-4">
      {/* Search Form */}
      <form onSubmit={handleSearchSubmit} className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1">
            <Label htmlFor="search" className="sr-only">
              Search tags
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                type="text"
                placeholder="Search by tag name, description, model, or external ID..."
                value={localSearchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10 pr-10"
              />
              {localSearchTerm && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleClearSearch}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Search Button */}
          <Button type="submit" className="gap-2">
            <Search className="h-4 w-4" />
            Search
          </Button>
        </div>
      </form>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 pt-2 border-t">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="showDeleted"
            checked={showDeleted}
            onCheckedChange={onToggleShowDeleted}
          />
          <Label
            htmlFor="showDeleted"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Show Inactive Tags
          </Label>
        </div>

        {/* Current Results Info */}
        <div className="text-sm text-muted-foreground">
          {hasActiveFilters && (
            <>
              Showing {totalRecords} of {totalAllRecords} tags
              {searchFilters.searchTerm && (
                <span className="ml-1">
                  matching &quot;{searchFilters.searchTerm}&quot;
                </span>
              )}
              {showDeleted && <span className="ml-1">(inactive only)</span>}
            </>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 pt-2 border-t">
          <span className="text-sm font-medium">Active filters:</span>

          {searchFilters.searchTerm && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: &quot;{searchFilters.searchTerm}&quot;
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSearchChange({ searchTerm: "" })}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {showDeleted && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Show Inactive
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleShowDeleted}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
