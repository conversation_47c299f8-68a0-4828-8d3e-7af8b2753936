import mapboxgl, { LngLatBoundsLike } from "mapbox-gl";
import { useEffect, useState } from "react";

export function useMapBounds(items: any, key: string = "gpsPoint") {
  const [bounds, setBounds] = useState<LngLatBoundsLike | null>(null);

  useEffect(() => {
    const coordinates = items.reduce((acc: any, cur: any) => {
      acc.push([parseFloat(cur[key].longitude), parseFloat(cur[key].latitude)]);
      return acc;
    }, []);

    const bounds = coordinates.reduce(function (bounds: any, coord: any) {
      return bounds.extend(coord);
    }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));

    setBounds(bounds);
  }, [items, key]);

  return bounds;
}
