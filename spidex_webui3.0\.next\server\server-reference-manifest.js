self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"60dd0736e91437fcd9de28aa5a08947f61ee08eae1\": {\n      \"workers\": {\n        \"app/(login-layout)/login/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Clib%5C%5Cserver-actions%5C%5Clogin.ts%22%2C%5B%7B%22id%22%3A%2260dd0736e91437fcd9de28aa5a08947f61ee08eae1%22%2C%22exportedName%22%3A%22login%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(login-layout)/login/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"