import "../../globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "mapbox-gl/dist/mapbox-gl.css";
import { ThemeProvider } from "@/components/theme-provider";
import { QueryProvider } from "@/components/providers/query-provider";
import { ClientSessionProvider } from "@/components/providers/session-provider";
import { AuthProvider } from "@/lib/contexts/auth-context";
import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { Toaster } from "sonner";
import NextTopLoader from "nextjs-toploader";
import { What3WordsScript } from "@/components/what3words-script";
import { ApolloProvider } from "@/components/providers/apollo-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Spidex",
  description: "Spidex",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head></head>
      <body className={inter.className} suppressHydrationWarning>
        <What3WordsScript />
        <NextTopLoader />
        <ClientSessionProvider>
          <AuthProvider>
            <ApolloProvider>
              <QueryProvider>
                <ThemeProvider
                  attribute="class"
                  defaultTheme="light"
                  enableSystem
                  disableTransitionOnChange
                >
                  <SidebarProvider>
                    <AppSidebar />
                    <SidebarInset>{children}</SidebarInset>
                  </SidebarProvider>
                  <Toaster
                    position="top-right"
                    toastOptions={{
                      style: {
                        background: "#22c55e",
                        color: "#000000",
                        border: "1px solid #fff",
                      },
                      className: "custom-toast",
                    }}
                    richColors={false}
                  />
                </ThemeProvider>
              </QueryProvider>
            </ApolloProvider>
          </AuthProvider>
        </ClientSessionProvider>
      </body>
    </html>
  );
}
