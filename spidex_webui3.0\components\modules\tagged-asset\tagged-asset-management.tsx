"use client";

import { useState } from "react";
import { Plus, Package } from "lucide-react";
import { useSession } from "next-auth/react";
import { useTaggedAssetManagement } from "@/hooks/use-tagged-asset-management";
import { getTableConfig } from "@/config/table-config";
import {
  TaggedAsset,
  CreateTaggedAssetFormData,
  UpdateTaggedAssetFormData,
} from "@/types/tagged-asset";
import { TaggedAssetDataTable } from "./tagged-asset-data-table";
import { TaggedAssetSearchPagination } from "./tagged-asset-search-pagination";
import { TaggedAssetForm } from "./tagged-asset-form";
import { Button } from "@/components/ui/button";

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";
import { toast } from "sonner";

export default function TaggedAssetManagement() {
  const { data: session, status } = useSession();

  // Get table configuration for this page
  const tableConfig = getTableConfig("tagged-asset-management");

  const {
    taggedAssets: paginatedTaggedAssets,
    assetOptions,
    tagOptions,
    isLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    loadData,
    loadAssetOptions,
    loadTagOptions,
    createTaggedAsset,
    updateTaggedAsset,
    deleteTaggedAsset,
    toggleTaggedAssetStatus,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useTaggedAssetManagement({
    initialPageSize: tableConfig.defaultPageSize,
    availablePageSizes: tableConfig.availablePageSizes,
  });

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingTaggedAsset, setEditingTaggedAsset] =
    useState<TaggedAsset | null>(null);
  const [isFormLoading, setIsFormLoading] = useState(false);

  // Handle opening form for new tagged asset
  const handleCreateNew = () => {
    setEditingTaggedAsset(null);
    setIsFormOpen(true);
  };

  // Handle opening form for editing
  const handleEdit = (taggedAsset: TaggedAsset) => {
    console.log("✏️ Opening edit form for tagged asset:", taggedAsset);
    setEditingTaggedAsset(taggedAsset);
    setIsFormOpen(true);
  };

  // Handle closing form
  const closeForm = () => {
    setIsFormOpen(false);
    setEditingTaggedAsset(null);
    setIsFormLoading(false);
  };

  const handleFormSubmit = async (
    data: CreateTaggedAssetFormData | UpdateTaggedAssetFormData
  ) => {
    try {
      setIsFormLoading(true);
      console.log("🚀 Tagged asset form submission started:", {
        editingTaggedAsset: !!editingTaggedAsset,
        data,
      });

      if (editingTaggedAsset) {
        // Update existing tagged asset
        console.log("🔄 Updating tagged asset:", editingTaggedAsset.id);
        await updateTaggedAsset(data as UpdateTaggedAssetFormData);
        toast.success("Tagged asset updated successfully", {
          description: "The tagged asset has been updated successfully.",
        });
      } else {
        // Create new tagged asset
        console.log("➕ Creating new tagged asset");
        await createTaggedAsset(data as CreateTaggedAssetFormData);
        toast.success("Tagged asset created successfully", {
          description: "The tagged asset has been added to the system.",
        });
      }

      closeForm();
    } catch (error) {
      console.error("❌ Tagged asset form submission error:", error);
      toast.error(
        editingTaggedAsset
          ? "Failed to update tagged asset"
          : "Failed to create tagged asset",
        {
          description: "Please check your input and try again.",
        }
      );
    } finally {
      setIsFormLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteTaggedAsset(id);
      toast.success("Tagged asset deleted successfully", {
        description: "The tagged asset has been removed from the system.",
      });
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Failed to delete tagged asset", {
        description: "Please try again later.",
      });
    }
  };

  const handleToggleStatus = async (taggedAsset: TaggedAsset) => {
    try {
      await toggleTaggedAssetStatus(taggedAsset);
      toast.success(
        `Tagged asset ${
          taggedAsset.deleted ? "activated" : "deactivated"
        } successfully`,
        {
          description: `The tagged asset has been ${
            taggedAsset.deleted ? "activated" : "deactivated"
          }.`,
        }
      );
    } catch (error) {
      console.error("Toggle status error:", error);
      toast.error("Failed to update tagged asset status", {
        description: "Please try again later.",
      });
    }
  };

  const handleRefresh = () => {
    loadData();
    loadAssetOptions();
    loadTagOptions();
    toast.success("Data refreshed successfully", {
      description: "Tagged asset data has been refreshed.",
    });
  };

  if (status === "loading") {
    return <div>Loading session...</div>;
  }

  if (!session?.user?.token) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-destructive mb-4">
            Authentication required. Please log in.
          </p>
          <Button onClick={() => (window.location.href = "/login")}>
            Go to Login
          </Button>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-destructive mb-4">{error}</p>
          <Button onClick={loadData}>Try Again</Button>
          <div className="mt-4 text-sm text-muted-foreground">
            <p>Session: {session?.user?.name || "No user"}</p>
            <p>Tenant: {session?.user?.tenantId || "No tenant"}</p>
            <p>Token: {session?.user?.token ? "Present" : "Missing"}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/digital-carpet">
                Digital Carpet
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Tagged Asset Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={handleCreateNew} className="gap-2 ml-auto">
          <Plus className="h-4 w-4" />
          Add Tagged Asset
        </Button>
      </header>
      <main>
        {/* Search and Filters */}
        <div className="p-4">
          <TaggedAssetSearchPagination
            searchFilters={searchFilters}
            onSearchChange={updateSearchFilters}
            onClearSearch={clearSearch}
            showDeleted={showDeleted}
            onToggleShowDeleted={toggleShowDeleted}
            onRefresh={handleRefresh}
            isLoading={isLoading}
          />
        </div>

        {/* Data Table */}
        <div className="p-4 pt-0">
          <TaggedAssetDataTable
            data={paginatedTaggedAssets}
            isLoading={isLoading}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onToggleStatus={handleToggleStatus}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
          />
        </div>
      </main>

      {/* Form Sheet */}
      <Sheet
        open={isFormOpen}
        onOpenChange={(open) => {
          if (!open) {
            closeForm();
          } else {
            setIsFormOpen(true);
          }
        }}
      >
        <SheetContent className="w-1/4 sm:max-w-4xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {editingTaggedAsset
                ? "Edit Tagged Asset"
                : "Create New Tagged Asset"}
            </SheetTitle>
            <SheetDescription>
              {editingTaggedAsset
                ? "Update the tagged asset information below."
                : "Fill in the details to create a new tagged asset."}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <TaggedAssetForm
              taggedAsset={editingTaggedAsset || undefined}
              assetOptions={assetOptions}
              tagOptions={tagOptions}
              isLoading={isFormLoading}
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
