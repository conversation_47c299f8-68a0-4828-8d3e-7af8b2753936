"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  Tag,
  TagModel,
  TagSearchFilters,
  TagPaginationParams,
  TagPageSize,
  DEFAULT_TAG_PAGE_SIZE,
  TAG_PAGE_SIZES,
  TAG_PAGE_SIZE_ALL,
  CreateTagFormData,
  UpdateTagFormData,
} from "@/types/tag";
import { toast } from "sonner";

interface UseTagManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: TagPageSize;
  initialPageSize?: number;
  availablePageSizes?: TagPageSize[];
}

export const useTagManagement = ({
  autoLoad = true,
  defaultPageSize = DEFAULT_TAG_PAGE_SIZE,
  initialPageSize,
  availablePageSizes,
}: UseTagManagementOptions = {}) => {
  // Get authenticated API instance and session
  const spidexApi = useSpidexApi();
  const { data: session, status } = useSession();

  // State
  const [tagModels, setTagModels] = useState<TagModel[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);

  // Search and pagination state - ensure all values are properly initialized
  const [searchFilters, setSearchFilters] = useState<TagSearchFilters>(() => ({
    searchTerm: "",
    modelId: "",
    status: undefined,
    deleted: false,
  }));

  const [pagination, setPagination] = useState<TagPaginationParams>({
    pageNumber: 1,
    pageSize: initialPageSize
      ? (initialPageSize as TagPageSize)
      : typeof defaultPageSize === "number"
      ? defaultPageSize
      : 10,
    sortBy: "name",
    sortOrder: "asc",
  });

  // Client-side pagination - load all data once and paginate in memory
  const [allTagsCache, setAllTagsCache] = useState<Tag[]>([]);

  // Filter tags based on showDeleted state and search filters
  const filteredTags = useMemo(() => {
    console.log("Filtering tags:", {
      showDeleted,
      totalTags: allTagsCache.length,
      deletedCount: allTagsCache.filter((tag) => tag.deleted).length,
      activeCount: allTagsCache.filter((tag) => !tag.deleted).length,
      searchFilters,
    });

    let filtered = allTagsCache;

    // First apply showDeleted filter
    if (!showDeleted) {
      // Show only non-deleted tags
      filtered = filtered.filter((tag) => !tag.deleted);
      console.log("Filtered out deleted tags:", filtered.length);
    }

    // Apply search filters
    if (searchFilters.searchTerm) {
      const searchTerm = searchFilters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (tag) =>
          tag.name.toLowerCase().includes(searchTerm) ||
          tag.description.toLowerCase().includes(searchTerm) ||
          (tag.externalId &&
            tag.externalId.toLowerCase().includes(searchTerm)) ||
          (tag.modelName &&
            typeof tag.modelName === "string" &&
            tag.modelName.toLowerCase().includes(searchTerm))
      );
      console.log("Applied search term filter:", filtered.length);
    }

    // Apply status filter
    if (searchFilters.status !== undefined) {
      filtered = filtered.filter((tag) => tag.status === searchFilters.status);
      console.log(
        `Applied ${
          searchFilters.status ? "active" : "inactive"
        } status filter:`,
        filtered.length
      );
    }

    // Apply model filter
    if (searchFilters.modelId && searchFilters.modelId !== "") {
      filtered = filtered.filter((tag) => {
        // Handle both string and number comparison for modelId
        const tagModelId = tag.modelId?.toString();
        const filterModelId = searchFilters.modelId?.toString();
        return tagModelId === filterModelId;
      });
      console.log(
        "Applied model filter:",
        filtered.length,
        "for modelId:",
        searchFilters.modelId
      );
    }

    console.log("Final filtered tags:", filtered.length);
    return filtered;
  }, [allTagsCache, showDeleted, searchFilters]);

  // Client-side pagination - always use filtered data
  const paginatedTags = useMemo(() => {
    console.log("Client-side pagination calculation:", {
      pageSize: pagination.pageSize,
      pageNumber: pagination.pageNumber,
      totalRecords: allTagsCache.length,
      filteredRecords: filteredTags.length,
      showDeleted: showDeleted,
      cacheEmpty: allTagsCache.length === 0,
    });

    // If filtered tags is empty, return empty array
    if (filteredTags.length === 0) {
      console.log("No tags to display, returning empty array");
      return [];
    }

    if (pagination.pageSize === TAG_PAGE_SIZE_ALL) {
      console.log("Showing all filtered tags:", filteredTags.length);
      return filteredTags;
    }

    // Client-side pagination on filtered data
    const pageSize = pagination.pageSize as number;
    const startIndex = (pagination.pageNumber - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const result = filteredTags.slice(startIndex, endIndex);

    console.log("Client-side pagination result:", {
      startIndex,
      endIndex,
      pageSize,
      totalRecords: allTagsCache.length,
      filteredRecords: filteredTags.length,
      resultCount: result.length,
      showDeleted: showDeleted,
      actualResult: result.map(
        (tag) => `${tag.name}${tag.deleted ? " (deleted)" : ""}`
      ),
    });

    return result;
  }, [filteredTags, pagination.pageNumber, pagination.pageSize, showDeleted]);

  // Calculate total pages for client-side pagination using filtered data
  const totalPages = useMemo(() => {
    if (pagination.pageSize === TAG_PAGE_SIZE_ALL) {
      return 1;
    }

    const currentPageSize = pagination.pageSize as number;
    return Math.ceil(filteredTags.length / currentPageSize);
  }, [filteredTags.length, pagination.pageSize]);

  // Load all data once for client-side pagination
  const loadData = useCallback(async () => {
    if (status !== "authenticated" || !session?.user?.token) {
      console.log("Tag management: User not authenticated", {
        status,
        hasToken: !!session?.user?.token,
      });
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Set authentication in API service
      spidexApi.setAuthToken(session.user.token);
      spidexApi.setTenantId(session.user.tenantId);

      console.log("Loading all tag management data for client-side pagination");

      // Load tag models only once (they don't change often)
      let tagModelsData = tagModels;

      if (tagModels.length === 0) {
        console.log("Loading tag models...");
        tagModelsData = await spidexApi.getAllTagConfigs();
        console.log("Tag models loaded:", tagModelsData.length, "models");
        setTagModels(tagModelsData);
      }

      // Load all tags for client-side pagination (using real API data)
      console.log("Loading all tags...");
      const tagsData = await spidexApi.getAllTags(1, 1000); // Load all tags once

      // Enrich tags with model information
      const enrichedTags = tagsData.map((tag: any) => {
        const model = tagModelsData.find(
          (m: any) => m.modelId?.toString() === tag.modelId?.toString()
        );
        return {
          ...tag,
          modelId: tag.modelId?.toString() || "", // Ensure modelId is string for form compatibility
          modelName:
            model?.modelName || (tag.modelId ? tag.modelId.toString() : null),
          status: tag.status === "true" || tag.status === true,
        };
      });

      // Store all tags in cache for client-side pagination (using real API data)
      setAllTagsCache(enrichedTags);

      // Debug: Check actual deleted status from API
      console.log("Real API data - deleted tags:", {
        total: enrichedTags.length,
        deleted: enrichedTags.filter((tag) => tag.deleted).length,
        deletedTags: enrichedTags
          .filter((tag) => tag.deleted)
          .map((tag) => `${tag.name} (${tag.externalId})`),
      });

      console.log("Tag management data loaded successfully");
      console.log(
        `Cached ${enrichedTags.length} tags for client-side pagination`
      );
    } catch (error) {
      console.error("Error loading tag management data:", error);
      setError(error instanceof Error ? error.message : "Failed to load data");
    } finally {
      setIsLoading(false);
    }
  }, [spidexApi, session, status, tagModels]);

  // Auto-load data when authenticated (only on initial mount)
  useEffect(() => {
    console.log("Auto-load effect triggered:", {
      autoLoad,
      status,
      hasToken: !!session?.user?.token,
    });
    if (autoLoad && status === "authenticated" && session?.user?.token) {
      console.log("Loading all data for client-side pagination");
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad, status, session?.user?.token]); // Removed loadData dependency to prevent reloading on session changes

  // Create tag
  const createTag = useCallback(
    async (tagData: CreateTagFormData): Promise<void> => {
      try {
        setIsLoading(true);
        setError(null);

        // Set authentication in API service
        spidexApi.setAuthToken(session?.user?.token!);
        spidexApi.setTenantId(session?.user?.tenantId!);

        // Prepare tag data with common values matching old application format
        const tagPayload = {
          id: null,
          ...tagData,
          modelId: parseInt(tagData.modelId), // Convert to number to match old app
          tenantId: session?.user?.tenantId,
          properties: {},
          status: tagData.status ?? true,
          deleted: false,
          createdBy: session?.user?.userId,
          createdTime: new Date().toISOString(),
          modifiedBy: session?.user?.userId,
          modifiedTime: new Date().toISOString(),
        };

        console.log("Creating tag:", tagPayload);

        const newTag = await spidexApi.createTag(tagPayload);

        // Add to cache and refresh data
        await loadData();

        console.log("Tag created successfully:", newTag);
        toast.success("Tag created successfully", {
          description: `Tag "${tagData.name}" has been created successfully.`,
        });
      } catch (error) {
        console.error("Error creating tag:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create tag";
        setError(errorMessage);
        toast.error("Failed to create tag", {
          description: errorMessage,
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  // Update tag
  const updateTag = useCallback(
    async (tagData: UpdateTagFormData): Promise<void> => {
      try {
        setIsLoading(true);
        setError(null);

        // Set authentication in API service
        spidexApi.setAuthToken(session?.user?.token!);
        spidexApi.setTenantId(session?.user?.tenantId!);

        // Find existing tag to preserve data
        const existingTag = allTagsCache.find((tag) => tag.id === tagData.id);
        if (!existingTag) {
          throw new Error("Tag not found");
        }

        // Prepare tag data preserving existing values and matching old application format
        const tagPayload = {
          ...existingTag,
          ...tagData,
          modelId: parseInt(tagData.modelId), // Convert to number to match old app
          modifiedBy: session?.user?.userId,
          modifiedTime: new Date().toISOString(),
        };

        console.log("Updating tag:", tagPayload);

        const updatedTag = await spidexApi.updateTag(tagPayload);

        // Refresh data
        await loadData();

        console.log("Tag updated successfully:", updatedTag);
        toast.success("Tag updated successfully", {
          description: `Tag "${tagData.name}" has been updated successfully.`,
        });
      } catch (error) {
        console.error("Error updating tag:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update tag";
        setError(errorMessage);
        toast.error("Failed to update tag", {
          description: errorMessage,
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, allTagsCache, loadData]
  );

  // Delete tag (soft delete)
  const deleteTag = useCallback(
    async (tagId: string): Promise<void> => {
      try {
        setIsLoading(true);
        setError(null);

        // Set authentication in API service
        spidexApi.setAuthToken(session?.user?.token!);
        spidexApi.setTenantId(session?.user?.tenantId!);

        console.log("Deleting tag:", tagId);

        await spidexApi.deleteTag(tagId);

        // Refresh data
        await loadData();

        console.log("Tag deleted successfully");
        toast.success("Tag deleted successfully", {
          description: "The tag has been deleted successfully.",
        });
      } catch (error) {
        console.error("Error deleting tag:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete tag";
        setError(errorMessage);
        toast.error("Failed to delete tag", {
          description: errorMessage,
        });
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  // Pagination controls
  const goToPage = useCallback((page: number) => {
    console.log("Going to page:", page);
    setPagination((prev) => ({ ...prev, pageNumber: page }));
  }, []);

  const changePageSize = useCallback((size: TagPageSize) => {
    console.log("Changing page size:", size);
    setPagination((prev) => ({ ...prev, pageSize: size, pageNumber: 1 }));
  }, []);

  const changeSorting = useCallback(
    (sortBy: string, sortOrder: "asc" | "desc") => {
      console.log("Changing sorting:", { sortBy, sortOrder });
      setPagination((prev) => ({ ...prev, sortBy, sortOrder, pageNumber: 1 }));
    },
    []
  );

  // Search controls
  const updateSearchFilters = useCallback(
    (filters: Partial<TagSearchFilters>) => {
      console.log("Updating search filters:", filters);
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      setPagination((prev) => ({ ...prev, pageNumber: 1 }));
    },
    []
  );

  const clearSearch = useCallback(() => {
    console.log("Clearing search filters");
    setSearchFilters({
      searchTerm: "",
      modelId: "",
      status: undefined,
      deleted: false,
    });
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
  }, []);

  // Toggle deleted tags visibility
  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => {
      const newValue = !prev;
      console.log("Toggling inactive view:", {
        from: prev ? "showing all" : "active only",
        to: newValue ? "showing all" : "active only",
      });
      return newValue;
    });
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
  }, []);

  // Calculate separate counts for active and inactive records
  const activeRecordsCount = useMemo(() => {
    return allTagsCache.filter((tag) => !tag.deleted && tag.status).length;
  }, [allTagsCache]);

  const inactiveRecordsCount = useMemo(() => {
    return allTagsCache.filter((tag) => tag.deleted || !tag.status).length;
  }, [allTagsCache]);

  // Ensure searchFilters is always properly initialized
  const safeSearchFilters = {
    searchTerm: searchFilters.searchTerm || "",
    modelId: searchFilters.modelId || "",
    status: searchFilters.status,
    deleted: searchFilters.deleted ?? false,
  };

  return {
    // Data
    tags: paginatedTags,
    allTags: allTagsCache,
    filteredTags,
    tagModels,

    // State
    isLoading,
    error,
    showDeleted,

    // Search and pagination
    searchFilters: safeSearchFilters,
    pagination,
    totalPages,
    totalRecords: filteredTags.length, // Use filtered count for display
    totalAllRecords: allTagsCache.length, // Total count including deleted
    activeRecordsCount, // Count of active (not deleted AND enabled) records
    inactiveRecordsCount, // Count of inactive (deleted OR not enabled) records
    hasNextPage: pagination.pageNumber < totalPages,
    hasPreviousPage: pagination.pageNumber > 1,

    // Actions
    loadData,
    createTag,
    updateTag,
    deleteTag,

    // Pagination controls
    goToPage,
    changePageSize,
    changeSorting,

    // Search controls
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,

    // Utilities
    availablePageSizes: availablePageSizes || [
      ...TAG_PAGE_SIZES,
      TAG_PAGE_SIZE_ALL,
    ],
  };
};
