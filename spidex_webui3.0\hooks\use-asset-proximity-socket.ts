import { useEffect, useRef, useState } from "react";
import { getAssetSocketManager } from "@/lib/websocket/asset-socket";

export default function useAssetProximitySocket(
  gatewayIds: string[],
  socketUrl?: string,
  onProximityEvent?: (event: any) => void
) {
  const socketManagerRef = useRef<any>(null);
  const didConnectRef = useRef(false);
  const subscriptionIdsRef = useRef<string[]>([]);
  const isActiveRef = useRef(true);
  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);

  useEffect(() => {
    isActiveRef.current = true;
    if (!gatewayIds || gatewayIds.length === 0) {
      if (socketManagerRef.current && didConnectRef.current) {
        try {
          socketManagerRef.current.disconnect();
        } catch {}
        didConnectRef.current = false;
      }
      setConnected(false);
      setConnecting(false);
      return;
    }
    if (!socketManagerRef.current) {
      socketManagerRef.current = getAssetSocketManager(socketUrl);
    }
    const socketManager = socketManagerRef.current;

    function subscribeAll() {
      subscriptionIdsRef.current.forEach((subId) => {
        try {
          socketManager.unsubscribe(subId);
        } catch {}
      });
      subscriptionIdsRef.current = [];
      gatewayIds.forEach((gatewayId) => {
        if (!gatewayId) return;
        try {
          const subId = socketManager.subscribeToProximityData(
            gatewayId,
            (data: any) => {
              if (onProximityEvent) {
                onProximityEvent({ ...data });
              } else {
                console.log(`[Proximity][Gateway:${gatewayId}]`, data);
              }
            }
          );
          subscriptionIdsRef.current.push(subId);
        } catch (err) {
          console.error(
            `Failed to subscribe to proximity for gateway ${gatewayId}:`,
            err
          );
        }
      });
    }

    const connectionChangeHandler = (connected: boolean) => {
      setConnected(connected);
      setConnecting(false);
      if (connected && isActiveRef.current) {
        subscribeAll();
      }
    };
    socketManager.setConnectionChangeCallback(connectionChangeHandler);

    if (socketManager.isConnected()) {
      setConnected(true);
      setConnecting(false);
      subscribeAll();
    } else {
      setConnecting(true);
      socketManager.connect();
      didConnectRef.current = true;
    }

    return () => {
      isActiveRef.current = false;
      socketManager.setConnectionChangeCallback(undefined);
      subscriptionIdsRef.current.forEach((subId) => {
        try {
          socketManager.unsubscribe(subId);
        } catch {}
      });
      subscriptionIdsRef.current = [];
      if (didConnectRef.current) {
        try {
          socketManager.disconnect();
        } catch {}
        didConnectRef.current = false;
      }
      setConnected(false);
      setConnecting(false);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(gatewayIds), socketUrl, onProximityEvent]);

  return { connected, connecting };
}
