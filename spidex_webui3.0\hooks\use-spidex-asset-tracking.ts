"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import {
  Asset,
  Gateway,
  Location,
  Area,
  SensorType,
  SensorData,
  AssetSearchFilters,
  TableData,
  WebSocketMessage,
} from "@/types/asset-tracking";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import { getAssetSocketManager } from "@/lib/websocket/asset-socket";

// Retry utility function for API calls
async function retryApiCall<T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`API call attempt ${attempt}/${maxRetries}`);
      const result = await apiCall();
      console.log(`API call succeeded on attempt ${attempt}`);
      return result;
    } catch (error) {
      lastError = error as Error;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      // Log different types of errors with appropriate icons
      if (
        errorMessage.includes("timeout") ||
        errorMessage.includes("AbortError")
      ) {
        console.warn(`API call timed out on attempt ${attempt}:`, errorMessage);
      } else if (errorMessage.includes("fetch")) {
        console.warn(`Network error on attempt ${attempt}:`, errorMessage);
      } else if (errorMessage.includes("Authentication")) {
        console.warn(
          `Authentication error on attempt ${attempt}:`,
          errorMessage
        );
      } else {
        console.warn(`API call failed on attempt ${attempt}:`, errorMessage);
      }

      if (attempt < maxRetries) {
        console.log(`Retrying in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
        delay *= 1.5; // Exponential backoff
      }
    }
  }

  console.error(`All ${maxRetries} API call attempts failed`);
  throw lastError!;
}

interface UseSpidexAssetTrackingOptions {
  socketUrl?: string;
  autoConnect?: boolean;
}

export const useSpidexAssetTracking = ({
  socketUrl = "https://spdxwebsoc.spidex.io",
  autoConnect = true,
}: UseSpidexAssetTrackingOptions) => {
  // Get authenticated API instance and session
  const spidexApi = useSpidexApi();
  const { data: session, status } = useSession();

  // WebSocket manager reference
  const socketManager = useRef(getAssetSocketManager(socketUrl));

  // State
  const [gateways, setGateways] = useState<Gateway[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [areas, setAreas] = useState<Area[]>([]);
  const [zones, setZones] = useState<
    Array<{ id: string; name: string; assetCount: number; color: string }>
  >([]);
  const [currentZoneData, setCurrentZoneData] = useState<Record<string, any[]>>(
    {}
  );
  const [gpsData, setGpsData] = useState<Record<string, any>>({});
  const [selectedGateway, setSelectedGateway] = useState<Gateway | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null
  );
  const [currentTableData, setCurrentTableData] = useState<TableData>({});
  const [sensorData, setSensorData] = useState<
    Record<string, Record<SensorType, SensorData>>
  >({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSocketConnected, setIsSocketConnected] = useState(false);
  const [socketError, setSocketError] = useState<string | null>(null);
  const [searchFilters, setSearchFilters] = useState<AssetSearchFilters>({
    searchTerm: "",
  });

  // Cache tagged assets to avoid repeated API calls during socket updates
  const [cachedTaggedAssets, setCachedTaggedAssets] = useState<any[]>([]);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Check authentication status
        if (status === "loading") {
          console.log("Session loading, waiting...");
          setIsLoading(false);
          return;
        }

        if (status === "unauthenticated") {
          console.log("User not authenticated");
          setSocketError("Please log in to access asset tracking data.");
          setIsLoading(false);
          return;
        }

        if (!session?.user?.token) {
          console.log("No token in session");
          setSocketError(
            "Authentication token not found. Please log in again."
          );
          setIsLoading(false);
          return;
        }

        console.log("User authenticated, loading data...");

        // Set the authentication token and tenant ID in the API service
        spidexApi.setAuthToken(session.user.token);
        spidexApi.setTenantId(session.user.tenantId);
        console.log("Authentication token set in API service");
        console.log("Tenant ID set in API service:", session.user.tenantId);
        console.log("API service authenticated:", spidexApi.isAuthenticated());

        // Use the new comprehensive API method with retry logic
        const data = await retryApiCall(
          () => spidexApi.fetchAssetTrackingData(),
          3
        );

        setGateways(data.gateways);
        setLocations(data.locations);
        setAreas(data.areas);
        setZones(data.zones);
        setCurrentTableData(data.tableData);

        // Cache tagged assets for socket updates (CRITICAL for real-time updates)
        console.log("Loading tagged assets for socket updates...");
        const taggedAssets = await retryApiCall(
          () => spidexApi.fetchTaggedAssets(),
          3
        );
        setCachedTaggedAssets(taggedAssets);
        console.log("Tagged assets cached:", taggedAssets.length);

        // Initialize sensor data for all assets
        const initialSensorData: Record<
          string,
          Record<SensorType, SensorData>
        > = {};
        const allAssets = Object.values(data.tableData).flat() as Asset[];
        allAssets.forEach((asset: Asset) => {
          initialSensorData[asset.deviceLogId] = {} as Record<
            SensorType,
            SensorData
          >;
          Object.values(SensorType).forEach((sensorType) => {
            initialSensorData[asset.deviceLogId][sensorType] = {
              value: Math.random() * 100,
              loading: false,
              selected: false,
              date: new Date().toISOString(),
              lineChartData: [],
            };
          });
        });
        setSensorData(initialSensorData);

        console.log("Initial data loaded - socket updates enabled");

        // Real socket connection will be handled in separate useEffect
        setSocketError(null);
      } catch (error) {
        console.error("Error loading asset tracking data:", error);

        setSocketError(
          "Failed to load data from Spidex API. Please check your connection."
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [spidexApi, status, session]);

  // Real WebSocket connection and proximity data updates
  useEffect(() => {
    if (!autoConnect) return;

    const manager = socketManager.current;

    // Set up connection callbacks
    manager.setConnectionChangeCallback(setIsSocketConnected);
    manager.setErrorCallback(setSocketError);

    // Connect to WebSocket
    manager.connect().catch((error) => {
      console.error("Failed to connect to WebSocket:", error);
      setSocketError("Failed to connect to real-time data stream");
    });

    return () => {
      manager.disconnect();
    };
  }, [autoConnect]);

  // Subscribe to proximity data for all gateways when connected
  useEffect(() => {
    if (!isSocketConnected || gateways.length === 0) return;

    const manager = socketManager.current;
    const subscriptionIds: string[] = [];

    // Subscribe to proximity data for each gateway
    gateways.forEach((gateway) => {
      try {
        const subscriptionId = manager.subscribeToProximityData(
          gateway.id,
          (message: WebSocketMessage) => {
            console.log("Received WebSocket message:", message);

            // Check if we have cached tagged assets (required for processing)
            if (!cachedTaggedAssets || cachedTaggedAssets.length === 0) {
              console.warn(
                "No cached tagged assets available for socket update"
              );
              return;
            }

            // Get current table data and update it
            setCurrentTableData((prevTableData) => {
              // Process the socket message synchronously with cached tagged assets
              try {
                console.log(
                  "🔄 Processing socket message for table data update"
                );
                console.log(
                  "📊 Current table data keys:",
                  Object.keys(prevTableData)
                );
                console.log(
                  "🏷️ Cached tagged assets count:",
                  cachedTaggedAssets.length
                );

                const updatedTableData = spidexApi.updateTableDataFromSocket(
                  prevTableData,
                  message,
                  gateways,
                  cachedTaggedAssets,
                  true // filterCurrent = true for socket updates
                );

                console.log("Table data updated from socket");
                console.log(
                  "Updated table data keys:",
                  Object.keys(updatedTableData)
                );

                return updatedTableData;
              } catch (error) {
                console.error(
                  "❌ Error updating table data from socket:",
                  error
                );
                return prevTableData;
              }
            });

            // Also update zone data structure (matches old implementation)
            updateZoneDataFromSocket(message);
          }
        );

        subscriptionIds.push(subscriptionId);
      } catch (error) {
        console.error(`Failed to subscribe to gateway ${gateway.id}:`, error);
      }
    });

    // Subscribe to GPS data for transit gateways
    gateways
      .filter((gateway) => gateway.categoryType === "transit")
      .forEach((gateway) => {
        try {
          const subscriptionId = manager.subscribeToGpsData(
            gateway.id,
            (message: WebSocketMessage) => {
              console.log(
                "📍 Received GPS WebSocket message for gateway:",
                gateway.id,
                message
              );

              // Check if the message sourceId matches the gateway ID
              if (message.sourceId && message.sourceId !== gateway.id) {
                console.warn(
                  `⚠️ GPS message sourceId (${message.sourceId}) doesn't match gateway ID (${gateway.id})`
                );
              }

              // Update GPS data state with the received message
              // Use the sourceId from the message as the key, not the gateway.id
              const gpsKey = message.sourceId || gateway.id;
              setGpsData((prev) => ({
                ...prev,
                [gpsKey]: message,
              }));

              console.log(`GPS data stored with key: ${gpsKey}`, message);
            }
          );

          subscriptionIds.push(subscriptionId);
          console.log(
            `✅ Subscribed to GPS data for transit gateway ${gateway.id} with ID: ${subscriptionId}`
          );
        } catch (error) {
          console.error(
            `❌ Failed to subscribe to GPS data for transit gateway ${gateway.id}:`,
            error
          );
        }
      });

    return () => {
      // Unsubscribe from all topics
      subscriptionIds.forEach((id) => {
        try {
          manager.unsubscribe(id);
        } catch (error) {
          console.error("Error unsubscribing:", error);
        }
      });
    };
  }, [isSocketConnected, gateways, cachedTaggedAssets, spidexApi]);

  // Gateway selection
  const selectGateway = useCallback(
    (gateway: Gateway) => {
      setSelectedGateway(gateway);
      setSelectedLocation(
        locations.find((loc) => loc.id === gateway.locId) || null
      );
    },
    [locations]
  );

  // Asset selection
  const selectAsset = useCallback((asset: Asset) => {
    // Update sensor data for selected asset
    setSensorData((prev) => ({
      ...prev,
      [asset.deviceLogId]: {
        ...prev[asset.deviceLogId],
        // Mark as selected or update specific sensor data
      },
    }));
  }, []);

  // Filter assets based on search criteria
  const filteredAssets = useCallback(
    (gatewayId: string) => {
      const assets = currentTableData[gatewayId] || [];

      if (!searchFilters.searchTerm) {
        return assets;
      }

      const searchTerm = searchFilters.searchTerm.toLowerCase();
      return assets.filter(
        (asset) =>
          asset.name.toLowerCase().includes(searchTerm) ||
          asset.deviceId.toLowerCase().includes(searchTerm) ||
          asset.macId.toLowerCase().includes(searchTerm) ||
          asset.areaName.toLowerCase().includes(searchTerm)
      );
    },
    [currentTableData, searchFilters]
  );

  // Get assets by location
  const getAssetsByLocation = useCallback(
    (locationId: string) => {
      const locationGateways = gateways.filter((gw) => gw.locId === locationId);
      const assets: Asset[] = [];

      locationGateways.forEach((gateway) => {
        const gatewayAssets = currentTableData[gateway.id] || [];
        assets.push(...gatewayAssets);
      });

      return assets;
    },
    [gateways, currentTableData]
  );

  // Get assets by area
  const getAssetsByArea = useCallback(
    (areaId: string) => {
      const allAssets = Object.values(currentTableData).flat();
      return allAssets.filter((asset) => {
        const area = areas.find((a) => a.name === asset.areaName);
        return area?.id === areaId;
      });
    },
    [currentTableData, areas]
  );

  // Get zones with asset counts (count assets from currentTableData)
  const getZonesWithAssetCounts = useCallback(() => {
    if (!selectedLocation?.id) {
      return zones.map((zone) => ({
        ...zone,
        assetCount: 0,
      }));
    }

    // Get all assets for the selected location from currentTableData
    const locationGateways = gateways.filter(
      (gw) => gw.locId === selectedLocation.id
    );
    const allLocationAssets: any[] = [];

    locationGateways.forEach((gateway) => {
      const gatewayAssets = currentTableData[gateway.id] || [];
      allLocationAssets.push(...gatewayAssets);
    });

    // Group assets by zone name
    const zoneAssetCounts: Record<string, number> = {};
    allLocationAssets.forEach((asset) => {
      const zoneName = asset.zone || asset.areaName || "Unknown Zone";
      zoneAssetCounts[zoneName] = (zoneAssetCounts[zoneName] || 0) + 1;
    });

    // Create hierarchical zones structure for tree view
    const zonesWithCounts: Array<{
      id: string;
      name: string;
      assetCount: number;
      color: string;
      type?: string;
      children?: Array<{
        id: string;
        name: string;
        assetCount: number;
        color: string;
        type?: string;
      }>;
    }> = [];

    // If we have zone data structure, use it to create hierarchical zones
    if (currentZoneData[selectedLocation.id]) {
      const locationZoneData = currentZoneData[selectedLocation.id];

      locationZoneData.forEach((zoneItem: any) => {
        // Handle main zone/area
        const mainZoneName = zoneItem.area;
        const mainZoneCount = zoneAssetCounts[mainZoneName] || 0;

        const mainZone = {
          id: zoneItem.areaId || zoneItem.area,
          name: mainZoneName,
          assetCount: mainZoneCount,
          color: "bg-blue-500", // Default color
          type: zoneItem.type || "Fixed",
          children: [] as Array<{
            id: string;
            name: string;
            assetCount: number;
            color: string;
            type?: string;
          }>,
        };

        // Handle sub-zones (lint zones)
        if (zoneItem.children && zoneItem.children.length > 0) {
          zoneItem.children.forEach((childZone: any) => {
            const childZoneName = childZone.area;
            const childZoneCount = zoneAssetCounts[childZoneName] || 0;

            mainZone.children!.push({
              id: childZone.gatLintId || childZone.areaId || childZone.area,
              name: childZoneName,
              assetCount: childZoneCount,
              color: "bg-green-500", // Different color for sub-zones
              type: childZone.type || "Lint",
            });
          });
        }

        zonesWithCounts.push(mainZone);
      });
    } else {
      // Fallback: create zones from unique zone names in assets
      const uniqueZones = [
        ...new Set(
          allLocationAssets.map((asset) => asset.zone || asset.areaName)
        ),
      ];
      uniqueZones.forEach((zoneName, index) => {
        if (zoneName) {
          zonesWithCounts.push({
            id: `zone_${index}`,
            name: zoneName,
            assetCount: zoneAssetCounts[zoneName] || 0,
            color: index % 2 === 0 ? "bg-blue-500" : "bg-green-500",
            type: "Zone",
            children: [],
          });
        }
      });
    }

    return zonesWithCounts;
  }, [zones, currentZoneData, selectedLocation, gateways, currentTableData]);

  // Get gateway cards for location (matches old implementation)
  const getGatewayCardsForLocation = useCallback(
    (locationId: string) => {
      const locationGateways = gateways.filter((gw) => gw.locId === locationId);

      return locationGateways.map((gateway) => {
        const cardData = {
          id: gateway.id,
          name: gateway.name,
          area: gateway.areaName || `Area-${gateway.id}`,
          areaId: gateway.areaId || "",
          assetCount: currentTableData[gateway.id]?.length || 0,
          type: gateway.categoryType || "",
          gateway: gateway,
        };
        return cardData;
      });
    },
    [gateways, currentTableData]
  );

  // Update zone data from socket message (matches old implementation)
  const updateZoneDataFromSocket = useCallback(
    (socketMessage: any) => {
      if (!selectedLocation?.id || !cachedTaggedAssets) return;

      setCurrentZoneData((state) => {
        const tempDataReturn = { ...state };
        const tgAssetLink = cachedTaggedAssets.find(
          (x) => x.id === socketMessage.deviceLogId
        );
        if (!tgAssetLink) return state;

        const deviceKey = "devicePhyId";
        const newAsset = { ...socketMessage };
        newAsset.devicePhyId = socketMessage[deviceKey];
        newAsset.key = `${socketMessage[deviceKey]} ${Math.floor(
          Date.now() / 1000
        )}`;
        newAsset.macId = tgAssetLink?.taggedAssetInfo?.assetExternalId;
        newAsset.deviceId = tgAssetLink?.taggedAssetInfo?.tagExternalId;
        newAsset.tagName = tgAssetLink?.taggedAssetInfo?.tagName;
        newAsset.name = tgAssetLink?.taggedAssetInfo?.assetName;
        newAsset.tgAssetLink = { ...tgAssetLink };
        newAsset.areaName = spidexApi.getAssetArea(newAsset, gateways);
        newAsset.zone = spidexApi.getAssetZone(newAsset, gateways);

        let zone = tempDataReturn[selectedLocation.id];
        if (!zone) return state;

        const gatewayLint = socketMessage.sourceId !== socketMessage.lintLogId;
        const foundZone = gatewayLint
          ? zone.find((x) => x.area === newAsset.areaName)?.children
          : zone.find((x) => x.area === newAsset.areaName);

        // Remove asset from all zones first (deduplication)
        zone.forEach((z: any) => {
          const foundAsset = z?.assets?.find(
            (x: any) => x.devicePhyId === socketMessage[deviceKey]
          );
          if (foundAsset) {
            z.assets =
              z?.assets?.filter(
                (x: any) => x.devicePhyId !== socketMessage[deviceKey]
              ) || [];
          }
          // Also check children zones
          if (z.children) {
            z.children.forEach((child: any) => {
              const foundChildAsset = child?.assets?.find(
                (x: any) => x.devicePhyId === socketMessage[deviceKey]
              );
              if (foundChildAsset) {
                child.assets =
                  child?.assets?.filter(
                    (x: any) => x.devicePhyId !== socketMessage[deviceKey]
                  ) || [];
              }
            });
          }
        });

        // Add asset to the correct zone
        if (gatewayLint && foundZone) {
          const lintZone = foundZone.find((x: any) => x.area === newAsset.zone);
          if (lintZone) {
            lintZone.assets = [...(lintZone?.assets || []), newAsset];
          }
        } else if (foundZone) {
          foundZone.assets = [...(foundZone?.assets || []), newAsset];
        }

        return tempDataReturn;
      });
    },
    [selectedLocation, cachedTaggedAssets, gateways]
  );

  // Initialize zone data for location (matches old implementation)
  const initializeZoneDataForLocation = useCallback(
    (locationId: string) => {
      setCurrentZoneData((state) => {
        const tempData = { ...state };
        const gatewayAreasZones: any[] = [];

        gateways
          .filter((x) => x.locId === locationId)
          .forEach((gateway) => {
            const item: any = {
              area: gateway.areaName || "",
              areaId: gateway.areaId || "",
              type: gateway.categoryType || "",
              assets: [],
            };

            if (gateway.categoryType === "lint") {
              item.children = [];
              gateway?.gatewayLint?.forEach((lint) => {
                const lintItem = {
                  area: lint.areaName || "",
                  areaId: lint.areaId || "",
                  gatLintId: lint.gatLintId || "",
                  type: "lint",
                  assets: [],
                };
                item.children.push(lintItem);
              });
            }
            gatewayAreasZones.push(item);
          });

        tempData[locationId] = gatewayAreasZones;
        return tempData;
      });
    },
    [gateways]
  );

  // Load proximity data for location (matches old implementation)
  const loadProximityDataForLocation = useCallback(
    async (locationId: string) => {
      try {
        setIsLoading(true);

        // Ensure authentication and tenant ID are set
        if (session?.user?.token && session?.user?.tenantId) {
          spidexApi.setAuthToken(session.user.token);
          spidexApi.setTenantId(session.user.tenantId);
          console.log(
            "🔄 Loading proximity data with tenant ID:",
            session.user.tenantId
          );
        }

        // Initialize zone data structure first
        initializeZoneDataForLocation(locationId);

        const newTableData = await spidexApi.loadProximityDataForLocation(
          locationId
        );
        setCurrentTableData(newTableData);

        console.log("Proximity data loaded for location");
      } catch (error) {
        console.error("Error loading proximity data:", error);
        setSocketError("Failed to load proximity data for location.");
      } finally {
        setIsLoading(false);
      }
    },
    [initializeZoneDataForLocation, spidexApi, session]
  );

  // Refresh data
  const refreshData = useCallback(async () => {
    try {
      setIsLoading(true);

      // Ensure authentication and tenant ID are set
      if (session?.user?.token && session?.user?.tenantId) {
        spidexApi.setAuthToken(session.user.token);
        spidexApi.setTenantId(session.user.tenantId);
        console.log("Refreshing with tenant ID:", session.user.tenantId);
      }

      // Use the new comprehensive API method with retry logic
      const data = await retryApiCall(
        () => spidexApi.fetchAssetTrackingData(),
        3
      );

      setGateways(data.gateways);
      setLocations(data.locations);
      setAreas(data.areas);
      setZones(data.zones);
      setCurrentTableData(data.tableData);

      // Initialize zone data for the first location if available
      if (data.locations.length > 0) {
        const firstLocationId = data.locations[0].id;
        initializeZoneDataForLocation(firstLocationId);
      }

      // Cache tagged assets for socket updates with retry logic
      const taggedAssets = await retryApiCall(
        () => spidexApi.fetchTaggedAssets(),
        3
      );
      setCachedTaggedAssets(taggedAssets);

      console.log("Data refreshed - socket updates ready");
      setSocketError(null); // Clear any previous errors
    } catch (error) {
      console.error("Error refreshing data:", error);

      setSocketError(
        "Failed to refresh data from Spidex API. Please check your connection."
      );
    } finally {
      setIsLoading(false);
    }
  }, [spidexApi, session, initializeZoneDataForLocation]);

  // Connect/disconnect socket
  const connectSocket = useCallback(() => {
    const manager = socketManager.current;
    manager.connect().catch((error) => {
      console.error("Failed to connect to WebSocket:", error);
      setSocketError("Failed to connect to real-time data stream");
    });
  }, []);

  const disconnectSocket = useCallback(() => {
    const manager = socketManager.current;
    manager.disconnect();
  }, []);

  // Subscribe to GPS data for transit gateways via WebSocket
  const subscribeToGpsData = useCallback((gatewayId: string) => {
    const manager = socketManager.current;

    try {
      console.log(`Subscribing to GPS data for gateway: ${gatewayId}`);

      const subscriptionId = manager.subscribeToGpsData(
        gatewayId,
        (message: WebSocketMessage) => {
          console.log("Received GPS WebSocket message:", message);

          // Update GPS data state with the received message
          setGpsData((prev) => ({
            ...prev,
            [gatewayId]: message,
          }));
        }
      );

      console.log(
        `✅ Subscribed to GPS data for gateway ${gatewayId} with ID: ${subscriptionId}`
      );
      return subscriptionId;
    } catch (error) {
      console.error(
        `❌ Failed to subscribe to GPS data for gateway ${gatewayId}:`,
        error
      );
      return null;
    }
  }, []);

  // Note: Initial data loading is handled by the main useEffect above
  // No need for a separate useEffect that calls refreshData

  return {
    // Data
    gateways,
    locations,
    areas,
    zones: getZonesWithAssetCounts(),
    currentTableData,
    sensorData,
    gpsData,

    // Selected items
    selectedGateway,
    selectedLocation,

    // State
    isLoading,
    isSocketConnected,
    socketError,
    searchFilters,

    // Actions
    selectGateway,
    selectAsset,
    setSearchFilters,
    filteredAssets,
    getAssetsByLocation,
    getAssetsByArea,
    getGatewayCardsForLocation,
    loadProximityDataForLocation,
    subscribeToGpsData,
    refreshData,
    connectSocket,
    disconnectSocket,
  };
};
