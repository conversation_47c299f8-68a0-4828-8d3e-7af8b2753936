import { useContext, useEffect, useState } from 'react';
import { Button, Table, CommonDrawer, Form, Input, Row, Col, message } from '../../../../components';
import { CRUD, Pages, ModuleNames } from '../../../../constants';
import { getAllPages, getAllRoles, getAllPagesRolesLinks, linkPageRole, addRole } from '../../../../services';
import Context from '../../../../context';
import { BreadcrumbList } from '../../../../shared';
import s from './index.module.less';
import { PlusOutlined } from '@ant-design/icons';
import { startCase, get } from 'lodash';

const Rbac = () => {
  const [data, setData] = useState([]);
  const [roles, setRoles] = useState([]);
  const [pages, setPages] = useState([]);
  const [showAddRole, setShowAddRole] = useState(false);
  const [pagesAvailable, setPagesAvailable] = useState([]);
  const [context, setContext] = useContext(Context);
  const [selectedPages, setSelectedPages] = useState({});

  const [form] = Form.useForm();

  useEffect(() => {
    const init = async () => {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      try {
        const { data } = await getAllPages();
        const tempData = {};
        data
          .filter((x) => x.deleted === false)
          .forEach((item) => {
            tempData[item.id] = { [CRUD.VIEW]: false, [CRUD.ADD]: false, [CRUD.UPDATE]: false, [CRUD.DELETE]: false };
          });
        const rolesRes = await getAllRoles();
        setRoles(rolesRes.data.filter((x) => x.deleted === false));
        const linkRes = await getAllPagesRolesLinks();
        const tempLinksData = [];
        linkRes.data.forEach((item) => {
          item.pagePermissions?.forEach((page) => {
            tempLinksData.push({
              key: `${item.id}-${page.pageName}`,
              role: item.id,
              page: page.pageName,
              permissions: page.permissions.join(','),
            });
          });
        });
        setData(tempLinksData);
        setSelectedPages(tempData);
        setPagesAvailable(data.filter((x) => x.deleted === false));
      } catch {
        message.error('Unable to get pages');
      }
      const tempPages = [];
      Object.keys(Pages).forEach((item) => {
        tempPages.push({
          page: item,
          description: Pages[item],
        });
      });
      setPages(tempPages);
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
    };
    init();
    //eslint-disable-next-line
  }, []);

  const clickPagePermission = (item, permission) => {
    const temp = { ...selectedPages };
    if (item in temp && permission in temp[item]) {
      temp[item][permission] = !temp[item][permission];
    }
    setSelectedPages(temp);
  };

  const clickPageAllPermissions = (item) => {
    const temp = { ...selectedPages };
    Object.keys(temp[item]).forEach((permission) => {
      if (item in temp && permission in temp[item]) {
        temp[item][permission] = true;
      }
    });
    setSelectedPages(temp);
  };

  const buttonType = (item, permission) => {
    if (item in selectedPages && permission in selectedPages[item]) {
      if (selectedPages[item][permission]) return 'primary';
    } else return 'default';
  };

  const tableColsRolePages = [
    { title: 'Role', key: 'role', dataIndex: 'role' },
    { title: 'Page', key: 'page', dataIndex: 'page' },
    {
      title: 'Permissions',
      key: 'permissions',
      dataIndex: 'permissions',
    },
  ];

  const tableColsRoles = [
    { title: 'Role', key: 'name', dataIndex: 'name' },
    { title: 'Created At', render: (record) => <>{new Date(record.createdTime).toLocaleDateString()}</> },
  ];
  const tableColsPages = [
    { title: 'Page', key: 'page', dataIndex: 'page' },
    { title: 'Description', key: 'description', dataIndex: 'description' },
  ];

  const closeAddRole = () => {
    setShowAddRole(false);
  };

  const addNewRole = async () => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const values = form.getFieldsValue();

    try {
      const dateTimeNow = new Date().toISOString();
      const newRole = {
        name: values.name,
        id: values.name,
        enable: true,
        deleted: false,
        createdTime: dateTimeNow,
        modifiedTime: dateTimeNow,
        createdBy: context.profile.username,
        modifiedBy: context.profile.username,
      };
      const { data } = await addRole(newRole);
      const selectedPagesArray = [];
      Object.keys(selectedPages).forEach((item) => {
        const permissions = Object.keys(selectedPages[item]).filter((x) => selectedPages[item][x]);
        if (permissions.length > 0) {
          selectedPagesArray.push({
            pageName: pagesAvailable.find((x) => x.id === item).pageName,
            permissions: permissions,
          });
        }
      });
      const linkObj = {
        id: data.name,
        pagePermissions: selectedPagesArray,
        enable: true,
        deleted: false,
        createdTime: dateTimeNow,
        modifiedTime: dateTimeNow,
        createdBy: context.profile.username,
        modifiedBy: context.profile.username,
      };
      await linkPageRole(linkObj);
    } catch {
      message.error('Unable to add role');
    } finally {
      setShowAddRole(false);
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
    }
  };

  const clear = () => {
    const tempData = { ...selectedPages };
    Object.keys(tempData).forEach((item) => {
      Object.keys(tempData[item]).forEach((permission) => {
        tempData[item][permission] = false;
      });
    });
    setSelectedPages(tempData);
    form.resetFields();
  };

  const PagePermission = ({ id, pageName }) => {
    return (
      <div key={id} className={s.permissionCheckContainer}>
        <h5 className={s.pageName}>{startCase(pageName?.replace('-', ' '))}:</h5>
        <Row gutter={16} justify="center">
          <Col>
            <Button type="link" onClick={() => clickPageAllPermissions(id)}>
              Add All
            </Button>
          </Col>
          <Col>
            <Button onClick={() => clickPagePermission(id, CRUD.ADD)} type={buttonType(id, CRUD.ADD)}>
              C
            </Button>
          </Col>
          <Col>
            <Button onClick={() => clickPagePermission(id, CRUD.VIEW)} type={buttonType(id, CRUD.VIEW)}>
              R
            </Button>
          </Col>
          <Col>
            <Button onClick={() => clickPagePermission(id, CRUD.UPDATE)} type={buttonType(id, CRUD.UPDATE)}>
              U
            </Button>
          </Col>
          <Col>
            <Button onClick={() => clickPagePermission(id, CRUD.DELETE)} type={buttonType(id, CRUD.DELETE)}>
              D
            </Button>
          </Col>
        </Row>
      </div>
    );
  };

  const rbacBreadcrumbsName = get(context.tenantProfile, `moduleNames[${Pages.RBAC}].displayName`, ModuleNames.RBAC);

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.USER_MANAGEMENT, ModuleNames.USER, rbacBreadcrumbsName]} />
        </Col>
        <Col>
          <Button type="link" className="commonAddButton actionButton" onClick={() => setShowAddRole(true)}>
            <PlusOutlined /> Add Role
          </Button>
        </Col>
      </Row>
      <div className={s.rolePageContainer}>
        <h3>Role-Page Permissions</h3>
        <Table size="small" scroll={{ x: true }} columns={tableColsRolePages} dataSource={data} />
      </div>
      <Row gutter={8}>
        <Col xs={{ span: 24 }} lg={{ span: 12 }}>
          <div className={s.bottomContainer}>
            <h3>Roles</h3>
            <Table rowKey="id" size="small" scroll={{ x: true }} columns={tableColsRoles} dataSource={roles} />
          </div>
        </Col>
        <Col xs={{ span: 24 }} lg={{ span: 12 }}>
          <div className={s.bottomContainer}>
            <h3>Pages</h3>
            <Table rowKey="page" size="small" scroll={{ x: true }} columns={tableColsPages} dataSource={pages} />
          </div>
        </Col>
      </Row>
      <CommonDrawer title="Role" visible={showAddRole} closeAdd={closeAddRole}>
        <Row>
          <Col span={24}>
            <Form form={form}>
              <Form.Item name="name" required={true} label="Role Name">
                <Input placeholder="Role Name" />
              </Form.Item>
              <Row className={s.crudMeaning}>
                <Col>C = ( Create / Add )</Col>
                <Col>R = ( Read / View )</Col>
                <Col>U = ( Update / Edit )</Col>
                <Col>D = ( Delete )</Col>
              </Row>
              <h4 className={s.formHeading}>Page Permissions:</h4>
              {pagesAvailable.map((x) => (
                <PagePermission key={x.id} id={x.id} pageName={x.pageName} />
              ))}

              <Row gutter={6} className={s.saveBtn}>
                <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
                  <Button block className="commonSaveButton formButton" onClick={addNewRole}>
                    Save
                  </Button>
                </Col>
              </Row>
              <Row className="footer" gutter={6}>
                <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
                  <Button onClick={clear} block className={['clearButton', !context.isCompact ? 'clear' : null]}>
                    Clear
                  </Button>
                </Col>
                <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
                  <Button block danger type="primary" onClick={() => setShowAddRole(false)}>
                    Close
                  </Button>
                </Col>
              </Row>
            </Form>
          </Col>
        </Row>
      </CommonDrawer>
    </>
  );
};

export default Rbac;
