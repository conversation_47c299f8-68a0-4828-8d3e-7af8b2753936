"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/faye-websocket";
exports.ids = ["vendor-chunks/faye-websocket"];
exports.modules = {

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/eventsource.js":
/*!*************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/eventsource.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Stream      = (__webpack_require__(/*! stream */ \"stream\").Stream),\n    util        = __webpack_require__(/*! util */ \"util\"),\n    driver      = __webpack_require__(/*! websocket-driver */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\"),\n    Headers     = __webpack_require__(/*! websocket-driver/lib/websocket/driver/headers */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver/headers.js\"),\n    API         = __webpack_require__(/*! ./websocket/api */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js\"),\n    EventTarget = __webpack_require__(/*! ./websocket/api/event_target */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js\"),\n    Event       = __webpack_require__(/*! ./websocket/api/event */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\");\n\nvar EventSource = function(request, response, options) {\n  this.writable = true;\n  options = options || {};\n\n  this._stream = response.socket;\n  this._ping   = options.ping  || this.DEFAULT_PING;\n  this._retry  = options.retry || this.DEFAULT_RETRY;\n\n  var scheme       = driver.isSecureRequest(request) ? 'https:' : 'http:';\n  this.url         = scheme + '//' + request.headers.host + request.url;\n  this.lastEventId = request.headers['last-event-id'] || '';\n  this.readyState  = API.CONNECTING;\n\n  var headers = new Headers(),\n      self    = this;\n\n  if (options.headers) {\n    for (var key in options.headers) headers.set(key, options.headers[key]);\n  }\n\n  if (!this._stream || !this._stream.writable) return;\n  process.nextTick(function() { self._open() });\n\n  this._stream.setTimeout(0);\n  this._stream.setNoDelay(true);\n\n  var handshake = 'HTTP/1.1 200 OK\\r\\n' +\n                  'Content-Type: text/event-stream\\r\\n' +\n                  'Cache-Control: no-cache, no-store\\r\\n' +\n                  'Connection: close\\r\\n' +\n                  headers.toString() +\n                  '\\r\\n' +\n                  'retry: ' + Math.floor(this._retry * 1000) + '\\r\\n\\r\\n';\n\n  this._write(handshake);\n\n  this._stream.on('drain', function() { self.emit('drain') });\n\n  if (this._ping)\n    this._pingTimer = setInterval(function() { self.ping() }, this._ping * 1000);\n\n  ['error', 'end'].forEach(function(event) {\n    self._stream.on(event, function() { self.close() });\n  });\n};\nutil.inherits(EventSource, Stream);\n\nEventSource.isEventSource = function(request) {\n  if (request.method !== 'GET') return false;\n  var accept = (request.headers.accept || '').split(/\\s*,\\s*/);\n  return accept.indexOf('text/event-stream') >= 0;\n};\n\nvar instance = {\n  DEFAULT_PING:   10,\n  DEFAULT_RETRY:  5,\n\n  _write: function(chunk) {\n    if (!this.writable) return false;\n    try {\n      return this._stream.write(chunk, 'utf8');\n    } catch (e) {\n      return false;\n    }\n  },\n\n  _open: function() {\n    if (this.readyState !== API.CONNECTING) return;\n\n    this.readyState = API.OPEN;\n\n    var event = new Event('open');\n    event.initEvent('open', false, false);\n    this.dispatchEvent(event);\n  },\n\n  write: function(message) {\n    return this.send(message);\n  },\n\n  end: function(message) {\n    if (message !== undefined) this.write(message);\n    this.close();\n  },\n\n  send: function(message, options) {\n    if (this.readyState > API.OPEN) return false;\n\n    message = String(message).replace(/(\\r\\n|\\r|\\n)/g, '$1data: ');\n    options = options || {};\n\n    var frame = '';\n    if (options.event) frame += 'event: ' + options.event + '\\r\\n';\n    if (options.id)    frame += 'id: '    + options.id    + '\\r\\n';\n    frame += 'data: ' + message + '\\r\\n\\r\\n';\n\n    return this._write(frame);\n  },\n\n  ping: function() {\n    return this._write(':\\r\\n\\r\\n');\n  },\n\n  close: function() {\n    if (this.readyState > API.OPEN) return false;\n\n    this.readyState = API.CLOSED;\n    this.writable = false;\n    if (this._pingTimer) clearInterval(this._pingTimer);\n    if (this._stream) this._stream.end();\n\n    var event = new Event('close');\n    event.initEvent('close', false, false);\n    this.dispatchEvent(event);\n\n    return true;\n  }\n};\n\nfor (var method in instance) EventSource.prototype[method] = instance[method];\nfor (var key in EventTarget) EventSource.prototype[key] = EventTarget[key];\n\nmodule.exports = EventSource;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/eventsource.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket.js":
/*!***********************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// API references:\n//\n// * https://html.spec.whatwg.org/multipage/comms.html#network\n// * https://dom.spec.whatwg.org/#interface-eventtarget\n// * https://dom.spec.whatwg.org/#interface-event\n\n\n\nvar util   = __webpack_require__(/*! util */ \"util\"),\n    driver = __webpack_require__(/*! websocket-driver */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\"),\n    API    = __webpack_require__(/*! ./websocket/api */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js\");\n\nvar WebSocket = function(request, socket, body, protocols, options) {\n  options = options || {};\n\n  this._stream = socket;\n  this._driver = driver.http(request, { maxLength: options.maxLength, protocols: protocols });\n\n  var self = this;\n  if (!this._stream || !this._stream.writable) return;\n  if (!this._stream.readable) return this._stream.end();\n\n  var catchup = function() { self._stream.removeListener('data', catchup) };\n  this._stream.on('data', catchup);\n\n  API.call(this, options);\n\n  process.nextTick(function() {\n    self._driver.start();\n    self._driver.io.write(body);\n  });\n};\nutil.inherits(WebSocket, API);\n\nWebSocket.isWebSocket = function(request) {\n  return driver.isWebSocket(request);\n};\n\nWebSocket.validateOptions = function(options, validKeys) {\n  driver.validateOptions(options, validKeys);\n};\n\nWebSocket.WebSocket   = WebSocket;\nWebSocket.Client      = __webpack_require__(/*! ./websocket/client */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/client.js\");\nWebSocket.EventSource = __webpack_require__(/*! ./eventsource */ \"(ssr)/./node_modules/faye-websocket/lib/faye/eventsource.js\");\n\nmodule.exports        = WebSocket;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js":
/*!***************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket/api.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Stream      = (__webpack_require__(/*! stream */ \"stream\").Stream),\n    util        = __webpack_require__(/*! util */ \"util\"),\n    driver      = __webpack_require__(/*! websocket-driver */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\"),\n    EventTarget = __webpack_require__(/*! ./api/event_target */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js\"),\n    Event       = __webpack_require__(/*! ./api/event */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\");\n\nvar API = function(options) {\n  options = options || {};\n  driver.validateOptions(options, ['headers', 'extensions', 'maxLength', 'ping', 'proxy', 'tls', 'ca']);\n\n  this.readable = this.writable = true;\n\n  var headers = options.headers;\n  if (headers) {\n    for (var name in headers) this._driver.setHeader(name, headers[name]);\n  }\n\n  var extensions = options.extensions;\n  if (extensions) {\n    [].concat(extensions).forEach(this._driver.addExtension, this._driver);\n  }\n\n  this._ping          = options.ping;\n  this._pingId        = 0;\n  this.readyState     = API.CONNECTING;\n  this.bufferedAmount = 0;\n  this.protocol       = '';\n  this.url            = this._driver.url;\n  this.version        = this._driver.version;\n\n  var self = this;\n\n  this._driver.on('open',    function(e) { self._open() });\n  this._driver.on('message', function(e) { self._receiveMessage(e.data) });\n  this._driver.on('close',   function(e) { self._beginClose(e.reason, e.code) });\n\n  this._driver.on('error', function(error) {\n    self._emitError(error.message);\n  });\n  this.on('error', function() {});\n\n  this._driver.messages.on('drain', function() {\n    self.emit('drain');\n  });\n\n  if (this._ping)\n    this._pingTimer = setInterval(function() {\n      self._pingId += 1;\n      self.ping(self._pingId.toString());\n    }, this._ping * 1000);\n\n  this._configureStream();\n\n  if (!this._proxy) {\n    this._stream.pipe(this._driver.io);\n    this._driver.io.pipe(this._stream);\n  }\n};\nutil.inherits(API, Stream);\n\nAPI.CONNECTING = 0;\nAPI.OPEN       = 1;\nAPI.CLOSING    = 2;\nAPI.CLOSED     = 3;\n\nAPI.CLOSE_TIMEOUT = 30000;\n\nvar instance = {\n  write: function(data) {\n    return this.send(data);\n  },\n\n  end: function(data) {\n    if (data !== undefined) this.send(data);\n    this.close();\n  },\n\n  pause: function() {\n    return this._driver.messages.pause();\n  },\n\n  resume: function() {\n    return this._driver.messages.resume();\n  },\n\n  send: function(data) {\n    if (this.readyState > API.OPEN) return false;\n    if (!(data instanceof Buffer)) data = String(data);\n    return this._driver.messages.write(data);\n  },\n\n  ping: function(message, callback) {\n    if (this.readyState > API.OPEN) return false;\n    return this._driver.ping(message, callback);\n  },\n\n  close: function(code, reason) {\n    if (code === undefined) code = 1000;\n    if (reason === undefined) reason = '';\n\n    if (code !== 1000 && (code < 3000 || code > 4999))\n      throw new Error(\"Failed to execute 'close' on WebSocket: \" +\n                      \"The code must be either 1000, or between 3000 and 4999. \" +\n                      code + \" is neither.\");\n\n    if (this.readyState < API.CLOSING) {\n      var self = this;\n      this._closeTimer = setTimeout(function() {\n        self._beginClose('', 1006);\n      }, API.CLOSE_TIMEOUT);\n    }\n\n    if (this.readyState !== API.CLOSED) this.readyState = API.CLOSING;\n\n    this._driver.close(reason, code);\n  },\n\n  _configureStream: function() {\n    var self = this;\n\n    this._stream.setTimeout(0);\n    this._stream.setNoDelay(true);\n\n    ['close', 'end'].forEach(function(event) {\n      this._stream.on(event, function() { self._finalizeClose() });\n    }, this);\n\n    this._stream.on('error', function(error) {\n      self._emitError('Network error: ' + self.url + ': ' + error.message);\n      self._finalizeClose();\n    });\n  },\n\n  _open: function() {\n    if (this.readyState !== API.CONNECTING) return;\n\n    this.readyState = API.OPEN;\n    this.protocol = this._driver.protocol || '';\n\n    var event = new Event('open');\n    event.initEvent('open', false, false);\n    this.dispatchEvent(event);\n  },\n\n  _receiveMessage: function(data) {\n    if (this.readyState > API.OPEN) return false;\n\n    if (this.readable) this.emit('data', data);\n\n    var event = new Event('message', { data: data });\n    event.initEvent('message', false, false);\n    this.dispatchEvent(event);\n  },\n\n  _emitError: function(message) {\n    if (this.readyState >= API.CLOSING) return;\n\n    var event = new Event('error', { message: message });\n    event.initEvent('error', false, false);\n    this.dispatchEvent(event);\n  },\n\n  _beginClose: function(reason, code) {\n    if (this.readyState === API.CLOSED) return;\n    this.readyState = API.CLOSING;\n    this._closeParams = [reason, code];\n\n    if (this._stream) {\n      this._stream.destroy();\n      if (!this._stream.readable) this._finalizeClose();\n    }\n  },\n\n  _finalizeClose: function() {\n    if (this.readyState === API.CLOSED) return;\n    this.readyState = API.CLOSED;\n\n    if (this._closeTimer) clearTimeout(this._closeTimer);\n    if (this._pingTimer) clearInterval(this._pingTimer);\n    if (this._stream) this._stream.end();\n\n    if (this.readable) this.emit('end');\n    this.readable = this.writable = false;\n\n    var reason = this._closeParams ? this._closeParams[0] : '',\n        code   = this._closeParams ? this._closeParams[1] : 1006;\n\n    var event = new Event('close', { code: code, reason: reason });\n    event.initEvent('close', false, false);\n    this.dispatchEvent(event);\n  }\n};\n\nfor (var method in instance) API.prototype[method] = instance[method];\nfor (var key in EventTarget) API.prototype[key] = EventTarget[key];\n\nmodule.exports = API;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js":
/*!*********************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket/api/event.js ***!
  \*********************************************************************/
/***/ ((module) => {

eval("\n\nvar Event = function(eventType, options) {\n  this.type = eventType;\n  for (var key in options)\n    this[key] = options[key];\n};\n\nEvent.prototype.initEvent = function(eventType, canBubble, cancelable) {\n  this.type       = eventType;\n  this.bubbles    = canBubble;\n  this.cancelable = cancelable;\n};\n\nEvent.prototype.stopPropagation = function() {};\nEvent.prototype.preventDefault  = function() {};\n\nEvent.CAPTURING_PHASE = 1;\nEvent.AT_TARGET       = 2;\nEvent.BUBBLING_PHASE  = 3;\n\nmodule.exports = Event;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmF5ZS13ZWJzb2NrZXQvbGliL2ZheWUvd2Vic29ja2V0L2FwaS9ldmVudC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aXNcXERvY3VtZW50c1xcUHJvamVjdHNcXFNwaWRleFxcc3BpZGV4X3dlYnVpMy4wXFxub2RlX21vZHVsZXNcXGZheWUtd2Vic29ja2V0XFxsaWJcXGZheWVcXHdlYnNvY2tldFxcYXBpXFxldmVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBFdmVudCA9IGZ1bmN0aW9uKGV2ZW50VHlwZSwgb3B0aW9ucykge1xuICB0aGlzLnR5cGUgPSBldmVudFR5cGU7XG4gIGZvciAodmFyIGtleSBpbiBvcHRpb25zKVxuICAgIHRoaXNba2V5XSA9IG9wdGlvbnNba2V5XTtcbn07XG5cbkV2ZW50LnByb3RvdHlwZS5pbml0RXZlbnQgPSBmdW5jdGlvbihldmVudFR5cGUsIGNhbkJ1YmJsZSwgY2FuY2VsYWJsZSkge1xuICB0aGlzLnR5cGUgICAgICAgPSBldmVudFR5cGU7XG4gIHRoaXMuYnViYmxlcyAgICA9IGNhbkJ1YmJsZTtcbiAgdGhpcy5jYW5jZWxhYmxlID0gY2FuY2VsYWJsZTtcbn07XG5cbkV2ZW50LnByb3RvdHlwZS5zdG9wUHJvcGFnYXRpb24gPSBmdW5jdGlvbigpIHt9O1xuRXZlbnQucHJvdG90eXBlLnByZXZlbnREZWZhdWx0ICA9IGZ1bmN0aW9uKCkge307XG5cbkV2ZW50LkNBUFRVUklOR19QSEFTRSA9IDE7XG5FdmVudC5BVF9UQVJHRVQgICAgICAgPSAyO1xuRXZlbnQuQlVCQkxJTkdfUEhBU0UgID0gMztcblxubW9kdWxlLmV4cG9ydHMgPSBFdmVudDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js":
/*!****************************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Event = __webpack_require__(/*! ./event */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\");\n\nvar EventTarget = {\n  onopen:     null,\n  onmessage:  null,\n  onerror:    null,\n  onclose:    null,\n\n  addEventListener: function(eventType, listener, useCapture) {\n    this.on(eventType, listener);\n  },\n\n  removeEventListener: function(eventType, listener, useCapture) {\n    this.removeListener(eventType, listener);\n  },\n\n  dispatchEvent: function(event) {\n    event.target = event.currentTarget = this;\n    event.eventPhase = Event.AT_TARGET;\n\n    if (this['on' + event.type])\n      this['on' + event.type](event);\n\n    this.emit(event.type, event);\n  }\n};\n\nmodule.exports = EventTarget;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmF5ZS13ZWJzb2NrZXQvbGliL2ZheWUvd2Vic29ja2V0L2FwaS9ldmVudF90YXJnZXQuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsWUFBWSxtQkFBTyxDQUFDLG9GQUFTOztBQUU3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXG5vZGVfbW9kdWxlc1xcZmF5ZS13ZWJzb2NrZXRcXGxpYlxcZmF5ZVxcd2Vic29ja2V0XFxhcGlcXGV2ZW50X3RhcmdldC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBFdmVudCA9IHJlcXVpcmUoJy4vZXZlbnQnKTtcblxudmFyIEV2ZW50VGFyZ2V0ID0ge1xuICBvbm9wZW46ICAgICBudWxsLFxuICBvbm1lc3NhZ2U6ICBudWxsLFxuICBvbmVycm9yOiAgICBudWxsLFxuICBvbmNsb3NlOiAgICBudWxsLFxuXG4gIGFkZEV2ZW50TGlzdGVuZXI6IGZ1bmN0aW9uKGV2ZW50VHlwZSwgbGlzdGVuZXIsIHVzZUNhcHR1cmUpIHtcbiAgICB0aGlzLm9uKGV2ZW50VHlwZSwgbGlzdGVuZXIpO1xuICB9LFxuXG4gIHJlbW92ZUV2ZW50TGlzdGVuZXI6IGZ1bmN0aW9uKGV2ZW50VHlwZSwgbGlzdGVuZXIsIHVzZUNhcHR1cmUpIHtcbiAgICB0aGlzLnJlbW92ZUxpc3RlbmVyKGV2ZW50VHlwZSwgbGlzdGVuZXIpO1xuICB9LFxuXG4gIGRpc3BhdGNoRXZlbnQ6IGZ1bmN0aW9uKGV2ZW50KSB7XG4gICAgZXZlbnQudGFyZ2V0ID0gZXZlbnQuY3VycmVudFRhcmdldCA9IHRoaXM7XG4gICAgZXZlbnQuZXZlbnRQaGFzZSA9IEV2ZW50LkFUX1RBUkdFVDtcblxuICAgIGlmICh0aGlzWydvbicgKyBldmVudC50eXBlXSlcbiAgICAgIHRoaXNbJ29uJyArIGV2ZW50LnR5cGVdKGV2ZW50KTtcblxuICAgIHRoaXMuZW1pdChldmVudC50eXBlLCBldmVudCk7XG4gIH1cbn07XG5cbm1vZHVsZS5leHBvcnRzID0gRXZlbnRUYXJnZXQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event_target.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/faye-websocket/lib/faye/websocket/client.js":
/*!******************************************************************!*\
  !*** ./node_modules/faye-websocket/lib/faye/websocket/client.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar util   = __webpack_require__(/*! util */ \"util\"),\n    net    = __webpack_require__(/*! net */ \"net\"),\n    tls    = __webpack_require__(/*! tls */ \"tls\"),\n    url    = __webpack_require__(/*! url */ \"url\"),\n    driver = __webpack_require__(/*! websocket-driver */ \"(ssr)/./node_modules/websocket-driver/lib/websocket/driver.js\"),\n    API    = __webpack_require__(/*! ./api */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api.js\"),\n    Event  = __webpack_require__(/*! ./api/event */ \"(ssr)/./node_modules/faye-websocket/lib/faye/websocket/api/event.js\");\n\nvar DEFAULT_PORTS    = { 'http:': 80, 'https:': 443, 'ws:':80, 'wss:': 443 },\n    SECURE_PROTOCOLS = ['https:', 'wss:'];\n\nvar Client = function(_url, protocols, options) {\n  options = options || {};\n\n  this.url     = _url;\n  this._driver = driver.client(this.url, { maxLength: options.maxLength, protocols: protocols });\n\n  ['open', 'error'].forEach(function(event) {\n    this._driver.on(event, function() {\n      self.headers    = self._driver.headers;\n      self.statusCode = self._driver.statusCode;\n    });\n  }, this);\n\n  var proxy      = options.proxy || {},\n      endpoint   = url.parse(proxy.origin || this.url),\n      port       = endpoint.port || DEFAULT_PORTS[endpoint.protocol],\n      secure     = SECURE_PROTOCOLS.indexOf(endpoint.protocol) >= 0,\n      onConnect  = function() { self._onConnect() },\n      netOptions = options.net || {},\n      originTLS  = options.tls || {},\n      socketTLS  = proxy.origin ? (proxy.tls || {}) : originTLS,\n      self       = this;\n\n  netOptions.host = socketTLS.host = endpoint.hostname;\n  netOptions.port = socketTLS.port = port;\n\n  originTLS.ca = originTLS.ca || options.ca;\n  socketTLS.servername = socketTLS.servername || endpoint.hostname;\n\n  this._stream = secure\n               ? tls.connect(socketTLS, onConnect)\n               : net.connect(netOptions, onConnect);\n\n  if (proxy.origin) this._configureProxy(proxy, originTLS);\n\n  API.call(this, options);\n};\nutil.inherits(Client, API);\n\nClient.prototype._onConnect = function() {\n  var worker = this._proxy || this._driver;\n  worker.start();\n};\n\nClient.prototype._configureProxy = function(proxy, originTLS) {\n  var uri    = url.parse(this.url),\n      secure = SECURE_PROTOCOLS.indexOf(uri.protocol) >= 0,\n      self   = this,\n      name;\n\n  this._proxy = this._driver.proxy(proxy.origin);\n\n  if (proxy.headers) {\n    for (name in proxy.headers) this._proxy.setHeader(name, proxy.headers[name]);\n  }\n\n  this._proxy.pipe(this._stream, { end: false });\n  this._stream.pipe(this._proxy);\n\n  this._proxy.on('connect', function() {\n    if (secure) {\n      var options = { socket: self._stream, servername: uri.hostname };\n      for (name in originTLS) options[name] = originTLS[name];\n      self._stream = tls.connect(options);\n      self._configureStream();\n    }\n    self._driver.io.pipe(self._stream);\n    self._stream.pipe(self._driver.io);\n    self._driver.start();\n  });\n\n  this._proxy.on('error', function(error) {\n    self._driver.emit('error', error);\n  });\n};\n\nmodule.exports = Client;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/faye-websocket/lib/faye/websocket/client.js\n");

/***/ })

};
;