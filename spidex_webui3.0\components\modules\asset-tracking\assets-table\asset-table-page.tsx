import React from "react";
import { query } from "@/lib/api/apollo-client";
import { auth } from "@/lib/auth/auth";
import { gql } from "@apollo/client";
import TaggedAssetTable from "./tagged-assets-table";

const GET_TAGGED_ASSETS = gql`
  query Assets($tenantId: String!, $locationId: String!) {
    taggedAssets(tenantId: $tenantId, locationId: $locationId) {
      statusOnline
      area
      subArea
      proximity
      taggedAssetInfo {
        assetName
        tagExternalId
      }
      assetId
      id
      lastSeen
      areaId
      subAreaId
      gatewayId
      gatewayLintId
      areaGpsPoint {
        latitude
        longitude
      }
    }
    areas(tenantId: $tenantId, locationId: $locationId) {
      id
      name
      gatewayId
    }
    subAreas(tenantId: $tenantId, locationId: $locationId) {
      id
      name
      gatewayId
      gatewayLintId
    }
  }
`;
const GET_LOCATIONS = gql`
  query Locations($tenantId: String!) {
    locations(tenantId: $tenantId) {
      id
      name
    }
  }
`;

export default async function AssetTablePage({
  locationId,
}: {
  locationId: string;
}) {
  const session = await auth();
  const tenantId = session?.user.tenantId;
  const context = {
    headers: { Authorization: `Bearer ${session?.user.token}` },
  };
  const {
    data: { locations = [] },
  } = await query({
    query: GET_LOCATIONS,
    variables: { tenantId },
    context,
  });
  const variables = { tenantId, locationId };
  const {
    data: { taggedAssets = [], areas = [], subAreas = [] },
  } = await query({ query: GET_TAGGED_ASSETS, variables, context });

  const selectedLocation =
    locations.find((loc: any) => loc.id === locationId) || locations[0];

  return (
    <TaggedAssetTable
      data={taggedAssets}
      locations={locations}
      selectedLocationId={selectedLocation.id}
      areas={areas}
      subAreas={subAreas}
    />
  );
}
