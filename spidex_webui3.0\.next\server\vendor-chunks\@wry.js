"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wry";
exports.ids = ["vendor-chunks/@wry"];
exports.modules = {

/***/ "(ssr)/./node_modules/@wry/caches/lib/strong.js":
/*!************************************************!*\
  !*** ./node_modules/@wry/caches/lib/strong.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StrongCache: () => (/* binding */ StrongCache)\n/* harmony export */ });\nfunction defaultDispose() { }\nclass StrongCache {\n    constructor(max = Infinity, dispose = defaultDispose) {\n        this.max = max;\n        this.dispose = dispose;\n        this.map = new Map();\n        this.newest = null;\n        this.oldest = null;\n    }\n    has(key) {\n        return this.map.has(key);\n    }\n    get(key) {\n        const node = this.getNode(key);\n        return node && node.value;\n    }\n    get size() {\n        return this.map.size;\n    }\n    getNode(key) {\n        const node = this.map.get(key);\n        if (node && node !== this.newest) {\n            const { older, newer } = node;\n            if (newer) {\n                newer.older = older;\n            }\n            if (older) {\n                older.newer = newer;\n            }\n            node.older = this.newest;\n            node.older.newer = node;\n            node.newer = null;\n            this.newest = node;\n            if (node === this.oldest) {\n                this.oldest = newer;\n            }\n        }\n        return node;\n    }\n    set(key, value) {\n        let node = this.getNode(key);\n        if (node) {\n            return node.value = value;\n        }\n        node = {\n            key,\n            value,\n            newer: null,\n            older: this.newest\n        };\n        if (this.newest) {\n            this.newest.newer = node;\n        }\n        this.newest = node;\n        this.oldest = this.oldest || node;\n        this.map.set(key, node);\n        return node.value;\n    }\n    clean() {\n        while (this.oldest && this.map.size > this.max) {\n            this.delete(this.oldest.key);\n        }\n    }\n    delete(key) {\n        const node = this.map.get(key);\n        if (node) {\n            if (node === this.newest) {\n                this.newest = node.older;\n            }\n            if (node === this.oldest) {\n                this.oldest = node.newer;\n            }\n            if (node.newer) {\n                node.newer.older = node.older;\n            }\n            if (node.older) {\n                node.older.newer = node.newer;\n            }\n            this.map.delete(key);\n            this.dispose(node.value, key);\n            return true;\n        }\n        return false;\n    }\n}\n//# sourceMappingURL=strong.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wry/caches/lib/strong.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wry/caches/lib/weak.js":
/*!**********************************************!*\
  !*** ./node_modules/@wry/caches/lib/weak.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WeakCache: () => (/* binding */ WeakCache)\n/* harmony export */ });\nfunction noop() { }\nconst defaultDispose = noop;\nconst _WeakRef = typeof WeakRef !== \"undefined\"\n    ? WeakRef\n    : function (value) {\n        return { deref: () => value };\n    };\nconst _WeakMap = typeof WeakMap !== \"undefined\" ? WeakMap : Map;\nconst _FinalizationRegistry = typeof FinalizationRegistry !== \"undefined\"\n    ? FinalizationRegistry\n    : function () {\n        return {\n            register: noop,\n            unregister: noop,\n        };\n    };\nconst finalizationBatchSize = 10024;\nclass WeakCache {\n    constructor(max = Infinity, dispose = defaultDispose) {\n        this.max = max;\n        this.dispose = dispose;\n        this.map = new _WeakMap();\n        this.newest = null;\n        this.oldest = null;\n        this.unfinalizedNodes = new Set();\n        this.finalizationScheduled = false;\n        this.size = 0;\n        this.finalize = () => {\n            const iterator = this.unfinalizedNodes.values();\n            for (let i = 0; i < finalizationBatchSize; i++) {\n                const node = iterator.next().value;\n                if (!node)\n                    break;\n                this.unfinalizedNodes.delete(node);\n                const key = node.key;\n                delete node.key;\n                node.keyRef = new _WeakRef(key);\n                this.registry.register(key, node, node);\n            }\n            if (this.unfinalizedNodes.size > 0) {\n                queueMicrotask(this.finalize);\n            }\n            else {\n                this.finalizationScheduled = false;\n            }\n        };\n        this.registry = new _FinalizationRegistry(this.deleteNode.bind(this));\n    }\n    has(key) {\n        return this.map.has(key);\n    }\n    get(key) {\n        const node = this.getNode(key);\n        return node && node.value;\n    }\n    getNode(key) {\n        const node = this.map.get(key);\n        if (node && node !== this.newest) {\n            const { older, newer } = node;\n            if (newer) {\n                newer.older = older;\n            }\n            if (older) {\n                older.newer = newer;\n            }\n            node.older = this.newest;\n            node.older.newer = node;\n            node.newer = null;\n            this.newest = node;\n            if (node === this.oldest) {\n                this.oldest = newer;\n            }\n        }\n        return node;\n    }\n    set(key, value) {\n        let node = this.getNode(key);\n        if (node) {\n            return (node.value = value);\n        }\n        node = {\n            key,\n            value,\n            newer: null,\n            older: this.newest,\n        };\n        if (this.newest) {\n            this.newest.newer = node;\n        }\n        this.newest = node;\n        this.oldest = this.oldest || node;\n        this.scheduleFinalization(node);\n        this.map.set(key, node);\n        this.size++;\n        return node.value;\n    }\n    clean() {\n        while (this.oldest && this.size > this.max) {\n            this.deleteNode(this.oldest);\n        }\n    }\n    deleteNode(node) {\n        if (node === this.newest) {\n            this.newest = node.older;\n        }\n        if (node === this.oldest) {\n            this.oldest = node.newer;\n        }\n        if (node.newer) {\n            node.newer.older = node.older;\n        }\n        if (node.older) {\n            node.older.newer = node.newer;\n        }\n        this.size--;\n        const key = node.key || (node.keyRef && node.keyRef.deref());\n        this.dispose(node.value, key);\n        if (!node.keyRef) {\n            this.unfinalizedNodes.delete(node);\n        }\n        else {\n            this.registry.unregister(node);\n        }\n        if (key)\n            this.map.delete(key);\n    }\n    delete(key) {\n        const node = this.map.get(key);\n        if (node) {\n            this.deleteNode(node);\n            return true;\n        }\n        return false;\n    }\n    scheduleFinalization(node) {\n        this.unfinalizedNodes.add(node);\n        if (!this.finalizationScheduled) {\n            this.finalizationScheduled = true;\n            queueMicrotask(this.finalize);\n        }\n    }\n}\n//# sourceMappingURL=weak.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wry/caches/lib/weak.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wry/context/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/@wry/context/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slot: () => (/* reexport safe */ _slot_js__WEBPACK_IMPORTED_MODULE_0__.Slot),\n/* harmony export */   asyncFromGen: () => (/* binding */ asyncFromGen),\n/* harmony export */   bind: () => (/* binding */ bind),\n/* harmony export */   noContext: () => (/* binding */ noContext),\n/* harmony export */   setTimeout: () => (/* binding */ setTimeoutWithContext),\n/* harmony export */   wrapYieldingFiberMethods: () => (/* binding */ wrapYieldingFiberMethods)\n/* harmony export */ });\n/* harmony import */ var _slot_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./slot.js */ \"(ssr)/./node_modules/@wry/context/lib/slot.js\");\n\n\nconst { bind, noContext } = _slot_js__WEBPACK_IMPORTED_MODULE_0__.Slot;\n// Like global.setTimeout, except the callback runs with captured context.\n\nfunction setTimeoutWithContext(callback, delay) {\n    return setTimeout(bind(callback), delay);\n}\n// Turn any generator function into an async function (using yield instead\n// of await), with context automatically preserved across yields.\nfunction asyncFromGen(genFn) {\n    return function () {\n        const gen = genFn.apply(this, arguments);\n        const boundNext = bind(gen.next);\n        const boundThrow = bind(gen.throw);\n        return new Promise((resolve, reject) => {\n            function invoke(method, argument) {\n                try {\n                    var result = method.call(gen, argument);\n                }\n                catch (error) {\n                    return reject(error);\n                }\n                const next = result.done ? resolve : invokeNext;\n                if (isPromiseLike(result.value)) {\n                    result.value.then(next, result.done ? reject : invokeThrow);\n                }\n                else {\n                    next(result.value);\n                }\n            }\n            const invokeNext = (value) => invoke(boundNext, value);\n            const invokeThrow = (error) => invoke(boundThrow, error);\n            invokeNext();\n        });\n    };\n}\nfunction isPromiseLike(value) {\n    return value && typeof value.then === \"function\";\n}\n// If you use the fibers npm package to implement coroutines in Node.js,\n// you should call this function at least once to ensure context management\n// remains coherent across any yields.\nconst wrappedFibers = [];\nfunction wrapYieldingFiberMethods(Fiber) {\n    // There can be only one implementation of Fiber per process, so this array\n    // should never grow longer than one element.\n    if (wrappedFibers.indexOf(Fiber) < 0) {\n        const wrap = (obj, method) => {\n            const fn = obj[method];\n            obj[method] = function () {\n                return noContext(fn, arguments, this);\n            };\n        };\n        // These methods can yield, according to\n        // https://github.com/laverdet/node-fibers/blob/ddebed9b8ae3883e57f822e2108e6943e5c8d2a8/fibers.js#L97-L100\n        wrap(Fiber, \"yield\");\n        wrap(Fiber.prototype, \"run\");\n        wrap(Fiber.prototype, \"throwInto\");\n        wrappedFibers.push(Fiber);\n    }\n    return Fiber;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wry/context/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wry/context/lib/slot.js":
/*!***********************************************!*\
  !*** ./node_modules/@wry/context/lib/slot.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slot: () => (/* binding */ Slot)\n/* harmony export */ });\n// This currentContext variable will only be used if the makeSlotClass\n// function is called, which happens only if this is the first copy of the\n// @wry/context package to be imported.\nlet currentContext = null;\n// This unique internal object is used to denote the absence of a value\n// for a given Slot, and is never exposed to outside code.\nconst MISSING_VALUE = {};\nlet idCounter = 1;\n// Although we can't do anything about the cost of duplicated code from\n// accidentally bundling multiple copies of the @wry/context package, we can\n// avoid creating the Slot class more than once using makeSlotClass.\nconst makeSlotClass = () => class Slot {\n    constructor() {\n        // If you have a Slot object, you can find out its slot.id, but you cannot\n        // guess the slot.id of a Slot you don't have access to, thanks to the\n        // randomized suffix.\n        this.id = [\n            \"slot\",\n            idCounter++,\n            Date.now(),\n            Math.random().toString(36).slice(2),\n        ].join(\":\");\n    }\n    hasValue() {\n        for (let context = currentContext; context; context = context.parent) {\n            // We use the Slot object iself as a key to its value, which means the\n            // value cannot be obtained without a reference to the Slot object.\n            if (this.id in context.slots) {\n                const value = context.slots[this.id];\n                if (value === MISSING_VALUE)\n                    break;\n                if (context !== currentContext) {\n                    // Cache the value in currentContext.slots so the next lookup will\n                    // be faster. This caching is safe because the tree of contexts and\n                    // the values of the slots are logically immutable.\n                    currentContext.slots[this.id] = value;\n                }\n                return true;\n            }\n        }\n        if (currentContext) {\n            // If a value was not found for this Slot, it's never going to be found\n            // no matter how many times we look it up, so we might as well cache\n            // the absence of the value, too.\n            currentContext.slots[this.id] = MISSING_VALUE;\n        }\n        return false;\n    }\n    getValue() {\n        if (this.hasValue()) {\n            return currentContext.slots[this.id];\n        }\n    }\n    withValue(value, callback, \n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args, thisArg) {\n        const slots = {\n            __proto__: null,\n            [this.id]: value,\n        };\n        const parent = currentContext;\n        currentContext = { parent, slots };\n        try {\n            // Function.prototype.apply allows the arguments array argument to be\n            // omitted or undefined, so args! is fine here.\n            return callback.apply(thisArg, args);\n        }\n        finally {\n            currentContext = parent;\n        }\n    }\n    // Capture the current context and wrap a callback function so that it\n    // reestablishes the captured context when called.\n    static bind(callback) {\n        const context = currentContext;\n        return function () {\n            const saved = currentContext;\n            try {\n                currentContext = context;\n                return callback.apply(this, arguments);\n            }\n            finally {\n                currentContext = saved;\n            }\n        };\n    }\n    // Immediately run a callback function without any captured context.\n    static noContext(callback, \n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args, thisArg) {\n        if (currentContext) {\n            const saved = currentContext;\n            try {\n                currentContext = null;\n                // Function.prototype.apply allows the arguments array argument to be\n                // omitted or undefined, so args! is fine here.\n                return callback.apply(thisArg, args);\n            }\n            finally {\n                currentContext = saved;\n            }\n        }\n        else {\n            return callback.apply(thisArg, args);\n        }\n    }\n};\nfunction maybe(fn) {\n    try {\n        return fn();\n    }\n    catch (ignored) { }\n}\n// We store a single global implementation of the Slot class as a permanent\n// non-enumerable property of the globalThis object. This obfuscation does\n// nothing to prevent access to the Slot class, but at least it ensures the\n// implementation (i.e. currentContext) cannot be tampered with, and all copies\n// of the @wry/context package (hopefully just one) will share the same Slot\n// implementation. Since the first copy of the @wry/context package to be\n// imported wins, this technique imposes a steep cost for any future breaking\n// changes to the Slot class.\nconst globalKey = \"@wry/context:Slot\";\nconst host = \n// Prefer globalThis when available.\n// https://github.com/benjamn/wryware/issues/347\nmaybe(() => globalThis) ||\n    // Fall back to global, which works in Node.js and may be converted by some\n    // bundlers to the appropriate identifier (window, self, ...) depending on the\n    // bundling target. https://github.com/endojs/endo/issues/576#issuecomment-1178515224\n    maybe(() => global) ||\n    // Otherwise, use a dummy host that's local to this module. We used to fall\n    // back to using the Array constructor as a namespace, but that was flagged in\n    // https://github.com/benjamn/wryware/issues/347, and can be avoided.\n    Object.create(null);\n// Whichever globalHost we're using, make TypeScript happy about the additional\n// globalKey property.\nconst globalHost = host;\nconst Slot = globalHost[globalKey] ||\n    // Earlier versions of this package stored the globalKey property on the Array\n    // constructor, so we check there as well, to prevent Slot class duplication.\n    Array[globalKey] ||\n    (function (Slot) {\n        try {\n            Object.defineProperty(globalHost, globalKey, {\n                value: Slot,\n                enumerable: false,\n                writable: false,\n                // When it was possible for globalHost to be the Array constructor (a\n                // legacy Slot dedup strategy), it was important for the property to be\n                // configurable:true so it could be deleted. That does not seem to be as\n                // important when globalHost is the global object, but I don't want to\n                // cause similar problems again, and configurable:true seems safest.\n                // https://github.com/endojs/endo/issues/576#issuecomment-1178274008\n                configurable: true\n            });\n        }\n        finally {\n            return Slot;\n        }\n    })(makeSlotClass());\n//# sourceMappingURL=slot.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHdyeS9jb250ZXh0L2xpYi9zbG90LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsU0FBUztBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aXNcXERvY3VtZW50c1xcUHJvamVjdHNcXFNwaWRleFxcc3BpZGV4X3dlYnVpMy4wXFxub2RlX21vZHVsZXNcXEB3cnlcXGNvbnRleHRcXGxpYlxcc2xvdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUaGlzIGN1cnJlbnRDb250ZXh0IHZhcmlhYmxlIHdpbGwgb25seSBiZSB1c2VkIGlmIHRoZSBtYWtlU2xvdENsYXNzXG4vLyBmdW5jdGlvbiBpcyBjYWxsZWQsIHdoaWNoIGhhcHBlbnMgb25seSBpZiB0aGlzIGlzIHRoZSBmaXJzdCBjb3B5IG9mIHRoZVxuLy8gQHdyeS9jb250ZXh0IHBhY2thZ2UgdG8gYmUgaW1wb3J0ZWQuXG5sZXQgY3VycmVudENvbnRleHQgPSBudWxsO1xuLy8gVGhpcyB1bmlxdWUgaW50ZXJuYWwgb2JqZWN0IGlzIHVzZWQgdG8gZGVub3RlIHRoZSBhYnNlbmNlIG9mIGEgdmFsdWVcbi8vIGZvciBhIGdpdmVuIFNsb3QsIGFuZCBpcyBuZXZlciBleHBvc2VkIHRvIG91dHNpZGUgY29kZS5cbmNvbnN0IE1JU1NJTkdfVkFMVUUgPSB7fTtcbmxldCBpZENvdW50ZXIgPSAxO1xuLy8gQWx0aG91Z2ggd2UgY2FuJ3QgZG8gYW55dGhpbmcgYWJvdXQgdGhlIGNvc3Qgb2YgZHVwbGljYXRlZCBjb2RlIGZyb21cbi8vIGFjY2lkZW50YWxseSBidW5kbGluZyBtdWx0aXBsZSBjb3BpZXMgb2YgdGhlIEB3cnkvY29udGV4dCBwYWNrYWdlLCB3ZSBjYW5cbi8vIGF2b2lkIGNyZWF0aW5nIHRoZSBTbG90IGNsYXNzIG1vcmUgdGhhbiBvbmNlIHVzaW5nIG1ha2VTbG90Q2xhc3MuXG5jb25zdCBtYWtlU2xvdENsYXNzID0gKCkgPT4gY2xhc3MgU2xvdCB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIC8vIElmIHlvdSBoYXZlIGEgU2xvdCBvYmplY3QsIHlvdSBjYW4gZmluZCBvdXQgaXRzIHNsb3QuaWQsIGJ1dCB5b3UgY2Fubm90XG4gICAgICAgIC8vIGd1ZXNzIHRoZSBzbG90LmlkIG9mIGEgU2xvdCB5b3UgZG9uJ3QgaGF2ZSBhY2Nlc3MgdG8sIHRoYW5rcyB0byB0aGVcbiAgICAgICAgLy8gcmFuZG9taXplZCBzdWZmaXguXG4gICAgICAgIHRoaXMuaWQgPSBbXG4gICAgICAgICAgICBcInNsb3RcIixcbiAgICAgICAgICAgIGlkQ291bnRlcisrLFxuICAgICAgICAgICAgRGF0ZS5ub3coKSxcbiAgICAgICAgICAgIE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnNsaWNlKDIpLFxuICAgICAgICBdLmpvaW4oXCI6XCIpO1xuICAgIH1cbiAgICBoYXNWYWx1ZSgpIHtcbiAgICAgICAgZm9yIChsZXQgY29udGV4dCA9IGN1cnJlbnRDb250ZXh0OyBjb250ZXh0OyBjb250ZXh0ID0gY29udGV4dC5wYXJlbnQpIHtcbiAgICAgICAgICAgIC8vIFdlIHVzZSB0aGUgU2xvdCBvYmplY3QgaXNlbGYgYXMgYSBrZXkgdG8gaXRzIHZhbHVlLCB3aGljaCBtZWFucyB0aGVcbiAgICAgICAgICAgIC8vIHZhbHVlIGNhbm5vdCBiZSBvYnRhaW5lZCB3aXRob3V0IGEgcmVmZXJlbmNlIHRvIHRoZSBTbG90IG9iamVjdC5cbiAgICAgICAgICAgIGlmICh0aGlzLmlkIGluIGNvbnRleHQuc2xvdHMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZSA9IGNvbnRleHQuc2xvdHNbdGhpcy5pZF07XG4gICAgICAgICAgICAgICAgaWYgKHZhbHVlID09PSBNSVNTSU5HX1ZBTFVFKVxuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICBpZiAoY29udGV4dCAhPT0gY3VycmVudENvbnRleHQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gQ2FjaGUgdGhlIHZhbHVlIGluIGN1cnJlbnRDb250ZXh0LnNsb3RzIHNvIHRoZSBuZXh0IGxvb2t1cCB3aWxsXG4gICAgICAgICAgICAgICAgICAgIC8vIGJlIGZhc3Rlci4gVGhpcyBjYWNoaW5nIGlzIHNhZmUgYmVjYXVzZSB0aGUgdHJlZSBvZiBjb250ZXh0cyBhbmRcbiAgICAgICAgICAgICAgICAgICAgLy8gdGhlIHZhbHVlcyBvZiB0aGUgc2xvdHMgYXJlIGxvZ2ljYWxseSBpbW11dGFibGUuXG4gICAgICAgICAgICAgICAgICAgIGN1cnJlbnRDb250ZXh0LnNsb3RzW3RoaXMuaWRdID0gdmFsdWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChjdXJyZW50Q29udGV4dCkge1xuICAgICAgICAgICAgLy8gSWYgYSB2YWx1ZSB3YXMgbm90IGZvdW5kIGZvciB0aGlzIFNsb3QsIGl0J3MgbmV2ZXIgZ29pbmcgdG8gYmUgZm91bmRcbiAgICAgICAgICAgIC8vIG5vIG1hdHRlciBob3cgbWFueSB0aW1lcyB3ZSBsb29rIGl0IHVwLCBzbyB3ZSBtaWdodCBhcyB3ZWxsIGNhY2hlXG4gICAgICAgICAgICAvLyB0aGUgYWJzZW5jZSBvZiB0aGUgdmFsdWUsIHRvby5cbiAgICAgICAgICAgIGN1cnJlbnRDb250ZXh0LnNsb3RzW3RoaXMuaWRdID0gTUlTU0lOR19WQUxVRTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGdldFZhbHVlKCkge1xuICAgICAgICBpZiAodGhpcy5oYXNWYWx1ZSgpKSB7XG4gICAgICAgICAgICByZXR1cm4gY3VycmVudENvbnRleHQuc2xvdHNbdGhpcy5pZF07XG4gICAgICAgIH1cbiAgICB9XG4gICAgd2l0aFZhbHVlKHZhbHVlLCBjYWxsYmFjaywgXG4gICAgLy8gR2l2ZW4gdGhlIHByZXZhbGVuY2Ugb2YgYXJyb3cgZnVuY3Rpb25zLCBzcGVjaWZ5aW5nIGFyZ3VtZW50cyBpcyBsaWtlbHlcbiAgICAvLyB0byBiZSBtdWNoIG1vcmUgY29tbW9uIHRoYW4gc3BlY2lmeWluZyBgdGhpc2AsIGhlbmNlIHRoaXMgb3JkZXJpbmc6XG4gICAgYXJncywgdGhpc0FyZykge1xuICAgICAgICBjb25zdCBzbG90cyA9IHtcbiAgICAgICAgICAgIF9fcHJvdG9fXzogbnVsbCxcbiAgICAgICAgICAgIFt0aGlzLmlkXTogdmFsdWUsXG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHBhcmVudCA9IGN1cnJlbnRDb250ZXh0O1xuICAgICAgICBjdXJyZW50Q29udGV4dCA9IHsgcGFyZW50LCBzbG90cyB9O1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gRnVuY3Rpb24ucHJvdG90eXBlLmFwcGx5IGFsbG93cyB0aGUgYXJndW1lbnRzIGFycmF5IGFyZ3VtZW50IHRvIGJlXG4gICAgICAgICAgICAvLyBvbWl0dGVkIG9yIHVuZGVmaW5lZCwgc28gYXJncyEgaXMgZmluZSBoZXJlLlxuICAgICAgICAgICAgcmV0dXJuIGNhbGxiYWNrLmFwcGx5KHRoaXNBcmcsIGFyZ3MpO1xuICAgICAgICB9XG4gICAgICAgIGZpbmFsbHkge1xuICAgICAgICAgICAgY3VycmVudENvbnRleHQgPSBwYXJlbnQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gQ2FwdHVyZSB0aGUgY3VycmVudCBjb250ZXh0IGFuZCB3cmFwIGEgY2FsbGJhY2sgZnVuY3Rpb24gc28gdGhhdCBpdFxuICAgIC8vIHJlZXN0YWJsaXNoZXMgdGhlIGNhcHR1cmVkIGNvbnRleHQgd2hlbiBjYWxsZWQuXG4gICAgc3RhdGljIGJpbmQoY2FsbGJhY2spIHtcbiAgICAgICAgY29uc3QgY29udGV4dCA9IGN1cnJlbnRDb250ZXh0O1xuICAgICAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgY29uc3Qgc2F2ZWQgPSBjdXJyZW50Q29udGV4dDtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY3VycmVudENvbnRleHQgPSBjb250ZXh0O1xuICAgICAgICAgICAgICAgIHJldHVybiBjYWxsYmFjay5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY3VycmVudENvbnRleHQgPSBzYXZlZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICB9XG4gICAgLy8gSW1tZWRpYXRlbHkgcnVuIGEgY2FsbGJhY2sgZnVuY3Rpb24gd2l0aG91dCBhbnkgY2FwdHVyZWQgY29udGV4dC5cbiAgICBzdGF0aWMgbm9Db250ZXh0KGNhbGxiYWNrLCBcbiAgICAvLyBHaXZlbiB0aGUgcHJldmFsZW5jZSBvZiBhcnJvdyBmdW5jdGlvbnMsIHNwZWNpZnlpbmcgYXJndW1lbnRzIGlzIGxpa2VseVxuICAgIC8vIHRvIGJlIG11Y2ggbW9yZSBjb21tb24gdGhhbiBzcGVjaWZ5aW5nIGB0aGlzYCwgaGVuY2UgdGhpcyBvcmRlcmluZzpcbiAgICBhcmdzLCB0aGlzQXJnKSB7XG4gICAgICAgIGlmIChjdXJyZW50Q29udGV4dCkge1xuICAgICAgICAgICAgY29uc3Qgc2F2ZWQgPSBjdXJyZW50Q29udGV4dDtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY3VycmVudENvbnRleHQgPSBudWxsO1xuICAgICAgICAgICAgICAgIC8vIEZ1bmN0aW9uLnByb3RvdHlwZS5hcHBseSBhbGxvd3MgdGhlIGFyZ3VtZW50cyBhcnJheSBhcmd1bWVudCB0byBiZVxuICAgICAgICAgICAgICAgIC8vIG9taXR0ZWQgb3IgdW5kZWZpbmVkLCBzbyBhcmdzISBpcyBmaW5lIGhlcmUuXG4gICAgICAgICAgICAgICAgcmV0dXJuIGNhbGxiYWNrLmFwcGx5KHRoaXNBcmcsIGFyZ3MpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgY3VycmVudENvbnRleHQgPSBzYXZlZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBjYWxsYmFjay5hcHBseSh0aGlzQXJnLCBhcmdzKTtcbiAgICAgICAgfVxuICAgIH1cbn07XG5mdW5jdGlvbiBtYXliZShmbikge1xuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiBmbigpO1xuICAgIH1cbiAgICBjYXRjaCAoaWdub3JlZCkgeyB9XG59XG4vLyBXZSBzdG9yZSBhIHNpbmdsZSBnbG9iYWwgaW1wbGVtZW50YXRpb24gb2YgdGhlIFNsb3QgY2xhc3MgYXMgYSBwZXJtYW5lbnRcbi8vIG5vbi1lbnVtZXJhYmxlIHByb3BlcnR5IG9mIHRoZSBnbG9iYWxUaGlzIG9iamVjdC4gVGhpcyBvYmZ1c2NhdGlvbiBkb2VzXG4vLyBub3RoaW5nIHRvIHByZXZlbnQgYWNjZXNzIHRvIHRoZSBTbG90IGNsYXNzLCBidXQgYXQgbGVhc3QgaXQgZW5zdXJlcyB0aGVcbi8vIGltcGxlbWVudGF0aW9uIChpLmUuIGN1cnJlbnRDb250ZXh0KSBjYW5ub3QgYmUgdGFtcGVyZWQgd2l0aCwgYW5kIGFsbCBjb3BpZXNcbi8vIG9mIHRoZSBAd3J5L2NvbnRleHQgcGFja2FnZSAoaG9wZWZ1bGx5IGp1c3Qgb25lKSB3aWxsIHNoYXJlIHRoZSBzYW1lIFNsb3Rcbi8vIGltcGxlbWVudGF0aW9uLiBTaW5jZSB0aGUgZmlyc3QgY29weSBvZiB0aGUgQHdyeS9jb250ZXh0IHBhY2thZ2UgdG8gYmVcbi8vIGltcG9ydGVkIHdpbnMsIHRoaXMgdGVjaG5pcXVlIGltcG9zZXMgYSBzdGVlcCBjb3N0IGZvciBhbnkgZnV0dXJlIGJyZWFraW5nXG4vLyBjaGFuZ2VzIHRvIHRoZSBTbG90IGNsYXNzLlxuY29uc3QgZ2xvYmFsS2V5ID0gXCJAd3J5L2NvbnRleHQ6U2xvdFwiO1xuY29uc3QgaG9zdCA9IFxuLy8gUHJlZmVyIGdsb2JhbFRoaXMgd2hlbiBhdmFpbGFibGUuXG4vLyBodHRwczovL2dpdGh1Yi5jb20vYmVuamFtbi93cnl3YXJlL2lzc3Vlcy8zNDdcbm1heWJlKCgpID0+IGdsb2JhbFRoaXMpIHx8XG4gICAgLy8gRmFsbCBiYWNrIHRvIGdsb2JhbCwgd2hpY2ggd29ya3MgaW4gTm9kZS5qcyBhbmQgbWF5IGJlIGNvbnZlcnRlZCBieSBzb21lXG4gICAgLy8gYnVuZGxlcnMgdG8gdGhlIGFwcHJvcHJpYXRlIGlkZW50aWZpZXIgKHdpbmRvdywgc2VsZiwgLi4uKSBkZXBlbmRpbmcgb24gdGhlXG4gICAgLy8gYnVuZGxpbmcgdGFyZ2V0LiBodHRwczovL2dpdGh1Yi5jb20vZW5kb2pzL2VuZG8vaXNzdWVzLzU3NiNpc3N1ZWNvbW1lbnQtMTE3ODUxNTIyNFxuICAgIG1heWJlKCgpID0+IGdsb2JhbCkgfHxcbiAgICAvLyBPdGhlcndpc2UsIHVzZSBhIGR1bW15IGhvc3QgdGhhdCdzIGxvY2FsIHRvIHRoaXMgbW9kdWxlLiBXZSB1c2VkIHRvIGZhbGxcbiAgICAvLyBiYWNrIHRvIHVzaW5nIHRoZSBBcnJheSBjb25zdHJ1Y3RvciBhcyBhIG5hbWVzcGFjZSwgYnV0IHRoYXQgd2FzIGZsYWdnZWQgaW5cbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vYmVuamFtbi93cnl3YXJlL2lzc3Vlcy8zNDcsIGFuZCBjYW4gYmUgYXZvaWRlZC5cbiAgICBPYmplY3QuY3JlYXRlKG51bGwpO1xuLy8gV2hpY2hldmVyIGdsb2JhbEhvc3Qgd2UncmUgdXNpbmcsIG1ha2UgVHlwZVNjcmlwdCBoYXBweSBhYm91dCB0aGUgYWRkaXRpb25hbFxuLy8gZ2xvYmFsS2V5IHByb3BlcnR5LlxuY29uc3QgZ2xvYmFsSG9zdCA9IGhvc3Q7XG5leHBvcnQgY29uc3QgU2xvdCA9IGdsb2JhbEhvc3RbZ2xvYmFsS2V5XSB8fFxuICAgIC8vIEVhcmxpZXIgdmVyc2lvbnMgb2YgdGhpcyBwYWNrYWdlIHN0b3JlZCB0aGUgZ2xvYmFsS2V5IHByb3BlcnR5IG9uIHRoZSBBcnJheVxuICAgIC8vIGNvbnN0cnVjdG9yLCBzbyB3ZSBjaGVjayB0aGVyZSBhcyB3ZWxsLCB0byBwcmV2ZW50IFNsb3QgY2xhc3MgZHVwbGljYXRpb24uXG4gICAgQXJyYXlbZ2xvYmFsS2V5XSB8fFxuICAgIChmdW5jdGlvbiAoU2xvdCkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGdsb2JhbEhvc3QsIGdsb2JhbEtleSwge1xuICAgICAgICAgICAgICAgIHZhbHVlOiBTbG90LFxuICAgICAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgICAgIHdyaXRhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAvLyBXaGVuIGl0IHdhcyBwb3NzaWJsZSBmb3IgZ2xvYmFsSG9zdCB0byBiZSB0aGUgQXJyYXkgY29uc3RydWN0b3IgKGFcbiAgICAgICAgICAgICAgICAvLyBsZWdhY3kgU2xvdCBkZWR1cCBzdHJhdGVneSksIGl0IHdhcyBpbXBvcnRhbnQgZm9yIHRoZSBwcm9wZXJ0eSB0byBiZVxuICAgICAgICAgICAgICAgIC8vIGNvbmZpZ3VyYWJsZTp0cnVlIHNvIGl0IGNvdWxkIGJlIGRlbGV0ZWQuIFRoYXQgZG9lcyBub3Qgc2VlbSB0byBiZSBhc1xuICAgICAgICAgICAgICAgIC8vIGltcG9ydGFudCB3aGVuIGdsb2JhbEhvc3QgaXMgdGhlIGdsb2JhbCBvYmplY3QsIGJ1dCBJIGRvbid0IHdhbnQgdG9cbiAgICAgICAgICAgICAgICAvLyBjYXVzZSBzaW1pbGFyIHByb2JsZW1zIGFnYWluLCBhbmQgY29uZmlndXJhYmxlOnRydWUgc2VlbXMgc2FmZXN0LlxuICAgICAgICAgICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9lbmRvanMvZW5kby9pc3N1ZXMvNTc2I2lzc3VlY29tbWVudC0xMTc4Mjc0MDA4XG4gICAgICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgIHJldHVybiBTbG90O1xuICAgICAgICB9XG4gICAgfSkobWFrZVNsb3RDbGFzcygpKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNsb3QuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wry/context/lib/slot.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wry/equality/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/@wry/equality/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   equal: () => (/* binding */ equal)\n/* harmony export */ });\nconst { toString, hasOwnProperty } = Object.prototype;\nconst fnToStr = Function.prototype.toString;\nconst previousComparisons = new Map();\n/**\n * Performs a deep equality check on two JavaScript values, tolerating cycles.\n */\nfunction equal(a, b) {\n    try {\n        return check(a, b);\n    }\n    finally {\n        previousComparisons.clear();\n    }\n}\n// Allow default imports as well.\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (equal);\nfunction check(a, b) {\n    // If the two values are strictly equal, our job is easy.\n    if (a === b) {\n        return true;\n    }\n    // Object.prototype.toString returns a representation of the runtime type of\n    // the given value that is considerably more precise than typeof.\n    const aTag = toString.call(a);\n    const bTag = toString.call(b);\n    // If the runtime types of a and b are different, they could maybe be equal\n    // under some interpretation of equality, but for simplicity and performance\n    // we just return false instead.\n    if (aTag !== bTag) {\n        return false;\n    }\n    switch (aTag) {\n        case '[object Array]':\n            // Arrays are a lot like other objects, but we can cheaply compare their\n            // lengths as a short-cut before comparing their elements.\n            if (a.length !== b.length)\n                return false;\n        // Fall through to object case...\n        case '[object Object]': {\n            if (previouslyCompared(a, b))\n                return true;\n            const aKeys = definedKeys(a);\n            const bKeys = definedKeys(b);\n            // If `a` and `b` have a different number of enumerable keys, they\n            // must be different.\n            const keyCount = aKeys.length;\n            if (keyCount !== bKeys.length)\n                return false;\n            // Now make sure they have the same keys.\n            for (let k = 0; k < keyCount; ++k) {\n                if (!hasOwnProperty.call(b, aKeys[k])) {\n                    return false;\n                }\n            }\n            // Finally, check deep equality of all child properties.\n            for (let k = 0; k < keyCount; ++k) {\n                const key = aKeys[k];\n                if (!check(a[key], b[key])) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        case '[object Error]':\n            return a.name === b.name && a.message === b.message;\n        case '[object Number]':\n            // Handle NaN, which is !== itself.\n            if (a !== a)\n                return b !== b;\n        // Fall through to shared +a === +b case...\n        case '[object Boolean]':\n        case '[object Date]':\n            return +a === +b;\n        case '[object RegExp]':\n        case '[object String]':\n            return a == `${b}`;\n        case '[object Map]':\n        case '[object Set]': {\n            if (a.size !== b.size)\n                return false;\n            if (previouslyCompared(a, b))\n                return true;\n            const aIterator = a.entries();\n            const isMap = aTag === '[object Map]';\n            while (true) {\n                const info = aIterator.next();\n                if (info.done)\n                    break;\n                // If a instanceof Set, aValue === aKey.\n                const [aKey, aValue] = info.value;\n                // So this works the same way for both Set and Map.\n                if (!b.has(aKey)) {\n                    return false;\n                }\n                // However, we care about deep equality of values only when dealing\n                // with Map structures.\n                if (isMap && !check(aValue, b.get(aKey))) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        case '[object Uint16Array]':\n        case '[object Uint8Array]': // Buffer, in Node.js.\n        case '[object Uint32Array]':\n        case '[object Int32Array]':\n        case '[object Int8Array]':\n        case '[object Int16Array]':\n        case '[object ArrayBuffer]':\n            // DataView doesn't need these conversions, but the equality check is\n            // otherwise the same.\n            a = new Uint8Array(a);\n            b = new Uint8Array(b);\n        // Fall through...\n        case '[object DataView]': {\n            let len = a.byteLength;\n            if (len === b.byteLength) {\n                while (len-- && a[len] === b[len]) {\n                    // Keep looping as long as the bytes are equal.\n                }\n            }\n            return len === -1;\n        }\n        case '[object AsyncFunction]':\n        case '[object GeneratorFunction]':\n        case '[object AsyncGeneratorFunction]':\n        case '[object Function]': {\n            const aCode = fnToStr.call(a);\n            if (aCode !== fnToStr.call(b)) {\n                return false;\n            }\n            // We consider non-native functions equal if they have the same code\n            // (native functions require === because their code is censored).\n            // Note that this behavior is not entirely sound, since !== function\n            // objects with the same code can behave differently depending on\n            // their closure scope. However, any function can behave differently\n            // depending on the values of its input arguments (including this)\n            // and its calling context (including its closure scope), even\n            // though the function object is === to itself; and it is entirely\n            // possible for functions that are not === to behave exactly the\n            // same under all conceivable circumstances. Because none of these\n            // factors are statically decidable in JavaScript, JS function\n            // equality is not well-defined. This ambiguity allows us to\n            // consider the best possible heuristic among various imperfect\n            // options, and equating non-native functions that have the same\n            // code has enormous practical benefits, such as when comparing\n            // functions that are repeatedly passed as fresh function\n            // expressions within objects that are otherwise deeply equal. Since\n            // any function created from the same syntactic expression (in the\n            // same code location) will always stringify to the same code\n            // according to fnToStr.call, we can reasonably expect these\n            // repeatedly passed function expressions to have the same code, and\n            // thus behave \"the same\" (with all the caveats mentioned above),\n            // even though the runtime function objects are !== to one another.\n            return !endsWith(aCode, nativeCodeSuffix);\n        }\n    }\n    // Otherwise the values are not equal.\n    return false;\n}\nfunction definedKeys(obj) {\n    // Remember that the second argument to Array.prototype.filter will be\n    // used as `this` within the callback function.\n    return Object.keys(obj).filter(isDefinedKey, obj);\n}\nfunction isDefinedKey(key) {\n    return this[key] !== void 0;\n}\nconst nativeCodeSuffix = \"{ [native code] }\";\nfunction endsWith(full, suffix) {\n    const fromIndex = full.length - suffix.length;\n    return fromIndex >= 0 &&\n        full.indexOf(suffix, fromIndex) === fromIndex;\n}\nfunction previouslyCompared(a, b) {\n    // Though cyclic references can make an object graph appear infinite from the\n    // perspective of a depth-first traversal, the graph still contains a finite\n    // number of distinct object references. We use the previousComparisons cache\n    // to avoid comparing the same pair of object references more than once, which\n    // guarantees termination (even if we end up comparing every object in one\n    // graph to every object in the other graph, which is extremely unlikely),\n    // while still allowing weird isomorphic structures (like rings with different\n    // lengths) a chance to pass the equality test.\n    let bSet = previousComparisons.get(a);\n    if (bSet) {\n        // Return true here because we can be sure false will be returned somewhere\n        // else if the objects are not equivalent.\n        if (bSet.has(b))\n            return true;\n    }\n    else {\n        previousComparisons.set(a, bSet = new Set);\n    }\n    bSet.add(b);\n    return false;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wry/equality/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@wry/trie/lib/index.js":
/*!*********************************************!*\
  !*** ./node_modules/@wry/trie/lib/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trie: () => (/* binding */ Trie)\n/* harmony export */ });\n// A [trie](https://en.wikipedia.org/wiki/Trie) data structure that holds\n// object keys weakly, yet can also hold non-object keys, unlike the\n// native `WeakMap`.\n// If no makeData function is supplied, the looked-up data will be an empty,\n// null-prototype Object.\nconst defaultMakeData = () => Object.create(null);\n// Useful for processing arguments objects as well as arrays.\nconst { forEach, slice } = Array.prototype;\nconst { hasOwnProperty } = Object.prototype;\nclass Trie {\n    constructor(weakness = true, makeData = defaultMakeData) {\n        this.weakness = weakness;\n        this.makeData = makeData;\n    }\n    lookup() {\n        return this.lookupArray(arguments);\n    }\n    lookupArray(array) {\n        let node = this;\n        forEach.call(array, key => node = node.getChildTrie(key));\n        return hasOwnProperty.call(node, \"data\")\n            ? node.data\n            : node.data = this.makeData(slice.call(array));\n    }\n    peek() {\n        return this.peekArray(arguments);\n    }\n    peekArray(array) {\n        let node = this;\n        for (let i = 0, len = array.length; node && i < len; ++i) {\n            const map = node.mapFor(array[i], false);\n            node = map && map.get(array[i]);\n        }\n        return node && node.data;\n    }\n    remove() {\n        return this.removeArray(arguments);\n    }\n    removeArray(array) {\n        let data;\n        if (array.length) {\n            const head = array[0];\n            const map = this.mapFor(head, false);\n            const child = map && map.get(head);\n            if (child) {\n                data = child.removeArray(slice.call(array, 1));\n                if (!child.data && !child.weak && !(child.strong && child.strong.size)) {\n                    map.delete(head);\n                }\n            }\n        }\n        else {\n            data = this.data;\n            delete this.data;\n        }\n        return data;\n    }\n    getChildTrie(key) {\n        const map = this.mapFor(key, true);\n        let child = map.get(key);\n        if (!child)\n            map.set(key, child = new Trie(this.weakness, this.makeData));\n        return child;\n    }\n    mapFor(key, create) {\n        return this.weakness && isObjRef(key)\n            ? this.weak || (create ? this.weak = new WeakMap : void 0)\n            : this.strong || (create ? this.strong = new Map : void 0);\n    }\n}\nfunction isObjRef(value) {\n    switch (typeof value) {\n        case \"object\":\n            if (value === null)\n                break;\n        // Fall through to return true...\n        case \"function\":\n            return true;\n    }\n    return false;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@wry/trie/lib/index.js\n");

/***/ })

};
;