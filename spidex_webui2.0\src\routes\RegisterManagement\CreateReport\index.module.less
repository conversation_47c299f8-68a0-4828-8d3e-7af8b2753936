@import '../../../Colors.less';

.reportCardsContainer {
  padding: 20px;
  .reportCard {
    min-width: 250px;
    max-width: 280px;
    padding: 10px;
    .reportCardTitle {
      background-color: @color-dark-grey;

      .title {
        color: @white-hex;
        margin: 0 !important;
        padding: 5px !important;
      }
    }
    .cardBody {
      padding: 50px 20px 20px 20px;
      background-color: @light-sky-red-hex;
      height: 300px;
      margin-bottom: 5px;
      .datePicker {
        width: 100% !important;
      }
      .vendorField {
        margin: -15px 0px 1px 0px;
        text-align: center;
      }
      .vendorFieldRadio {
        margin: 1px 0px 1px 0px;
      }
    }
  }
}

.reportHeader {
  flex-direction: column;
  font-size: 22px !important;
}

.reportTable {
  margin-top: 20px;
}
.reportHeading {
  font-size: 22px !important;
}
.selectInputFields {
  text-align: center;
}

.submitButton {
  height: 32px !important;
  padding: 3px 15px !important;
}
.pdfTable {
  table,
  th,
  td {
    margin: 3px;
    padding: 3px;
    border: 1px solid @black-hex;
    border-collapse: collapse;
    word-wrap: normal !important;
    letter-spacing: 0.1rem !important;
    word-break: normal !important;
    white-space: normal !important;
  }
}
