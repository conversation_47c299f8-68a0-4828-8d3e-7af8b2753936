import { Gateway, Asset, Location, Area } from "@/types/asset-tracking";

const SPIDEX_API_BASE = process.env.NEXT_PUBLIC_SPIDEX_API_BASE_URL;

export class SpidexApiService {
  private static instance: SpidexApiService;
  private authToken: string | null = null;
  private tenantId: string | null = null;

  public static getInstance(): SpidexApiService {
    if (!SpidexApiService.instance) {
      SpidexApiService.instance = new SpidexApiService();
    }
    return SpidexApiService.instance;
  }

  /**
   * Set authentication token for API requests
   */
  setAuthToken(token: string | null) {
    this.authToken = token;
  }

  /**
   * Get current authentication token
   */
  getAuthToken(): string | null {
    return this.authToken;
  }

  /**
   * Set tenant ID for API requests
   */
  setTenantId(tenantId: string | null) {
    this.tenantId = tenantId;
  }

  /**
   * Get current tenant ID
   */
  getTenantId(): string | null {
    return this.tenantId;
  }

  /**
   * Check if authentication token and tenant ID are available
   */
  isAuthenticated(): boolean {
    return !!this.authToken && !!this.tenantId;
  }

  /**
   * Validate authentication and tenant ID before making API calls
   */
  private validateAuthentication(): void {
    if (!this.authToken) {
      throw new Error("Authentication token is required. Please log in first.");
    }
    if (!this.tenantId) {
      throw new Error(
        "Tenant ID is required. Please ensure you are properly authenticated."
      );
    }
  }

  /**
   * Get authentication headers with proper Spidex API content-type
   */
  private getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      "Content-Type": "application/api.spidex.v1+json",
      Accept: "application/api.spidex.v1+json",
    };

    if (this.authToken) {
      headers["Authorization"] = `Bearer ${this.authToken}`;
    } else {
      console.warn("⚠️ SpidexApiService: No auth token available for request");
    }

    return headers;
  }

  /**
   * Validate API response and handle common error cases
   */
  private async validateResponse(
    response: Response,
    endpoint: string
  ): Promise<any> {
    if (!response.ok) {
      const errorText = await response.text().catch(() => "Unknown error");

      if (response.status === 401) {
        console.error(`🔒 Authentication failed for ${endpoint}:`, errorText);
        throw new Error(
          `Authentication failed. Please check your credentials and try again.`
        );
      } else if (response.status === 403) {
        console.error(`🚫 Access forbidden for ${endpoint}:`, errorText);
        throw new Error(
          `Access forbidden. You may not have permission to access this resource.`
        );
      } else if (response.status === 404) {
        console.error(`🔍 Resource not found for ${endpoint}:`, errorText);
        throw new Error(
          `Resource not found. The requested data may not exist.`
        );
      } else if (response.status >= 500) {
        console.error(`🔥 Server error for ${endpoint}:`, errorText);
        throw new Error(
          `Server error (${response.status}). Please try again later.`
        );
      } else {
        console.error(
          `❌ HTTP error ${response.status} for ${endpoint}:`,
          errorText
        );
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }
    }

    try {
      const data = await response.json();
      return data;
    } catch (parseError) {
      console.error(
        `📄 Failed to parse JSON response from ${endpoint}:`,
        parseError
      );
      throw new Error(`Invalid response format from server.`);
    }
  }

  /**
   * Fetch all gateways from the Spidex API
   */
  async fetchGateways(): Promise<Gateway[]> {
    try {
      this.validateAuthentication();

      const headers = this.getAuthHeaders();
      const endpoint = `${SPIDEX_API_BASE}/gateway/all?tenantId=${this.tenantId}`;

      console.log(`Fetching gateways from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers,
        signal: AbortSignal.timeout(30000), // 30 second timeout
      });

      const gateways: Gateway[] = await this.validateResponse(
        response,
        "fetchGateways"
      );

      // Filter out deleted gateways and add computed properties
      return gateways
        .filter((gateway) => !gateway.deleted)
        .map((gateway) => ({
          ...gateway,
          active: gateway.provisioned,
          ipAddress: gateway.externalId, // Use externalId as IP for display
          locationId: gateway.locId,
          macId: gateway.externalId,
          createdDate: new Date(gateway.createdTime).toISOString(),
          modifiedDate: new Date(gateway.modifiedTime).toISOString(),
        }));
    } catch (error) {
      console.error("Error fetching gateways:", error);
      throw error;
    }
  }

  /**
   * Fetch all locations from the Spidex API
   */
  async fetchLocations(): Promise<Location[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/location/tId/?tenantId=${this.tenantId}`;

      console.log(`Fetching locations from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000), // 30 second timeout
      });

      const locations: Location[] = await this.validateResponse(
        response,
        "fetchLocations"
      );

      // Filter out deleted locations
      return locations.filter((location) => !location.deleted);
    } catch (error) {
      console.error("Error fetching locations:", error);
      throw error;
    }
  }

  /**
   * Fetch tagged assets from the Spidex API
   */
  async fetchTaggedAssets(): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tgasset/all?tenantId=${this.tenantId}`;

      console.log(`Fetching tagged assets from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000), // 30 second timeout
      });

      const taggedAssets = await this.validateResponse(
        response,
        "fetchTaggedAssets"
      );
      return taggedAssets;
    } catch (error) {
      console.error("Error fetching tagged assets:", error);
      throw error;
    }
  }

  /**
   * Fetch proximity sensor data for a location (this provides the real asset data)
   */
  async fetchProximityDataByLocation(locationId: string): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/sensor/proximity/initial/${this.tenantId}/${locationId}`;

      console.log(`Fetching proximity data from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000), // 30 second timeout
      });

      const proximityData = await this.validateResponse(
        response,
        "fetchProximityDataByLocation"
      );
      return proximityData;
    } catch (error) {
      console.error("Error fetching proximity data:", error);
      // Return empty array if proximity data is not available
      return [];
    }
  }

  /**
   * Extract unique locations from gateways
   */
  extractLocationsFromGateways(gateways: Gateway[]): Location[] {
    const locationMap = new Map<string, Location>();

    gateways.forEach((gateway) => {
      if (gateway.locId && gateway.locName && !locationMap.has(gateway.locId)) {
        locationMap.set(gateway.locId, {
          id: gateway.locId,
          name: gateway.locName,
          description: `Location: ${gateway.locName}`,
          branchId: gateway.tenantId,
          deleted: false,
          createdBy: gateway.createdBy,
          createdDate: new Date(gateway.createdTime).toISOString(),
          modifiedBy: gateway.modifiedBy,
          modifiedDate: new Date(gateway.modifiedTime).toISOString(),
        });
      }
    });

    return Array.from(locationMap.values());
  }

  /**
   * Extract unique areas from gateways and their lints
   */
  extractAreasFromGateways(gateways: Gateway[]): Area[] {
    const areaMap = new Map<string, Area>();

    gateways.forEach((gateway) => {
      // Add main gateway area
      if (gateway.areaId && gateway.areaName && !areaMap.has(gateway.areaId)) {
        areaMap.set(gateway.areaId, {
          id: gateway.areaId,
          name: gateway.areaName,
          description: `Area: ${gateway.areaName}`,
          locationId: gateway.locId,
          deleted: false,
          createdBy: gateway.createdBy,
          createdDate: new Date(gateway.createdTime).toISOString(),
          modifiedBy: gateway.modifiedBy,
          modifiedDate: new Date(gateway.modifiedTime).toISOString(),
        });
      }

      // Add lint areas
      gateway.gatewayLint?.forEach((lint) => {
        if (lint.areaId && lint.areaName && !areaMap.has(lint.areaId)) {
          areaMap.set(lint.areaId, {
            id: lint.areaId,
            name: lint.areaName,
            description: `Lint Area: ${lint.areaName}`,
            locationId: gateway.locId,
            deleted: false,
            createdBy: gateway.createdBy,
            createdDate: new Date(gateway.createdTime).toISOString(),
            modifiedBy: gateway.modifiedBy,
            modifiedDate: new Date(gateway.modifiedTime).toISOString(),
          });
        }
      });
    });

    return Array.from(areaMap.values());
  }

  /**
   * Extract zones from areas (for zone-based view)
   */
  extractZonesFromAreas(
    areas: Area[]
  ): Array<{ id: string; name: string; assetCount: number; color: string }> {
    const colors = [
      "bg-green-500",
      "bg-blue-500",
      "bg-purple-500",
      "bg-orange-500",
      "bg-red-500",
      "bg-indigo-500",
    ];

    return areas.slice(0, 6).map((area, index) => ({
      id: area.id,
      name: area.name,
      assetCount: 0, // Will be calculated dynamically from real asset data
      color: colors[index % colors.length],
    }));
  }

  tableArea(asset: any, gateways: Gateway[]): string {
    const gateway = gateways.find((x) => x.id === asset?.sourceId);
    return gateway?.areaName || `Area-${asset.sourceId}`;
  }

  /**
   * Get zone name for an asset based on sourceId and lintLogId
   * This matches the logic from the old implementation
   */
  getAssetZone(asset: any, gateways: Gateway[]): string {
    if (asset.sourceId === asset.lintLogId) {
      // If sourceId equals lintLogId, use the gateway's area name
      return this.tableArea(asset, gateways);
    } else {
      debugger;
      // Otherwise, find the specific lint area
      const gateway = gateways.find((x) => x.id === asset.sourceId);
      const zone = gateway?.gatewayLint?.find(
        (x) => x.gatLintId === asset.lintLogId
      );
      return zone?.areaName || `Zone-${asset.sourceId}`;
    }
  }

  /**
   * Map proximity data to assets using tagged asset information
   */
  mapProximityDataToAssets(
    proximityData: any[],
    taggedAssets: any[],
    gateways: Gateway[]
  ): Record<string, Asset[]> {
    const tableData: Record<string, Asset[]> = {};

    // Initialize empty arrays for each gateway
    gateways.forEach((gateway) => {
      tableData[gateway.id] = [];
    });

    proximityData.forEach((proximityItem) => {
      // Find the corresponding tagged asset
      const tgAssetLink = taggedAssets.find(
        (x) => x.id === proximityItem.deviceLogId
      );

      if (tgAssetLink) {
        // Create asset with real proximity data and proper zone calculation
        // This matches the exact mapping from the old implementation
        const asset: Asset = {
          id: proximityItem.deviceLogId,
          deviceLogId: proximityItem.deviceLogId,
          devicePhyId: proximityItem.devPhysicalId || proximityItem.devicePhyId,
          macId: tgAssetLink.taggedAssetInfo?.assetExternalId || "",
          deviceId: tgAssetLink.taggedAssetInfo?.tagExternalId || "",
          tagName: tgAssetLink.taggedAssetInfo?.tagName || "",
          name: tgAssetLink.taggedAssetInfo?.assetName || "",
          areaName: this.getAssetArea(proximityItem, gateways),
          zone: this.getAssetZone(proximityItem, gateways),
          key: `${
            proximityItem.devicePhyId || proximityItem.devPhysicalId
          } ${Math.floor(Date.now() / 1000)}`,
          tgAssetLink: tgAssetLink,
          eventTime: proximityItem.eventTime,
          rssi: proximityItem.rssi,
          // Proximity value based on attribute type (matches old implementation)
          proximity:
            proximityItem.attributeType === "int"
              ? proximityItem.proximity
              : proximityItem.attributeValue,
          lastSeen: new Date(proximityItem.eventTime * 1000).toISOString(),
          // Proximity sensor data properties
          sourceId: proximityItem.sourceId,
          lintLogId: proximityItem.lintLogId,
          attributeType: proximityItem.attributeType,
          attributeValue: proximityItem.attributeValue,
        };

        // Add to the appropriate gateway
        const gatewayId = proximityItem.sourceId;
        if (tableData[gatewayId]) {
          tableData[gatewayId].push(asset);
        }
      }
    });

    return tableData;
  }

  /**
   * Map proximity data to assets with deduplication logic (matches old implementation)
   * Each asset can only exist in one gateway at a time
   */
  mapProximityDataToAssetsWithDeduplication(
    proximityData: any[],
    taggedAssets: any[],
    gateways: Gateway[]
  ): Record<string, Asset[]> {
    const tableData: Record<string, Asset[]> = {};

    // Initialize empty arrays for each gateway
    gateways.forEach((gateway) => {
      tableData[gateway.id] = [];
    });

    let processedCount = 0;
    let skippedCount = 0;

    proximityData.forEach((proximityItem) => {
      // Find the corresponding tagged asset
      const tgAssetLink = taggedAssets.find(
        (x) => x.id === proximityItem.deviceLogId
      );

      if (tgAssetLink) {
        processedCount++;

        const deviceKey =
          proximityItem.devPhysicalId || proximityItem.devicePhyId;

        // DEDUPLICATION LOGIC (matches old implementation):
        // Remove asset from all gateways first if it exists anywhere
        Object.keys(tableData).forEach((gatewayId) => {
          tableData[gatewayId] = tableData[gatewayId].filter(
            (asset) => asset.devicePhyId !== deviceKey
          );
        });

        // Create asset with real proximity data and proper zone calculation
        const asset: Asset = {
          id: proximityItem.deviceLogId,
          deviceLogId: proximityItem.deviceLogId,
          devicePhyId: deviceKey,
          macId: tgAssetLink.taggedAssetInfo?.assetExternalId || "",
          deviceId: tgAssetLink.taggedAssetInfo?.tagExternalId || "",
          tagName: tgAssetLink.taggedAssetInfo?.tagName || "",
          name: tgAssetLink.taggedAssetInfo?.assetName || "",
          areaName: this.getAssetArea(proximityItem, gateways),
          zone: this.getAssetZone(proximityItem, gateways),
          key: `${deviceKey} ${Math.floor(Date.now() / 1000)}`,
          tgAssetLink: tgAssetLink,
          eventTime: proximityItem.eventTime,
          rssi: proximityItem.rssi,
          // Proximity value based on attribute type (matches old implementation)
          proximity:
            proximityItem.attributeType === "int"
              ? proximityItem.proximity
              : proximityItem.attributeValue,
          lastSeen: new Date(proximityItem.eventTime * 1000).toISOString(),
          // Proximity sensor data properties
          sourceId: proximityItem.sourceId,
          lintLogId: proximityItem.lintLogId,
          attributeType: proximityItem.attributeType,
          attributeValue: proximityItem.attributeValue,
          // Include lintDtos if available
          lintDtos: proximityItem.lintDtos || [],
        };

        // Add to the specific gateway where it was detected
        const gatewayId = proximityItem.sourceId;
        if (tableData[gatewayId]) {
          tableData[gatewayId].push(asset);

          // Sort assets by name (matches old implementation)
          tableData[gatewayId].sort((a, b) => {
            if (a.name < b.name) return -1;
            if (a.name > b.name) return 1;
            return 0;
          });
        }
      } else {
        skippedCount++;
      }
    });

    return tableData;
  }

  /**
   * Get area name for an asset (used when sourceId === lintLogId)
   */
  getAssetArea(proximityItem: any, gateways: Gateway[]): string {
    const gateway = gateways.find((x) => x.id === proximityItem.sourceId);
    return gateway?.areaName || `Area-${proximityItem.sourceId}`;
  }

  /**
   * Fetch all data needed for asset tracking
   */
  async fetchAssetTrackingData() {
    try {
      // Fetch data from separate API endpoints
      const [gateways, locations] = await Promise.all([
        this.fetchGateways(),
        this.fetchLocations(),
      ]);

      const areas = this.extractAreasFromGateways(gateways);
      const zones = this.extractZonesFromAreas(areas);

      // Initialize empty table data
      let tableData: Record<string, Asset[]> = {};
      gateways.forEach((gateway) => {
        tableData[gateway.id] = [];
      });

      return {
        gateways,
        locations,
        areas,
        zones,
        tableData,
      };
    } catch (error) {
      console.error("Error fetching asset tracking data:", error);
      throw error;
    }
  }

  /**
   * Load proximity data for a specific location (matches old implementation)
   */
  async loadProximityDataForLocation(
    locationId: string
  ): Promise<Record<string, Asset[]>> {
    try {
      // Fetch required data
      const [gateways, taggedAssets, proximityData] = await Promise.all([
        this.fetchGateways(),
        this.fetchTaggedAssets(),
        this.fetchProximityDataByLocation(locationId),
      ]);

      // Initialize empty table data
      const tableData: Record<string, Asset[]> = {};
      gateways.forEach((gateway) => {
        tableData[gateway.id] = [];
      });

      // Process proximity data with deduplication logic (matches old implementation)
      const processedTableData = this.mapProximityDataToAssetsWithDeduplication(
        proximityData,
        taggedAssets,
        gateways
      );

      return processedTableData;
    } catch (error) {
      console.error("Error loading proximity data for location:", error);
      throw error;
    }
  }

  /**
   * Get gateway by ID
   */
  async getGatewayById(gatewayId: string): Promise<Gateway | null> {
    try {
      const gateways = await this.fetchGateways();
      return gateways.find((g) => g.id === gatewayId) || null;
    } catch (error) {
      console.error("Error fetching gateway by ID:", error);
      return null;
    }
  }

  /**
   * Get areas by location ID
   */
  getAreasByLocationId(areas: Area[], locationId: string): Area[] {
    return areas.filter((area) => area.locationId === locationId);
  }

  /**
   * Get gateways by location ID
   */
  getGatewaysByLocationId(gateways: Gateway[], locationId: string): Gateway[] {
    return gateways.filter((gateway) => gateway.locId === locationId);
  }

  /**
   * Extract latitude and longitude from lintDtos attributeValues array
   * @param asset Asset with potential lintDtos data
   * @returns {latitude: number, longitude: number} | null
   */
  extractLocationFromLintDtos(
    asset: Asset
  ): { latitude: number; longitude: number } | null {
    if (!asset.lintDtos || asset.lintDtos.length === 0) {
      return null;
    }

    // Find the most recent lintDto with attributeValues
    const lintWithLocation = asset.lintDtos
      .filter(
        (lint) => lint.attributeValues && lint.attributeValues.length >= 2
      )
      .sort((a, b) => b.lintTime - a.lintTime)[0]; // Most recent first

    if (!lintWithLocation) {
      return null;
    }

    try {
      const latitude = parseFloat(lintWithLocation.attributeValues![0]);
      const longitude = parseFloat(lintWithLocation.attributeValues![1]);

      // Validate coordinates
      if (isNaN(latitude) || isNaN(longitude)) {
        console.warn(
          "Invalid coordinates in lintDtos:",
          lintWithLocation.attributeValues
        );
        return null;
      }

      // Basic coordinate validation (rough world bounds)
      if (
        latitude < -90 ||
        latitude > 90 ||
        longitude < -180 ||
        longitude > 180
      ) {
        console.warn("Coordinates out of valid range:", {
          latitude,
          longitude,
        });
        return null;
      }

      return { latitude, longitude };
    } catch (error) {
      console.error("Error parsing coordinates from lintDtos:", error);
      return null;
    }
  }

  /**
   * Update table data from socket message (matches old implementation)
   */
  updateTableDataFromSocket(
    currentTableData: Record<string, Asset[]>,
    socketMessage: any,
    gateways: Gateway[],
    taggedAssets: any[],
    filterCurrent: boolean = false
  ): Record<string, Asset[]> {
    try {
      // Device key logic (matches old implementation)
      let deviceKey = "devicePhyId";
      if (!filterCurrent) deviceKey = "devPhysicalId";

      // Find the corresponding tagged asset
      const tgAssetLink = taggedAssets.find(
        (x) => x.id === socketMessage.deviceLogId
      );

      if (!tgAssetLink) {
        return currentTableData;
      }

      const updatedTableData = { ...currentTableData };
      const devicePhyId = socketMessage[deviceKey];
      const gatewayId = socketMessage.sourceId;

      // DEDUPLICATION LOGIC (matches old implementation):
      // Remove asset from all gateways first if it exists anywhere
      Object.keys(updatedTableData).forEach((gId) => {
        updatedTableData[gId] = updatedTableData[gId].filter(
          (asset) => asset.devicePhyId !== devicePhyId
        );
      });

      // Create asset with real socket data (matches old implementation)
      const newAsset = { ...socketMessage };
      newAsset.devicePhyId = devicePhyId;
      newAsset.key = `${devicePhyId} ${Math.floor(Date.now() / 1000)}`;
      newAsset.macId = tgAssetLink?.taggedAssetInfo?.assetExternalId;
      newAsset.deviceId = tgAssetLink?.taggedAssetInfo?.tagExternalId;
      newAsset.tagName = tgAssetLink?.taggedAssetInfo?.tagName;
      newAsset.name = tgAssetLink?.taggedAssetInfo?.assetName;
      newAsset.tgAssetLink = { ...tgAssetLink };
      newAsset.areaName = this.getAssetArea(socketMessage, gateways);
      newAsset.zone = this.getAssetZone(socketMessage, gateways);

      // Create asset with proper typing (using processed newAsset data)
      const asset: Asset = {
        id: newAsset.deviceLogId,
        deviceLogId: newAsset.deviceLogId,
        devicePhyId: newAsset.devicePhyId,
        macId: newAsset.macId || "",
        deviceId: newAsset.deviceId || "",
        tagName: newAsset.tagName || "",
        name: newAsset.name || "",
        areaName: newAsset.areaName,
        zone: newAsset.zone,
        key: newAsset.key,
        tgAssetLink: newAsset.tgAssetLink,
        eventTime: newAsset.eventTime,
        rssi: newAsset.rssi,
        proximity:
          newAsset.attributeType === "int"
            ? newAsset.proximity
            : newAsset.attributeValue,
        lastSeen: new Date(newAsset.eventTime * 1000).toISOString(),
        // Socket message properties
        sourceId: newAsset.sourceId,
        lintLogId: newAsset.lintLogId,
        attributeType: newAsset.attributeType,
        attributeValue: newAsset.attributeValue,
        // Include lintDtos from socket message if available
        lintDtos: socketMessage.lintDtos || [],
      };

      // Add to the specific gateway where it was detected
      if (updatedTableData[gatewayId]) {
        updatedTableData[gatewayId].push(asset);

        // Sort assets by name (matches old implementation)
        updatedTableData[gatewayId].sort((a, b) => {
          if (a.name < b.name) return -1;
          if (a.name > b.name) return 1;
          return 0;
        });
      }

      return updatedTableData;
    } catch (error) {
      console.error("Error updating table data from socket:", error);
      return currentTableData;
    }
  }

  // ===== USER MANAGEMENT API METHODS =====

  /**
   * Fetch all user accounts with pagination support
   */
  async getAllAccounts(
    pageNumber: number = 1,
    size: number = 1000
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/user/all`;

      // For client-side pagination, always request a large number to get all records
      // The API doesn't seem to support proper server-side pagination anyway
      const actualSize = size < 500 ? 1000 : size; // Use 1000 for typical page sizes, otherwise use requested size

      const params = new URLSearchParams({
        pageNumber: pageNumber.toString(),
        size: actualSize.toString(),
      });

      console.log(`Fetching accounts from: ${endpoint}?${params}`, {
        originalRequest: { pageNumber, size },
        actualRequest: { pageNumber, size: actualSize },
        reason:
          size < 500
            ? "Using 1000 to get all records for client-side pagination"
            : "Using requested size",
      });

      const response = await fetch(`${endpoint}?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const accounts = await this.validateResponse(response, "getAllAccounts");
      // Return all accounts (including deleted ones) - filtering will be done in the UI layer

      console.log(`API Response Analysis:`, {
        requestedPage: pageNumber,
        requestedSize: size,
        actualSize: actualSize,
        totalReceived: accounts.length,
        deletedCount: accounts.filter((acc: any) => acc.deleted).length,
        activeCount: accounts.filter((acc: any) => !acc.deleted).length,
        apiUrl: `${endpoint}?${params}`,
        isClientSidePagination: size < 500,
        note:
          size < 500
            ? "Requested 1000 records for client-side pagination"
            : "Using requested size",
      });

      return accounts; // Return all accounts including deleted ones
    } catch (error) {
      console.error("Error fetching accounts:", error);
      throw error;
    }
  }

  /**
   * Get a specific account by user ID
   */
  async getAccount(userId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/user/${userId}`;

      console.log(`Fetching account from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "getAccount");
    } catch (error) {
      console.error("Error fetching account:", error);
      throw error;
    }
  }

  /**
   * Create a new user account
   */
  async addAccount(account: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/user/create`;

      console.log(`Creating account at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(account),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "addAccount");
    } catch (error) {
      console.error("Error creating account:", error);
      throw error;
    }
  }

  /**
   * Update an existing user account
   */
  async updateAccount(account: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/user/`;

      console.log(`Updating account at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(account),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "updateAccount");
    } catch (error) {
      console.error("Error updating account:", error);
      throw error;
    }
  }

  /**
   * Delete a user account
   */
  async deleteAccount(userId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/user/${userId}`;

      console.log(`Deleting account at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteAccount");
    } catch (error) {
      console.error("Error deleting account:", error);
      throw error;
    }
  }

  /**
   * Reset user password
   */
  async passwordReset(values: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/user/reset`;

      console.log(`Resetting password at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(values),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "passwordReset");
    } catch (error) {
      console.error("Error resetting password:", error);
      throw error;
    }
  }

  /**
   * Forgot password functionality
   */
  async forgotPassword(values: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/user/forgotPassword`;

      console.log(`Processing forgot password at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(values),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "forgotPassword");
    } catch (error) {
      console.error("Error processing forgot password:", error);
      throw error;
    }
  }

  /**
   * Fetch all roles for user assignment
   */
  async getAllRoles(
    pageNumber: number = 1,
    size: number = 100
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/role/all`;
      const params = new URLSearchParams({
        pageNumber: pageNumber.toString(),
        size: size.toString(),
      });

      console.log(`Fetching roles from: ${endpoint}?${params}`);

      const response = await fetch(`${endpoint}?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const roles = await this.validateResponse(response, "getAllRoles");
      return roles.filter((role: any) => !role.deleted);
    } catch (error) {
      console.error("Error fetching roles:", error);
      throw error;
    }
  }

  /**
   * Fetch all tenants with pagination support
   */
  async getAllTenants(
    pageNumber: number = 1,
    size: number = 1000
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tenant/all`;
      const params = new URLSearchParams({
        pageNumber: pageNumber.toString(),
        size: size.toString(),
      });

      console.log(`Fetching tenants from: ${endpoint}?${params}`);

      const response = await fetch(`${endpoint}?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const tenants = await this.validateResponse(response, "getAllTenants");
      return tenants; // Return all tenants including deleted ones for proper filtering
    } catch (error) {
      console.error("Error fetching tenants:", error);
      throw error;
    }
  }

  /**
   * Get a specific tenant by ID
   */
  async getTenant(tenantId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tenant/${tenantId}`;

      console.log(`Fetching tenant from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "getTenant");
    } catch (error) {
      console.error("Error fetching tenant:", error);
      throw error;
    }
  }

  /**
   * Create a new tenant
   */
  async createTenant(tenantData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tenant/`;

      console.log(`Creating tenant at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(tenantData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "createTenant");
    } catch (error) {
      console.error("Error creating tenant:", error);
      throw error;
    }
  }

  /**
   * Update an existing tenant
   */
  async updateTenant(tenantData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tenant/`;

      console.log(`Updating tenant at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(tenantData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "updateTenant");
    } catch (error) {
      console.error("Error updating tenant:", error);
      throw error;
    }
  }

  /**
   * Delete a tenant (soft delete)
   */
  async deleteTenant(tenantId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tenant/${tenantId}`;

      console.log(`Deleting tenant at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteTenant");
    } catch (error) {
      console.error("Error deleting tenant:", error);
      throw error;
    }
  }

  // ==========================================
  // RBAC Management Methods
  // ==========================================

  /**
   * Get all roles with pagination
   */
  async getAllRolesForRbac(
    pageNumber: number = 1,
    size: number = 100
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/role/all`;
      const params = new URLSearchParams({
        pageNumber: pageNumber.toString(),
        size: size.toString(),
      });

      console.log(`Fetching roles for RBAC from: ${endpoint}?${params}`);

      const response = await fetch(`${endpoint}?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const roles = await this.validateResponse(response, "getAllRolesForRbac");
      return roles || [];
    } catch (error) {
      console.error("Error fetching roles for RBAC:", error);
      throw error;
    }
  }

  /**
   * Create a new role
   */
  async createRole(roleData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/role/`;

      console.log(`Creating role at: ${endpoint}`, roleData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(roleData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "createRole");
    } catch (error) {
      console.error("Error creating role:", error);
      throw error;
    }
  }

  /**
   * Delete a role
   */
  async deleteRole(roleId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/role/${roleId}`;

      console.log(`Deleting role at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteRole");
    } catch (error) {
      console.error("Error deleting role:", error);
      throw error;
    }
  }

  /**
   * Get all pages
   */
  async getAllPages(): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/pageAccess/all`;

      console.log(`Fetching pages from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const pages = await this.validateResponse(response, "getAllPages");
      return pages || [];
    } catch (error) {
      console.error("Error fetching pages:", error);
      throw error;
    }
  }

  /**
   * Create a new page
   */
  async createPage(pageData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/pageAccess/`;

      console.log(`Creating page at: ${endpoint}`, pageData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(pageData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "createPage");
    } catch (error) {
      console.error("Error creating page:", error);
      throw error;
    }
  }

  /**
   * Delete a page
   */
  async deletePage(pageId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/pageAccess/${pageId}`;

      console.log(`Deleting page at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deletePage");
    } catch (error) {
      console.error("Error deleting page:", error);
      throw error;
    }
  }

  /**
   * Get all role-page links
   */
  async getAllPagesRolesLinks(): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/roleAccess/all`;

      console.log(`Fetching role-page links from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const links = await this.validateResponse(
        response,
        "getAllPagesRolesLinks"
      );
      return links || [];
    } catch (error) {
      console.error("Error fetching role-page links:", error);
      throw error;
    }
  }

  /**
   * Create role-page permission link
   */
  async linkPageRole(linkData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/roleAccess/`;

      console.log(`Creating role-page link at: ${endpoint}`, linkData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(linkData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "linkPageRole");
    } catch (error) {
      console.error("Error creating role-page link:", error);
      throw error;
    }
  }

  /**
   * Delete role-page permission link
   */
  async deletePageRoleLink(linkId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/roleAccess/${linkId}`;

      console.log(`Deleting role-page link at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deletePageRoleLink");
    } catch (error) {
      console.error("Error deleting role-page link:", error);
      throw error;
    }
  }

  /**
   * Get role-page links by specific role
   */
  async getPagesRolesLinksByRole(roleId: string): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/roleAccess/role/${roleId}`;

      console.log(`Fetching role-page links by role from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const links = await this.validateResponse(
        response,
        "getPagesRolesLinksByRole"
      );
      return links || [];
    } catch (error) {
      console.error("Error fetching role-page links by role:", error);
      throw error;
    }
  }

  // ==========================================
  // Branch Management Methods
  // ==========================================

  /**
   * Fetch all branches with pagination support
   */
  async getAllBranches(
    pageNumber: number = 1,
    size: number = 1000
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/branch/all`;
      const params = new URLSearchParams({
        tenantId: this.tenantId!,
        pageNumber: pageNumber.toString(),
        size: size.toString(),
      });

      console.log(`Fetching branches from: ${endpoint}?${params}`);

      const response = await fetch(`${endpoint}?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const branches = await this.validateResponse(response, "getAllBranches");
      return branches || [];
    } catch (error) {
      console.error("Error fetching branches:", error);
      throw error;
    }
  }

  /**
   * Create a new branch
   */
  async createBranch(branchData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/branch/`;

      console.log(`Creating branch at: ${endpoint}`, branchData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(branchData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "createBranch");
    } catch (error) {
      console.error("Error creating branch:", error);
      throw error;
    }
  }

  /**
   * Update an existing branch
   */
  async updateBranch(branchData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/branch/`;

      console.log(`Updating branch at: ${endpoint}`, branchData);

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(branchData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "updateBranch");
    } catch (error) {
      console.error("Error updating branch:", error);
      throw error;
    }
  }

  /**
   * Delete a branch (soft delete)
   */
  async deleteBranch(branchId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/branch/${branchId}`;

      console.log(`Deleting branch at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteBranch");
    } catch (error) {
      console.error("Error deleting branch:", error);
      throw error;
    }
  }

  /**
   * Fetch all locations with pagination support
   */
  async getAllLocations(
    pageNumber: number = 1,
    size: number = 1000
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/location/tId/`;
      const params = new URLSearchParams({
        tenantId: this.tenantId!,
        pageNumber: pageNumber.toString(),
        size: size.toString(),
      });

      console.log(`Fetching locations from: ${endpoint}?${params}`);

      const response = await fetch(`${endpoint}?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const locations = await this.validateResponse(
        response,
        "getAllLocations"
      );
      return locations || [];
    } catch (error) {
      console.error("Error fetching locations:", error);
      throw error;
    }
  }

  /**
   * Create a new location
   */
  async createLocation(locationData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/location/`;

      console.log(`Creating location at: ${endpoint}`, locationData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(locationData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "createLocation");
    } catch (error) {
      console.error("Error creating location:", error);
      throw error;
    }
  }

  /**
   * Update an existing location
   */
  async updateLocation(locationData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/location/`;

      console.log(`Updating location at: ${endpoint}`, locationData);

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(locationData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "updateLocation");
    } catch (error) {
      console.error("Error updating location:", error);
      throw error;
    }
  }

  /**
   * Delete a location (soft delete)
   */
  async deleteLocation(locationId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/location/${locationId}`;

      console.log(`Deleting location at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteLocation");
    } catch (error) {
      console.error("Error deleting location:", error);
      throw error;
    }
  }

  // ==========================================
  // AREA MANAGEMENT METHODS
  // ==========================================

  /**
   * Fetch all areas with pagination support
   */
  async getAllAreas(
    tenantId?: string,
    pageNumber: number = 1,
    size: number = 1000
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const targetTenantId = tenantId || this.tenantId;
      const endpoint = `${SPIDEX_API_BASE}/area/tId/?tenantId=${targetTenantId}&pageNumber=${pageNumber}&size=${size}`;

      console.log(`Fetching areas from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const areas = await this.validateResponse(response, "getAllAreas");
      return areas || [];
    } catch (error) {
      console.error("Error fetching areas:", error);
      throw error;
    }
  }

  /**
   * Create a new area
   */
  async createArea(areaData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/area/`;

      console.log(`Creating area: ${endpoint}`, areaData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(areaData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "createArea");
    } catch (error) {
      console.error("Error creating area:", error);
      throw error;
    }
  }

  /**
   * Update an existing area
   */
  async updateArea(areaData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/area/`;

      console.log(`Updating area: ${endpoint}`, areaData);

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(areaData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "updateArea");
    } catch (error) {
      console.error("Error updating area:", error);
      throw error;
    }
  }

  /**
   * Delete an area (soft delete)
   */
  async deleteArea(areaId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/area/${areaId}`;

      console.log(`Deleting area: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteArea");
    } catch (error) {
      console.error("Error deleting area:", error);
      throw error;
    }
  }

  // ==========================================
  // GATEWAY MANAGEMENT METHODS
  // ==========================================

  /**
   * Fetch all gateways with pagination support
   */
  async getAllGateways(
    tenantId?: string,
    pageNumber: number = 1,
    size: number = 1000
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const targetTenantId = tenantId || this.tenantId;
      const endpoint = `${SPIDEX_API_BASE}/gateway/all?tenantId=${targetTenantId}&pageNumber=${pageNumber}&size=${size}`;

      console.log(`Fetching gateways from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const gateways = await this.validateResponse(response, "getAllGateways");
      return gateways || [];
    } catch (error) {
      console.error("Error fetching gateways:", error);
      throw error;
    }
  }

  /**
   * Create a new gateway
   */
  async createGateway(gatewayData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/gateway/`;

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(gatewayData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "createGateway");
    } catch (error) {
      console.error("Error creating gateway:", error);
      throw error;
    }
  }

  /**
   * Update an existing gateway (used for binding to areas)
   */
  async updateGateway(gatewayData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/gateway/`;

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(gatewayData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "updateGateway");
    } catch (error) {
      console.error("Error updating gateway:", error);
      throw error;
    }
  }

  /**
   * Delete a gateway by ID
   */
  async deleteGateway(gatewayId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/gateway/${gatewayId}`;

      console.log(`Deleting gateway: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteGateway");
    } catch (error) {
      console.error("Error deleting gateway:", error);
      throw error;
    }
  }

  /**
   * Fetch all gateway configurations/models
   */
  async getAllGatewayConfigs(): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/deviceModel/all`;

      console.log(`Fetching gateway configs from: ${endpoint}`);
      console.log(`Auth headers:`, this.getAuthHeaders());

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      console.log(`Gateway configs response status: ${response.status}`);

      if (!response.ok) {
        console.error(
          `Gateway configs API error: ${response.status} ${response.statusText}`
        );
        // Try to get error details
        try {
          const errorText = await response.text();
          console.error(`Gateway configs error details:`, errorText);
        } catch (e) {
          console.error(`Could not read error response:`, e);
        }
        return [];
      }

      const response_data = await this.validateResponse(
        response,
        "getAllGatewayConfigs"
      );

      console.log(`Raw gateway configs response:`, response_data);

      // Extract the data array from the response (similar to old application)
      const configs = response_data?.data || response_data || [];

      console.log(`Extracted gateway configs array:`, configs);

      // Filter for gateway configurations only
      const gatewayConfigs = (configs || [])
        .filter((config: any) => config.spdxModelConfig?.gatewayConfig)
        .map((config: any) => ({
          ...config,
          modelId: config.modelId?.toString(), // Ensure modelId is string
        }));

      console.log(`Filtered gateway configs:`, gatewayConfigs);

      return gatewayConfigs;
    } catch (error) {
      console.error("Error fetching gateway configs:", error);
      console.error(
        "Error details:",
        error instanceof Error ? error.message : String(error)
      );
      // Return empty array if configs are not available
      return [];
    }
  }

  // ==========================================
  // TAG MANAGEMENT METHODS
  // ==========================================

  /**
   * Fetch all tags with pagination support
   */
  async getAllTags(
    pageNumber: number = 1,
    size: number = 1000
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tag/all/`;
      const params = new URLSearchParams({
        tenantId: this.tenantId!,
        pageNumber: pageNumber.toString(),
        size: size.toString(),
      });

      console.log(`Fetching tags from: ${endpoint}?${params}`);

      const response = await fetch(`${endpoint}?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const tags = await this.validateResponse(response, "getAllTags");
      return tags || [];
    } catch (error) {
      console.error("Error fetching tags:", error);
      throw error;
    }
  }

  /**
   * Create a new tag
   */
  async createTag(tagData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tag/`;

      console.log(`Creating tag at: ${endpoint}`, tagData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(tagData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "createTag");
    } catch (error) {
      console.error("Error creating tag:", error);
      throw error;
    }
  }

  /**
   * Update an existing tag
   */
  async updateTag(tagData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tag/`;

      console.log(`Updating tag at: ${endpoint}`, tagData);

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(tagData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "updateTag");
    } catch (error) {
      console.error("Error updating tag:", error);
      throw error;
    }
  }

  /**
   * Delete a tag (soft delete)
   */
  async deleteTag(tagId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tag/${tagId}`;

      console.log(`Deleting tag at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteTag");
    } catch (error) {
      console.error("Error deleting tag:", error);
      throw error;
    }
  }

  /**
   * Fetch all tag configurations/models
   */
  async getAllTagConfigs(): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/deviceModel/all`;

      console.log(`Fetching tag configs from: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      console.log(`Tag configs response status: ${response.status}`);

      if (!response.ok) {
        console.error(
          `Tag configs API error: ${response.status} ${response.statusText}`
        );
        // Try to get error details
        try {
          const errorText = await response.text();
          console.error(`Tag configs error details:`, errorText);
        } catch (e) {
          console.error(`Could not read error response:`, e);
        }
        return [];
      }

      const response_data = await this.validateResponse(
        response,
        "getAllTagConfigs"
      );

      console.log(`Raw tag configs response:`, response_data);

      // Extract the data array from the response (similar to old application)
      const configs = response_data?.data || response_data || [];

      console.log(`Extracted configs array:`, configs);

      // Filter for tag configurations only
      const tagConfigs = (configs || [])
        .filter((config: any) => config.spdxModelConfig?.tagConfig)
        .map((config: any) => ({
          ...config,
          modelId: config.modelId?.toString(), // Ensure modelId is string
        }));

      console.log(`Filtered tag configs:`, tagConfigs);

      return tagConfigs;
    } catch (error) {
      console.error("Error fetching tag configs:", error);
      // Return empty array if configs are not available
      return [];
    }
  }

  /**
   * Create bulk tags from CSV data
   */
  async createBulkTags(tagsData: any[]): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tag/all`;

      console.log(`Creating bulk tags at: ${endpoint}`, tagsData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(tagsData),
        signal: AbortSignal.timeout(60000), // Longer timeout for bulk operations
      });

      return await this.validateResponse(response, "createBulkTags");
    } catch (error) {
      console.error("Error creating bulk tags:", error);
      throw error;
    }
  }

  // ===== ASSET MANAGEMENT METHODS =====

  /**
   * Fetch all assets with pagination support
   */
  async getAllAssets(
    pageNumber: number = 1,
    size: number = 1000
  ): Promise<any[]> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/asset/all`;
      const params = new URLSearchParams({
        tenantId: this.tenantId!,
        pageNumber: pageNumber.toString(),
        size: size.toString(),
      });

      console.log(`Fetching assets from: ${endpoint}?${params}`);

      const response = await fetch(`${endpoint}?${params}`, {
        method: "GET",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      const assets = await this.validateResponse(response, "getAllAssets");
      return assets || [];
    } catch (error) {
      console.error("Error fetching assets:", error);
      throw error;
    }
  }

  /**
   * Create a new asset
   */
  async createAsset(assetData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/asset/`;

      console.log(`Creating asset at: ${endpoint}`, assetData);

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(assetData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "createAsset");
    } catch (error) {
      console.error("Error creating asset:", error);
      throw error;
    }
  }

  /**
   * Create a new tagged asset
   */
  async createTaggedAsset(taggedAssetData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tgasset/`;

      const response = await fetch(endpoint, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(taggedAssetData),
        signal: AbortSignal.timeout(30000),
      });

      const result = await this.validateResponse(response, "createTaggedAsset");
      return result;
    } catch (error) {
      console.error("Error creating tagged asset:", error);
      throw error;
    }
  }

  /**
   * Update an existing asset
   */
  async updateAsset(assetData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/asset/`;

      console.log(`Updating asset at: ${endpoint}`, assetData);

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(assetData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "updateAsset");
    } catch (error) {
      console.error("Error updating asset:", error);
      throw error;
    }
  }

  /**
   * Update an existing tagged asset
   */
  async updateTaggedAsset(taggedAssetData: any): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tgasset/`;

      console.log(`Updating tagged asset at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "PUT",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(taggedAssetData),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "updateTaggedAsset");
    } catch (error) {
      console.error("Error updating tagged asset:", error);
      throw error;
    }
  }

  /**
   * Delete a tagged asset
   */
  async deleteTaggedAsset(taggedAssetId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/tgasset/${taggedAssetId}`;

      console.log(`Deleting tagged asset at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteTaggedAsset");
    } catch (error) {
      console.error("Error deleting tagged asset:", error);
      throw error;
    }
  }

  /**
   * Delete an asset (soft delete)
   */
  async deleteAsset(assetId: string): Promise<any> {
    try {
      this.validateAuthentication();

      const endpoint = `${SPIDEX_API_BASE}/asset/${assetId}`;

      console.log(`Deleting asset at: ${endpoint}`);

      const response = await fetch(endpoint, {
        method: "DELETE",
        headers: this.getAuthHeaders(),
        signal: AbortSignal.timeout(30000),
      });

      return await this.validateResponse(response, "deleteAsset");
    } catch (error) {
      console.error("Error deleting asset:", error);
      throw error;
    }
  }
}

export const spidexApi = SpidexApiService.getInstance();
