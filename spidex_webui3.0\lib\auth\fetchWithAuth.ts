"use server";

import { auth } from "@/lib/auth/auth";
import { SPIDEX_API_BASE_URL } from "@/lib/constants";

type FetchOptions = RequestInit & {
  token?: string; // use this and create another wrapper on top for using different token.
};

export default async function fetchWithAuth(
  url: string,
  options?: FetchOptions
): Promise<Response> {
  const session = await auth();
  const user = session?.user;
  const defaultToken = user?.token || "";
  const token = options?.token ?? defaultToken;

  if (!token && url !== "/user/login") {
    throw Error("Auth Token missing!");
  }

  const { ...restOptions } = options ?? {};

  const headers = new Headers(restOptions.headers);
  headers.append("Content-Type", "application/api.spidex.v1+json");
  headers.append("Accept", "application/api.spidex.v1+json");
  headers.append("Authorization", `Bearer ${token}`);
  const fetchOptions: RequestInit = {
    ...restOptions,
    headers: headers,
  };
  const fullUrl = `${SPIDEX_API_BASE_URL}${url}`;

  const res = await fetch(fullUrl, fetchOptions);
  if (!res.ok) {
    let errorMessage: string | undefined;
    try {
      const errorBody = await res.json();
      errorMessage = errorBody.debugMessage;
    } catch {
      console.log("Fetch error: missing error body");
    }
    throw new Error(`Fetch failed (${url}): ${res.status} ${errorMessage}`);
  }
  return res;
}
