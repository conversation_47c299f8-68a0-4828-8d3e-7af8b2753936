/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nextjs-toploader";
exports.ids = ["vendor-chunks/nextjs-toploader"];
exports.modules = {

/***/ "(rsc)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js")

module.exports = createProxy("C:\\Users\\<USER>\\Documents\\Projects\\Spidex\\spidex_webui3.0\\node_modules\\nextjs-toploader\\dist\\index.js")


/***/ }),

/***/ "(ssr)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar J = Object.create;\nvar y = Object.defineProperty;\nvar X = Object.getOwnPropertyDescriptor;\nvar _ = Object.getOwnPropertyNames;\nvar D = Object.getPrototypeOf, G = Object.prototype.hasOwnProperty;\nvar a = (r, o)=>y(r, \"name\", {\n        value: o,\n        configurable: !0\n    });\nvar Q = (r, o)=>{\n    for(var i in o)y(r, i, {\n        get: o[i],\n        enumerable: !0\n    });\n}, M = (r, o, i, g)=>{\n    if (o && typeof o == \"object\" || typeof o == \"function\") for (let c of _(o))!G.call(r, c) && c !== i && y(r, c, {\n        get: ()=>o[c],\n        enumerable: !(g = X(o, c)) || g.enumerable\n    });\n    return r;\n};\nvar N = (r, o, i)=>(i = r != null ? J(D(r)) : {}, M(o || !r || !r.__esModule ? y(i, \"default\", {\n        value: r,\n        enumerable: !0\n    }) : i, r)), V = (r)=>M(y({}, \"__esModule\", {\n        value: !0\n    }), r);\nvar Z = {};\nQ(Z, {\n    default: ()=>Y\n});\nmodule.exports = V(Z);\nvar t = N(__webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\")), v = N(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\")), s = N(__webpack_require__(/*! nprogress */ \"(ssr)/./node_modules/nprogress/nprogress.js\"));\nvar O = a(({ color: r, height: o, showSpinner: i, crawl: g, crawlSpeed: c, initialPosition: L, easing: T, speed: E, shadow: x, template: k, zIndex: H = 1600, showAtBottom: S = !1, showForHashAnchor: z = !0 })=>{\n    let C = \"#29d\", m = r != null ? r : C, K = o != null ? o : 3, W = !x && x !== void 0 ? \"\" : x ? `box-shadow:${x}` : `box-shadow:0 0 10px ${m},0 0 5px ${m}`, j = v.createElement(\"style\", null, `#nprogress{pointer-events:none}#nprogress .bar{background:${m};position:fixed;z-index:${H};${S ? \"bottom: 0;\" : \"top: 0;\"}left:0;width:100%;height:${K}px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;${W};opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:${H};${S ? \"bottom: 15px;\" : \"top: 15px;\"}right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:${m};border-left-color:${m};border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}`), u = a((h)=>new URL(h, window.location.href).href, \"toAbsoluteURL\"), B = a((h, f)=>{\n        let l = new URL(u(h)), b = new URL(u(f));\n        return l.href.split(\"#\")[0] === b.href.split(\"#\")[0];\n    }, \"isHashAnchor\"), F = a((h, f)=>{\n        let l = new URL(u(h)), b = new URL(u(f));\n        return l.hostname.replace(/^www\\./, \"\") === b.hostname.replace(/^www\\./, \"\");\n    }, \"isSameHostName\");\n    return v.useEffect({\n        \"O.useEffect\": ()=>{\n            s.configure({\n                showSpinner: i != null ? i : !0,\n                trickle: g != null ? g : !0,\n                trickleSpeed: c != null ? c : 200,\n                minimum: L != null ? L : .08,\n                easing: T != null ? T : \"ease\",\n                speed: E != null ? E : 200,\n                template: k != null ? k : '<div class=\"bar\" role=\"bar\"><div class=\"peg\"></div></div><div class=\"spinner\" role=\"spinner\"><div class=\"spinner-icon\"></div></div>'\n            });\n            function h(e, d) {\n                let n = new URL(e), p = new URL(d);\n                if (n.hostname === p.hostname && n.pathname === p.pathname && n.search === p.search) {\n                    let w = n.hash, P = p.hash;\n                    return w !== P && n.href.replace(w, \"\") === p.href.replace(P, \"\");\n                }\n                return !1;\n            }\n            a(h, \"isAnchorOfCurrentUrl\");\n            var f = document.querySelectorAll(\"html\");\n            let l = a({\n                \"O.useEffect.l\": ()=>f.forEach({\n                        \"O.useEffect.l\": (e)=>e.classList.remove(\"nprogress-busy\")\n                    }[\"O.useEffect.l\"])\n            }[\"O.useEffect.l\"], \"removeNProgressClass\");\n            function b(e) {\n                for(; e && e.tagName.toLowerCase() !== \"a\";)e = e.parentElement;\n                return e;\n            }\n            a(b, \"findClosestAnchor\");\n            function A(e) {\n                try {\n                    let d = e.target, n = b(d), p = n == null ? void 0 : n.href;\n                    if (p) {\n                        let w = window.location.href, P = n.target === \"_blank\", q = [\n                            \"tel:\",\n                            \"mailto:\",\n                            \"sms:\",\n                            \"blob:\",\n                            \"download:\"\n                        ].some({\n                            \"O.useEffect.A.q\": (I)=>p.startsWith(I)\n                        }[\"O.useEffect.A.q\"]);\n                        if (!F(window.location.href, n.href)) return;\n                        let $ = h(w, p) || B(window.location.href, n.href);\n                        if (!z && $) return;\n                        p === w || P || q || $ || e.ctrlKey || e.metaKey || e.shiftKey || e.altKey || !u(n.href).startsWith(\"http\") ? (s.start(), s.done(), l()) : s.start();\n                    }\n                } catch (d) {\n                    s.start(), s.done();\n                }\n            }\n            a(A, \"handleClick\"), ({\n                \"O.useEffect\": (e)=>{\n                    let d = e.pushState;\n                    e.pushState = ({\n                        \"O.useEffect\": (...n)=>(s.done(), l(), d.apply(e, n))\n                    })[\"O.useEffect\"];\n                }\n            })[\"O.useEffect\"](window.history), ({\n                \"O.useEffect\": (e)=>{\n                    let d = e.replaceState;\n                    e.replaceState = ({\n                        \"O.useEffect\": (...n)=>(s.done(), l(), d.apply(e, n))\n                    })[\"O.useEffect\"];\n                }\n            })[\"O.useEffect\"](window.history);\n            function R() {\n                s.done(), l();\n            }\n            a(R, \"handlePageHide\");\n            function U() {\n                s.done();\n            }\n            return a(U, \"handleBackAndForth\"), window.addEventListener(\"popstate\", U), document.addEventListener(\"click\", A), window.addEventListener(\"pagehide\", R), ({\n                \"O.useEffect\": ()=>{\n                    document.removeEventListener(\"click\", A), window.removeEventListener(\"pagehide\", R), window.removeEventListener(\"popstate\", U);\n                }\n            })[\"O.useEffect\"];\n        }\n    }[\"O.useEffect\"], []), j;\n}, \"NextTopLoader\"), Y = O;\nO.propTypes = {\n    color: t.string,\n    height: t.number,\n    showSpinner: t.bool,\n    crawl: t.bool,\n    crawlSpeed: t.number,\n    initialPosition: t.number,\n    easing: t.string,\n    speed: t.number,\n    template: t.string,\n    shadow: t.oneOfType([\n        t.string,\n        t.bool\n    ]),\n    zIndex: t.number,\n    showAtBottom: t.bool\n}; /**\n *\n * NextTopLoader\n * @license MIT\n * @param {NextTopLoaderProps} props The properties to configure NextTopLoader\n * @returns {React.JSX.Element}\n *\n */  //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nextjs-toploader/dist/index.js\n");

/***/ })

};
;