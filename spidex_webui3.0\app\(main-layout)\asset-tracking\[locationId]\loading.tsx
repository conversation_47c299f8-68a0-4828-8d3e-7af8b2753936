import { Loader2 } from "lucide-react";

export default function Loading() {
  return (
    <div className="flex flex-col items-center justify-center h-[60vh] w-full">
      <Loader2 className="animate-spin text-primary mb-4" size={48} />
      <div className="text-lg font-semibold text-muted-foreground mb-2">
        Loading Asset Tracking Dashboard...
      </div>
      <div className="text-sm text-muted-foreground">
        Fetching assets and locations. Please wait.
      </div>
    </div>
  );
}
