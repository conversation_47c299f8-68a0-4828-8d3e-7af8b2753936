"use client";

import { useState, useEffect } from "react";
import { LocationSearchFilters } from "@/types/location";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, X, Filter } from "lucide-react";

interface LocationSearchPaginationProps {
  searchFilters: LocationSearchFilters;
  showDeleted: boolean;
  totalRecords: number;
  branches?: Array<{ id: string; name: string }>;
  onSearchChange: (filters: Partial<LocationSearchFilters>) => void;
  onClearSearch: () => void;
  onToggleShowDeleted: (show: boolean) => void;
}

export function LocationSearchPagination({
  searchFilters,
  showDeleted,
  totalRecords,
  branches = [],
  onSearchChange,
  onClearSearch,
  onToggleShowDeleted,
}: LocationSearchPaginationProps) {
  const [localSearchTerm, setLocalSearchTerm] = useState(
    searchFilters.searchTerm || ""
  );

  // Update local search term when filters change
  useEffect(() => {
    setLocalSearchTerm(searchFilters.searchTerm || "");
  }, [searchFilters.searchTerm]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange({ searchTerm: localSearchTerm });
  };

  const handleSearchChange = (value: string) => {
    setLocalSearchTerm(value);
    // Debounced search - trigger search after user stops typing
    const timeoutId = setTimeout(() => {
      onSearchChange({ searchTerm: value });
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  const handleClearSearch = () => {
    setLocalSearchTerm("");
    onClearSearch();
  };

  const handleBranchChange = (branchId: string) => {
    onSearchChange({ branchId: branchId === "all" ? "" : branchId });
  };

  const hasActiveFilters =
    searchFilters.searchTerm || searchFilters.branchId || showDeleted;

  return (
    <div className="space-y-4">
      {/* Search Form */}
      <form onSubmit={handleSearchSubmit} className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1">
            <Label htmlFor="search" className="sr-only">
              Search locations
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                type="text"
                placeholder="Search by location name, address, or coordinates..."
                value={localSearchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10 pr-10"
              />
              {localSearchTerm && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleClearSearch}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Branch Filter */}
          <div className="w-full sm:w-48">
            <Label htmlFor="branch-filter" className="sr-only">
              Filter by branch
            </Label>
            <Select
              value={searchFilters.branchId || "all"}
              onValueChange={handleBranchChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="All branches" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Branches</SelectItem>
                {branches.map((branch) => (
                  <SelectItem key={branch.id} value={branch.id}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Clear Filters Button */}
          {hasActiveFilters && (
            <Button
              type="button"
              variant="outline"
              onClick={handleClearSearch}
              className="whitespace-nowrap"
            >
              Clear All
            </Button>
          )}
        </div>
      </form>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 pt-2 border-t">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="showDeleted"
            checked={showDeleted}
            onCheckedChange={onToggleShowDeleted}
          />
          <Label
            htmlFor="showDeleted"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Show Deleted Locations
          </Label>
        </div>

        {/* Current Results Info */}
        <div className="text-sm text-muted-foreground">
          {hasActiveFilters && (
            <>
              Showing {totalRecords} of {totalAllRecords} locations
              {searchFilters.searchTerm && (
                <span className="ml-1">
                  matching &quot;{searchFilters.searchTerm}&quot;
                </span>
              )}
              {searchFilters.branchId && (
                <span className="ml-1">
                  in{" "}
                  {branches.find((b) => b.id === searchFilters.branchId)
                    ?.name || "selected branch"}
                </span>
              )}
              {showDeleted && <span className="ml-1">(deleted only)</span>}
            </>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 pt-2 border-t">
          <span className="text-sm font-medium">Active filters:</span>

          {searchFilters.searchTerm && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: &quot;{searchFilters.searchTerm}&quot;
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSearchChange({ searchTerm: "" })}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {searchFilters.branchId && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Branch:{" "}
              {branches.find((b) => b.id === searchFilters.branchId)?.name ||
                "Unknown"}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSearchChange({ branchId: "" })}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {showDeleted && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Show Deleted
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onToggleShowDeleted(false)}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
