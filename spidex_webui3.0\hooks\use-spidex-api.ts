"use client";

import { useEffect, useMemo } from "react";
import { useSession } from "next-auth/react";
import { SpidexApiService } from "@/lib/api/spidex-api";

/**
 * Hook to get SpidexApiService instance with automatic token management
 */
export function useSpidexApi() {
  const { data: session } = useSession();
  const spidexApi = useMemo(() => SpidexApiService.getInstance(), []);

  // Update token and tenant ID whenever session changes
  useEffect(() => {
    if (session?.user?.token && session?.user?.tenantId) {
      spidexApi.setAuthToken(session.user.token);
      spidexApi.setTenantId(session.user.tenantId);
      console.log(
        "Spidex<PERSON>pi configured with token and tenant ID:",
        session.user.tenantId
      );
    } else {
      spidexApi.setAuthToken(null);
      spidexApi.setTenantId(null);
      console.log("SpidexApi cleared - no session or missing tenant ID");
    }
  }, [session, spidexApi]);

  return spidexApi;
}
