export interface TaggedAssetInfo {
  assetExternalId: string;
  tagExternalId: string;
  tagName: string;
  assetName: string;
  assetType: string;
}

export interface TaggedAsset {
  id: string;
  assetId: string;
  deviceId: string;
  modelId: string;
  status: string;
  provisioned: boolean;
  taggedAssetInfo: TaggedAssetInfo;
  deleted: boolean;
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
  createdTime?: string;
  modifiedTime?: string;
}

export interface CreateTaggedAssetFormData {
  assetId: string;
  deviceId: string;
  status?: string;
}

export interface UpdateTaggedAssetFormData extends CreateTaggedAssetFormData {
  id: string;
}

export interface TaggedAssetSearchFilters {
  searchTerm?: string;
  status?: string;
  provisioned?: boolean;
}

export interface TaggedAssetPaginationParams {
  page: number;
  pageSize: number;
  searchTerm?: string;
  status?: string;
  provisioned?: boolean;
  showDeleted?: boolean;
}

// For dropdown selections
export interface AssetOption {
  id: string;
  name: string;
  externalId: string;
  assetType: string;
}

export interface TagOption {
  id: string;
  name: string;
  externalId: string;
  modelId: string;
}

// API Response types
export interface TaggedAssetApiResponse {
  data: TaggedAsset[];
  total: number;
  page: number;
  pageSize: number;
}

export interface TaggedAssetStatsResponse {
  totalRecords: number;
  activeRecords: number;
  inactiveRecords: number;
  provisionedRecords: number;
  unprovisionedRecords: number;
}
