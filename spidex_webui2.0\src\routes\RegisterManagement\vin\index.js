import React, { useEffect, useState, useContext } from 'react';
import {
  Input,
  Checkbox,
  Table,
  Button,
  Form,
  Popconfirm,
  CommonDrawer,
  CommonCompactView,
  message,
  Row,
  Col,
  Pagination,
  DatePicker,
  Drawer,
  Typography,
  Upload,
  Select,
  Tooltip,
} from '../../../components';
import {
  getAllVinByPagination,
  addVin,
  updateVin,
  deleteVin,
  getVehicleModel,
  addAllVins,
  getAllVehicleModel,
} from '../../../services';
import Context from '../../../context';
import { PlusOutlined, CloudUploadOutlined, DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { BreadcrumbList, PermissionContainer } from '../../../shared';
import { buildCommonApiValues, calcDrawerWidth } from '../../../utils';
import { get } from 'lodash';
import {
  CRUD,
  Pages,
  ModuleNames,
  MastersPageSizeDefault,
  DateFormats,
  VehicleManufacturers,
} from '../../../constants';
import moment from 'moment';
import { Link } from 'react-router-dom';
import s from './index.module.less';

const { Option } = Select;
const { Search } = Input;
const { Title } = Typography;

const Vin = () => {
  const [context, setContext] = useContext(Context);
  const [action, setAction] = useState('new');
  const [totalVinInfo, setTotalVinInfo] = useState({ items: 0, current: 1, pageSize: MastersPageSizeDefault });
  const [showDeleted, setShowDeleted] = useState(false);
  const [visible, setVisible] = useState(false);
  const [vins, setVins] = useState([]);
  const [vinsOriginal, setVinsOriginal] = useState([]);
  const [vinsInfo, setVinsInfo] = useState({});
  const [vinInfo, setVinInfo] = useState(false);
  const [vehicleModels, setVehicleModels] = useState([]);
  const [visibleBulk, setVisibleBulk] = useState(false);
  const [warningMsg, setWarningMsg] = useState('');
  const [csvArray, setCsvArray] = useState([]);
  const [models, setModels] = useState([]);
  const [modelName, setModelName] = useState([]);

  const [form] = Form.useForm();

  const formInitState = { vehicleManufacturer: VehicleManufacturers[0] };
  const [formInitValues, setFormInitValues] = useState({ ...formInitState });

  const openAdd = () => {
    setAction('new');
    setVisible(true);
    const formData = form.getFieldsValue();
    onSelectVehicleManufacturer(formData.vehicleManufacturer);
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const modelFound = models.find((x) => x.id === parseInt(autoSearchId));
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveVinAction({
        createdTime: commonValues.createdTime,
        modifiedTime: commonValues.modifiedTime,
        modifiedBy: commonValues.modifiedBy,
        createdBy: commonValues.createdBy,
        deleted: commonValues.deleted,
        ...values,
        vehicleModel: parseInt(autoSearchId),
        modelName: modelFound.model + '-' + modelFound.variant,
      });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      updateVinCall(values);
      if (type === 'add') form.resetFields({});
      setFormInitValues(null);
      setVisible(false);
    }
  };

  //Save vin data
  const saveVinAction = async (vin) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const newVin = { ...vin };
    addVin(newVin)
      .then((res) => {
        setVins((state) => [res.data, ...state]);
        message.success('Succesfully Added VIN');
        form.resetFields();
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        if (e.response.data.debugMessage) {
          message.error('VIN number is already existed');
          setVisible(true);
        } else {
          message.error('Unable to add VIN, try again later');
        }
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  //update vin data
  const editVinAction = async (vin) => {
    const vehicleModelRes = await getVehicleModel(vin.vehicleModel);
    form.setFieldsValue({
      ...vin,
      manufactureDate: moment(new Date(vin.manufactureDate)),
      vehicleModel: vehicleModelRes.data.model,
      vehicleManufacturer: vehicleModelRes.data.vehicleManufacturer,
    });
    setAction('edit');
    setVinsInfo(vin);
    setVisible(true);
  };

  const updateVinCall = (values) => {
    const data = { ...vinsInfo, ...values, modifiedTime: new Date(), vehicleModel: parseInt(autoSearchId) };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateVin(data)
      .then((res) => {
        setVins((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, res.data);
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        message.success('Succesfully Updated VIN');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update VIN, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  //Delete Vin Data
  const setDeleteVinAction = (vinId, visible) => {
    setVins((state) => {
      const tempData = [...state];
      tempData.find((x) => x.vinNumber === vinId).visible = visible;
      tempData
        .filter((x) => x.vinNumber !== vinId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteVinAction = (vinId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteVin(vinId)
      .then(() => {
        setVins((state) => {
          const tempState = [...state];
          tempState.find((x) => x.vinNumber === vinId).visible = false;
          tempState.find((x) => x.vinNumber === vinId).deleted = true;
          return [...state].filter((x) => x.vinNumber !== vinId);
        });
        message.success('Succesfully Deleted VIN');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete VIN, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const makeActive = (data) => {
    updateVinCall({ ...data, deleted: false });
  };

  const closeAdd = () => {
    setVisible(false);
    setVinInfo(false);
    if (action === 'edit') form.resetFields();
  };

  const clearForm = () => {
    form.resetFields();
  };

  //view vin information
  const viewVinInfo = async (e) => {
    try {
      const vehicleModelRes = await getVehicleModel(e.vehicleModel);
      setVinsInfo({ ...e, vehicleModelName: vehicleModelRes.data.model });
      setVinInfo(true);
    } catch (e) {
      console.log(e);
      message.error('Unable to get VIN details, try again later');
    }
  };

  //get all vin data by pagination
  const onPaginationChange = (e) => {
    init(+e - 1);
  };

  const init = async (page = 0) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const pagination = {
      size: totalVinInfo.pageSize,
      page: page,
    };

    try {
      const vinRes = await getAllVinByPagination(pagination, showDeleted);
      const vinData = vinRes.data?.content || [];
      vinData.forEach((x, index) => {
        x.id = index;
      });
      const modelRes = await getAllVehicleModel();
      const modelData = modelRes.data.filter((x) => x.deleted === false);
      setModels(modelData);
      setVins(vinData);
      setVinsOriginal(vinData);
      setTotalVinInfo((ps) => ({ ...ps, current: page, items: vinRes.data.totalElements }));
    } catch (e) {
      console.log(e);
      message.error('Unable to get VIN details, try again later');
    } finally {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
    }
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, [showDeleted]);

  //If it is vin table set the tableModeNormal to true
  //If it is search table set the tableModeNormal to false
  const [tableModeNormal, setTableModeNormal] = useState(true);
  //onSearch Vin data with vehicle manufacturer,chasis number,vin number,engine number
  const onSearchVinData = async (e) => {
    if (!e.target.value) {
      setVins(vinsOriginal);
      setTableModeNormal(true);
    } else {
      try {
        setVins(() => {
          const tempData = [...vins];
          return tempData
            .filter(
              (x) =>
                x.vehicleManufacturer.toLowerCase().includes(e.target.value.toLowerCase()) ||
                x.chasisNumber.toLowerCase().includes(e.target.value.toLowerCase()) ||
                x.vinNumber.toLowerCase().includes(e.target.value.toLowerCase()) ||
                x.engineNumber.toLowerCase().includes(e.target.value.toLowerCase())
            )
            .filter((x) => x.deleted === false);
        });
        setTableModeNormal(false);
      } catch {
        message.error('Could not retrieve VIN');
      }
    }
  };

  const [autoSearchId, setAutoSearchId] = useState(0);
  const onSelect = (_, option) => {
    setAutoSearchId(option.key.split('-')[0]);
  };

  //Getting all the vehicle models based on vehicle manufacturer selected.
  const onSelectVehicleManufacturer = (val) => {
    const foundVehicleModels = models.filter((x) => x.vehicleManufacturer === val);
    setVehicleModels(foundVehicleModels);
  };

  //Open Bulk VINs
  const openAddBulk = () => {
    setVisibleBulk(true);
  };

  //On closing Bulk VINs
  const closeAddBulk = () => {
    setVisibleBulk(false);
    form.resetFields();
  };

  //Process CSV
  const [isTrue, setIsTrue] = useState(true);
  const processCSV = (str) => {
    let isValid = true;
    const allTextLines = str.split(/\r\n|\n/);
    const headers = allTextLines[0].split(',');
    const headersReq = ['vehicleManufacturer', 'manufactureDate', 'chasisNumber', 'engineNumber', 'vinNumber'];
    const valid = JSON.stringify(headersReq).toLowerCase() === JSON.stringify(headers).toLowerCase();
    if (!valid) {
      setWarningMsg('Headers do not match please refer the sample file.');
      setCsvArray([]);
      isValid = false;
    }
    if (!isValid) {
      setCsvArray([]);
    } else {
      const rows = str.slice(str.indexOf('\n') + 1).split('\n');
      const newArray = rows.map((row) => {
        const values = row.split(',');
        const eachObject = headers.reduce((obj, header, i) => {
          obj[header] = values[i].replace(/[\r]/, '');
          return obj;
        }, {});
        return eachObject;
      });

      setCsvArray(newArray);
    }
    setIsTrue(isValid);
  };

  //Save action for add bulk VINs
  const [selectVehicleModel, setSelectVehicleModel] = useState('');
  const saveAllVinsAction = async () => {
    const commonValues = buildCommonApiValues(context.profile);
    const values = csvArray.map((x) => ({
      ...x,
      createdTime: commonValues.createdTime,
      modifiedTime: commonValues.modifiedTime,
      modifiedBy: commonValues.modifiedBy,
      createdBy: commonValues.createdBy,
      deleted: commonValues.deleted,
      vehicleModel: parseInt(selectVehicleModel),
      manufactureDate: moment(x.manufactureDate, DateFormats.TYPE_8).toISOString(),
      modelName: modelName,
    }));
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addAllVins(values)
      .then((res) => {
        const vinNumberFound = res.data.map((x) => x.status === 'Failed Already Exist');
        vinNumberFound.map((x) => {
          if (x) {
            message.error(`VIN Already Existed,try with new VIN`);
          } else {
            setVins((state) => [res.data, ...state]);
            setVisibleBulk(false);
            message.success('Succesfully Added VINs');
          }
        });
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add VINS, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const tableCols = [
    { title: <strong> VIN Number </strong>, key: 'vinNumber', dataIndex: 'vinNumber' },
    { title: <strong> Model </strong>, key: 'modelName', dataIndex: 'modelName' },
    { title: <strong> Vehicle Manufacturer </strong>, key: 'vehicleManufacturer', dataIndex: 'vehicleManufacturer' },
    {
      title: <strong> Actions </strong>,
      width: 350,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <Button type="link" className="actionButton" onClick={() => makeActive(record)}>
                Activate
              </Button>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.VIN} permission={CRUD.VIEW}>
              <Button type="link" className="actionButton" onClick={() => viewVinInfo(record)}>
                View
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.VIN} permission={CRUD.UPDATE}>
              <Button type="link" className="actionButton" onClick={() => editVinAction(record)}>
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.VIN} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete VIN ${record.vehicleManufacturer}?`}
                visible={record.visible || false}
                onConfirm={() => deleteVinAction(record.vinNumber)}
                onCancel={() => setDeleteVinAction(record.vinNumber, false)}
              >
                <Button type="link" className="actionButton" onClick={() => setDeleteVinAction(record.vinNumber, true)}>
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  //Table for bulk uploading VINs
  const bulkUploadVinCols = [
    { title: 'VIN Number', key: 'vinNumber', dataIndex: 'vinNumber' },
    { title: 'Vehicle Manufacturer', key: 'vehicleManufacturer', dataIndex: 'vehicleManufacturer' },
    {
      title: 'vehicleModel',
      render: () => modelName,
    },
    { title: 'Chasis Number', key: 'chasisNumber', dataIndex: 'chasisNumber' },
    { title: 'Engine Number', key: 'engineNumber', dataIndex: 'engineNumber' },
  ];

  //vin breadcrumbs name
  const vinBreadcrumbsName = get(context.tenantProfile, `moduleNames[${Pages.VIN}].displayName`, ModuleNames.VIN);

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.MASTERS, vinBreadcrumbsName, 'Register VIN']} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.VIN} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add VIN
            </Button>
          </PermissionContainer>
          <PermissionContainer page={Pages.VIN} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAddBulk}>
              Bulk VINs <CloudUploadOutlined />
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <Row className="searchDiv" justify="end">
        <Col xs={24} lg={6}>
          <Search placeholder="Enter VIN,Manufacturer" allowClear enterButton onChange={onSearchVinData} />
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <>
            {tableModeNormal ? (
              <>
                <Table
                  size="small"
                  scroll={{ x: true }}
                  rowKey="id"
                  columns={tableCols}
                  dataSource={vins}
                  rowClassName={(record) => record.deleted && 'rowInactive'}
                  pagination={false}
                />
                <Row justify="end">
                  <Col>
                    <div className="m-2">
                      <Pagination
                        onChange={onPaginationChange}
                        current={totalVinInfo.current + 1}
                        total={totalVinInfo.items}
                        defaultPageSize={totalVinInfo.pageSize}
                        showSizeChanger={false}
                        showQuickJumper={false}
                      />
                    </div>
                  </Col>
                </Row>
              </>
            ) : (
              <Table
                size="small"
                scroll={{ x: true }}
                rowKey="id"
                columns={tableCols}
                dataSource={vins}
                rowClassName={(record) => record.deleted && 'rowInactive'}
              />
            )}
          </>
        ) : (
          <>
            <CommonCompactView
              data={vins}
              onEdit={editVinAction}
              onDelete={deleteVinAction}
              permissions={[
                { pageName: Pages.VIN, permission: CRUD.UPDATE, label: 'Edit' },
                { pageName: Pages.VIN, permission: CRUD.DELETE, label: 'Delete' },
              ]}
              title="vehicleManufacturer"
              dataList={[{ label: 'Vehicle Manufacturer', value: 'vehicleManufacturer' }]}
            />
            <Row justify="end">
              <Col>
                <div className="m-2">
                  <Pagination
                    onChange={onPaginationChange}
                    current={totalVinInfo.current + 1}
                    total={totalVinInfo.items}
                    defaultPageSize={totalVinInfo.pageSize}
                    showSizeChanger={false}
                    showQuickJumper={false}
                  />
                </div>
              </Col>
            </Row>
          </>
        )}
      </>

      <CommonDrawer title="VIN Information" visible={vinInfo} closeAdd={closeAdd}>
        <div className="infoDrawer">
          {vinsInfo ? (
            <Row className="infoContainer" span={24}>
              <Col className="info" span={24}>
                <Row>
                  <Col className="infoTitle" span={10}>
                    VIN Number :
                  </Col>
                  <Col>{vinsInfo.vinNumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Model :
                  </Col>
                  <Col>{vinsInfo.modelName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Chasis Number :
                  </Col>
                  <Col>{vinsInfo.chasisNumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Engine Number :
                  </Col>
                  <Col>{vinsInfo.engineNumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Manufacture Date :
                  </Col>
                  <Col>{new Date(vinsInfo.manufactureDate).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Vehicle Model :
                  </Col>
                  <Col>{vinsInfo.vehicleModelName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Vehicle Manufacturer :
                  </Col>
                  <Col>{vinsInfo.vehicleManufacturer}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Deleted :
                  </Col>
                  <Col>{String(vinsInfo.deleted)}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created By :
                  </Col>
                  <Col>{vinsInfo.createdBy}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created Time :
                  </Col>
                  <Col>{new Date(vinsInfo.createdTime).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified Time :
                  </Col>
                  <Col>{new Date(vinsInfo.modifiedTime).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified By :
                  </Col>
                  <Col>{vinsInfo.modifiedBy}</Col>
                </Row>
                <br />
                <Row justify="center">
                  <Button type="primary" onClick={() => editVinAction(vinsInfo)}>
                    Edit
                  </Button>
                </Row>
              </Col>
            </Row>
          ) : (
            <></>
          )}
        </div>
      </CommonDrawer>

      <CommonDrawer title="VIN" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          autoComplete={'new-password'}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 16 }}
          labelCol={{ span: 8 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="VIN Number"
            name="vinNumber"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input VIN number !',
              },
            ]}
          >
            <Input placeholder="VIN Number" disabled={action === 'edit' ? true : false} />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Chasis Number"
            name="chasisNumber"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input chasis number !',
              },
            ]}
          >
            <Input placeholder="Chasis Number" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="Engine Number" name="engineNumber">
            <Input placeholder="Engine Number" />
          </Form.Item>
          <Form.Item shouldUpdate={true} hasFeedback label="Manufacture Date" name="manufactureDate">
            <DatePicker format={DateFormats.TYPE_4} placeholder="DD-MM-YYYY" />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Vehicle Manufacturer"
            name="vehicleManufacturer"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input vehicle manufacturer!',
              },
            ]}
          >
            <Select
              placeholder="Vehicle Manufacturer"
              onSelect={(val, option) => onSelectVehicleManufacturer(val, option)}
            >
              {VehicleManufacturers.map((m) => (
                <Option title={m} key={m} value={m}>
                  {m}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Vehicle Model"
            name="vehicleModel"
            rules={[
              {
                required: true,
                message: 'Please input vehicle model!',
              },
            ]}
          >
            <Select onSelect={(val, option) => onSelect(val, option)} placeholder="Vehicle Model">
              {vehicleModels.map((a) => (
                <Select.Option key={a.id} value={`${a.id}-${a.variant}`}>
                  {a.model}-{a.variant}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
      <Drawer
        width={calcDrawerWidth(true)}
        className="commonDrawer"
        onClose={closeAddBulk}
        bodyStyle={{ padding: 0 }}
        visible={visibleBulk}
        getContainer={false}
      >
        <Title className="title">Bulk VINs</Title>
        <Row justify="start" gutter={6} className="content">
          <Col>
            <Link to="/sample_format_for_vin_upload.csv" target="_blank" download>
              <Button type="link">
                Sample File
                <DownloadOutlined />
              </Button>
            </Link>
          </Col>
          <Col>
            <Upload
              accept=".csv"
              multiple={false}
              maxCount={1}
              beforeUpload={(file) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                  processCSV(e.target.result);
                };
                reader.readAsText(file);
                return false;
              }}
            >
              <Button icon={<UploadOutlined />}> Upload</Button>
            </Upload>
          </Col>
          <Col>
            <h4 className={s.warningInfo}>{!isTrue && warningMsg}</h4>
          </Col>
          <Col>
            <Select
              showSearch
              allowClear
              placeholder="Vehicle Model"
              onSelect={(e, option) => {
                setModelName(e);
                setSelectVehicleModel(option.key);
              }}
              onClear={() => setSelectVehicleModel('')}
              style={{ width: context.isCompact ? '100%' : 200 }}
            >
              {models.map((a) => (
                <Option key={a.id} value={`${a.model}-${a.variant}`}>
                  <Tooltip placement="right" title={`${a.model}-${a.variant}`}>
                    {a.model}-{a.variant}
                  </Tooltip>
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
        <div className={s.tableContainer}>
          <Table
            size="small"
            scroll={{ x: true }}
            rowKey="externalId"
            loading={context.isLoading}
            columns={bulkUploadVinCols}
            dataSource={csvArray}
            rowClassName={(record) => record.deleted && 'rowInactive'}
            className="m-4"
          />
          <Row gutter={10} className={`${s.btnContainer} m-3`}>
            <Col lg={{ span: 4 }}>
              <Button block type="primary" onClick={saveAllVinsAction} disabled={selectVehicleModel === ''}>
                Create Bulk VINs
              </Button>
            </Col>
            <Col lg={{ span: 4 }}>
              <Button block danger type="primary" onClick={closeAddBulk}>
                Close
              </Button>
            </Col>
          </Row>
        </div>
      </Drawer>
    </>
  );
};

export default Vin;
