"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DataTablePagination } from "@/components/ui/data-table-pagination";
import { Location } from "@/types/asset-tracking";
import { ColumnDef } from "@tanstack/react-table";
import { formatDistanceToNowStrict, format as formatDateTime } from "date-fns";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";
import React, { useMemo } from "react";
import AssetMetricsBar from "./asset-metrics-bar";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";

import { Input } from "@/components/ui/input";
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  flexRender,
  SortingState,
} from "@tanstack/react-table";
import { ChevronUp, ChevronDown, ChevronsUpDown } from "lucide-react";
import useAssetProximitySocket from "@/hooks/use-asset-proximity-socket";
import { useState, useCallback, useEffect } from "react";
import { SocketStatusIndicator } from "@/components/ui/socket-status-indicator";
import AssetSheet from "./asset-sheet";
import Link from "next/link";

export type TaggedAsset = {
  id: string;
  area: string;
  subArea: string;
  proximity: number;
  taggedAssetInfo: any;
  assetId: string;
  lastSeen: number;
  areaId: string;
  subAreaId: string;
  gatewayId: string;
  gatewayLintId: string;
  statusOnline?: string;
  areaGpsPoint?: { latitude: string; longitude: string } | null;
};

export const columns: ColumnDef<TaggedAsset>[] = [
  {
    id: "assetName",
    accessorKey: "taggedAssetInfo.assetName",
    header: "Asset Name",
    enableSorting: true,
    cell: ({ row }) => <AssetNameCell row={row} />,
  },
  {
    id: "tagExternalId",
    accessorKey: "taggedAssetInfo.tagExternalId",
    header: "Device ID",
    enableSorting: true,
  },
  {
    id: "statusOnline",
    accessorKey: "statusOnline",
    header: "Status",
    enableSorting: true,
    cell: ({ getValue }) => {
      const status = String(getValue<string>()).toLowerCase();
      const isOnline = status === "online";
      return isOnline ? (
        <Badge
          variant="secondary"
          className="bg-green-100 text-green-800 border-green-200"
        >
          Online
        </Badge>
      ) : (
        <Badge
          variant="secondary"
          className="bg-red-100 text-red-800 border-red-200"
        >
          Offline
        </Badge>
      );
    },
  },
  {
    id: "area",
    accessorKey: "area",
    header: "Area",
    enableSorting: true,
    cell: ({ getValue }) => (
      <Badge
        variant="secondary"
        className="bg-blue-100 text-blue-800 border-blue-200"
      >
        {getValue<string>()}
      </Badge>
    ),
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || filterValue === "__all__") return true;
      return row.original.areaId === filterValue;
    },
    enableColumnFilter: true,
  },
  {
    id: "subArea",
    accessorKey: "subArea",
    header: "Sub Area",
    enableSorting: true,
    cell: ({ getValue }) => (
      <Badge
        variant="secondary"
        className="bg-purple-100 text-purple-800 border-purple-200"
      >
        {getValue<string>()}
      </Badge>
    ),
    filterFn: (row, columnId, filterValue) => {
      if (!filterValue || filterValue === "__all__") return true;
      return row.original.subAreaId === filterValue;
    },
    enableColumnFilter: true,
  },
  {
    id: "proximity",
    accessorKey: "proximity",
    header: "Proximity",
    enableSorting: true,
    cell: ({ getValue }) => (
      <Badge
        variant="secondary"
        className="bg-yellow-100 text-yellow-800 border-yellow-200"
      >
        {getValue<number>()} m
      </Badge>
    ),
  },
  {
    id: "lastSeen",
    accessorKey: "lastSeen",
    header: "Last Seen",
    enableSorting: true,
    sortingFn: (rowA, rowB, columnId) => {
      const a = rowA.getValue<number>(columnId);
      const b = rowB.getValue<number>(columnId);
      return a === b ? 0 : a > b ? 1 : -1;
    },
    cell: ({ getValue }) => {
      const value = getValue<number>();
      const date = value ? new Date(value * 1000) : null;
      return (
        <span
          className="text-muted-foreground"
          title={date ? formatDateTime(date, "yyyy-MM-dd HH:mm:ss") : ""}
        >
          {date ? formatDistanceToNowStrict(date, { addSuffix: true }) : "-"}
        </span>
      );
    },
    filterFn: (row, columnId, filterValue) => {
      if (filterValue !== "alerts") return true;
      const lastSeen = row.getValue<number>(columnId);
      if (!lastSeen) return false;
      const now = Math.floor(Date.now() / 1000);
      const alertThreshold = 60 * 30;
      return now - lastSeen > alertThreshold;
    },
  },
];

function AssetNameCell({ row }: { row: any }) {
  const assetName = row.original.taggedAssetInfo?.assetName;
  const id = row.original.id;
  const areaGpsPoint = row.original.areaGpsPoint;
  const [open, setOpen] = React.useState(false);
  return (
    <>
      <span
        className="text-primary underline cursor-pointer hover:text-primary/80"
        onClick={() => setOpen(true)}
      >
        {assetName}
      </span>
      <AssetSheet
        open={open}
        onOpenChange={setOpen}
        assetName={assetName}
        assetId={id}
        areaGpsPoint={areaGpsPoint}
      />
    </>
  );
}

export default function TaggedAssetTable({
  data,
  locations,
  selectedLocationId,
  areas,
  subAreas,
}: {
  data: TaggedAsset[];
  locations: Location[];
  selectedLocationId: string;
  areas: {
    id: string;
    name: string;
    gatewayId?: string;
    gpsPoint?: { latitude: string; longitude: string } | null;
  }[];
  subAreas: { id: string; name: string; gatewayLintId: string }[];
}) {
  const SOCKET_URL = process.env.NEXT_PUBLIC_SPIDEX_SOCKET_URI || "";
  const [liveData, setLiveData] = useState<TaggedAsset[]>(data);
  useEffect(() => {
    setLiveData(data);
  }, [data]);

  const handleProximityEvent = useCallback(
    (event: any) => {
      setLiveData((prev) => {
        const idx = prev.findIndex((row) => row.id === event.deviceLogId);
        if (idx === -1) return prev;
        const updated = [...prev];
        const row = { ...updated[idx] };
        row.gatewayId = event.sourceId;
        row.gatewayLintId = event.lintLogId;
        const areaEntry = areas.find((a) => a.gatewayId === event.sourceId);
        if (areaEntry) {
          row.area = areaEntry.name;
          row.areaId = areaEntry.id;
          if (areaEntry.gpsPoint) {
            row.areaGpsPoint = areaEntry.gpsPoint;
          }
        }
        if (event.lintLogId && event.lintLogId !== event.sourceId) {
          const subAreaEntry = subAreas.find(
            (s) => s.gatewayLintId === event.lintLogId
          );
          if (subAreaEntry) {
            row.subArea = subAreaEntry.name;
            row.subAreaId = subAreaEntry.id;
          }
        } else {
          row.subArea = "";
          row.subAreaId = "";
        }
        row.proximity = event.proximity;
        row.lastSeen = event.eventTime;
        const now = Math.floor(Date.now() / 1000);
        if (row.lastSeen && now - row.lastSeen < 30) {
          row.statusOnline = "online";
        } else {
          row.statusOnline = "offline";
        }
        updated[idx] = row;
        return updated;
      });
    },
    [areas, subAreas]
  );

  const gatewayIds = React.useMemo(
    () => areas.filter((a) => a.gatewayId).map((a) => a.gatewayId!),
    [areas]
  );
  const { connected: socketConnected, connecting: socketConnecting } =
    useAssetProximitySocket(gatewayIds, SOCKET_URL, handleProximityEvent);
  const router = useRouter();
  // Set default visibility: all true except proximity
  const defaultVisibility = React.useMemo(() => {
    const vis: Record<string, boolean> = {};
    columns.forEach((col) => {
      vis[col.id as string] = col.id !== "proximity";
    });
    return vis;
  }, []);
  const [columnVisibility, setColumnVisibility] =
    React.useState<Record<string, boolean>>(defaultVisibility);

  // TanStack Table filter state
  const [columnFilters, setColumnFilters] = React.useState<any[]>([]);
  const [globalFilter, setGlobalFilter] = React.useState("");
  const [sorting, setSorting] = React.useState<SortingState>([]);

  // Only show Sub Area filter if subAreas exist
  const showSubAreaFilter = subAreas.length > 0;

  // Only include Sub Area column if subAreas exist
  const filteredColumns = React.useMemo(() => {
    if (showSubAreaFilter) return columns;
    // Filter out the subArea column
    return columns.filter((col) => col.id !== "subArea");
  }, [showSubAreaFilter]);

  function handleLocationChange(newLocationId: string) {
    if (newLocationId !== selectedLocationId) {
      router.push(`/asset-tracking/${newLocationId}`);
    }
  }

  // Sub Area filter for toolbar
  const subAreaFilter = showSubAreaFilter ? (
    <div className="flex items-center gap-2">
      <label htmlFor="subarea-select" className="text-sm font-medium">
        Sub Area
      </label>
      <Select
        value={
          columnFilters.find((f) => f.id === "subArea")?.value || "__all__"
        }
        onValueChange={(value) => {
          setColumnFilters((prev) => [
            ...prev.filter((f) => f.id !== "subArea"),
            { id: "subArea", value },
          ]);
        }}
      >
        <SelectTrigger id="subarea-select" className="w-[180px]">
          <SelectValue placeholder="All Sub Areas" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="__all__">All Sub Areas</SelectItem>
          {subAreas.map((subArea) => (
            <SelectItem key={subArea.id} value={subArea.id}>
              {subArea.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  ) : null;

  // Area filter for toolbar
  const areaFilter = (
    <div className="flex items-center gap-2">
      <label htmlFor="area-select" className="text-sm font-medium">
        Area
      </label>
      <Select
        value={columnFilters.find((f) => f.id === "area")?.value || "__all__"}
        onValueChange={(value) => {
          setColumnFilters((prev) => [
            ...prev.filter((f) => f.id !== "area"),
            { id: "area", value },
          ]);
        }}
      >
        <SelectTrigger id="area-select" className="w-[180px]">
          <SelectValue placeholder="All Areas" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="__all__">All Areas</SelectItem>
          {areas.map((area) => (
            <SelectItem key={area.id} value={area.id}>
              {area.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );

  const columnToolbar = (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Eye className="w-4 h-4" /> Columns
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {columns.map((col) => {
          const key = col.id as string;
          return (
            <DropdownMenuCheckboxItem
              key={key}
              checked={columnVisibility[key] ?? true}
              onCheckedChange={(checked) =>
                setColumnVisibility((prev) => ({ ...prev, [key]: checked }))
              }
            >
              {typeof col.header === "string" ? col.header : key}
            </DropdownMenuCheckboxItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );

  const locationFilter = (
    <div className="flex items-center gap-2">
      <label htmlFor="location-select" className="text-sm font-medium">
        Location
      </label>
      <Select value={selectedLocationId} onValueChange={handleLocationChange}>
        <SelectTrigger id="location-select" className="w-[180px]">
          <SelectValue placeholder="Select Location" />
        </SelectTrigger>
        <SelectContent>
          {locations.map((loc) => (
            <Link key={loc.id} href={`asset-tracking/${loc.id}`}>
              <SelectItem key={loc.id} value={loc.id}>
                {loc.name}
              </SelectItem>
            </Link>
          ))}
        </SelectContent>
      </Select>
    </div>
  );

  const searchBar = (
    <Input
      placeholder="Search..."
      value={globalFilter}
      onChange={(e) => setGlobalFilter(e.target.value)}
      className="w-full max-w-xs"
    />
  );

  const customToolbar = (
    <div className="pl-2 flex flex-row items-center w-full justify-between gap-4">
      <div className="flex flex-row items-center gap-4">
        {locationFilter}
        {areaFilter}
        {subAreaFilter}
      </div>
      <div className="flex flex-row items-center gap-4">
        {searchBar}
        {columnToolbar}
        <SocketStatusIndicator
          connected={socketConnected}
          connecting={socketConnecting}
          className="ml-2"
        />
      </div>
    </div>
  );

  // Use TanStack Table's columnFilters for metric/area filtering
  const table = useReactTable({
    data: liveData,
    columns: filteredColumns,
    state: {
      sorting,
      globalFilter,
      columnFilters,
      columnVisibility,
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: setGlobalFilter,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    enableColumnFilters: true,
  });

  // Handlers for metrics bar using TanStack filters
  const columnFiltersState = table.getState().columnFilters;
  const statusOnlineFilter = columnFiltersState.find(
    (f) => f.id === "statusOnline"
  );
  const lastSeenFilter = columnFiltersState.find(
    (f) => f.id === "lastSeen" && f.value === "alerts"
  );
  const areaFilterObj = columnFiltersState.find((f) => f.id === "area");
  const activeMetric =
    statusOnlineFilter &&
    typeof (statusOnlineFilter as { value?: string }).value === "string"
      ? (statusOnlineFilter as { value: string }).value
      : lastSeenFilter
      ? "alerts"
      : undefined;
  const activeArea =
    areaFilterObj &&
    typeof (areaFilterObj as { value?: string }).value === "string"
      ? (areaFilterObj as { value: string }).value
      : undefined;

  const handleMetricFilter = (type: string) => {
    if (type === activeMetric) {
      table.setColumnFilters([]);
      return;
    }
    if (type === "online" || type === "offline") {
      table.setColumnFilters([{ id: "statusOnline", value: type }]);
    } else if (type === "alerts") {
      table.setColumnFilters([{ id: "lastSeen", value: "alerts" }]);
    }
  };
  const handleAreaFilter = (areaId: string) => {
    if (areaId === activeArea) {
      table.setColumnFilters([]);
      return;
    }
    table.setColumnFilters([{ id: "area", value: areaId }]);
  };

  return (
    <div className="p-4">
      <AssetMetricsBar
        data={liveData}
        areas={areas}
        onMetricFilter={handleMetricFilter}
        onAreaFilter={handleAreaFilter}
        activeMetric={activeMetric}
        activeArea={activeArea}
      />
      <div className="space-y-2">
        {customToolbar}
        <div className="rounded-md border bg-card">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id} className="bg-muted">
                  {headerGroup.headers.map((header, colIdx) => {
                    const isSortable = header.column.getCanSort();
                    const sorted = header.column.getIsSorted();
                    return (
                      <TableHead
                        key={header.id}
                        onClick={
                          isSortable
                            ? header.column.getToggleSortingHandler()
                            : undefined
                        }
                        className={
                          (isSortable ? "cursor-pointer select-none " : "") +
                          (colIdx === 0 ? "pl-4" : "")
                        }
                      >
                        {header.isPlaceholder ? null : (
                          <span className="flex items-center gap-1">
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                            {sorted === "asc" && (
                              <ChevronUp className="h-4 w-4 text-primary" />
                            )}
                            {sorted === "desc" && (
                              <ChevronDown className="h-4 w-4 text-primary" />
                            )}
                            {isSortable && !sorted && (
                              <ChevronsUpDown className="h-4 w-4 opacity-30" />
                            )}
                          </span>
                        )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell, colIdx) => (
                      <TableCell
                        key={cell.id}
                        className={colIdx === 0 ? "pl-4" : undefined}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={filteredColumns.length}
                    className="h-24 text-center"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <DataTablePagination table={table} />
      </div>
    </div>
  );
}
