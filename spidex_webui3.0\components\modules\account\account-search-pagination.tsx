"use client";

import { useState, useEffect } from "react";
import { Search, X, Filter } from "lucide-react";
import { AccountSearchFilters, Role, Tenant } from "@/types/account";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface AccountSearchPaginationProps {
  searchFilters: AccountSearchFilters;
  showDeleted: boolean;
  roles: Role[];
  tenants: Tenant[];
  onSearchChange: (filters: Partial<AccountSearchFilters>) => void;
  onToggleShowDeleted: () => void;
  onClearFilters: () => void;
}

export function AccountSearchPagination({
  searchFilters,
  showDeleted,
  roles,
  tenants,
  onSearchChange,
  onToggleShowDeleted,
  onClearFilters,
}: AccountSearchPaginationProps) {
  const [mounted, setMounted] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent rendering until searchFilters is properly initialized
  if (!searchFilters || typeof searchFilters.searchTerm === "undefined") {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between gap-4">
          <div className="h-10 w-80 bg-gray-200 animate-pulse rounded" />
          <div className="h-10 w-32 bg-gray-200 animate-pulse rounded" />
        </div>
      </div>
    );
  }

  const hasActiveFilters =
    searchFilters.searchTerm ||
    searchFilters.tenantId ||
    searchFilters.roleId ||
    searchFilters.active !== undefined;

  if (!mounted) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search accounts..."
                className="pl-10 w-80"
                disabled
              />
            </div>
          </div>
        </div>
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div>Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main Search Bar */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search accounts by name, email, or user ID..."
              value={searchFilters.searchTerm}
              onChange={(e) => onSearchChange({ searchTerm: e.target.value })}
              className="pl-10 w-80"
            />
          </div>

          <Collapsible
            open={showAdvancedFilters}
            onOpenChange={setShowAdvancedFilters}
          >
            <CollapsibleTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </CollapsibleTrigger>
          </Collapsible>

          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="h-9 px-2 lg:px-3"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="show-deleted"
              checked={showDeleted}
              onCheckedChange={onToggleShowDeleted}
            />
            <Label htmlFor="show-deleted" className="text-sm font-medium">
              Show Deleted
            </Label>
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      <Collapsible
        open={showAdvancedFilters}
        onOpenChange={setShowAdvancedFilters}
      >
        <CollapsibleContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg bg-muted/50">
            <div className="space-y-2">
              <Label htmlFor="tenant-filter">Tenant</Label>
              <Select
                value={searchFilters.tenantId || "all-tenants"}
                onValueChange={(value) =>
                  onSearchChange({
                    tenantId: value === "all-tenants" ? undefined : value,
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All tenants" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all-tenants">All Tenants</SelectItem>
                  {tenants.map((tenant) => (
                    <SelectItem key={tenant.id} value={tenant.id}>
                      {tenant.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="role-filter">Role</Label>
              <Select
                value={searchFilters.roleId || "all-roles"}
                onValueChange={(value) =>
                  onSearchChange({
                    roleId: value === "all-roles" ? undefined : value,
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all-roles">All Roles</SelectItem>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status-filter">Status</Label>
              <Select
                value={
                  searchFilters.active === undefined
                    ? "all"
                    : searchFilters.active
                    ? "active"
                    : "inactive"
                }
                onValueChange={(value) => {
                  if (value === "all") {
                    onSearchChange({ active: undefined });
                  } else {
                    onSearchChange({ active: value === "active" });
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Filter Summary */}
      {hasActiveFilters && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Active filters:</span>
          {searchFilters.searchTerm && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs">
              Search: "{searchFilters.searchTerm}"
            </span>
          )}
          {searchFilters.tenantId && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs">
              Tenant: "
              {tenants.find((t) => t.id === searchFilters.tenantId)?.name ||
                "Unknown"}
              "
            </span>
          )}
          {searchFilters.roleId && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs">
              Role: "
              {roles.find((r) => r.id === searchFilters.roleId)?.name ||
                "Unknown"}
              "
            </span>
          )}
          {searchFilters.active !== undefined && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs">
              Status: {searchFilters.active ? "Active" : "Inactive"}
            </span>
          )}
        </div>
      )}
    </div>
  );
}
