"use client";

import { useEffect } from "react";
import { RefreshCw, Package, Search } from "lucide-react";
import { useSession } from "next-auth/react";
import { useTaggedAssetManagement } from "@/hooks/use-tagged-asset-management";
import { getTableConfig } from "@/config/table-config";
import { TaggedAsset } from "@/types/tagged-asset";
import { OnboardAssetDataTable } from "./onboard-asset-data-table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { toast } from "sonner";

export function OnboardAssetManagement() {
  const { data: session } = useSession();

  // Get table configuration for this page
  const tableConfig = getTableConfig("onboard-asset-management");

  const {
    taggedAssets: paginatedTaggedAssets,
    isLoading,
    error,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    loadData,
    updateTaggedAsset,
    goToPage,
    changePageSize,
    updateSearchFilters,
    availablePageSizes,
  } = useTaggedAssetManagement({
    initialPageSize: tableConfig.defaultPageSize,
    availablePageSizes: tableConfig.availablePageSizes,
  });

  // Load data on component mount
  useEffect(() => {
    const initializeData = async () => {
      try {
        await loadData();
      } catch (error) {
        console.error("Error initializing data:", error);
      }
    };

    initializeData();
  }, [loadData]); // Include loadData dependency

  // Handle refresh
  const handleRefresh = async () => {
    try {
      await loadData();
      toast.success("Data refreshed successfully");
    } catch (error) {
      console.error("Refresh error:", error);
      toast.error("Failed to refresh data");
    }
  };

  // Handle provision all
  const handleProvisionAll = async () => {
    try {
      const unprovisionedAssets = paginatedTaggedAssets.filter(
        (asset: TaggedAsset) => !asset.provisioned
      );

      if (unprovisionedAssets.length === 0) {
        toast.info("No assets to provision");
        return;
      }

      console.log("Provisioning assets:", unprovisionedAssets.length);

      for (const asset of unprovisionedAssets) {
        // Prepare the complete asset data as per the old application format
        const assetData = {
          tenantId:
            session?.user?.tenantId || "27b240fb-628e-466f-8225-b8c882c1670f",
          id: asset.id,
          assetId: asset.assetId,
          deviceId: asset.deviceId,
          modelId: Number(asset.modelId), // Ensure modelId is a number
          status: asset.status,
          deleted: asset.deleted,
          createdTime: asset.createdTime || asset.createdDate,
          modifiedTime: new Date().toISOString(),
          createdBy: asset.createdBy,
          modifiedBy: session?.user?.userId || "admin",
          provisioned: true,
          taggedAssetInfo: asset.taggedAssetInfo,
        };

        console.log("Provisioning asset:", assetData);
        await updateTaggedAsset(assetData as any);
      }

      await loadData();
      toast.success(
        `Successfully provisioned ${unprovisionedAssets.length} assets`
      );
    } catch (error) {
      console.error("Provision all error:", error);
      toast.error("Failed to provision all assets");
    }
  };

  // Handle provision single asset
  const handleProvisionAsset = async (
    asset: TaggedAsset,
    provisioned: boolean
  ) => {
    try {
      // Prepare the complete asset data as per the old application format
      const assetData = {
        tenantId:
          session?.user?.tenantId || "27b240fb-628e-466f-8225-b8c882c1670f",
        id: asset.id,
        assetId: asset.assetId,
        deviceId: asset.deviceId,
        modelId: Number(asset.modelId), // Ensure modelId is a number
        status: asset.status,
        deleted: asset.deleted,
        createdTime: asset.createdTime || asset.createdDate,
        modifiedTime: new Date().toISOString(),
        createdBy: asset.createdBy,
        modifiedBy: session?.user?.userId || "admin",
        provisioned,
        taggedAssetInfo: asset.taggedAssetInfo,
      };

      console.log("Updating asset provision status:", assetData);
      await updateTaggedAsset(assetData as any);

      await loadData();
      toast.success(
        provisioned
          ? "Asset provisioned successfully"
          : "Asset unprovisioned successfully"
      );
    } catch (error) {
      console.error("Provision asset error:", error);
      toast.error("Failed to update asset provision status");
    }
  };

  return (
    <>
      {/* Search and Actions */}
      <div className="p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Label htmlFor="search" className="sr-only">
              Search assets
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                type="text"
                placeholder="Search by asset name, tag name, external ID..."
                value={searchFilters.searchTerm || ""}
                onChange={(e) =>
                  updateSearchFilters({ searchTerm: e.target.value })
                }
                className="pl-10"
                disabled={isLoading}
              />
            </div>
          </div>
          <Button
            onClick={handleProvisionAll}
            disabled={isLoading || paginatedTaggedAssets.length === 0}
            className="whitespace-nowrap"
          >
            Provision All
          </Button>
        </div>
      </div>

      {/* Data Table */}
      <div className="p-4 pt-0">
        <OnboardAssetDataTable
          data={paginatedTaggedAssets}
          isLoading={isLoading}
          onProvision={handleProvisionAsset}
          pagination={pagination}
          totalRecords={totalRecords}
          totalPages={totalPages}
          availablePageSizes={availablePageSizes}
          onPageChange={goToPage}
          onPageSizeChange={changePageSize}
        />
      </div>
    </>
  );
}
