export interface LintDto {
  lintLogId: string;
  attributeValue: number;
  attributeValues?: string[]; // [latitude, longitude]
  lintTime: number;
}

export interface Asset {
  id: string;
  deviceLogId: string;
  devicePhyId: string;
  macId: string;
  deviceId: string;
  tagName: string;
  name: string;
  areaName: string;
  zone: string;
  key: string;
  tgAssetLink: TaggedAsset;
  eventTime?: number;
  rssi?: number;
  battery?: number;
  lastSeen?: string;
  // Properties for zone calculation (from proximity sensor data)
  sourceId?: string;
  lintLogId?: string;
  // Additional proximity data properties
  attributeType?: string;
  attributeValue?: string | number;
  proximity?: number;
  // Location data from lintDtos
  lintDtos?: LintDto[];
}

export interface TaggedAsset {
  id: string;
  taggedAssetInfo: {
    assetExternalId: string;
    tagExternalId: string;
    tagName: string;
    assetName: string;
    assetType: string;
  };
  deleted: boolean;
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
}

export interface GatewayLint {
  gatLintId: string;
  externalId: string;
  modelId: number;
  areaId: string;
  areaName: string;
  coverageMin: number;
  coverageMax: number;
  antenna: any;
  properties: Record<string, any>;
  xyzCoordinates: Record<string, any>;
}

export interface Gateway {
  tenantId: string;
  id: string;
  externalId: string;
  name: string;
  description: string;
  properties: Record<string, any> | null;
  areaId: string;
  areaName: string;
  locId: string;
  locName: string;
  modelId: number;
  ownerTenantId: string;
  deleted: boolean;
  createdTime: number;
  modifiedTime: number;
  createdBy: string;
  modifiedBy: string;
  provisioned: boolean;
  categoryType: string;
  parentId: string | null;
  locType: string | null;
  communicationType: string;
  gatewayLint: GatewayLint[];
  coverageMin: number;
  coverageMax: number;
  xyzCoordinates: Record<string, any> | null;
  virtual: boolean;
  // Computed properties for compatibility
  active?: boolean;
  ipAddress?: string;
  locationId?: string;
  macId?: string;
  createdDate?: string;
  modifiedDate?: string;
}

export interface Location {
  id: string;
  name: string;
  description: string;
  branchId: string;
  deleted: boolean;
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
}

export interface Area {
  id: string;
  name: string;
  description: string;
  locationId: string;
  deleted: boolean;
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
}

export interface Zone {
  id: string;
  name: string;
  description?: string;
  areaId?: string;
  assetCount?: number;
  type?: string;
}

export enum SensorType {
  TEMPERATURE = "temperature",
  HUMIDITY = "humidity",
  AMBLIGHT = "amblight",
  PRESSURE = "pressure",
  BATTERY = "battery",
  PROXIMITY = "proximity",
  RSSI = "rssi",
  CPU = "Cpu",
  MEMORY = "memoryUsage",
  DISK = "diskUsage",
  INBOUND = "inboundTraffic",
  OUTBOUND = "outboundTraffic",
}

export interface SensorData {
  value: number | string;
  loading: boolean;
  selected: boolean;
  date?: string;
  lineChartData: ChartDataPoint[];
}

export interface ChartDataPoint {
  value: number;
  time: string;
  timestamp: number;
}

export interface SensorMetrics {
  min: number;
  max: number;
  avg: number;
  current: number;
}

export interface WebSocketMessage {
  attributeType: string;
  attributeValue: string | number;
  eventTime: number;
  deviceLogId: string;
  sensorType?: SensorType;
  // Location data from lintDtos
  lintDtos?: LintDto[];
  // GPS-specific properties
  attributeName?: string;
  attributeValues?: string[];
  sourceId?: string;
  devicePhyId?: string;
  tenantId?: string;
  reportedTime?: number;
  deviceModel?: string;
  deviceSubComponentId?: string;
  sourcePhysicalId?: string;
  targetChannelType?: string;
  key?: string;
  locLogId?: string;
  lintLogId?: string;
}

export interface AssetTrackingState {
  assets: Asset[];
  gateways: Gateway[];
  locations: Location[];
  areas: Area[];
  selectedGateway: Gateway | null;
  selectedLocation: Location | null;
  sensorData: Record<string, Record<SensorType, SensorData>>;
  isLoading: boolean;
  error: string | null;
}

export interface AssetSearchFilters {
  searchTerm: string;
  gatewayId?: string;
  locationId?: string;
  areaId?: string;
  assetType?: string;
}

export interface DateRange {
  from: Date;
  to: Date;
}

export interface AssetTrackingConfig {
  socketUrl: string;
  apiBaseUrl: string;
  refreshInterval: number;
  maxRetries: number;
}

export interface SocketSubscription {
  id: string;
  topic: string;
  callback: (message: WebSocketMessage) => void;
  unsubscribe: () => void;
}

export interface AssetLocation {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp: number;
}

export interface MapAsset extends Asset {
  location: AssetLocation;
  isOnline: boolean;
  lastUpdate: string;
  sensorData?: Record<SensorType, SensorData>;
  battery?: number;
  rssi?: number;
  gatewayType?: string; // 'transit', 'fixed', or 'unknown'
}

export interface ZoneData {
  [locationId: string]: Asset[];
}

export interface TableData {
  [gatewayId: string]: Asset[];
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface AssetAnalytics {
  totalAssets: number;
  activeAssets: number;
  offlineAssets: number;
  batteryLowAssets: number;
  averageRssi: number;
  sensorMetrics: Record<SensorType, SensorMetrics>;
}
