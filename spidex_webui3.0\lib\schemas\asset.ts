import { z } from "zod";

// Asset Types
export const ASSET_TYPES = {
  VENDOR: "Vendor",
  VEHICLE: "Vehicle",
  WORKER: "Worker",
  CHAIR: "Chair",
  SANITARY: "Sanitary",
  OTHERS: "Others",
} as const;

// Base Asset Schema
const BaseAssetSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Asset name is required" })
    .max(200, { message: "Asset name must be less than 200 characters" }),
  externalId: z
    .string()
    .min(1, { message: "External ID is required" })
    .max(200, { message: "External ID must be less than 200 characters" }),
  assetType: z.enum(
    [
      ASSET_TYPES.WORKER,
      ASSET_TYPES.VENDOR,
      ASSET_TYPES.VEHICLE,
      ASSET_TYPES.SANITARY,
      ASSET_TYPES.OTHERS,
      ASSET_TYPES.CHAIR,
    ],
    { message: "Please select a valid asset type" }
  ),
  status: z.boolean().default(false),
  type: z
    .string()
    .max(200, { message: "Asset type must be less than 200 characters" })
    .optional(),
});

// Create Asset Schema with conditional validation for "Others" type
export const CreateAssetSchema = BaseAssetSchema.refine(
  (data) => {
    // If asset type is "Others", then type field is required
    if (data.assetType === ASSET_TYPES.OTHERS) {
      return !!data.type && data.type.trim().length > 0;
    }
    return true;
  },
  {
    message: "Asset type is required when selecting 'Others'",
    path: ["type"],
  }
);

// Update Asset Schema
export const UpdateAssetSchema = BaseAssetSchema.extend({
  id: z.string().min(1, { message: "Asset ID is required" }),
}).refine(
  (data) => {
    // If asset type is "Others", then type field is required
    if (data.assetType === ASSET_TYPES.OTHERS) {
      return !!data.type && data.type.trim().length > 0;
    }
    return true;
  },
  {
    message: "Asset type is required when selecting 'Others'",
    path: ["type"],
  }
);

// Asset Search Schema
export const AssetSearchSchema = z.object({
  searchTerm: z.string().optional(),
  assetType: z
    .enum([
      ASSET_TYPES.WORKER,
      ASSET_TYPES.VENDOR,
      ASSET_TYPES.VEHICLE,
      ASSET_TYPES.SANITARY,
      ASSET_TYPES.OTHERS,
      ASSET_TYPES.CHAIR,
    ])
    .optional(),
});

// Asset Pagination Schema
export const AssetPaginationSchema = z.object({
  page: z.number().min(1).default(1),
  size: z.number().min(1).max(100).default(10),
});

// Export types
export type CreateAssetFormData = z.infer<typeof CreateAssetSchema>;
export type UpdateAssetFormData = z.infer<typeof UpdateAssetSchema>;
export type AssetSearchFilters = z.infer<typeof AssetSearchSchema>;
export type AssetPaginationParams = z.infer<typeof AssetPaginationSchema>;
