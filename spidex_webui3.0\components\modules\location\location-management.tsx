"use client";

import { useState } from "react";
import { Plus, Building2 } from "lucide-react";
import { useSession } from "next-auth/react";
import { useLocationManagement } from "@/hooks/use-location-management";
import { getTableConfig } from "@/config/table-config";
import { useBranchManagement } from "@/hooks/use-branch-management";
import {
  Location,
  CreateLocationFormData,
  UpdateLocationFormData,
} from "@/types/location";
import { LocationDataTable } from "./location-data-table";
import { LocationSearchPagination } from "./location-search-pagination";
import { LocationForm } from "./location-form";
import { Button } from "@/components/ui/button";

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

interface LocationManagementProps {
  branchId?: string;
}

export default function LocationManagement({
  branchId,
}: LocationManagementProps) {
  const { data: session, status } = useSession();
  const {
    locations: paginatedLocations,
    filteredLocations,
    isLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    totalAllRecords,
    activeRecordsCount,
    inactiveRecordsCount,
    loadData,
    createLocation,
    updateLocation,
    deleteLocation,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useLocationManagement({ branchId });

  // Load branches for the form
  const { branches: allBranches, isLoading: branchesLoading } =
    useBranchManagement({ autoLoad: true });

  // Form state
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  const [tableKey, setTableKey] = useState(0);

  // Get active branches for dropdown
  const activeBranches = allBranches.filter((branch) => !branch.deleted);

  const openCreateForm = () => {
    setEditingLocation(null);
    setIsFormOpen(true);
  };

  const openEditForm = (location: Location) => {
    setEditingLocation(location);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setEditingLocation(null);
  };

  const handleCreateLocation = async (data: CreateLocationFormData) => {
    try {
      // Add tenant ID and other required fields
      const locationData = {
        ...data,
        tenantId: session?.user?.tenantId || "",
        properties: {},
        createdBy: session?.user?.id || "",
        createdTime: Date.now(),
        modifiedBy: session?.user?.id || "",
        modifiedTime: Date.now(),
      };

      await createLocation(locationData);
      toast.success("Location created successfully");
      closeForm();
      setTableKey((prev) => prev + 1); // Force table refresh
    } catch (error) {
      console.error("Error creating location:", error);
      toast.error("Failed to create location. Please try again.");
    }
  };

  const handleUpdateLocation = async (data: UpdateLocationFormData) => {
    try {
      if (!editingLocation) {
        throw new Error("No location selected for editing");
      }

      // Preserve all original location fields and only update the form fields
      const locationData = {
        ...editingLocation, // Preserve all original fields including tenantId, createdTime, createdBy, etc.
        ...data, // Override with form data (id, name, address, branchId, geoJson, gpsPoint)
        modifiedBy: session?.user?.id || "",
        modifiedTime: Date.now(),
      };

      await updateLocation(locationData);
      toast.success("Location updated successfully");
      closeForm();
      setTableKey((prev) => prev + 1); // Force table refresh
    } catch (error) {
      console.error("Error updating location:", error);
      toast.error("Failed to update location. Please try again.");
    }
  };

  const handleDeleteLocation = async (locationId: string) => {
    try {
      await deleteLocation(locationId);
      toast.success("Location deleted successfully");
      setTableKey((prev) => prev + 1); // Force table refresh
    } catch (error) {
      console.error("Error deleting location:", error);
      toast.error("Failed to delete location. Please try again.");
    }
  };

  const handleToggleActive = async (location: Location) => {
    try {
      // Preserve all original location fields and only update the deleted status
      const updatedData = {
        ...location, // Preserve all original fields including tenantId, createdTime, createdBy, etc.
        deleted: false,
        modifiedBy: session?.user?.id || "",
        modifiedTime: Date.now(),
      };

      await updateLocation(updatedData);
      toast.success("Location activated successfully");
      setTableKey((prev) => prev + 1); // Force table refresh
    } catch (error) {
      console.error("Error activating location:", error);
      toast.error("Failed to activate location. Please try again.");
    }
  };

  const handleReloadData = async () => {
    try {
      await loadData();
      toast.success("Data reloaded successfully");
    } catch (error) {
      console.error("Error reloading data:", error);
      toast.error("Failed to reload data. Please try again.");
    }
  };

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p className="text-destructive">Error: {error}</p>
          <Button onClick={handleReloadData} className="mt-4">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/digital-carpet">
                Digital Carpet
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Location Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={openCreateForm} className="gap-2 ml-auto">
          <Plus className="h-4 w-4" />
          Add Location
        </Button>
      </header>
      <main>
        {/* Search and Filters */}
        <div className="p-4">
          <LocationSearchPagination
            searchFilters={searchFilters}
            showDeleted={showDeleted}
            totalRecords={totalRecords}
            totalAllRecords={totalAllRecords}
            branches={activeBranches}
            onSearchChange={updateSearchFilters}
            onClearSearch={clearSearch}
            onToggleShowDeleted={toggleShowDeleted}
          />
        </div>

        {/* Data Table */}
        <div className="p-4 pt-0">
          <LocationDataTable
            key={tableKey}
            data={paginatedLocations}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onEdit={openEditForm}
            onDelete={handleDeleteLocation}
            onToggleActive={handleToggleActive}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
          />
        </div>
      </main>

      {/* Form Sheet */}
      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-1/4 sm:max-w-2xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              {editingLocation ? "Edit Location" : "Create New Location"}
            </SheetTitle>
            <SheetDescription>
              {editingLocation
                ? "Update the location information below."
                : "Fill in the details to create a new location."}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <LocationForm
              location={editingLocation || undefined}
              branches={activeBranches}
              isLoading={isLoading}
              onSubmit={async (data: any) => {
                if (editingLocation) {
                  await handleUpdateLocation(data as UpdateLocationFormData);
                } else {
                  await handleCreateLocation(data as CreateLocationFormData);
                }
              }}
              onCancel={closeForm}
              onClear={() => {
                // Clear form logic is handled within the form component
              }}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
