import getAxiosInstance from '../index';
import { Roles } from '../../constants';

const axios = getAxiosInstance(process.argv[2]);
const ROLE_URI = '/role/';

export const addRole = (role) => {
  return axios.post(`${ROLE_URI}`, role);
};

export const deleteRole = (roleId) => {
  return axios.delete(`${ROLE_URI}${roleId}`);
};

export const getAllRoles = () => {
  return axios.get(`${ROLE_URI}all`);
};

const init = async () => {
  const isDelete = process.argv[3] === 'delete';
  try {
    const { data } = await getAllRoles();
    if (isDelete) {
      data.forEach(async (role) => {
        try {
          await deleteRole(role.id);
          console.log('Deleted Role: ', role.name, role.id);
        } catch (e) {
          console.log('Unable to delete role', e);
        }
      });
      Object.keys(Roles).forEach(async (role) => {
        const roleName = Roles[role];
        const dateTimeNow = new Date().toISOString();
        console.log('Created Role: ', roleName);
        try {
          await addRole({
            name: roleName,
            id: new Date().getTime(),
            enable: true,
            deleted: false,
            createdTime: dateTimeNow,
            modifiedTime: dateTimeNow,
            createdBy: 'system',
            modifiedBy: 'system',
          });
        } catch {
          console.log('Unable to create role');
        }
      });
    } else {
      const serverRoles = data.filter((x) => x.deleted === false).map((y) => y.name);
      Object.keys(Roles).forEach(async (role) => {
        const roleName = Roles[role];
        if (!serverRoles.includes(roleName)) {
          console.log('Created Role: ', roleName);
          const dateTimeNow = new Date().toISOString();
          try {
            await addRole({
              name: roleName,
              id: new Date().getTime(),
              enable: true,
              deleted: false,
              createdTime: dateTimeNow,
              modifiedTime: dateTimeNow,
              createdBy: 'system',
              modifiedBy: 'system',
            });
          } catch {
            console.log('Unable to create role');
          }
        }
      });
    }
  } catch (err) {
    console.log(err);
  }
};

init();
