"use client";

import { useState, useEffect } from "react";
import { Asset, AssetPaginationParams, AssetPageSize } from "@/types/asset";
import { ASSET_TYPES } from "@/lib/schemas/asset";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AssetTablePagination } from "./asset-table-pagination";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  ToggleLeft,
  ToggleRight,
  Package,
  User,
  Car,
  Building,
  Wrench,
  Calendar,
  CheckCircle,
} from "lucide-react";

interface AssetDataTableProps {
  data: Asset[];
  pagination: AssetPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: AssetPageSize[];
  onEdit: (asset: Asset) => void;
  onDelete: (assetId: string) => void;
  onToggleActive?: (asset: Asset) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: AssetPageSize) => void;
}

export function AssetDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onEdit,
  onDelete,
  onToggleActive,
  onPageChange,
  onPageSizeChange,
}: AssetDataTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [assetToDelete, setAssetToDelete] = useState<Asset | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Reset dropdown state when data changes
  useEffect(() => {
    setOpenDropdownId(null);
  }, [data]);

  const handleDeleteClick = (asset: Asset) => {
    setAssetToDelete(asset);
    setDeleteDialogOpen(true);
    setOpenDropdownId(null); // Close dropdown
  };

  const handleDeleteConfirm = () => {
    if (assetToDelete) {
      onDelete(assetToDelete.id);
      setAssetToDelete(null);
    }
    setDeleteDialogOpen(false);
  };

  const handleToggleActive = (asset: Asset) => {
    if (onToggleActive) {
      onToggleActive(asset);
    }
    setOpenDropdownId(null); // Close dropdown
  };

  const getAssetTypeIcon = (type: string) => {
    switch (type) {
      case ASSET_TYPES.WORKER:
        return <User className="h-4 w-4 text-blue-600" />;
      case ASSET_TYPES.VENDOR:
        return <Building className="h-4 w-4 text-green-600" />;
      case ASSET_TYPES.VEHICLE:
        return <Car className="h-4 w-4 text-purple-600" />;
      case ASSET_TYPES.SANITARY:
        return <Wrench className="h-4 w-4 text-orange-600" />;
      case ASSET_TYPES.OTHERS:
        return <Package className="h-4 w-4 text-gray-600" />;
      default:
        return <Package className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return "N/A";
    }
  };

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Asset</TableHead>
              <TableHead className="hidden md:table-cell">
                External ID
              </TableHead>
              <TableHead className="hidden lg:table-cell">Type</TableHead>
              <TableHead className="hidden lg:table-cell">Status</TableHead>
              <TableHead className="hidden xl:table-cell">Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <Package className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">No assets found</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              data.map((asset) => (
                <TableRow
                  key={asset.id}
                  className={asset.deleted ? "opacity-50" : ""}
                >
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <div className="flex items-center gap-2">
                        {getAssetTypeIcon(asset.assetType)}
                        <span className="font-semibold">{asset.name}</span>
                      </div>
                      <span className="text-sm text-muted-foreground md:hidden">
                        {asset.externalId}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <div
                      className="max-w-[150px] truncate"
                      title={asset.externalId}
                    >
                      {asset.externalId}
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center gap-2">
                      {getAssetTypeIcon(asset.assetType)}
                      <span>{asset.assetType}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <Badge
                      variant={
                        asset.deleted
                          ? "destructive"
                          : asset.status === "true"
                          ? "default"
                          : "secondary"
                      }
                    >
                      {asset.deleted
                        ? "Inactive"
                        : asset.status === "true"
                        ? "Active"
                        : "Disabled"}
                    </Badge>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span className="text-sm">
                        {formatDate(asset.createdTime)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu
                      open={openDropdownId === asset.id}
                      onOpenChange={(open) =>
                        setOpenDropdownId(open ? asset.id : null)
                      }
                    >
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {asset.deleted ? (
                          // Show only Activate option for inactive assets
                          <DropdownMenuItem
                            onClick={() => handleToggleActive(asset)}
                            className="text-green-600"
                          >
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Activate
                          </DropdownMenuItem>
                        ) : (
                          // Show Edit and Delete options for active assets
                          <>
                            <DropdownMenuItem
                              onClick={() => {
                                onEdit(asset);
                                setOpenDropdownId(null);
                              }}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteClick(asset)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <AssetTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              asset "{assetToDelete?.name}" and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
