"use client";
import { useMapBounds } from "@/lib/hooks/useMapBounds";
import { Branch } from "@/types/branch";
import { Location } from "@/types/location";
import { Area } from "@/types/area";
import { Gateway } from "@/types/gateway";

// Define the hierarchical item types to match server data
interface BranchItem extends Branch {
  children: LocationItem[];
}

interface LocationItem extends Location {
  children: AreaItem[];
}

interface AreaItem extends Area {
  children: GatewayItem[];
}

interface GatewayItem extends Gateway {
  children: [];
}

// Generic item type for map operations
interface MapItem {
  id: string;
  name: string;
  gpsPoint: {
    latitude: string | number;
    longitude: string | number;
  };
  children: MapItem[];
  [key: string]: any;
}
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from "react";
import Map, {
  FullscreenControl,
  GeolocateControl,
  Layer,
  MapRef,
  Marker,
  NavigationControl,
  Popup,
  ScaleControl,
  Source,
  useMap,
} from "react-map-gl";
import ControlPanel from "./_map-control-panel";
import { mapInitialState, mapReducer } from "./_map-reducer";

const initialViewState = {
  latitude: 0,
  longitude: 0,
  zoom: 2,
};

export default function MapView({ branches }: { branches: BranchItem[] }) {
  // const mapRef = useRef<MapRef>(null);
  const [state, dispatch] = useReducer(mapReducer, {
    ...mapInitialState,
    currentItems: branches,
  });
  const { currentItems, parentItem } = state;
  // const [currentItems, setCurrentItems] = useState<Item[]>(branches);
  const [popupInfo, setPopupInfo] = useState<MapItem | null>(null);
  const bounds = useMapBounds(currentItems);
  const depth = state.navigationHistory.length; // 0 = branches, 1 = locations, etc.

  const [geojsonData, setGeojsonData] = useState(null);
  useEffect(() => {
    // Fetch the local GeoJSON file
    fetch("/data.geojson")
      .then((response) => response.json())
      .then((data) => setGeojsonData(data))
      .catch((error) => console.error("Error loading GeoJSON:", error));
  }, []);

  const pins = useMemo(
    () =>
      currentItems.map((item) => {
        const { id, gpsPoint, name } = item;
        return (
          <Marker
            key={id}
            longitude={parseFloat(String(gpsPoint.longitude))}
            latitude={parseFloat(String(gpsPoint.latitude))}
            anchor="bottom"
            style={{ cursor: "pointer" }}
            onClick={(e) => {
              e.originalEvent.stopPropagation();
              // setPopupInfo(item);
              if (item.children.length > 0)
                dispatch({ type: "NAVIGATE_DOWN", payload: item });
            }}
          >
            <div className="flex flex-col items-center group">
              {/* Marker icon */}
              <div className="w-4 h-4 bg-red-600 rounded-full border-2 border-white shadow-lg" />
              {/* Name label, always visible */}
              <span className="mt-1 px-2 py-0.5 rounded bg-white text-xs text-gray-800 shadow group-hover:bg-blue-100 transition">
                {name}
              </span>
            </div>
          </Marker>
        );
      }),
    [currentItems]
  );

  const onSelectItem = useCallback((item: MapItem) => {
    if (item.children.length > 0)
      dispatch({ type: "NAVIGATE_DOWN", payload: item });

    // if (item.children.length > 0) setCurrentItems(item.children);
    // const { longitude: lon, latitude: lat } = item.gpsPoint;
    // mapRef.current?.flyTo({
    //   center: [parseFloat(lon), parseFloat(lat)],
    //   duration: 3000,
    //   zoom: 18,
    // });
  }, []);

  const onBack = () => {
    dispatch({ type: "NAVIGATE_BACK" });
  };

  return (
    <div className="h-[100vh] relative">
      {/* Back Button - only show if parentItem exists */}
      {parentItem && <BackButton onClick={onBack} />}
      <Map
        initialViewState={initialViewState}
        mapStyle="mapbox://styles/ganache-lives/cmcp9taxq00kc01sdf21l91tc"
        mapboxAccessToken={process.env.NEXT_PUBLIC_MapboxAccessToken}
        projection={{ name: "globe" }}
      >
        {/* <Source type="geojson" data={geojsonData || ""}>
          <Layer
            id="data-layer"
            type="fill"
            paint={{
              "fill-color": "#888888",
              "fill-opacity": 0.4,
            }}
          />
        </Source> */}
        <GeolocateControl position="top-left" />
        <FullscreenControl position="top-left" />
        <NavigationControl position="top-left" />
        <ScaleControl />
        {pins}
        {popupInfo && (
          <Popup
            className="text-foreground"
            anchor="top"
            longitude={Number(popupInfo.gpsPoint.longitude)}
            latitude={Number(popupInfo.gpsPoint.latitude)}
            onClose={() => setPopupInfo(null)}
            closeButton={false}
          >
            <div>
              {popupInfo.name}, {popupInfo.address}
            </div>
          </Popup>
        )}
        <SetBounds bounds={bounds} depth={depth} />
      </Map>
      {/* <ControlPanel
        parentItem={parentItem}
        items={currentItems}
        onSelectItem={onSelectItem}
        onBack={onBack}
      /> */}
    </div>
  );
}

function BackButton({ onClick }: { onClick: () => void }) {
  return (
    <button
      onClick={onClick}
      className="absolute top-3 right-3 z-10 bg-white/90 hover:bg-blue-100 text-gray-800 px-2 py-1 rounded shadow transition flex items-center gap-1 text-sm"
      aria-label="Back"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-4 w-4"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M15 19l-7-7 7-7"
        />
      </svg>
      Back
    </button>
  );
}

function SetBounds({ bounds, depth }: { bounds: any; depth: number }) {
  const { current: map } = useMap();
  // Choose zoom level based on depth
  const zoomLevels = [2, 10, 15, 16];
  const zoom = zoomLevels[depth] ?? 2;
  useEffect(() => {
    if (map && bounds) {
      map.fitBounds(bounds, { padding: 100, duration: 2000, zoom });
    }
  }, [bounds, map, zoom]);
  return null;
}
