import axios, { AxiosInstance, AxiosResponse } from "axios";
import {
  Asset,
  Gateway,
  Location,
  Area,
  TaggedAsset,
  SensorType,
  ApiResponse,
  PaginationParams,
  DateRange,
} from "@/types/asset-tracking";
import { API_ENDPOINTS } from "@/lib/constants/asset-tracking";
import { SPIDEX_API_BASE_URL } from "@/lib/constants";

class AssetTrackingAPI {
  private api: AxiosInstance;
  private alternateApi: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: SPIDEX_API_BASE_URL,
      headers: {
        "Content-Type": "application/api.spidex.v1+json",
        Accept: "application/api.spidex.v1+json",
      },
    });

    this.alternateApi = axios.create({
      baseURL: SPIDEX_API_BASE_URL,
      headers: {
        "Content-Type": "application/api.spidex.v1+json",
        Accept: "application/api.spidex.v1+json",
      },
    });

    // Add request interceptor for authentication
    this.setupInterceptors();
  }

  private setupInterceptors() {
    const requestInterceptor = (config: any) => {
      const token = this.getAuthToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      config.data = config.data || {};
      return config;
    };

    const errorInterceptor = (error: any) => {
      console.error("API Request Error:", error);
      return Promise.reject(error);
    };

    this.api.interceptors.request.use(requestInterceptor, errorInterceptor);
    this.alternateApi.interceptors.request.use(
      requestInterceptor,
      errorInterceptor
    );
  }

  private getAuthToken(): string | null {
    // Try to get token from NextAuth session
    if (typeof window !== "undefined") {
      // Get the session from NextAuth
      try {
        // Check if we can access the session from the global NextAuth state
        const nextAuthSession = (window as any).__NEXT_AUTH_SESSION__;
        if (nextAuthSession?.user?.token) {
          return nextAuthSession.user.token;
        }

        // Try to get from session storage (NextAuth stores it there)
        const sessionKeys = Object.keys(sessionStorage).filter(
          (key) =>
            key.startsWith("next-auth.session-token") ||
            key.includes("next-auth")
        );

        for (const key of sessionKeys) {
          try {
            const sessionData = sessionStorage.getItem(key);
            if (sessionData) {
              const session = JSON.parse(sessionData);
              if (session?.user?.token) {
                return session.user.token;
              }
            }
          } catch (e) {
            // Continue to next key
          }
        }

        // Fallback to localStorage for backward compatibility
        return localStorage.getItem("token");
      } catch (e) {
        // Ignore errors and return null
      }
    }
    return null;
  }

  // Gateway APIs
  async getAllGateways(tenantId: string): Promise<ApiResponse<Gateway[]>> {
    const response = await this.api.get(API_ENDPOINTS.GATEWAYS, {
      params: { tenantId },
    });
    return response.data;
  }

  // Location APIs
  async getAllLocations(tenantId: string): Promise<ApiResponse<Location[]>> {
    const response = await this.api.get(API_ENDPOINTS.LOCATIONS, {
      params: { tenantId },
    });
    return response.data;
  }

  // Area APIs
  async getAllAreas(tenantId: string): Promise<ApiResponse<Area[]>> {
    const response = await this.api.get(API_ENDPOINTS.AREAS, {
      params: { tenantId },
    });
    return response.data;
  }

  // Asset APIs
  async getAllAssets(tenantId: string): Promise<ApiResponse<Asset[]>> {
    const response = await this.api.get(API_ENDPOINTS.ASSETS, {
      params: { tenantId },
    });
    return response.data;
  }

  async getAllTaggedAssets(
    tenantId: string
  ): Promise<ApiResponse<TaggedAsset[]>> {
    const response = await this.api.get(API_ENDPOINTS.TAGGED_ASSETS, {
      params: { tenantId },
    });
    return response.data;
  }

  async getAssetsByPagination(
    tenantId: string,
    params: PaginationParams
  ): Promise<ApiResponse<{ assets: Asset[]; total: number }>> {
    const response = await this.api.get(`${API_ENDPOINTS.ASSETS}by`, {
      params: { tenantId, ...params },
    });
    return response.data;
  }

  async getAssetInfo(assetExternalId: string): Promise<ApiResponse<Asset>> {
    const response = await this.api.get(
      `${API_ENDPOINTS.ASSETS}${assetExternalId}`
    );
    return response.data;
  }

  // Sensor Data APIs
  async getSensorInitialData(
    tenantId: string,
    devLogId: string,
    sensorType: SensorType
  ): Promise<ApiResponse<any>> {
    const response = await this.api.get(
      `${API_ENDPOINTS.SENSOR_HEALTH}byType/${tenantId}/${devLogId}/${sensorType}`
    );
    return response.data;
  }

  async getTempSensorInitialData(
    tenantId: string,
    devLogId: string
  ): Promise<ApiResponse<any>> {
    const response = await this.api.get(
      `${API_ENDPOINTS.SENSOR_TEMP}initial/${tenantId}/${devLogId}`
    );
    return response.data;
  }

  async getHumiditySensorInitialData(
    tenantId: string,
    devLogId: string
  ): Promise<ApiResponse<any>> {
    const response = await this.api.get(
      `${API_ENDPOINTS.SENSOR_HUMIDITY}initial/${tenantId}/${devLogId}`
    );
    return response.data;
  }

  async getAmblightSensorInitialData(
    tenantId: string,
    devLogId: string
  ): Promise<ApiResponse<any>> {
    const response = await this.api.get(
      `${API_ENDPOINTS.SENSOR_AMBLIGHT}initial/${tenantId}/${devLogId}`
    );
    return response.data;
  }

  async getPressureSensorInitialData(
    tenantId: string,
    devLogId: string
  ): Promise<ApiResponse<any>> {
    const response = await this.api.get(
      `${API_ENDPOINTS.SENSOR_PRESSURE}initial/${tenantId}/${devLogId}`
    );
    return response.data;
  }

  // Sensor Metrics APIs
  async getTempSensorMetrics(
    tenantId: string,
    deviceId: string
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_TEMP}initial/aggr/`,
      {
        tenantId,
        deviceId,
      }
    );
    return response.data;
  }

  async getHumiditySensorMetrics(
    tenantId: string,
    deviceId: string
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_HUMIDITY}initial/aggr/`,
      {
        tenantId,
        deviceId,
      }
    );
    return response.data;
  }

  async getAmblightSensorMetrics(
    tenantId: string,
    deviceId: string
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_AMBLIGHT}initial/aggr/`,
      {
        tenantId,
        deviceId,
      }
    );
    return response.data;
  }

  async getPressureSensorMetrics(
    tenantId: string,
    deviceId: string
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_PRESSURE}initial/aggr/`,
      {
        tenantId,
        deviceId,
      }
    );
    return response.data;
  }

  // Historical Data APIs
  async getSensorDataByDate(
    tenantId: string,
    deviceId: string,
    sensorType: SensorType,
    dateRange: DateRange
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(`${API_ENDPOINTS.SENSOR_HEALTH}date`, {
      tenantId,
      deviceId,
      attributeName: sensorType,
      fromDate: dateRange.from.toISOString(),
      toDate: dateRange.to.toISOString(),
    });
    return response.data;
  }

  async getTempSensorByDate(
    tenantId: string,
    deviceId: string,
    dateRange: DateRange
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_TEMP}date/aggr`,
      {
        tenantId,
        deviceId,
        fromDate: dateRange.from.toISOString(),
        toDate: dateRange.to.toISOString(),
      }
    );
    return response.data;
  }

  async getHumidSensorByDate(
    tenantId: string,
    deviceId: string,
    dateRange: DateRange
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_HUMIDITY}date/aggr`,
      {
        tenantId,
        deviceId,
        fromDate: dateRange.from.toISOString(),
        toDate: dateRange.to.toISOString(),
      }
    );
    return response.data;
  }

  async getAmblSensorByDate(
    tenantId: string,
    deviceId: string,
    dateRange: DateRange
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_AMBLIGHT}date/aggr`,
      {
        tenantId,
        deviceId,
        fromDate: dateRange.from.toISOString(),
        toDate: dateRange.to.toISOString(),
      }
    );
    return response.data;
  }

  async getPressureSensorByDate(
    tenantId: string,
    deviceId: string,
    dateRange: DateRange
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_PRESSURE}date/aggr`,
      {
        tenantId,
        deviceId,
        fromDate: dateRange.from.toISOString(),
        toDate: dateRange.to.toISOString(),
      }
    );
    return response.data;
  }

  // Proximity Data APIs
  async getSensorProximityInitial(sourceId: string): Promise<ApiResponse<any>> {
    const response = await this.api.get(
      `${API_ENDPOINTS.SENSOR_PROXIMITY}initial/${sourceId}`
    );
    return response.data;
  }

  async getSensorProximityInitialByLocation(
    tenantId: string,
    locationId: string
  ): Promise<ApiResponse<any>> {
    const response = await this.api.get(
      `${API_ENDPOINTS.SENSOR_PROXIMITY}initial/${tenantId}/${locationId}`
    );
    return response.data;
  }

  async getSensorProximityByDate(
    tenantId: string,
    deviceId: string,
    dateRange: DateRange
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_PROXIMITY}date`,
      {
        tenantId,
        deviceId,
        fromDate: dateRange.from.toISOString(),
        toDate: dateRange.to.toISOString(),
      }
    );
    return response.data;
  }

  // Gateway Sensor Data API
  async getGatewaySensorInitialData(
    tenantId: string,
    deviceId: string,
    attributeName: string,
    params: any = {}
  ): Promise<ApiResponse<any>> {
    const response = await this.api.post(
      `${API_ENDPOINTS.SENSOR_HEALTH}initial/aggr`,
      {
        tenantId,
        deviceId,
        attributeName,
        ...params,
      }
    );
    return response.data;
  }

  // Reports API
  async getRfidReport(
    tenantId: string,
    dateRange: DateRange
  ): Promise<ApiResponse<any>> {
    const response = await this.alternateApi.get(
      `${
        API_ENDPOINTS.RFID_REPORT
      }${tenantId}/${dateRange.from.toISOString()}/${dateRange.to.toISOString()}`
    );
    return response.data;
  }
}

// Export singleton instance
export const assetTrackingAPI = new AssetTrackingAPI();
export default assetTrackingAPI;
