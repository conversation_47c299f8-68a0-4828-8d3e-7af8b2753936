@import '../../../Colors.less';

.drawer {
  .content {
    padding: 24px !important;
  }
  .title {
    font-size: 1.5rem;
    font-weight: bold;
    background-color: @white-hex !important;
    color: @primary-color-hex;
    padding: 24px 70px 24px 24px !important;
    border-bottom: 5px solid @white3-hex;
  }
  .footer {
    button {
      margin-top: 10px;
    }
  }
  :global {
    .ant-drawer-close {
      margin-right: 10px !important;
      position: absolute;
      z-index: 1000 !important;
      right: 0;
      top: 30px;
      .anticon-close {
        color: @primary-color-hex !important;
        padding: 10px !important;
      }
    }
  }
}
