"use client";

import React, { useState } from "react";
import { ChevronDown, ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface ZoneTreeNode {
  id: string;
  name: string;
  assetCount: number;
  color: string;
  type?: string;
  children?: ZoneTreeNode[];
}

interface ZoneTreeProps {
  data: ZoneTreeNode[];
  className?: string;
}

interface ZoneTreeItemProps {
  node: ZoneTreeNode;
  level: number;
  isLast: boolean;
}

const ZoneTreeItem: React.FC<ZoneTreeItemProps> = ({ node, level, isLast }) => {
  const [isExpanded, setIsExpanded] = useState(true); // Default expanded like old implementation
  const hasChildren = node.children && node.children.length > 0;

  const toggleExpanded = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
  };

  const getTypeColor = (type?: string) => {
    switch (type?.toLowerCase()) {
      case "fixed":
        return "bg-blue-100 text-blue-800";
      case "lint":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <>
      <tr className="border-b hover:bg-gray-50">
        <td className="p-4">
          <div
            className="flex items-center cursor-pointer"
            style={{ paddingLeft: `${level * 20}px` }}
            onClick={toggleExpanded}
          >
            {hasChildren ? (
              isExpanded ? (
                <ChevronDown className="h-4 w-4 mr-2 text-gray-500" />
              ) : (
                <ChevronRight className="h-4 w-4 mr-2 text-gray-500" />
              )
            ) : (
              <div className="w-4 h-4 mr-2" />
            )}
            <span className="font-medium">{node.name}</span>
          </div>
        </td>
        <td className="p-4">
          <Badge variant="outline" className={getTypeColor(node.type)}>
            {node.type || "Zone"}
          </Badge>
        </td>
        <td className="p-4">
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            {node.assetCount}
          </Badge>
        </td>
      </tr>
      {hasChildren &&
        isExpanded &&
        node.children!.map((child, index) => (
          <ZoneTreeItem
            key={child.id}
            node={child}
            level={level + 1}
            isLast={index === node.children!.length - 1}
          />
        ))}
    </>
  );
};

export const ZoneTree: React.FC<ZoneTreeProps> = ({ data, className = "" }) => {
  return (
    <div className={`overflow-x-auto ${className}`}>
      <table className="w-full">
        <thead className="bg-gray-50 border-b">
          <tr>
            <th className="text-left p-4 font-medium text-gray-900">Zone</th>
            <th className="text-left p-4 font-medium text-gray-900">Type</th>
            <th className="text-left p-4 font-medium text-gray-900">Count</th>
          </tr>
        </thead>
        <tbody>
          {data.map((node, index) => (
            <ZoneTreeItem
              key={node.id}
              node={node}
              level={0}
              isLast={index === data.length - 1}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ZoneTree;
