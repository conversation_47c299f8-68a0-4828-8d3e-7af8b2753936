import * as z from "zod";

// Re-export asset schemas
export * from "./asset";

export const LoginSchema = z.object({
  username: z.string().min(1, { message: "Email is required" }),
  password: z.string().min(1, { message: "Password is required" }),
});

// Account Management Schemas
export const CreateAccountSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Name is required" })
    .max(200, { message: "Name must be less than 200 characters" }),
  userId: z
    .string()
    .min(1, { message: "Username is required" })
    .max(50, { message: "Username must be less than 50 characters" }),
  emailId: z
    .string()
    .email({ message: "Invalid email format" })
    .optional()
    .or(z.literal("")),
  mobileNumber: z
    .string()
    .min(10, { message: "Mobile number must be at least 10 digits" })
    .max(15, { message: "Mobile number must be less than 15 digits" })
    .regex(/^[0-9+\-\s()]+$/, { message: "Invalid mobile number format" }),
  tenantId: z.string().min(1, { message: "Tenant is required" }),
  roles: z
    .array(z.string())
    .min(1, { message: "At least one role is required" }),
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters" })
    .optional(),
});

export const UpdateAccountSchema = z.object({
  id: z.string().min(1, { message: "Account ID is required" }),
  name: z
    .string()
    .min(1, { message: "Name is required" })
    .max(200, { message: "Name must be less than 200 characters" }),
  userId: z
    .string()
    .min(1, { message: "Username is required" })
    .max(50, { message: "Username must be less than 50 characters" }),
  emailId: z
    .string()
    .email({ message: "Invalid email format" })
    .optional()
    .or(z.literal("")),
  mobileNumber: z
    .string()
    .min(10, { message: "Mobile number must be at least 10 digits" })
    .max(15, { message: "Mobile number must be less than 15 digits" })
    .regex(/^[0-9+\-\s()]+$/, { message: "Invalid mobile number format" }),
  tenantId: z.string().min(1, { message: "Tenant is required" }),
  roles: z
    .array(z.string())
    .min(1, { message: "At least one role is required" }),
  active: z.boolean(),
});

export const PasswordResetSchema = z
  .object({
    userId: z.string().min(1, { message: "User ID is required" }),
    currentPassword: z.string().optional(),
    newPassword: z
      .string()
      .min(6, { message: "Password must be at least 6 characters" }),
    confirmPassword: z
      .string()
      .min(6, { message: "Confirm password is required" }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export const ForgotPasswordSchema = z
  .object({
    email: z
      .string()
      .email({ message: "Invalid email format" })
      .optional()
      .or(z.literal("")),
    userId: z.string().min(1, { message: "Username is required" }).optional(),
  })
  .refine((data) => data.email || data.userId, {
    message: "Either email or username is required",
    path: ["email"],
  });

export const AccountSearchSchema = z.object({
  searchTerm: z.string().optional(),
  tenantId: z.string().optional(),
  roleId: z.string().optional(),
  active: z.boolean().optional(),
  deleted: z.boolean().optional(),
});

export const AccountPaginationSchema = z.object({
  pageNumber: z.number().min(1, { message: "Page number must be at least 1" }),
  pageSize: z.union([z.number().min(1), z.literal("all")]),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

// Role Management Schemas
export const CreateRoleSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Role name is required" })
    .max(100, { message: "Role name must be less than 100 characters" }),
  description: z
    .string()
    .max(500, { message: "Description must be less than 500 characters" })
    .optional(),
  permissions: z.array(z.string()).optional(),
});

export const UpdateRoleSchema = z.object({
  id: z.string().min(1, { message: "Role ID is required" }),
  name: z
    .string()
    .min(1, { message: "Role name is required" })
    .max(100, { message: "Role name must be less than 100 characters" }),
  description: z
    .string()
    .max(500, { message: "Description must be less than 500 characters" })
    .optional(),
  permissions: z.array(z.string()).optional(),
});

// Tenant Management Schemas
export const CreateTenantSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Tenant name is required" })
    .max(200, { message: "Tenant name must be less than 200 characters" }),
  type: z
    .string()
    .min(1, { message: "Tenant type is required" })
    .max(200, { message: "Tenant type must be less than 200 characters" }),
  orgName: z
    .string()
    .min(1, { message: "Organization name is required" })
    .max(200, {
      message: "Organization name must be less than 200 characters",
    }),
  enable: z.boolean(),
  latitude: z
    .number()
    .min(-90, { message: "Latitude must be between -90 and 90" })
    .max(90, { message: "Latitude must be between -90 and 90" })
    .optional(),
  longitude: z
    .number()
    .min(-180, { message: "Longitude must be between -180 and 180" })
    .max(180, { message: "Longitude must be between -180 and 180" })
    .optional(),
});

export const UpdateTenantSchema = z.object({
  id: z.string().min(1, { message: "Tenant ID is required" }),
  name: z
    .string()
    .min(1, { message: "Tenant name is required" })
    .max(200, { message: "Tenant name must be less than 200 characters" }),
  type: z
    .string()
    .min(1, { message: "Tenant type is required" })
    .max(200, { message: "Tenant type must be less than 200 characters" }),
  orgName: z
    .string()
    .min(1, { message: "Organization name is required" })
    .max(200, {
      message: "Organization name must be less than 200 characters",
    }),
  enable: z.boolean(),
  latitude: z
    .number()
    .min(-90, { message: "Latitude must be between -90 and 90" })
    .max(90, { message: "Latitude must be between -90 and 90" })
    .optional(),
  longitude: z
    .number()
    .min(-180, { message: "Longitude must be between -180 and 180" })
    .max(180, { message: "Longitude must be between -180 and 180" })
    .optional(),
  deleted: z.boolean().optional(),
});

// Export types from schemas
export type CreateAccountFormData = z.infer<typeof CreateAccountSchema>;
export type UpdateAccountFormData = z.infer<typeof UpdateAccountSchema>;
export type PasswordResetFormData = z.infer<typeof PasswordResetSchema>;
export type ForgotPasswordFormData = z.infer<typeof ForgotPasswordSchema>;
export type AccountSearchFilters = z.infer<typeof AccountSearchSchema>;
export type AccountPaginationParams = z.infer<typeof AccountPaginationSchema>;
export type CreateRoleFormData = z.infer<typeof CreateRoleSchema>;
export type UpdateRoleFormData = z.infer<typeof UpdateRoleSchema>;
export type CreateTenantFormData = z.infer<typeof CreateTenantSchema>;
export type UpdateTenantFormData = z.infer<typeof UpdateTenantSchema>;

// Branch Management Schemas
export const CreateBranchSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Branch name is required" })
    .max(200, { message: "Branch name must be less than 200 characters" }),
  address: z
    .string()
    .min(1, { message: "Address is required" })
    .max(200, { message: "Address must be less than 200 characters" }),
  gpsPoint: z.object({
    latitude: z
      .string()
      .min(1, { message: "Latitude is required" })
      .regex(/^-?([1-8]?[0-9](\.[0-9]+)?|90(\.0+)?)$/, {
        message: "Invalid latitude format",
      }),
    longitude: z
      .string()
      .min(1, { message: "Longitude is required" })
      .regex(/^-?((1[0-7][0-9])|([1-9]?[0-9]))(\.[0-9]+)?$|^-?180(\.0+)?$/, {
        message: "Invalid longitude format",
      }),
  }),
  geoJson: z
    .string()
    .min(1, { message: "Geo location is required" })
    .max(2000, { message: "Geo location must be less than 2000 characters" }),
});

export const UpdateBranchSchema = z.object({
  id: z.string().min(1, { message: "Branch ID is required" }),
  name: z
    .string()
    .min(1, { message: "Branch name is required" })
    .max(200, { message: "Branch name must be less than 200 characters" }),
  address: z
    .string()
    .min(1, { message: "Address is required" })
    .max(200, { message: "Address must be less than 200 characters" }),
  gpsPoint: z.object({
    latitude: z
      .string()
      .min(1, { message: "Latitude is required" })
      .regex(/^-?([1-8]?[0-9](\.[0-9]+)?|90(\.0+)?)$/, {
        message: "Invalid latitude format",
      }),
    longitude: z
      .string()
      .min(1, { message: "Longitude is required" })
      .regex(/^-?((1[0-7][0-9])|([1-9]?[0-9]))(\.[0-9]+)?$|^-?180(\.0+)?$/, {
        message: "Invalid longitude format",
      }),
  }),
  geoJson: z
    .string()
    .min(1, { message: "Geo location is required" })
    .max(2000, { message: "Geo location must be less than 2000 characters" }),
});

export const BranchSearchSchema = z.object({
  searchTerm: z.string().optional(),
  deleted: z.boolean().optional(),
});

export const BranchPaginationSchema = z.object({
  pageNumber: z.number().min(1, { message: "Page number must be at least 1" }),
  pageSize: z.union([z.number().min(1), z.literal("all")]),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export type CreateBranchFormData = z.infer<typeof CreateBranchSchema>;
export type UpdateBranchFormData = z.infer<typeof UpdateBranchSchema>;
export type BranchSearchFilters = z.infer<typeof BranchSearchSchema>;
export type BranchPaginationParams = z.infer<typeof BranchPaginationSchema>;

// Location Management Schemas
export const CreateLocationSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Location name is required" })
    .max(200, { message: "Location name must be less than 200 characters" }),
  address: z
    .string()
    .min(1, { message: "Address is required" })
    .max(200, { message: "Address must be less than 200 characters" }),
  branchId: z.string().min(1, { message: "Branch is required" }),
  geoJson: z
    .string()
    .min(1, { message: "Geo location is required" })
    .max(2000, { message: "Geo location must be less than 2000 characters" }),
  gpsPoint: z.object({
    latitude: z
      .string()
      .min(1, { message: "Latitude is required" })
      .regex(/^-?([1-8]?[0-9](\.[0-9]+)?|90(\.0+)?)$/, {
        message: "Invalid latitude format",
      }),
    longitude: z
      .string()
      .min(1, { message: "Longitude is required" })
      .regex(/^-?((1[0-7][0-9])|([1-9]?[0-9]))(\.[0-9]+)?$|^-?180(\.0+)?$/, {
        message: "Invalid longitude format",
      }),
  }),
});

export const UpdateLocationSchema = z.object({
  id: z.string().min(1, { message: "Location ID is required" }),
  name: z
    .string()
    .min(1, { message: "Location name is required" })
    .max(200, { message: "Location name must be less than 200 characters" }),
  address: z
    .string()
    .min(1, { message: "Address is required" })
    .max(200, { message: "Address must be less than 200 characters" }),
  branchId: z.string().min(1, { message: "Branch is required" }),
  geoJson: z
    .string()
    .min(1, { message: "Geo location is required" })
    .max(2000, { message: "Geo location must be less than 2000 characters" }),
  gpsPoint: z.object({
    latitude: z
      .string()
      .min(1, { message: "Latitude is required" })
      .regex(/^-?([1-8]?[0-9](\.[0-9]+)?|90(\.0+)?)$/, {
        message: "Invalid latitude format",
      }),
    longitude: z
      .string()
      .min(1, { message: "Longitude is required" })
      .regex(/^-?((1[0-7][0-9])|([1-9]?[0-9]))(\.[0-9]+)?$|^-?180(\.0+)?$/, {
        message: "Invalid longitude format",
      }),
  }),
});

export const LocationSearchSchema = z.object({
  searchTerm: z.string().optional(),
  branchId: z.string().optional(),
  deleted: z.boolean().optional(),
});

export const LocationPaginationSchema = z.object({
  pageNumber: z.number().min(1, { message: "Page number must be at least 1" }),
  pageSize: z.union([z.number().min(1), z.literal("all")]),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export type CreateLocationFormData = z.infer<typeof CreateLocationSchema>;
export type UpdateLocationFormData = z.infer<typeof UpdateLocationSchema>;
export type LocationSearchFilters = z.infer<typeof LocationSearchSchema>;
export type LocationPaginationParams = z.infer<typeof LocationPaginationSchema>;

// Area Management Schemas
export const CreateAreaSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Area name is required" })
    .max(200, { message: "Area name must be less than 200 characters" }),
  address: z
    .string()
    .min(1, { message: "Address is required" })
    .max(200, { message: "Address must be less than 200 characters" }),
  branchId: z.string().min(1, { message: "Branch selection is required" }),
  locationId: z.string().min(1, { message: "Location selection is required" }),
  gpsPoint: z.object({
    latitude: z
      .string()
      .min(1, { message: "Latitude is required" })
      .refine((val) => !isNaN(parseFloat(val)), {
        message: "Latitude must be a valid number",
      })
      .refine((val) => parseFloat(val) >= -90 && parseFloat(val) <= 90, {
        message: "Latitude must be between -90 and 90",
      }),
    longitude: z
      .string()
      .min(1, { message: "Longitude is required" })
      .refine((val) => !isNaN(parseFloat(val)), {
        message: "Longitude must be a valid number",
      })
      .refine((val) => parseFloat(val) >= -180 && parseFloat(val) <= 180, {
        message: "Longitude must be between -180 and 180",
      }),
  }),
  geoJson: z
    .string()
    .min(1, { message: "Geo location is required" })
    .max(2000, { message: "Geo location must be less than 2000 characters" }),
  level: z.string().min(1, { message: "Level is required" }),
  min: z.string().min(1, { message: "Min is required" }),
  max: z.string().min(1, { message: "Max is required" }),
});

export const UpdateAreaSchema = z.object({
  id: z.string().min(1, { message: "Area ID is required" }),
  name: z
    .string()
    .min(1, { message: "Area name is required" })
    .max(200, { message: "Area name must be less than 200 characters" }),
  address: z
    .string()
    .min(1, { message: "Address is required" })
    .max(200, { message: "Address must be less than 200 characters" }),
  branchId: z.string().min(1, { message: "Branch selection is required" }),
  locationId: z.string().min(1, { message: "Location selection is required" }),
  gpsPoint: z.object({
    latitude: z
      .string()
      .min(1, { message: "Latitude is required" })
      .refine((val) => !isNaN(parseFloat(val)), {
        message: "Latitude must be a valid number",
      })
      .refine((val) => parseFloat(val) >= -90 && parseFloat(val) <= 90, {
        message: "Latitude must be between -90 and 90",
      }),
    longitude: z
      .string()
      .min(1, { message: "Longitude is required" })
      .refine((val) => !isNaN(parseFloat(val)), {
        message: "Longitude must be a valid number",
      })
      .refine((val) => parseFloat(val) >= -180 && parseFloat(val) <= 180, {
        message: "Longitude must be between -180 and 180",
      }),
  }),
  geoJson: z
    .string()
    .min(1, { message: "Geo location is required" })
    .max(2000, { message: "Geo location must be less than 2000 characters" }),
  level: z.string().min(1, { message: "Level is required" }),
  min: z.string().min(1, { message: "Min is required" }),
  max: z.string().min(1, { message: "Max is required" }),
});

export const AreaSearchSchema = z.object({
  searchTerm: z.string().optional(),
  branchId: z.string().optional(),
  locationId: z.string().optional(),
  deleted: z.boolean().optional(),
});

export const AreaPaginationSchema = z.object({
  pageNumber: z.number().min(1, { message: "Page number must be at least 1" }),
  pageSize: z.union([z.number().min(1), z.literal("all")]),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export type CreateAreaFormData = z.infer<typeof CreateAreaSchema>;
export type UpdateAreaFormData = z.infer<typeof UpdateAreaSchema>;
export type AreaSearchFilters = z.infer<typeof AreaSearchSchema>;
export type AreaPaginationParams = z.infer<typeof AreaPaginationSchema>;

// Tag Management Schemas
export const CreateTagSchema = z.object({
  name: z
    .string()
    .min(1, { message: "Tag name is required" })
    .max(200, { message: "Tag name must be less than 200 characters" }),
  description: z
    .string()
    .min(1, { message: "Description is required" })
    .max(200, { message: "Description must be less than 200 characters" }),
  modelId: z.string().min(1, { message: "Model selection is required" }),
  externalId: z
    .string()
    .min(1, { message: "External ID is required" })
    .max(100, { message: "External ID must be less than 100 characters" }),
  status: z.boolean().optional(),
});

export const UpdateTagSchema = z.object({
  id: z.string().min(1, { message: "Tag ID is required" }),
  name: z
    .string()
    .min(1, { message: "Tag name is required" })
    .max(200, { message: "Tag name must be less than 200 characters" }),
  description: z
    .string()
    .min(1, { message: "Description is required" })
    .max(200, { message: "Description must be less than 200 characters" }),
  modelId: z.string().min(1, { message: "Model selection is required" }),
  externalId: z
    .string()
    .min(1, { message: "External ID is required" })
    .max(100, { message: "External ID must be less than 100 characters" }),
  status: z.boolean().optional(),
});

export const TagSearchSchema = z.object({
  searchTerm: z.string().optional(),
  modelId: z.string().optional(),
  status: z.boolean().optional(),
  deleted: z.boolean().optional(),
});

export const TagPaginationSchema = z.object({
  pageNumber: z.number().min(1, { message: "Page number must be at least 1" }),
  pageSize: z.union([z.number().min(1), z.literal("all")]),
  sortBy: z.string().optional(),
  sortOrder: z.enum(["asc", "desc"]).optional(),
});

export type CreateTagFormData = z.infer<typeof CreateTagSchema>;
export type UpdateTagFormData = z.infer<typeof UpdateTagSchema>;
export type TagSearchFilters = z.infer<typeof TagSearchSchema>;
export type TagPaginationParams = z.infer<typeof TagPaginationSchema>;
