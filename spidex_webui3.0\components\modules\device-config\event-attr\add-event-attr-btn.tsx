import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  She<PERSON><PERSON>lose,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import AddEventAttrForm from "./add-event-attr-form";

export default function AddEventAttributeBtn() {
  return (
    <Sheet>
      <SheetTrigger className="ml-auto" asChild>
        <Button>Add Event Attribute</Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Attribute</SheetTitle>
        </SheetHeader>
        <AddEventAttrForm />
        <SheetFooter>
          <SheetClose asChild>
            <Button type="submit">Save changes</Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
