@primary-color-hex: @primary-color;
@white-hex: #ffffff;
@black-hex: #000000;
@solitude-hex: #f2f4f8;
@solitude2-hex: #fafafa;
@ant-border-hex: #d9d9d9;
@warning-hex: #faad14;
@warning-hover-hex: tint(#faad14, 20%);
@google-button-hex: #5eba7d;
@google-button-hover-hex: tint(#5eba7d, 20%);
@disabled-grey-hex: #888888;
@light-grey-hex: #fafbfc;
@light-grey2-hex: #ececec;
@light-sky-blue: #dfedfd;
@light-sky-blue-hex: #9bb1c4;
@light-sky-red-hex: #f5f5f5;
@white2-hex: #f3f1f5;
@gray-hex: #848484;
@red-hex: #b90000;
@orange: #ffa004;
@grey-light-hex: #f8f8f8;
@gray-dark-hex: #bcc1cb;
@dark-gray-hex: #4b4c50;
@light-smoke-gray-hex: #d3d3d3;
@light-gray-hex: #a3a3a3;
@white3-hex: #f0f2f5;
@green-hex: #32cd30;
@green2-hex: #2ec22b;
@blue-hex: #1890ff;
@blue2-hex: #177ddc;
@color-red: #f44336;
@color-gray-light: #9a9a9a;
@color-skyblue: #2e5cf6;
@color-shaded-gray: #f7f7f7;
@color-medium-grey: #737575;
@color-light-dark: #969799;
@color-red-light: #ffdfdf;
@color-red-primary: #cd0000;
@color-lighten-grey: #eaeffe;
@color-primary: #2e5cf6;
@color-lightish-grey: #f9f9f9;
@color-light-white: #f7f9fe;
@color-hightlight-text: #2e5cf6;
@color-greyish-dark: #cccccc;
@color-dark-grey: #696969;
@primary-color-theme: #107bd0;
