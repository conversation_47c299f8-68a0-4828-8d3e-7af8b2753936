"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main-layout)/device/management/configure/location/[branchId]/page",{

/***/ "(app-pages-browser)/./components/modules/location/location-search-pagination.tsx":
/*!********************************************************************!*\
  !*** ./components/modules/location/location-search-pagination.tsx ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LocationSearchPagination: () => (/* binding */ LocationSearchPagination)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ LocationSearchPagination auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction LocationSearchPagination(param) {\n    let { searchFilters, showDeleted, totalRecords, branches = [], onSearchChange, onClearSearch, onToggleShowDeleted } = param;\n    var _branches_find, _branches_find1;\n    _s();\n    const [localSearchTerm, setLocalSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(searchFilters.searchTerm || \"\");\n    // Update local search term when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LocationSearchPagination.useEffect\": ()=>{\n            setLocalSearchTerm(searchFilters.searchTerm || \"\");\n        }\n    }[\"LocationSearchPagination.useEffect\"], [\n        searchFilters.searchTerm\n    ]);\n    const handleSearchSubmit = (e)=>{\n        e.preventDefault();\n        onSearchChange({\n            searchTerm: localSearchTerm\n        });\n    };\n    const handleSearchChange = (value)=>{\n        setLocalSearchTerm(value);\n        // Debounced search - trigger search after user stops typing\n        const timeoutId = setTimeout(()=>{\n            onSearchChange({\n                searchTerm: value\n            });\n        }, 300);\n        return ()=>clearTimeout(timeoutId);\n    };\n    const handleClearSearch = ()=>{\n        setLocalSearchTerm(\"\");\n        onClearSearch();\n    };\n    const handleBranchChange = (branchId)=>{\n        onSearchChange({\n            branchId: branchId === \"all\" ? \"\" : branchId\n        });\n    };\n    const hasActiveFilters = searchFilters.searchTerm || searchFilters.branchId || showDeleted;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSearchSubmit,\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"search\",\n                                    className: \"sr-only\",\n                                    children: \"Search locations\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            id: \"search\",\n                                            type: \"text\",\n                                            placeholder: \"Search by location name, address, or coordinates...\",\n                                            value: localSearchTerm,\n                                            onChange: (e)=>handleSearchChange(e.target.value),\n                                            className: \"pl-10 pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        localSearchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            type: \"button\",\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: handleClearSearch,\n                                            className: \"absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full sm:w-48\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                    htmlFor: \"branch-filter\",\n                                    className: \"sr-only\",\n                                    children: \"Filter by branch\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                    value: searchFilters.branchId || \"all\",\n                                    onValueChange: handleBranchChange,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                placeholder: \"All branches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                    value: \"all\",\n                                                    children: \"All Branches\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                branches.map((branch)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: branch.id,\n                                                        children: branch.name\n                                                    }, branch.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: handleClearSearch,\n                            className: \"whitespace-nowrap\",\n                            children: \"Clear All\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row sm:items-center gap-4 pt-2 border-t\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                id: \"showDeleted\",\n                                checked: showDeleted,\n                                onCheckedChange: onToggleShowDeleted\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                htmlFor: \"showDeleted\",\n                                className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                children: \"Show Deleted Locations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                \"Showing \",\n                                totalRecords,\n                                \" of \",\n                                totalAllRecords,\n                                \" locations\",\n                                searchFilters.searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-1\",\n                                    children: [\n                                        'matching \"',\n                                        searchFilters.searchTerm,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 17\n                                }, this),\n                                searchFilters.branchId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-1\",\n                                    children: [\n                                        \"in\",\n                                        \" \",\n                                        ((_branches_find = branches.find((b)=>b.id === searchFilters.branchId)) === null || _branches_find === void 0 ? void 0 : _branches_find.name) || \"selected branch\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 17\n                                }, this),\n                                showDeleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-1\",\n                                    children: \"(deleted only)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 31\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-2 pt-2 border-t\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: \"Active filters:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    searchFilters.searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                        variant: \"secondary\",\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            'Search: \"',\n                            searchFilters.searchTerm,\n                            '\"',\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>onSearchChange({\n                                        searchTerm: \"\"\n                                    }),\n                                className: \"h-4 w-4 p-0 ml-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, this),\n                    searchFilters.branchId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                        variant: \"secondary\",\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            \"Branch:\",\n                            \" \",\n                            ((_branches_find1 = branches.find((b)=>b.id === searchFilters.branchId)) === null || _branches_find1 === void 0 ? void 0 : _branches_find1.name) || \"Unknown\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>onSearchChange({\n                                        branchId: \"\"\n                                    }),\n                                className: \"h-4 w-4 p-0 ml-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, this),\n                    showDeleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                        variant: \"secondary\",\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            \"Show Deleted\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>onToggleShowDeleted(false),\n                                className: \"h-4 w-4 p-0 ml-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Spidex\\\\spidex_webui3.0\\\\components\\\\modules\\\\location\\\\location-search-pagination.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_s(LocationSearchPagination, \"MMWzVmPyGnrUk88drIREfLjueSs=\");\n_c = LocationSearchPagination;\nvar _c;\n$RefreshReg$(_c, \"LocationSearchPagination\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/modules/location/location-search-pagination.tsx\n"));

/***/ })

});