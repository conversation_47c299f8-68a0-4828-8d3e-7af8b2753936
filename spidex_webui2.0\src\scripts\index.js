import axios from 'axios';
require('dotenv').config();

const getAxiosInstance = (token) => {
  const instance = axios.create({
    baseURL: process.env.REACT_APP_SPIDEX_API_BASE_URL,
  });
  instance.defaults.headers['Content-Type'] = 'application/api.spidex.v1+json';
  instance.defaults.headers.common['Accept'] = 'application/api.spidex.v1+json';
  instance.interceptors.request.use(
    (req) => {
      req.headers.Authorization = `Bearer ${token}`;
      req.data = req.data || {};
      return req;
    },
    (err) => {
      return Promise.reject(err);
    }
  );
  return instance;
};

export default getAxiosInstance;
