"use client";

import { useState, useEffect } from "react";
import { Area } from "@/types/area";
import { Gateway } from "@/types/gateway";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Link, AlertCircle } from "lucide-react";

interface BindGatewayModalProps {
  isOpen: boolean;
  area: Area | null;
  gateways: Gateway[];
  isLoading?: boolean;
  onClose: () => void;
  onBindGateway: (gatewayId: string) => Promise<void>;
}

export function BindGatewayModal({
  isOpen,
  area,
  gateways,
  isLoading = false,
  onClose,
  onBindGateway,
}: BindGatewayModalProps) {
  const [selectedGatewayId, setSelectedGatewayId] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset selection when modal opens/closes or area changes
  useEffect(() => {
    if (isOpen && area) {
      setSelectedGatewayId("");
    }
  }, [isOpen, area]);

  // Filter available gateways (show all non-deleted gateways like old application)
  const availableGateways = gateways.filter((gateway) => !gateway.deleted);

  const selectedGateway = availableGateways.find(
    (gateway) => gateway.id === selectedGatewayId
  );

  const handleSubmit = async () => {
    if (!selectedGatewayId || !area) return;

    try {
      setIsSubmitting(true);
      await onBindGateway(selectedGatewayId);
      setSelectedGatewayId("");
      onClose();
    } catch (error) {
      console.error("Error binding gateway:", error);
      // Error handling is done in the parent component
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setSelectedGatewayId("");
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Link className="h-5 w-5" />
            {area
              ? `Bind Area ${area.name} to Gateway?`
              : "Bind Area to Gateway"}
          </DialogTitle>
          <DialogDescription>
            {area && (
              <>
                Bind area <strong>&quot;{area.name}&quot;</strong> to a gateway.
                Select an available gateway from the list below.
              </>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading gateways...</span>
            </div>
          ) : availableGateways.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No available gateways found. All gateways may already be bound
                to areas or there are no active gateways in the system.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="gateway-select">Select Gateway</Label>
              <Select
                value={selectedGatewayId}
                onValueChange={setSelectedGatewayId}
                disabled={isSubmitting}
              >
                <SelectTrigger id="gateway-select">
                  <SelectValue placeholder="Select Gateway" />
                </SelectTrigger>
                <SelectContent>
                  {availableGateways.map((gateway) => (
                    <SelectItem key={gateway.id} value={gateway.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{gateway.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {gateway.categoryType &&
                            `Type: ${gateway.categoryType}`}
                          {gateway.areaName &&
                            ` • Currently bound to: ${gateway.areaName}`}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedGateway && (
                <div className="mt-3 p-3 bg-muted rounded-md">
                  <h4 className="font-medium text-sm mb-2">
                    Selected Gateway Details:
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div>
                      <span className="font-medium">Name:</span>{" "}
                      {selectedGateway.name}
                    </div>
                    {selectedGateway.categoryType && (
                      <div>
                        <span className="font-medium">Type:</span>{" "}
                        {selectedGateway.categoryType}
                      </div>
                    )}
                    {selectedGateway.areaName && (
                      <div>
                        <span className="font-medium">Currently bound to:</span>{" "}
                        {selectedGateway.areaName}
                      </div>
                    )}
                    {selectedGateway.description && (
                      <div>
                        <span className="font-medium">Description:</span>{" "}
                        {selectedGateway.description}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              !selectedGatewayId ||
              isSubmitting ||
              isLoading ||
              availableGateways.length === 0
            }
          >
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Bind Gateway
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
