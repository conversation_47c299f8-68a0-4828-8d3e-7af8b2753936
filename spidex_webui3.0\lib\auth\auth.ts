import NextAuth, { AuthError, CredentialsSignin } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import type { Provider } from "next-auth/providers";
import { LoginSchema } from "@/lib/schemas";
import { isExpired } from "../utils";
import { SPIDEX_API_BASE_URL } from "../constants";

const providers: Provider[] = [
  Credentials({
    authorize: async (credentials) => {
      const validatedFields = LoginSchema.safeParse(credentials);
      if (validatedFields.success) {
        const { username, password } = validatedFields.data;
        const headers = new Headers();
        headers.append("Content-Type", "application/api.spidex.v1+json");
        headers.append("Accept", "application/api.spidex.v1+json");
        const res = await fetch(SPIDEX_API_BASE_URL + "/user/login", {
          method: "POST",
          body: JSON.stringify({ username, password }),
          headers: headers,
        });

        if (!res.ok) {
          const err = await res.json();
          console.log(err);
          throw new CredentialsSignin();
        }

        const user = await res.json();
        if (!user) {
          // No user found, so this is their first attempt to login
          // meaning this is also the place you could do registration
          throw new Error("User not found.");
        }

        // Log the user object to verify tenant ID is included
        console.log("Login successful, user data:", {
          userId: user.userId,
          tenantId: user.tenantId,
          roles: user.roles,
          hasToken: !!user.token,
          locationId: user.locationId,
          resetPassword: user.resetPassword,
          id: user.id,
        });

        // return user object with the their profile data
        return user;
      }
      return null;
    },
  }),
];

export const providerMap = providers.map((provider) => {
  if (typeof provider === "function") {
    const providerData = provider();
    return { id: providerData.id, name: providerData.name };
  } else {
    return { id: provider.id, name: provider.name };
  }
});

export const { handlers, auth, signIn, signOut } = NextAuth({
  providers,
  pages: {
    signIn: "/login",
    signOut: "/login",
  },
  callbacks: {
    async jwt({ user, token }) {
      if (user) {
        token.user = user;
      } else if (token.user && isExpired(token.user.token)) {
        // Subsequent logins, check if `access_token` is still valid
        return null;
      }
      return token;
    },
    async session({ session, token }) {
      session.user = token.user;
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Handle sign-out redirect
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allow callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl + "/login";
    },
  },
  session: {
    strategy: "jwt",
  },
});
