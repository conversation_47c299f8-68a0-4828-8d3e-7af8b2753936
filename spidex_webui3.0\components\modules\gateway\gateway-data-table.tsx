"use client";

import { useState } from "react";
import { More<PERSON><PERSON><PERSON><PERSON>, Edit, Trash2, Power, PowerOff } from "lucide-react";
import {
  Gateway,
  GatewayPaginationParams,
  GatewayPageSize,
} from "@/types/gateway";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { GatewayTablePagination } from "./gateway-table-pagination";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface GatewayDataTableProps {
  data: Gateway[];
  pagination: GatewayPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: GatewayPageSize[];
  onEdit?: (gateway: Gateway) => void;
  onDelete?: (gatewayId: string) => void;
  onView?: (gateway: Gateway) => void;
  onToggleActive?: (gatewayId: string, active: boolean) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: GatewayPageSize) => void;
  isLoading?: boolean;
}

export function GatewayDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onEdit,
  onDelete,
  onView,
  onToggleActive,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
}: GatewayDataTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [gatewayToDelete, setGatewayToDelete] = useState<Gateway | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  const handleDeleteClick = (gateway: Gateway) => {
    setGatewayToDelete(gateway);
    setDeleteDialogOpen(true);
    setOpenDropdownId(null); // Close dropdown
  };

  const handleDeleteConfirm = () => {
    if (gatewayToDelete && onDelete) {
      onDelete(gatewayToDelete.id);
    }
    setDeleteDialogOpen(false);
    setGatewayToDelete(null);
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>MAC ID</TableHead>
              <TableHead>Area/Location</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-left">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <p className="text-muted-foreground">No gateways found</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              data.map((gateway) => (
                <TableRow
                  key={gateway.id}
                  className={gateway.deleted ? "opacity-50" : ""}
                >
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <span className="font-semibold">{gateway.name}</span>
                      <span className="text-sm text-muted-foreground">
                        {gateway.description || "No description"}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {gateway.categoryType || "Unknown"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-sm">
                        {gateway.externalId || "N/A"}
                      </span>
                      {gateway.deleted && (
                        <Badge variant="secondary" className="text-xs">
                          Inactive
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {gateway.areaName ||
                        gateway.locationName ||
                        "Not assigned"}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={!gateway.deleted ? "default" : "secondary"}
                      className={
                        !gateway.deleted
                          ? "bg-green-100 text-green-800 hover:bg-green-100"
                          : "bg-gray-100 text-gray-800"
                      }
                    >
                      {!gateway.deleted ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-left">
                    <DropdownMenu
                      open={openDropdownId === gateway.id}
                      onOpenChange={(open) =>
                        setOpenDropdownId(open ? gateway.id : null)
                      }
                    >
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />

                        {/* For inactive gateways, show only Activate button */}
                        {gateway.deleted ? (
                          onToggleActive && (
                            <DropdownMenuItem
                              onClick={() => {
                                onToggleActive(gateway.id, true);
                                setOpenDropdownId(null);
                              }}
                            >
                              <Power className="mr-2 h-4 w-4" />
                              Activate
                            </DropdownMenuItem>
                          )
                        ) : (
                          /* For active gateways, show all actions */
                          <>
                            {onView && (
                              <DropdownMenuItem
                                onClick={() => {
                                  onView(gateway);
                                  setOpenDropdownId(null);
                                }}
                              >
                                View Details
                              </DropdownMenuItem>
                            )}

                            {onEdit && (
                              <DropdownMenuItem
                                onClick={() => {
                                  onEdit(gateway);
                                  setOpenDropdownId(null);
                                }}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Gateway
                              </DropdownMenuItem>
                            )}

                            {onDelete && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteClick(gateway)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Gateway
                                </DropdownMenuItem>
                              </>
                            )}
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination at bottom of table */}
      <GatewayTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Gateway</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the gateway "
              {gatewayToDelete?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
