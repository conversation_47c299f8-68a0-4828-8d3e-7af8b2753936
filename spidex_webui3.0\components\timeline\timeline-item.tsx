import React from "react";

interface TimelineItemProps {
  icon: React.ReactNode;
  title: React.ReactNode;
  date: React.ReactNode;
  description: React.ReactNode;
  highlighted?: boolean;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
  onMouseEnter?: React.MouseEventHandler<HTMLDivElement>;
  onMouseLeave?: React.MouseEventHandler<HTMLDivElement>;
  className?: string;
}

export function TimelineItem({
  icon,
  title,
  date,
  description,
  highlighted = false,
  onClick,
  onMouseEnter,
  onMouseLeave,
  className = "",
  ...props
}: TimelineItemProps) {
  return (
    <div className="relative flex flex-row items-start mb-4">
      {/* Timeline line */}
      <div className="flex flex-col items-center z-10 min-w-[20px]">
        <div className="flex-1 w-px bg-gray-200" />
      </div>
      {/* Time and card in a column, close to the line */}
      <div className="flex flex-col flex-1">
        {/* Time above card, close to the line */}
        <div className="text-xs text-muted-foreground mb-1 pl-1">{date}</div>
        {/* Card with icon and text, minimal left margin */}
        <div
          className={`flex flex-col transition rounded-lg p-3 shadow-sm border ${
            highlighted
              ? "bg-primary/10 border-primary"
              : "bg-white border-gray-200"
          } hover:bg-primary/5 ml-0 ${className}`}
          onClick={onClick}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          {...props}
        >
          <div className="flex flex-row items-center gap-2 mb-1">
            <span
              className="flex-shrink-0 flex items-center justify-center"
              style={{ width: 32, height: 32 }}
            >
              {icon}
            </span>
            <span className="font-semibold text-base">{title}</span>
          </div>
          <div className="flex items-center gap-2 ml-2 text-sm text-gray-700">
            {description}
          </div>
        </div>
      </div>
    </div>
  );
}

export default TimelineItem;
