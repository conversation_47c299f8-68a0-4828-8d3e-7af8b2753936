"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import {
  Asset,
  Gateway,
  Location,
  Area,
  TaggedAsset,
  SensorData,
  SensorType,
  WebSocketMessage,
  AssetSearchFilters,
  TableData,
} from "@/types/asset-tracking";
import { useAuthenticatedAssetAPI } from "@/hooks/use-authenticated-asset-api";
import { getAssetSocketManager } from "@/lib/websocket/asset-socket";
import {
  DEFAULT_SENSOR_DATA,
  SENSOR_GROUPS,
} from "@/lib/constants/asset-tracking";

interface UseAssetTrackingOptions {
  tenantId: string;
  socketUrl: string;
  autoConnect?: boolean;
}

export const useAssetTracking = ({
  tenantId,
  socketUrl,
  autoConnect = true,
}: UseAssetTrackingOptions) => {
  // Get authenticated API instance
  const assetTrackingAPI = useAuthenticatedAssetAPI();
  // Use real WebSocket manager
  const socketManager = useRef(getAssetSocketManager(socketUrl));

  // State
  const [selectedGateway, setSelectedGateway] = useState<Gateway | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null
  );
  const [currentTableData, setCurrentTableData] = useState<TableData>({});
  const [sensorData, setSensorData] = useState<
    Record<string, Record<SensorType, SensorData>>
  >({});
  const [isSocketConnected, setIsSocketConnected] = useState(false);
  const [socketError, setSocketError] = useState<string | null>(null);
  const [searchFilters, setSearchFilters] = useState<AssetSearchFilters>({
    searchTerm: "",
  });

  // Queries - use real API data only
  const { data: gateways, isLoading: gatewaysLoading } = useQuery({
    queryKey: ["gateways", tenantId],
    queryFn: () => assetTrackingAPI.getAllGateways(tenantId),
    select: (response) => response.data?.filter((g) => !g.deleted) || [],
    enabled: !!tenantId,
  });

  const { data: locations, isLoading: locationsLoading } = useQuery({
    queryKey: ["locations", tenantId],
    queryFn: () => assetTrackingAPI.getAllLocations(tenantId),
    select: (response) => response.data?.filter((l) => !l.deleted) || [],
    enabled: !!tenantId,
  });

  const { data: areas, isLoading: areasLoading } = useQuery({
    queryKey: ["areas", tenantId],
    queryFn: () => assetTrackingAPI.getAllAreas(tenantId),
    select: (response) => response.data?.filter((a) => !a.deleted) || [],
    enabled: !!tenantId,
  });

  const { data: taggedAssets, isLoading: taggedAssetsLoading } = useQuery({
    queryKey: ["taggedAssets", tenantId],
    queryFn: () => assetTrackingAPI.getAllTaggedAssets(tenantId),
    select: (response) => response.data || [],
    enabled: !!tenantId,
  });

  // WebSocket connection management
  useEffect(() => {
    if (!autoConnect) return;

    const manager = socketManager.current;

    manager.setConnectionChangeCallback(setIsSocketConnected);
    manager.setErrorCallback(setSocketError);

    manager.connect().catch((error) => {
      console.error("Failed to connect to WebSocket:", error);
      setSocketError("Failed to connect to real-time data stream");
    });

    return () => {
      manager.disconnect();
    };
  }, [autoConnect]);

  // Gateway selection and proximity data subscription
  const selectGateway = useCallback(
    (gateway: Gateway) => {
      setSelectedGateway(gateway);

      if (isSocketConnected && gateway?.id) {
        const manager = socketManager.current;

        // Subscribe to proximity data for this gateway
        try {
          manager.subscribeToProximityData(
            gateway.id,
            (message: WebSocketMessage) => {
              updateTableData(gateway.id, message);
            }
          );
        } catch (error) {
          console.error("Failed to subscribe to proximity data:", error);
        }
      }
    },
    [isSocketConnected]
  );

  // Asset sensor data subscription
  const subscribeToAssetSensors = useCallback(
    (deviceLogId: string, sensorTypes: SensorType[]) => {
      if (!isSocketConnected) return;

      const manager = socketManager.current;

      // Initialize sensor data for this device
      setSensorData((prev) => ({
        ...prev,
        [deviceLogId]: sensorTypes.reduce(
          (acc, sensorType) => ({
            ...acc,
            [sensorType]: { ...DEFAULT_SENSOR_DATA },
          }),
          {} as Record<SensorType, SensorData>
        ),
      }));

      // Subscribe to each sensor type
      sensorTypes.forEach((sensorType) => {
        try {
          manager.subscribeToSensorData(
            sensorType,
            deviceLogId,
            (message: WebSocketMessage) => {
              updateSensorData(deviceLogId, sensorType, message);
            }
          );
        } catch (error) {
          console.error(`Failed to subscribe to ${sensorType} data:`, error);
        }
      });
    },
    [isSocketConnected]
  );

  // Update table data with real-time proximity information
  const updateTableData = useCallback(
    (gatewayId: string, data: WebSocketMessage) => {
      if (!taggedAssets) return;

      setCurrentTableData((prev) => {
        const currentData = [...(prev[gatewayId] || [])];
        const deviceKey = "devicePhyId"; // This might need to be adjusted based on actual data structure

        // Remove existing entry for this device
        const filteredData = currentData.filter(
          (x) => x.devicePhyId !== data[deviceKey as keyof WebSocketMessage]
        );

        // Find the tagged asset link
        const tgAssetLink = taggedAssets.find((x) => x.id === data.deviceLogId);
        if (!tgAssetLink) return prev;

        // Create new asset entry
        const newAsset: Asset = {
          ...data,
          id: data.deviceLogId,
          devicePhyId: data[deviceKey as keyof WebSocketMessage] as string,
          key: `${data[deviceKey as keyof WebSocketMessage]}_${Math.floor(
            Date.now() / 1000
          )}`,
          macId: tgAssetLink.taggedAssetInfo.assetExternalId,
          deviceId: tgAssetLink.taggedAssetInfo.tagExternalId,
          tagName: tgAssetLink.taggedAssetInfo.tagName,
          name: tgAssetLink.taggedAssetInfo.assetName,
          tgAssetLink,
          areaName: "", // This would need to be calculated based on location data
          zone: "", // This would need to be calculated based on area data
          lastSeen: new Date().toISOString(),
        };

        filteredData.push(newAsset);

        return {
          ...prev,
          [gatewayId]: filteredData,
        };
      });
    },
    [taggedAssets]
  );

  // Update sensor data with real-time information
  const updateSensorData = useCallback(
    (deviceLogId: string, sensorType: SensorType, data: WebSocketMessage) => {
      setSensorData((prev) => {
        const deviceData = prev[deviceLogId] || {};
        const sensorInfo = deviceData[sensorType] || { ...DEFAULT_SENSOR_DATA };

        const value =
          data.attributeType === "float"
            ? parseFloat(data.attributeValue as string).toFixed(2)
            : data.attributeValue || "NA";

        const newDataPoint = {
          value: parseFloat(value as string) || 0,
          time: new Date(data.eventTime * 1000).toISOString(),
          timestamp: data.eventTime * 1000,
        };

        // Keep only the last 50 data points for performance
        const updatedLineChartData = [
          ...sensorInfo.lineChartData.slice(-49),
          newDataPoint,
        ];

        return {
          ...prev,
          [deviceLogId]: {
            ...deviceData,
            [sensorType]: {
              ...sensorInfo,
              value,
              loading: false,
              date: new Date(data.eventTime * 1000).toLocaleDateString(),
              lineChartData: updatedLineChartData,
            },
          },
        };
      });
    },
    []
  );

  // Asset selection for detailed view
  const selectAsset = useCallback(
    (asset: Asset) => {
      const assetSensors = [
        ...SENSOR_GROUPS.ENVIRONMENTAL,
        SensorType.PROXIMITY,
      ];
      subscribeToAssetSensors(asset.deviceLogId, assetSensors);
    },
    [subscribeToAssetSensors]
  );

  // Device selection for sensor monitoring
  const selectDevice = useCallback(
    (asset: Asset) => {
      const deviceSensors = [
        SensorType.BATTERY,
        ...SENSOR_GROUPS.ENVIRONMENTAL,
      ];
      subscribeToAssetSensors(asset.deviceLogId, deviceSensors);
    },
    [subscribeToAssetSensors]
  );

  // Search and filter functionality
  const filteredAssets = useCallback(
    (gatewayId: string) => {
      const assets = currentTableData[gatewayId] || [];

      if (!searchFilters.searchTerm) return assets;

      return assets.filter(
        (asset) =>
          asset.name
            ?.toLowerCase()
            .includes(searchFilters.searchTerm.toLowerCase()) ||
          asset.macId
            ?.toLowerCase()
            .includes(searchFilters.searchTerm.toLowerCase()) ||
          asset.deviceId
            ?.toLowerCase()
            .includes(searchFilters.searchTerm.toLowerCase())
      );
    },
    [currentTableData, searchFilters]
  );

  // Mutations for sensor data fetching
  const fetchSensorInitialData = useMutation({
    mutationFn: ({
      deviceLogId,
      sensorType,
    }: {
      deviceLogId: string;
      sensorType: SensorType;
    }) =>
      assetTrackingAPI.getSensorInitialData(tenantId, deviceLogId, sensorType),
    onSuccess: (data, variables) => {
      // Update sensor data with initial values
      setSensorData((prev) => ({
        ...prev,
        [variables.deviceLogId]: {
          ...prev[variables.deviceLogId],
          [variables.sensorType]: {
            ...prev[variables.deviceLogId]?.[variables.sensorType],
            value: data.data?.value || 0,
            loading: false,
          },
        },
      }));
    },
  });

  const isLoading =
    gatewaysLoading || locationsLoading || areasLoading || taggedAssetsLoading;

  return {
    // Data
    gateways: gateways || [],
    locations: locations || [],
    areas: areas || [],
    taggedAssets: taggedAssets || [],
    currentTableData,
    sensorData,

    // Selected items
    selectedGateway,
    selectedLocation,

    // Loading states
    isLoading,
    isSocketConnected,
    socketError,

    // Actions
    selectGateway,
    selectLocation: setSelectedLocation,
    selectAsset,
    selectDevice,
    subscribeToAssetSensors,
    setSearchFilters,
    filteredAssets,

    // Mutations
    fetchSensorInitialData,

    // Socket management
    socketManager: socketManager.current,
  };
};

export default useAssetTracking;
