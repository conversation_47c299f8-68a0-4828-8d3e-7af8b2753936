import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Switch,
  Popconfirm,
  CommonCompactView,
  CommonDrawer,
  message,
  Row,
  Col,
  Input,
  Checkbox,
  GoogleMapW3W,
} from '../../../../components';
import mapSvg from '../../../../assets/images/gmap.svg';
import { addTenant, updateTenant, deleteTenant, getAllTenants } from '../../../../services';
import Context from '../../../../context';
import { PlusOutlined } from '@ant-design/icons';
import { buildCommonApiValues } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import { get } from 'lodash';
import { CRUD, Pages, ModuleNames } from '../../../../constants';

const Tenant = () => {
  const [context, setContext] = useContext(Context);
  const [tenants, setTenants] = useState([]);
  const [tenant, setTenant] = useState({});
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [isMaps, setIsMaps] = useState(false);
  const [mapValues, setMapValues] = useState({});

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setTenants(tableData) : setTenants(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    form.setFieldsValue({ ...form.getFieldsValue(), ...mapValues });
    // eslint-disable-next-line
  }, [mapValues]);

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const [formInitValues, setFormInitValues] = useState(null);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    if (action === 'edit') form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveTenantAction({ ...commonValues, ...values, properties: {} });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateTenantAction(values);
      if (type === 'add') form.resetFields({});
      setFormInitValues(null);
      setVisible(false);
    }
  };

  const saveTenantAction = async (tenant) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addTenant(tenant)
      .then((res) => {
        setTenants((state) => [res.data, ...state]);
        message.success('Succesfully Added tenant');
        form.resetFields();
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Tenant, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editTenantAction = (tenant) => {
    form.setFieldsValue({ ...tenant });
    setAction('edit');
    setTenant(tenant);
    setVisible(true);
  };

  const updateTenantCall = (values) => {
    const data = { ...tenant, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateTenant(data)
      .then((res) => {
        setTenants((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, res.data);
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        message.success('Succesfully Updated tenant');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Tenant, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateTenantAction = async (values) => {
    updateTenantCall(values);
  };

  const makeActive = (data) => {
    updateTenantCall({ ...data, deleted: false });
  };

  const setDeleteTenantAction = (tenantId, visible) => {
    setTenants((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === tenantId).visible = visible;
      tempData
        .filter((x) => x.id !== tenantId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteTenantAction = (tenantId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteTenant(tenantId)
      .then(() => {
        setTenants((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === tenantId).visible = false;
          tempState.find((x) => x.id === tenantId).deleted = true;
          return [...state].filter((x) => x.id !== tenantId);
        });
        message.success('Succesfully Deleted tenant');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Tenant, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  useEffect(() => {
    if (formInitValues) {
      form.setFieldsValue({ ...formInitValues });
      setVisible(true);
    }
    // eslint-disable-next-line
  }, [formInitValues]);

  const clearForm = () => {
    form.resetFields();
  };

  useEffect(() => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      getAllTenants()
        .then((res) => {
          setTableData(res.data);
          setTenants(res.data.filter((x) => x.deleted === false));
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to get Tenant details, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
    }
    // eslint-disable-next-line
  }, []);

  const tableCols = [
    { title: <strong> Name </strong>, key: 'name', dataIndex: 'name' },
    { title: <strong> Type </strong>, key: 'type', dataIndex: 'type' },
    { title: <strong> Organization </strong>, key: 'orgName', dataIndex: 'orgName' },
    {
      title: <strong> Enabled </strong>,
      key: 'enable',
      render: (record) => <>{record.enable ? 'True' : 'False'}</>,
    },
    {
      title: <strong> Actions </strong>,
      width: 320,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <PermissionContainer page={Pages.BRANCH} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                  Activate
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.TENANT} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editTenantAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.TENANT} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete tenant ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteTenantAction(record.id)}
                onCancel={() => setDeleteTenantAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteTenantAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const tenantBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.TENANT}].displayName`,
    ModuleNames.TENANT
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.USER_MANAGEMENT, ModuleNames.USER, tenantBreadcrumbsName]} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.TENANT} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Tenant
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <Table
            size="small"
            scroll={{ x: true }}
            rowKey="id"
            loading={context.isLoading}
            columns={tableCols}
            dataSource={tenants}
            rowClassName={(record) => record.deleted && 'rowInactive'}
          />
        ) : (
          <CommonCompactView
            data={tenants}
            onEdit={editTenantAction}
            onDelete={deleteTenantAction}
            permissions={[
              { pageName: Pages.BRANCH, permission: CRUD.UPDATE, label: 'Edit' },
              { pageName: Pages.BRANCH, permission: CRUD.DELETE, label: 'Delete' },
            ]}
            title="name"
            dataList={[
              { label: 'Type', value: 'type' },
              { label: 'Enabled:', value: 'enable', type: 'boolean' },
            ]}
          />
        )}
      </>

      <CommonDrawer title="Area" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input tenant Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Tenant Name" />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Type"
            name="type"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input tenant TYpe!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Tenant Type" />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Organization"
            name="orgName"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Organization!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Organization" />
          </Form.Item>
          <Form.Item
            required={false}
            name="status"
            valuePropName="checked"
            label="Status"
            initialValue={false}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Switch />
          </Form.Item>
          <Form.Item label="GPS" required>
            <Row>
              <Col span={20}>
                <Input.Group compact>
                  <Form.Item
                    name={['latitude']}
                    noStyle
                    hasFeedback
                    rules={[{ required: true, message: 'Latitude is required' }]}
                  >
                    <Input style={{ width: '50%' }} placeholder="Latitude" />
                  </Form.Item>
                  <Form.Item
                    name={['longitude']}
                    noStyle
                    hasFeedback
                    rules={[{ required: true, message: 'Longitude is required' }]}
                  >
                    <Input style={{ width: '50%' }} placeholder="Longitude" />
                  </Form.Item>
                </Input.Group>
              </Col>
              <Col className="ml-2 locationSelector">
                <img src={mapSvg} alt="mapSvg" onClick={() => setIsMaps(true)} />
              </Col>
            </Row>
          </Form.Item>
          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
        {isMaps && <GoogleMapW3W setIsMaps={setIsMaps} onCloseUpdate={setMapValues}></GoogleMapW3W>}
      </CommonDrawer>
    </>
  );
};

export default Tenant;
