import { SpidexApiService } from "@/lib/api/spidex-api";
import {
  TaggedAsset,
  TaggedAssetApiResponse,
  CreateTaggedAssetFormData,
  TaggedAssetPaginationParams,
  AssetOption,
  TagOption,
  TaggedAssetStatsResponse,
} from "@/types/tagged-asset";

interface ApiResponse<T> {
  data: T;
  message?: string;
  status?: string;
}

class TaggedAssetAPI {
  private spidexApi: SpidexApiService;

  constructor() {
    this.spidexApi = SpidexApiService.getInstance();
  }

  /**
   * Get all tagged assets with pagination
   */
  async getAllTaggedAssetsByPagination(
    tenantId: string,
    params: TaggedAssetPaginationParams
  ): Promise<TaggedAssetApiResponse> {
    try {
      const response = await this.spidexApi.fetchTaggedAssets();
      return {
        data: response || [],
        total: response?.length || 0,
        page: params.page,
        pageSize: params.pageSize,
      };
    } catch (error) {
      console.error("Error fetching tagged assets:", error);
      throw error;
    }
  }

  /**
   * Get all tagged assets
   */
  async getAllTaggedAssets(
    tenantId: string
  ): Promise<ApiResponse<TaggedAsset[]>> {
    try {
      const response = await this.spidexApi.fetchTaggedAssets();
      return {
        data: response || [],
        message: "Tagged assets fetched successfully",
        status: "success",
      };
    } catch (error) {
      console.error("Error fetching all tagged assets:", error);
      throw error;
    }
  }

  /**
   * Create a new tagged asset
   */
  async createTaggedAsset(taggedAssetData: {
    id: null;
    createdBy: string;
    createdTime: string;
    modifiedBy: string;
    modifiedTime: string;
    deleted: boolean;
    tenantId: string;
    assetId: string;
    deviceId: string;
    status: boolean;
    modelId: number;
    taggedAssetInfo: {
      tagName: string;
      tagExternalId: string;
      assetName: string;
      assetExternalId: string;
    };
  }): Promise<ApiResponse<TaggedAsset>> {
    try {
      const response = await this.spidexApi.createTaggedAsset(taggedAssetData);
      return { data: response };
    } catch (error) {
      console.error("Error creating tagged asset:", error);
      throw error;
    }
  }

  /**
   * Update an existing tagged asset
   */
  async updateTaggedAsset(
    taggedAssetData: any
  ): Promise<ApiResponse<TaggedAsset>> {
    try {
      const response = await this.spidexApi.updateTaggedAsset(taggedAssetData);
      return { data: response };
    } catch (error) {
      console.error("Error updating tagged asset:", error);
      throw error;
    }
  }

  /**
   * Delete a tagged asset
   */
  async deleteTaggedAsset(taggedAssetId: string): Promise<ApiResponse<void>> {
    try {
      const response = await this.spidexApi.deleteTaggedAsset(taggedAssetId);
      return { data: response };
    } catch (error) {
      console.error("Error deleting tagged asset:", error);
      throw error;
    }
  }

  /**
   * Toggle tagged asset active/inactive status
   */
  async toggleTaggedAssetStatus(
    taggedAsset: TaggedAsset
  ): Promise<ApiResponse<TaggedAsset>> {
    try {
      const updatedTaggedAsset = {
        ...taggedAsset,
        deleted: !taggedAsset.deleted,
        modifiedBy: localStorage.getItem("username") || "system",
        modifiedTime: new Date(),
      };

      const response = await this.spidexApi.updateTaggedAsset(
        updatedTaggedAsset
      );
      return { data: response };
    } catch (error) {
      console.error("Error toggling tagged asset status:", error);
      throw error;
    }
  }

  /**
   * Get all assets for dropdown selection
   */
  async getAllAssetsForSelection(
    tenantId: string
  ): Promise<ApiResponse<AssetOption[]>> {
    try {
      const assets = await this.spidexApi.getAllAssets();

      // Transform assets to dropdown options
      const assetOptions: AssetOption[] = assets.map((asset) => ({
        id: asset.id,
        name: asset.name,
        externalId: asset.externalId,
        assetType: asset.assetType,
      }));

      return {
        data: assetOptions,
        message: "Assets fetched successfully",
        status: "success",
      };
    } catch (error) {
      console.error("Error fetching assets for selection:", error);
      throw error;
    }
  }

  /**
   * Get all tags for dropdown selection
   */
  async getAllTagsForSelection(
    tenantId: string
  ): Promise<ApiResponse<TagOption[]>> {
    try {
      const tags = await this.spidexApi.getAllTags();

      // Transform tags to dropdown options
      const tagOptions: TagOption[] = tags.map((tag) => ({
        id: tag.id,
        name: tag.name,
        externalId: tag.externalId || "",
        modelId: tag.modelId,
      }));

      return {
        data: tagOptions,
        message: "Tags fetched successfully",
        status: "success",
      };
    } catch (error) {
      console.error("Error fetching tags for selection:", error);
      throw error;
    }
  }

  /**
   * Get tagged asset statistics
   */
  async getTaggedAssetStats(
    tenantId: string
  ): Promise<TaggedAssetStatsResponse> {
    try {
      const response = await this.getAllTaggedAssets(tenantId);
      const taggedAssets = response.data || [];

      const totalRecords = taggedAssets.length;
      const activeRecords = taggedAssets.filter((ta) => !ta.deleted).length;
      const inactiveRecords = taggedAssets.filter((ta) => ta.deleted).length;
      const provisionedRecords = taggedAssets.filter(
        (ta) => ta.provisioned
      ).length;
      const unprovisionedRecords = taggedAssets.filter(
        (ta) => !ta.provisioned
      ).length;

      return {
        totalRecords,
        activeRecords,
        inactiveRecords,
        provisionedRecords,
        unprovisionedRecords,
      };
    } catch (error) {
      console.error("Error fetching tagged asset stats:", error);
      throw error;
    }
  }
}

// Create and export a singleton instance
export const taggedAssetAPI = new TaggedAssetAPI();
export default taggedAssetAPI;
