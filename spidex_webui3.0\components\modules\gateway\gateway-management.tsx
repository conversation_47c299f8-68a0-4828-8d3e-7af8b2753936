"use client";

import { useState } from "react";
import { Plus, Router, Wifi, RefreshCw } from "lucide-react";
import { useSession } from "next-auth/react";
import { useGatewayManagement } from "@/hooks/use-gateway-management";
import { getTableConfig } from "@/config/table-config";
import {
  Gateway,
  CreateGatewayFormData,
  UpdateGatewayFormData,
  GatewayPageSize,
} from "@/types/gateway";
import { GatewayDataTable } from "./gateway-data-table";
import { GatewaySearchPagination } from "./gateway-search-pagination";
import { GatewayForm } from "./gateway-form";
import { Button } from "@/components/ui/button";

import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

interface GatewayManagementProps {
  areaId?: string;
}

export default function GatewayManagement({ areaId }: GatewayManagementProps) {
  const { data: session, status } = useSession();

  // Get table configuration for this page
  const tableConfig = getTableConfig("gateway-management");

  const {
    gateways: paginatedGateways,
    allGateways,
    filteredGateways,
    areas,
    locations,
    models,
    isLoading,
    isCrudLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    totalAllRecords,
    activeRecordsCount,
    inactiveRecordsCount,
    loadData,
    createGateway,
    updateGateway,
    deleteGateway,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useGatewayManagement({
    initialPageSize: tableConfig.defaultPageSize,
    availablePageSizes: tableConfig.availablePageSizes as GatewayPageSize[],
    areaId,
  });

  // Form state
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingGateway, setEditingGateway] = useState<Gateway | null>(null);
  const [tableKey, setTableKey] = useState(0);

  // Form handlers
  const openCreateForm = () => {
    console.log("➕ Opening create form for new gateway");
    console.log("🔧 Gateway areas available:", areas.length);
    console.log("🔧 Gateway locations available:", locations.length);
    console.log("🔧 Gateway models available:", models.length);
    setEditingGateway(null);
    setIsFormOpen(true);
  };

  const openEditForm = (gateway: Gateway) => {
    console.log("✏️ Opening edit form for gateway:", gateway);
    console.log("🔧 Gateway areas available:", areas.length);
    console.log("🔧 Gateway locations available:", locations.length);
    console.log("🔧 Gateway models available:", models.length);
    setEditingGateway(gateway);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    // Small delay to ensure proper state cleanup
    setTimeout(() => {
      setEditingGateway(null);
      setTableKey((prev) => prev + 1); // Force table re-render
    }, 100);
  };

  // Handle form submission
  const handleFormSubmit = async (
    data: CreateGatewayFormData | UpdateGatewayFormData
  ) => {
    try {
      if (editingGateway) {
        // Update existing gateway
        await updateGateway(data as UpdateGatewayFormData);
        toast.success("Gateway updated successfully", {
          description: `${data.name} has been updated.`,
        });
      } else {
        // Create new gateway
        await createGateway(data as CreateGatewayFormData);
        toast.success("Gateway created successfully", {
          description: `${data.name} has been added to the system.`,
        });
      }

      closeForm();
    } catch (error) {
      console.error("Gateway form submission error:", error);

      // Extract debugMessage from error response
      let errorMessage = "Please check your input and try again.";

      if (error && typeof error === "object") {
        // Check if error has debugMessage property
        if ("debugMessage" in error && typeof error.debugMessage === "string") {
          errorMessage = error.debugMessage;
        }
        // Check if error has message property with JSON response
        else if ("message" in error && typeof error.message === "string") {
          try {
            const parsedError = JSON.parse(error.message);
            if (
              parsedError.debugMessage &&
              typeof parsedError.debugMessage === "string"
            ) {
              errorMessage = parsedError.debugMessage;
            }
          } catch (parseError) {
            // If parsing fails, use the original message
            errorMessage = error.message;
          }
        }
      }

      toast.error(
        editingGateway
          ? "Failed to update gateway"
          : "Failed to create gateway",
        {
          description: errorMessage,
        }
      );
    }
  };

  // Handle gateway deletion
  const handleDeleteGateway = async (gatewayId: string) => {
    try {
      await deleteGateway(gatewayId);
      toast.success("Gateway deleted successfully", {
        description: "The gateway has been removed from the system.",
      });
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Failed to delete gateway", {
        description: "Please try again later.",
      });
    }
  };

  // Handle toggle active status
  const handleToggleActive = async (gatewayId: string, active: boolean) => {
    try {
      const gateway = filteredGateways.find((g) => g.id === gatewayId);
      if (!gateway) return;

      await updateGateway({
        ...gateway,
        deleted: !active, // Toggle deleted status
        modifiedBy: session?.user?.userId || "system",
        modifiedTime: Date.now(),
      } as UpdateGatewayFormData);

      toast.success(
        active
          ? "Gateway activated successfully"
          : "Gateway deactivated successfully",
        {
          description: `${gateway.name} has been ${
            active ? "activated" : "deactivated"
          }.`,
        }
      );
    } catch (error) {
      console.error("Toggle active error:", error);
      toast.error("Failed to update gateway status", {
        description: "Please try again later.",
      });
    }
  };

  // Loading state
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Not authenticated
  if (status === "unauthenticated") {
    return (
      <div className="text-center">
        <p className="text-muted-foreground">
          Please log in to access gateway management.
        </p>
      </div>
    );
  }

  // Show loading state when initially loading data
  if (isLoading && paginatedGateways.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading gateway data...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/digital-carpet">
                Digital Carpet
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Gateway Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={openCreateForm} className="gap-2 ml-auto">
          <Plus className="h-4 w-4" />
          Add Gateway
        </Button>
      </header>
      <main>
        {/* Search and Filters */}
        <div className="p-4">
          <GatewaySearchPagination
            searchFilters={searchFilters}
            showDeleted={showDeleted}
            totalRecords={totalRecords}
            totalAllRecords={totalAllRecords}
            activeRecordsCount={activeRecordsCount}
            inactiveRecordsCount={inactiveRecordsCount}
            areas={areas}
            locations={locations}
            models={models}
            onSearchChange={updateSearchFilters}
            onClearSearch={clearSearch}
            onToggleShowDeleted={toggleShowDeleted}
          />
        </div>

        {/* Data Table */}
        <div className="p-4 pt-0">
          <GatewayDataTable
            key={tableKey}
            data={paginatedGateways}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onEdit={openEditForm}
            onDelete={handleDeleteGateway}
            onToggleActive={handleToggleActive}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
            isLoading={false}
          />
        </div>

        {/* Form Sheet */}
        <Sheet
          open={isFormOpen}
          onOpenChange={(open) => {
            if (!open) {
              closeForm();
            } else {
              setIsFormOpen(true);
            }
          }}
        >
          <SheetContent className="w-1/4 sm:max-w-4xl overflow-y-auto">
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5" />
                {editingGateway ? "Edit Gateway" : "Create New Gateway"}
              </SheetTitle>
              <SheetDescription>
                {editingGateway
                  ? "Update the gateway information below."
                  : "Fill in the details to create a new gateway."}
              </SheetDescription>
            </SheetHeader>
            <div className="mt-6">
              <GatewayForm
                key={editingGateway?.id || "new"}
                gateway={editingGateway || undefined}
                areas={areas}
                locations={locations}
                models={models}
                allGateways={allGateways}
                isLoading={isCrudLoading}
                onSubmit={handleFormSubmit}
                onCancel={closeForm}
              />
            </div>
          </SheetContent>
        </Sheet>
      </main>
    </>
  );
}
