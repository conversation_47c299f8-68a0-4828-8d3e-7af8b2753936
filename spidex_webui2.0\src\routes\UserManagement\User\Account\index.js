import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Select,
  Popconfirm,
  CommonCompactView,
  CommonDrawer,
  message,
  Row,
  Col,
  Input,
  Checkbox,
} from '../../../../components';
import {
  getAllRoles,
  getAllAccounts,
  addAccount,
  updateAccount,
  deleteAccount,
  getAllTenants,
} from '../../../../services';
import Context from '../../../../context';
import { PlusOutlined } from '@ant-design/icons';
import { buildCommonApiValues } from '../../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import * as config from '../../../../config';
import { get } from 'lodash';
import { CRUD, Pages, InputRegex, ModuleNames } from '../../../../constants';

const { Option } = Select;

const Account = () => {
  const [context, setContext] = useContext(Context);
  const [accounts, setAccounts] = useState([]);
  const [account, setAccount] = useState({});
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');
  const [tenants, setTenants] = useState([]);
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [roles, setRoles] = useState([]);

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setAccounts(tableData) : setAccounts(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const [formInitValues, setFormInitValues] = useState(null);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    if (action === 'edit') form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      saveAccountAction({
        ...values,
        ...commonValues,
        tenantId: values.tenantId,
        roles: values.roles,
        password: config.REACT_APP_DEFAULT_USER_PASSWORD,
        userId: values.userId,
        name: values.name,
      });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      updateAccountAction(values);
      if (type === 'add') form.resetFields({});
      setFormInitValues(null);
      setVisible(false);
    }
  };

  const saveAccountAction = async (account) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addAccount({ ...account, password: config.REACT_APP_DEFAULT_USER_PASSWORD })
      .then((res) => {
        setAccounts((state) => [res.data, ...state]);
        message.success('Succesfully Added account');
        form.resetFields();
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Account, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editAccountAction = (account) => {
    form.setFieldsValue({ ...account });
    setAction('edit');
    setAccount(account);
    setVisible(true);
  };

  const updateAccountCall = (values) => {
    const data = { ...account, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateAccount(data)
      .then((res) => {
        setAccounts((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, res.data);
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        message.success('Succesfully Updated account');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Account, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateAccountAction = (values) => {
    updateAccountCall(values);
  };

  const makeActive = (data) => {
    updateAccountCall({ ...data, deleted: false });
  };

  const setDeleteAccountAction = (userId, visible) => {
    setAccounts((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === userId).visible = visible;
      tempData
        .filter((x) => x.id !== userId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteAccountAction = (userId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteAccount(userId)
      .then(() => {
        setAccounts((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === userId).visible = false;
          tempState.find((x) => x.id === userId).deleted = true;
          return [...state].filter((x) => x.id !== userId);
        });
        message.success('Succesfully Deleted account');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Account, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  useEffect(() => {
    if (formInitValues) {
      form.setFieldsValue({ ...formInitValues });
      setVisible(true);
    }
    // eslint-disable-next-line
  }, [formInitValues]);

  const clearForm = () => {
    form.resetFields();
  };

  useEffect(() => {
    const init = async () => {
      if (context.profile.tenantId) {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
        const roleRes = await getAllRoles();
        setRoles(roleRes.data);
        const tenantRes = await getAllTenants();
        setTenants(tenantRes.data.filter((x) => x.deleted === false));
        getAllAccounts()
          .then((res) => {
            setTableData(res.data);
            setAccounts(res.data.filter((x) => x.deleted === false));
          })
          .catch((e) => {
            console.log(e);
            message.error('Unable to get Account details, try again later');
          })
          .finally(() => {
            setContext((state) => {
              return {
                ...state,
                isLoading: false,
              };
            });
          });
      }
    };
    init();
    // eslint-disable-next-line
  }, []);

  const tableCols = [
    { title: <strong> Name </strong>, key: 'name', dataIndex: 'name' },
    { title: <strong> Username </strong>, key: 'userId', dataIndex: 'userId' },
    {
      title: <strong> Actions </strong>,
      width: 350,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <PermissionContainer page={Pages.ACCOUNT} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                  Activate
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.ACCOUNT} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editAccountAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.ACCOUNT} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete user ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteAccountAction(record.id)}
                onCancel={() => setDeleteAccountAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteAccountAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const accountBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.ACCOUNT}].displayName`,
    ModuleNames.ACCOUNT
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.USER_MANAGEMENT, ModuleNames.USER, accountBreadcrumbsName]} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.ACCOUNT} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Account
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <Table
            size="small"
            scroll={{ x: true }}
            rowKey="id"
            loading={context.isLoading}
            columns={tableCols}
            dataSource={accounts}
            rowClassName={(record) => record.deleted && 'rowInactive'}
          />
        ) : (
          <CommonCompactView
            data={accounts}
            onEdit={editAccountAction}
            onDelete={deleteAccountAction}
            permissions={[
              { pageName: Pages.ACCOUNT, permission: CRUD.UPDATE, label: 'Edit' },
              { pageName: Pages.ACCOUNT, permission: CRUD.DELETE, label: 'Delete' },
            ]}
            title="name"
            dataList={[{ label: 'Username', value: 'userId' }]}
          />
        )}
      </>
      <CommonDrawer title="Account" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          autoComplete={'new-password'}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          {/* To stop browser from auto filling */}
          <input autoComplete={'new-password'} style={{ display: 'none' }} />
          <input autoComplete={'new-password'} type="password" style={{ display: 'none' }} />
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Full Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Full Name" />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Username"
            name="userId"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Username!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input autoComplete={'new-password'} placeholder="Username" />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Mobile"
            name="mobileNumber"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Mobile Number!',
              },
              { pattern: InputRegex.Mobile, message: 'Enter a valid Mobile Number' },
            ]}
          >
            <Input placeholder="Mobile" />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Email"
            name="emailId"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Email!',
                min: 1,
                max: 200,
              },
              { pattern: InputRegex.Email, message: 'Enter a valid Email address' },
            ]}
          >
            <Input placeholder="Email" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Roles"
            name="roles"
            rules={[
              {
                required: true,
                message: 'Please select Role(s)!',
              },
            ]}
          >
            <Select
              mode="multiple"
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              showSearch
              placeholder="Roles"
            >
              {roles
                .filter((x) => x.deleted === false)
                .map((b) => (
                  <Option title={b.name} key={b.id} value={b.name}>
                    {b.name}
                  </Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Tenant"
            name="tenantId"
            rules={[
              {
                required: true,

                message: 'Please select Tenant!',
              },
            ]}
          >
            <Select
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              showSearch
              placeholder="Tenant"
            >
              {tenants.map((b) => (
                <Option title={b.name} key={b.id} value={b.id}>
                  {b.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </>
  );
};

export default Account;
