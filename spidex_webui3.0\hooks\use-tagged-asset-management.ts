import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import { taggedAssetAPI } from "@/lib/api/tagged-asset-api";
import {
  TaggedAsset,
  TaggedAssetSearchFilters,
  CreateTaggedAssetFormData,
  UpdateTaggedAssetFormData,
  AssetOption,
  TagOption,
  TaggedAssetStatsResponse,
} from "@/types/tagged-asset";

interface PaginationState {
  page: number;
  pageSize: number;
}

interface UseTaggedAssetManagementReturn {
  // Data
  taggedAssets: TaggedAsset[];
  filteredTaggedAssets: TaggedAsset[];
  assetOptions: AssetOption[];
  tagOptions: TagOption[];
  stats: TaggedAssetStatsResponse | null;

  // State
  isLoading: boolean;
  error: string | null;
  showDeleted: boolean;
  searchFilters: TaggedAssetSearchFilters;
  pagination: PaginationState;
  totalPages: number;
  totalRecords: number;
  totalAllRecords: number;
  activeRecordsCount: number;
  inactiveRecordsCount: number;
  provisionedRecordsCount: number;
  unprovisionedRecordsCount: number;

  // Actions
  loadData: () => Promise<void>;
  loadAssetOptions: () => Promise<void>;
  loadTagOptions: () => Promise<void>;
  createTaggedAsset: (data: CreateTaggedAssetFormData) => Promise<void>;
  updateTaggedAsset: (data: UpdateTaggedAssetFormData) => Promise<void>;
  deleteTaggedAsset: (id: string) => Promise<void>;
  toggleTaggedAssetStatus: (taggedAsset: TaggedAsset) => Promise<void>;

  // Pagination & Search
  goToPage: (page: number) => void;
  changePageSize: (pageSize: number) => void;
  updateSearchFilters: (filters: Partial<TaggedAssetSearchFilters>) => void;
  clearSearch: () => void;
  toggleShowDeleted: () => void;

  // Constants
  availablePageSizes: number[];
}

const DEFAULT_PAGE_SIZE = 10;
const AVAILABLE_PAGE_SIZES = [5, 10, 20, 50];

interface UseTaggedAssetManagementOptions {
  initialPageSize?: number;
  availablePageSizes?: number[];
}

export function useTaggedAssetManagement(
  options?: UseTaggedAssetManagementOptions
): UseTaggedAssetManagementReturn {
  const { data: session } = useSession();
  const spidexApi = useSpidexApi();

  // State
  const [taggedAssets, setTaggedAssets] = useState<TaggedAsset[]>([]);
  const [assetOptions, setAssetOptions] = useState<AssetOption[]>([]);
  const [tagOptions, setTagOptions] = useState<TagOption[]>([]);
  const [stats, setStats] = useState<TaggedAssetStatsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);
  const [searchFilters, setSearchFilters] = useState<TaggedAssetSearchFilters>(
    {}
  );
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    pageSize: options?.initialPageSize || DEFAULT_PAGE_SIZE,
  });

  // Get tenant ID from session
  const tenantId =
    session?.user?.tenantId || "27b240fb-628e-466f-8225-b8c882c1670f";

  // Filter tagged assets based on search and show inactive
  const filteredTaggedAssets = taggedAssets.filter((taggedAsset) => {
    // Filter by deleted status (for "Show inactive tagged assets" checkbox)
    // When showDeleted is true, show only deleted items (deleted: true)
    // When showDeleted is false, show only active items (deleted: false)
    if (showDeleted && !taggedAsset.deleted) return false;
    if (!showDeleted && taggedAsset.deleted) return false;

    // Filter by search term
    if (searchFilters.searchTerm) {
      const searchTerm = searchFilters.searchTerm.toLowerCase();
      console.log("🔍 Searching for:", searchTerm);
      console.log("🔍 Checking tagged asset:", {
        assetName: taggedAsset.taggedAssetInfo?.assetName,
        tagName: taggedAsset.taggedAssetInfo?.tagName,
        assetExternalId: taggedAsset.taggedAssetInfo?.assetExternalId,
        tagExternalId: taggedAsset.taggedAssetInfo?.tagExternalId,
      });

      const matchesSearch =
        (taggedAsset.taggedAssetInfo?.assetName || "")
          .toLowerCase()
          .includes(searchTerm) ||
        (taggedAsset.taggedAssetInfo?.tagName || "")
          .toLowerCase()
          .includes(searchTerm) ||
        (taggedAsset.taggedAssetInfo?.assetExternalId || "")
          .toLowerCase()
          .includes(searchTerm) ||
        (taggedAsset.taggedAssetInfo?.tagExternalId || "")
          .toLowerCase()
          .includes(searchTerm);

      if (!matchesSearch) return false;
    }

    // Filter by status
    if (searchFilters.status !== undefined) {
      if (taggedAsset.status !== searchFilters.status) return false;
    }

    // Filter by provisioned status
    if (searchFilters.provisioned !== undefined) {
      if (taggedAsset.provisioned !== searchFilters.provisioned) return false;
    }

    return true;
  });

  // Paginated tagged assets
  const startIndex = (pagination.page - 1) * pagination.pageSize;
  const endIndex = startIndex + pagination.pageSize;
  const paginatedTaggedAssets = filteredTaggedAssets.slice(
    startIndex,
    endIndex
  );

  // Calculate totals
  const totalRecords = filteredTaggedAssets.length;
  const totalPages = Math.ceil(totalRecords / pagination.pageSize);
  const totalAllRecords = taggedAssets.length;
  const activeRecordsCount = taggedAssets.filter((ta) => !ta.deleted).length;
  const inactiveRecordsCount = taggedAssets.filter((ta) => ta.deleted).length;
  const provisionedRecordsCount = taggedAssets.filter(
    (ta) => ta.provisioned
  ).length;
  const unprovisionedRecordsCount = taggedAssets.filter(
    (ta) => !ta.provisioned
  ).length;

  // Load tagged assets data
  const loadData = useCallback(async () => {
    if (!session?.user?.token) {
      console.log("No session or token available");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("Loading tagged assets...");
      const response = await spidexApi.fetchTaggedAssets();
      console.log("Tagged assets response:", response);
      setTaggedAssets(response || []);

      // Calculate stats from the loaded data
      const totalRecords = response?.length || 0;
      const activeRecords =
        response?.filter((ta: any) => !ta.deleted).length || 0;
      const inactiveRecords =
        response?.filter((ta: any) => ta.deleted).length || 0;
      const provisionedRecords =
        response?.filter((ta: any) => ta.provisioned).length || 0;
      const unprovisionedRecords =
        response?.filter((ta: any) => !ta.provisioned).length || 0;

      setStats({
        totalRecords,
        activeRecords,
        inactiveRecords,
        provisionedRecords,
        unprovisionedRecords,
      });
    } catch (err) {
      console.error("Error loading tagged assets:", err);
      setError("Failed to load tagged assets. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [spidexApi, session?.user?.token]);

  // Load asset options for dropdown
  const loadAssetOptions = useCallback(async () => {
    if (!session?.user?.token) return;

    try {
      console.log("Loading asset options...");
      const response = await spidexApi.getAllAssets();
      console.log("Assets response:", response);

      // Transform assets to dropdown options
      const assetOptions: AssetOption[] = (response || []).map(
        (asset: any) => ({
          id: asset.id,
          name: asset.name,
          externalId: asset.externalId,
          assetType: asset.assetType,
        })
      );

      setAssetOptions(assetOptions);
    } catch (err) {
      console.error("Error loading asset options:", err);
    }
  }, [spidexApi, session?.user?.token]);

  // Load tag options for dropdown
  const loadTagOptions = useCallback(async () => {
    if (!session?.user?.token) return;

    try {
      console.log("Loading tag options...");
      const response = await spidexApi.getAllTags();
      console.log("Tags response:", response);

      // Transform tags to dropdown options
      const tagOptions: TagOption[] = (response || []).map((tag: any) => ({
        id: tag.id,
        name: tag.name,
        externalId: tag.externalId || "",
        modelId: tag.modelId,
      }));

      setTagOptions(tagOptions);
    } catch (err) {
      console.error("Error loading tag options:", err);
    }
  }, [spidexApi, session?.user?.token]);

  // Create tagged asset
  const createTaggedAsset = useCallback(
    async (data: CreateTaggedAssetFormData) => {
      if (!tenantId || !session?.user?.userId) {
        return;
      }

      try {
        // Find selected asset and tag info
        const selectedAsset = assetOptions.find((a) => a.id === data.assetId);
        const selectedTag = tagOptions.find((t) => t.id === data.deviceId);

        if (!selectedAsset || !selectedTag) {
          throw new Error("Selected asset or tag not found");
        }

        const currentTime = new Date().toISOString();

        const taggedAssetData = {
          id: null,
          createdBy: session.user.userId || "system",
          createdTime: currentTime,
          modifiedBy: session.user.userId || "system",
          modifiedTime: currentTime,
          deleted: false,
          tenantId,
          assetId: data.assetId,
          deviceId: data.deviceId,
          status: data.status === "ACTIVE" || data.status === "TAGGED", // Convert string to boolean for API
          modelId: Number(selectedTag.modelId), // Ensure it's a number
          taggedAssetInfo: {
            tagName: selectedTag.name,
            tagExternalId: selectedTag.externalId || "",
            assetName: selectedAsset.name,
            assetExternalId: selectedAsset.externalId || "",
          },
        };

        const response = await taggedAssetAPI.createTaggedAsset(
          taggedAssetData
        );

        setTaggedAssets((prev) => [response.data, ...prev]);
      } catch (err) {
        console.error("Error creating tagged asset:", err);
        throw err;
      }
    },
    [tenantId, session?.user?.userId, assetOptions, tagOptions, taggedAssetAPI]
  );

  // Update tagged asset
  const updateTaggedAsset = useCallback(
    async (data: UpdateTaggedAssetFormData | any) => {
      if (!tenantId || !session?.user?.userId) return;

      try {
        // Check if this is a complete tagged asset object (for provision operations)
        if (data.tenantId && data.taggedAssetInfo) {
          // This is a complete tagged asset object, pass it directly to the API
          const response = await taggedAssetAPI.updateTaggedAsset(data);
          setTaggedAssets((prev) =>
            prev.map((ta) => (ta.id === data.id ? response.data : ta))
          );
          return;
        }

        // This is a form data update, use the existing logic
        const existingTaggedAsset = taggedAssets.find(
          (ta) => ta.id === data.id
        );
        if (!existingTaggedAsset) {
          throw new Error("Tagged asset not found");
        }

        // Find selected asset and tag info
        const selectedAsset = assetOptions.find((a) => a.id === data.assetId);
        const selectedTag = tagOptions.find((t) => t.id === data.deviceId);

        if (!selectedAsset || !selectedTag) {
          throw new Error("Selected asset or tag not found");
        }

        const taggedAssetData = {
          ...existingTaggedAsset,
          ...data,
          tenantId,
          modifiedBy: session.user.userId || "system",
          modifiedTime: new Date(),
          modelId: selectedTag.modelId,
          taggedAssetInfo: {
            assetName: selectedAsset.name,
            assetExternalId: selectedAsset.externalId,
            tagName: selectedTag.name,
            tagExternalId: selectedTag.externalId,
            assetType: selectedAsset.assetType,
          },
        };

        const response = await taggedAssetAPI.updateTaggedAsset(
          taggedAssetData
        );
        setTaggedAssets((prev) =>
          prev.map((ta) => (ta.id === data.id ? response.data : ta))
        );
      } catch (err) {
        console.error("Error updating tagged asset:", err);
        throw err;
      }
    },
    [tenantId, session?.user?.userId, taggedAssets, assetOptions, tagOptions]
  );

  // Delete tagged asset
  const deleteTaggedAsset = useCallback(async (id: string) => {
    try {
      await taggedAssetAPI.deleteTaggedAsset(id);
      setTaggedAssets((prev) => prev.filter((ta) => ta.id !== id));
    } catch (err) {
      console.error("Error deleting tagged asset:", err);
      throw err;
    }
  }, []);

  // Toggle tagged asset status
  const toggleTaggedAssetStatus = useCallback(
    async (taggedAsset: TaggedAsset) => {
      try {
        const response = await taggedAssetAPI.toggleTaggedAssetStatus(
          taggedAsset
        );
        setTaggedAssets((prev) =>
          prev.map((ta) => (ta.id === taggedAsset.id ? response.data : ta))
        );
      } catch (err) {
        console.error("Error toggling tagged asset status:", err);
        throw err;
      }
    },
    []
  );

  // Pagination functions
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  }, []);

  const changePageSize = useCallback((pageSize: number) => {
    setPagination({ page: 1, pageSize });
  }, []);

  // Search functions
  const updateSearchFilters = useCallback(
    (filters: Partial<TaggedAssetSearchFilters>) => {
      setSearchFilters((prev) => {
        const newFilters = { ...prev, ...filters };
        return newFilters;
      });
      setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
    },
    [searchFilters]
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({});
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  // Load data on mount (only on initial mount)
  useEffect(() => {
    if (session?.user?.token) {
      console.log("Session available, loading data...");
      loadData();
      loadAssetOptions();
      loadTagOptions();
    } else {
      console.log("No session or token, skipping data load");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.user?.token]); // Removed function dependencies to prevent reloading on session changes

  return {
    // Data
    taggedAssets: paginatedTaggedAssets,
    filteredTaggedAssets,
    assetOptions,
    tagOptions,
    stats,

    // State
    isLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    totalAllRecords,
    activeRecordsCount,
    inactiveRecordsCount,
    provisionedRecordsCount,
    unprovisionedRecordsCount,

    // Actions
    loadData,
    loadAssetOptions,
    loadTagOptions,
    createTaggedAsset,
    updateTaggedAsset,
    deleteTaggedAsset,
    toggleTaggedAssetStatus,

    // Pagination & Search
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,

    // Constants
    availablePageSizes: options?.availablePageSizes || AVAILABLE_PAGE_SIZES,
  };
}
