@import '../../Colors.less';

.drawer {
  .content {
    padding: 24px !important;
  }
  .title {
    font-size: 1.5rem;
    font-weight: bold;
    background-color: @white-hex !important;
    color: @primary-color-hex;
    padding: 24px 70px 24px 24px !important;
    border-bottom: 5px solid @white3-hex;
  }
  .footer {
    button {
      margin-top: 10px;
    }
  }
  .googleRow {
    height: 75%;
    width: 100%;
    .googleCol {
      width: 100%;
    }
  }
  .mapHeader {
    padding: 0 10px 10px 10px;
    border-bottom: 5px solid @white3-hex;
    input {
      border: 1px solid @ant-border-hex;
      padding: 10px;
    }
    input:hover,
    input:focus,
    input:active,
    input:focus-visible {
      border: 1px solid @primary-color-hex !important;
      outline: 0;
    }
  }
  .saveButton {
    margin-right: 50px !important;
    position: absolute;
    right: 0;
    top: 20px;
  }

  :global {
    .ant-drawer-close {
      margin-right: 10px !important;
      position: absolute;
      z-index: 1000 !important;
      right: 0;
      top: 30px;
      .anticon-close {
        color: @primary-color-hex !important;
        padding: 10px !important;
      }
    }
    .ant-drawer-content {
      background-color: transparent !important;
    }
    .ant-drawer-body {
      padding: 20px;
      background-color: @white-hex !important;
    }
    #google-map {
      height: 100%;
      width: 100%;
    }
    .gm-style-mtc {
      button {
        color: @black-hex !important;
      }
    }
    #google-map-input {
      width: 100% !important;
    }
    input[name='what3words_3wa'] {
      padding: 10px 24px 10px 24px !important;
    }
    .ant-spin-nested-loading {
      height: 100vh;
      width: 100%;
      .ant-spin-container {
        height: 100%;
        width: 100%;
      }
    }
  }
}
