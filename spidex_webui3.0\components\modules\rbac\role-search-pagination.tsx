"use client";

import { useState } from "react";
import { Search, X, Filter, RotateCcw } from "lucide-react";
import { RoleSearchFilters } from "@/types/rbac";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";

interface RoleSearchPaginationProps {
  searchFilters: RoleSearchFilters;
  showDeleted: boolean;
  totalRecords: number;
  totalAllRecords: number;
  activeRecordsCount: number;
  inactiveRecordsCount: number;
  onUpdateSearchFilters: (filters: Partial<RoleSearchFilters>) => void;
  onClearSearch: () => void;
  onToggleShowDeleted: () => void;
}

export function RoleSearchPagination({
  searchFilters,
  showDeleted,
  totalRecords,
  totalAllRecords,
  activeRecordsCount,
  inactiveRecordsCount,
  onUpdateSearchFilters,
  onClearSearch,
  onToggleShowDeleted,
}: RoleSearchPaginationProps) {
  const [localFilters, setLocalFilters] = useState<RoleSearchFilters>(searchFilters);

  const handleFilterChange = (key: keyof RoleSearchFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onUpdateSearchFilters({ [key]: value });
  };

  const handleClearFilters = () => {
    setLocalFilters({});
    onClearSearch();
  };

  const hasActiveFilters = Object.values(searchFilters).some(value => 
    value !== undefined && value !== "" && value !== null
  );

  return (
    <div className="space-y-4">
      {/* Search Controls */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Role Name Search */}
        <div className="space-y-2">
          <Label htmlFor="role-name-search">Role Name</Label>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              id="role-name-search"
              placeholder="Search by role name..."
              value={localFilters.name || ""}
              onChange={(e) => handleFilterChange("name", e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <Label htmlFor="status-filter">Status</Label>
          <Select
            value={localFilters.enable?.toString() || "all"}
            onValueChange={(value) => 
              handleFilterChange("enable", value === "all" ? undefined : value === "true")
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="true">Active</SelectItem>
              <SelectItem value="false">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <Label>&nbsp;</Label>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleClearFilters}
              disabled={!hasActiveFilters}
              className="flex-1"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear
            </Button>
          </div>
        </div>
      </div>

      {/* Show Deleted Toggle and Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="show-deleted"
              checked={showDeleted}
              onCheckedChange={onToggleShowDeleted}
            />
            <Label htmlFor="show-deleted">Show Deleted</Label>
          </div>
          
          {hasActiveFilters && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Filter className="h-3 w-3" />
              Filters Active
            </Badge>
          )}
        </div>

        {/* Record Counts */}
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <span>Total: {totalAllRecords}</span>
          <span>Active: {activeRecordsCount}</span>
          <span>Deleted: {inactiveRecordsCount}</span>
          <span>Showing: {totalRecords}</span>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium">Active filters:</span>
          {searchFilters.name && (
            <Badge variant="outline" className="flex items-center gap-1">
              Name: {searchFilters.name}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => handleFilterChange("name", "")}
              />
            </Badge>
          )}
          {searchFilters.enable !== undefined && (
            <Badge variant="outline" className="flex items-center gap-1">
              Status: {searchFilters.enable ? "Active" : "Inactive"}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => handleFilterChange("enable", undefined)}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
