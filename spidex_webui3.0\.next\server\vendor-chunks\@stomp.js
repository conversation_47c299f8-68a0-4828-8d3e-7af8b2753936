"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@stomp";
exports.ids = ["vendor-chunks/@stomp"];
exports.modules = {

/***/ "(ssr)/./node_modules/@stomp/stompjs/esm6/augment-websocket.js":
/*!***************************************************************!*\
  !*** ./node_modules/@stomp/stompjs/esm6/augment-websocket.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   augmentWebsocket: () => (/* binding */ augmentWebsocket)\n/* harmony export */ });\n/**\n * @internal\n */\nfunction augmentWebsocket(webSocket, debug) {\n    webSocket.terminate = function () {\n        const noOp = () => { };\n        // set all callbacks to no op\n        this.onerror = noOp;\n        this.onmessage = noOp;\n        this.onopen = noOp;\n        const ts = new Date();\n        const id = Math.random().toString().substring(2, 8); // A simulated id\n        const origOnClose = this.onclose;\n        // Track delay in actual closure of the socket\n        this.onclose = closeEvent => {\n            const delay = new Date().getTime() - ts.getTime();\n            debug(`Discarded socket (#${id})  closed after ${delay}ms, with code/reason: ${closeEvent.code}/${closeEvent.reason}`);\n        };\n        this.close();\n        origOnClose?.call(webSocket, {\n            code: 4001,\n            reason: `Quick discarding socket (#${id}) without waiting for the shutdown sequence.`,\n            wasClean: false,\n        });\n    };\n}\n//# sourceMappingURL=augment-websocket.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stomp/stompjs/esm6/augment-websocket.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stomp/stompjs/esm6/byte.js":
/*!**************************************************!*\
  !*** ./node_modules/@stomp/stompjs/esm6/byte.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BYTE: () => (/* binding */ BYTE)\n/* harmony export */ });\n/**\n * Some byte values, used as per STOMP specifications.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nconst BYTE = {\n    // LINEFEED byte (octet 10)\n    LF: '\\x0A',\n    // NULL byte (octet 0)\n    NULL: '\\x00',\n};\n//# sourceMappingURL=byte.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0b21wL3N0b21wanMvZXNtNi9ieXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmlzXFxEb2N1bWVudHNcXFByb2plY3RzXFxTcGlkZXhcXHNwaWRleF93ZWJ1aTMuMFxcbm9kZV9tb2R1bGVzXFxAc3RvbXBcXHN0b21wanNcXGVzbTZcXGJ5dGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBTb21lIGJ5dGUgdmFsdWVzLCB1c2VkIGFzIHBlciBTVE9NUCBzcGVjaWZpY2F0aW9ucy5cbiAqXG4gKiBQYXJ0IG9mIGBAc3RvbXAvc3RvbXBqc2AuXG4gKlxuICogQGludGVybmFsXG4gKi9cbmV4cG9ydCBjb25zdCBCWVRFID0ge1xuICAgIC8vIExJTkVGRUVEIGJ5dGUgKG9jdGV0IDEwKVxuICAgIExGOiAnXFx4MEEnLFxuICAgIC8vIE5VTEwgYnl0ZSAob2N0ZXQgMClcbiAgICBOVUxMOiAnXFx4MDAnLFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJ5dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stomp/stompjs/esm6/byte.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stomp/stompjs/esm6/client.js":
/*!****************************************************!*\
  !*** ./node_modules/@stomp/stompjs/esm6/client.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ Client)\n/* harmony export */ });\n/* harmony import */ var _stomp_handler_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stomp-handler.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/stomp-handler.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/types.js\");\n/* harmony import */ var _versions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./versions.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/versions.js\");\n\n\n\n/**\n * STOMP Client Class.\n *\n * Part of `@stomp/stompjs`.\n */\nclass Client {\n    /**\n     * Underlying WebSocket instance, READONLY.\n     */\n    get webSocket() {\n        return this._stompHandler?._webSocket;\n    }\n    /**\n     * Disconnection headers.\n     */\n    get disconnectHeaders() {\n        return this._disconnectHeaders;\n    }\n    set disconnectHeaders(value) {\n        this._disconnectHeaders = value;\n        if (this._stompHandler) {\n            this._stompHandler.disconnectHeaders = this._disconnectHeaders;\n        }\n    }\n    /**\n     * `true` if there is an active connection to STOMP Broker\n     */\n    get connected() {\n        return !!this._stompHandler && this._stompHandler.connected;\n    }\n    /**\n     * version of STOMP protocol negotiated with the server, READONLY\n     */\n    get connectedVersion() {\n        return this._stompHandler ? this._stompHandler.connectedVersion : undefined;\n    }\n    /**\n     * if the client is active (connected or going to reconnect)\n     */\n    get active() {\n        return this.state === _types_js__WEBPACK_IMPORTED_MODULE_0__.ActivationState.ACTIVE;\n    }\n    _changeState(state) {\n        this.state = state;\n        this.onChangeState(state);\n    }\n    /**\n     * Create an instance.\n     */\n    constructor(conf = {}) {\n        /**\n         * STOMP versions to attempt during STOMP handshake. By default, versions `1.2`, `1.1`, and `1.0` are attempted.\n         *\n         * Example:\n         * ```javascript\n         *        // Try only versions 1.1 and 1.0\n         *        client.stompVersions = new Versions(['1.1', '1.0'])\n         * ```\n         */\n        this.stompVersions = _versions_js__WEBPACK_IMPORTED_MODULE_1__.Versions.default;\n        /**\n         * Will retry if Stomp connection is not established in specified milliseconds.\n         * Default 0, which switches off automatic reconnection.\n         */\n        this.connectionTimeout = 0;\n        /**\n         *  automatically reconnect with delay in milliseconds, set to 0 to disable.\n         */\n        this.reconnectDelay = 5000;\n        /**\n         * tracking the time to the next reconnection. Initialized to [Client#reconnectDelay]{@link Client#reconnectDelay}'s value and it may\n         * change depending on the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} setting\n         */\n        this._nextReconnectDelay = 0;\n        /**\n         * Maximum time to wait between reconnects, in milliseconds. Defaults to 15 minutes.\n         * Only relevant when [Client#reconnectTimeMode]{@link Client#reconnectTimeMode} not LINEAR (e.g., EXPONENTIAL).\n         * Set to 0 for no limit on wait time.\n         */\n        this.maxReconnectDelay = 15 * 60 * 1000; // 15 minutes in ms\n        /**\n         * Reconnection wait time mode, either linear (default) or exponential.\n         * Note: See [Client#maxReconnectDelay]{@link Client#maxReconnectDelay} for setting the maximum delay when exponential\n         *\n         * ```javascript\n         * client.configure({\n         *   reconnectTimeMode: ReconnectionTimeMode.EXPONENTIAL,\n         *   reconnectDelay: 200, // It will wait 200, 400, 800 ms...\n         *   maxReconnectDelay: 10000, // Optional, when provided, it will not wait more that these ms\n         * })\n         * ```\n         */\n        this.reconnectTimeMode = _types_js__WEBPACK_IMPORTED_MODULE_0__.ReconnectionTimeMode.LINEAR;\n        /**\n         * Incoming heartbeat interval in milliseconds. Set to 0 to disable.\n         */\n        this.heartbeatIncoming = 10000;\n        /**\n         * Outgoing heartbeat interval in milliseconds. Set to 0 to disable.\n         */\n        this.heartbeatOutgoing = 10000;\n        /**\n         * Outgoing heartbeat strategy.\n         * See https://github.com/stomp-js/stompjs/pull/579\n         *\n         * Can be worker or interval strategy, but will always use `interval`\n         * if web workers are unavailable, for example, in a non-browser environment.\n         *\n         * Using Web Workers may work better on long-running pages\n         * and mobile apps, as the browser may suspend Timers in the main page.\n         * Try the `Worker` mode if you discover disconnects when the browser tab is in the background.\n         *\n         * When used in a JS environment, use 'worker' or 'interval' as valid values.\n         *\n         * Defaults to `interval` strategy.\n         */\n        this.heartbeatStrategy = _types_js__WEBPACK_IMPORTED_MODULE_0__.TickerStrategy.Interval;\n        /**\n         * This switches on a non-standard behavior while sending WebSocket packets.\n         * It splits larger (text) packets into chunks of [maxWebSocketChunkSize]{@link Client#maxWebSocketChunkSize}.\n         * Only Java Spring brokers seem to support this mode.\n         *\n         * WebSockets, by itself, split large (text) packets,\n         * so it is not needed with a truly compliant STOMP/WebSocket broker.\n         * Setting it for such a broker will cause large messages to fail.\n         *\n         * `false` by default.\n         *\n         * Binary frames are never split.\n         */\n        this.splitLargeFrames = false;\n        /**\n         * See [splitLargeFrames]{@link Client#splitLargeFrames}.\n         * This has no effect if [splitLargeFrames]{@link Client#splitLargeFrames} is `false`.\n         */\n        this.maxWebSocketChunkSize = 8 * 1024;\n        /**\n         * Usually the\n         * [type of WebSocket frame]{@link https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send#Parameters}\n         * is automatically decided by type of the payload.\n         * Default is `false`, which should work with all compliant brokers.\n         *\n         * Set this flag to force binary frames.\n         */\n        this.forceBinaryWSFrames = false;\n        /**\n         * A bug in ReactNative chops a string on occurrence of a NULL.\n         * See issue [https://github.com/stomp-js/stompjs/issues/89]{@link https://github.com/stomp-js/stompjs/issues/89}.\n         * This makes incoming WebSocket messages invalid STOMP packets.\n         * Setting this flag attempts to reverse the damage by appending a NULL.\n         * If the broker splits a large message into multiple WebSocket messages,\n         * this flag will cause data loss and abnormal termination of connection.\n         *\n         * This is not an ideal solution, but a stop gap until the underlying issue is fixed at ReactNative library.\n         */\n        this.appendMissingNULLonIncoming = false;\n        /**\n         * Browsers do not immediately close WebSockets when `.close` is issued.\n         * This may cause reconnection to take a significantly long time in case\n         *  of some types of failures.\n         * In case of incoming heartbeat failure, this experimental flag instructs\n         * the library to discard the socket immediately\n         * (even before it is actually closed).\n         */\n        this.discardWebsocketOnCommFailure = false;\n        /**\n         * Activation state.\n         *\n         * It will usually be ACTIVE or INACTIVE.\n         * When deactivating, it may go from ACTIVE to INACTIVE without entering DEACTIVATING.\n         */\n        this.state = _types_js__WEBPACK_IMPORTED_MODULE_0__.ActivationState.INACTIVE;\n        // No op callbacks\n        const noOp = () => { };\n        this.debug = noOp;\n        this.beforeConnect = noOp;\n        this.onConnect = noOp;\n        this.onDisconnect = noOp;\n        this.onUnhandledMessage = noOp;\n        this.onUnhandledReceipt = noOp;\n        this.onUnhandledFrame = noOp;\n        this.onStompError = noOp;\n        this.onWebSocketClose = noOp;\n        this.onWebSocketError = noOp;\n        this.logRawCommunication = false;\n        this.onChangeState = noOp;\n        // These parameters would typically get proper values before connect is called\n        this.connectHeaders = {};\n        this._disconnectHeaders = {};\n        // Apply configuration\n        this.configure(conf);\n    }\n    /**\n     * Update configuration.\n     */\n    configure(conf) {\n        // bulk assign all properties to this\n        Object.assign(this, conf);\n        // Warn on incorrect maxReconnectDelay settings\n        if (this.maxReconnectDelay > 0 &&\n            this.maxReconnectDelay < this.reconnectDelay) {\n            this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`);\n            this.maxReconnectDelay = this.reconnectDelay;\n        }\n    }\n    /**\n     * Initiate the connection with the broker.\n     * If the connection breaks, as per [Client#reconnectDelay]{@link Client#reconnectDelay},\n     * it will keep trying to reconnect. If the [Client#reconnectTimeMode]{@link Client#reconnectTimeMode}\n     * is set to EXPONENTIAL it will increase the wait time exponentially\n     *\n     * Call [Client#deactivate]{@link Client#deactivate} to disconnect and stop reconnection attempts.\n     */\n    activate() {\n        const _activate = () => {\n            if (this.active) {\n                this.debug('Already ACTIVE, ignoring request to activate');\n                return;\n            }\n            this._changeState(_types_js__WEBPACK_IMPORTED_MODULE_0__.ActivationState.ACTIVE);\n            this._nextReconnectDelay = this.reconnectDelay;\n            this._connect();\n        };\n        // if it is deactivating, wait for it to complete before activating.\n        if (this.state === _types_js__WEBPACK_IMPORTED_MODULE_0__.ActivationState.DEACTIVATING) {\n            this.debug('Waiting for deactivation to finish before activating');\n            this.deactivate().then(() => {\n                _activate();\n            });\n        }\n        else {\n            _activate();\n        }\n    }\n    async _connect() {\n        await this.beforeConnect(this);\n        if (this._stompHandler) {\n            this.debug('There is already a stompHandler, skipping the call to connect');\n            return;\n        }\n        if (!this.active) {\n            this.debug('Client has been marked inactive, will not attempt to connect');\n            return;\n        }\n        // setup connection watcher\n        if (this.connectionTimeout > 0) {\n            // clear first\n            if (this._connectionWatcher) {\n                clearTimeout(this._connectionWatcher);\n            }\n            this._connectionWatcher = setTimeout(() => {\n                if (this.connected) {\n                    return;\n                }\n                // Connection not established, close the underlying socket\n                // a reconnection will be attempted\n                this.debug(`Connection not established in ${this.connectionTimeout}ms, closing socket`);\n                this.forceDisconnect();\n            }, this.connectionTimeout);\n        }\n        this.debug('Opening Web Socket...');\n        // Get the actual WebSocket (or a similar object)\n        const webSocket = this._createWebSocket();\n        this._stompHandler = new _stomp_handler_js__WEBPACK_IMPORTED_MODULE_2__.StompHandler(this, webSocket, {\n            debug: this.debug,\n            stompVersions: this.stompVersions,\n            connectHeaders: this.connectHeaders,\n            disconnectHeaders: this._disconnectHeaders,\n            heartbeatIncoming: this.heartbeatIncoming,\n            heartbeatOutgoing: this.heartbeatOutgoing,\n            heartbeatStrategy: this.heartbeatStrategy,\n            splitLargeFrames: this.splitLargeFrames,\n            maxWebSocketChunkSize: this.maxWebSocketChunkSize,\n            forceBinaryWSFrames: this.forceBinaryWSFrames,\n            logRawCommunication: this.logRawCommunication,\n            appendMissingNULLonIncoming: this.appendMissingNULLonIncoming,\n            discardWebsocketOnCommFailure: this.discardWebsocketOnCommFailure,\n            onConnect: frame => {\n                // Successfully connected, stop the connection watcher\n                if (this._connectionWatcher) {\n                    clearTimeout(this._connectionWatcher);\n                    this._connectionWatcher = undefined;\n                }\n                if (!this.active) {\n                    this.debug('STOMP got connected while deactivate was issued, will disconnect now');\n                    this._disposeStompHandler();\n                    return;\n                }\n                this.onConnect(frame);\n            },\n            onDisconnect: frame => {\n                this.onDisconnect(frame);\n            },\n            onStompError: frame => {\n                this.onStompError(frame);\n            },\n            onWebSocketClose: evt => {\n                this._stompHandler = undefined; // a new one will be created in case of a reconnect\n                if (this.state === _types_js__WEBPACK_IMPORTED_MODULE_0__.ActivationState.DEACTIVATING) {\n                    // Mark deactivation complete\n                    this._changeState(_types_js__WEBPACK_IMPORTED_MODULE_0__.ActivationState.INACTIVE);\n                }\n                // The callback is called before attempting to reconnect, this would allow the client\n                // to be `deactivated` in the callback.\n                this.onWebSocketClose(evt);\n                if (this.active) {\n                    this._schedule_reconnect();\n                }\n            },\n            onWebSocketError: evt => {\n                this.onWebSocketError(evt);\n            },\n            onUnhandledMessage: message => {\n                this.onUnhandledMessage(message);\n            },\n            onUnhandledReceipt: frame => {\n                this.onUnhandledReceipt(frame);\n            },\n            onUnhandledFrame: frame => {\n                this.onUnhandledFrame(frame);\n            },\n        });\n        this._stompHandler.start();\n    }\n    _createWebSocket() {\n        let webSocket;\n        if (this.webSocketFactory) {\n            webSocket = this.webSocketFactory();\n        }\n        else if (this.brokerURL) {\n            webSocket = new WebSocket(this.brokerURL, this.stompVersions.protocolVersions());\n        }\n        else {\n            throw new Error('Either brokerURL or webSocketFactory must be provided');\n        }\n        webSocket.binaryType = 'arraybuffer';\n        return webSocket;\n    }\n    _schedule_reconnect() {\n        if (this._nextReconnectDelay > 0) {\n            this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`);\n            this._reconnector = setTimeout(() => {\n                if (this.reconnectTimeMode === _types_js__WEBPACK_IMPORTED_MODULE_0__.ReconnectionTimeMode.EXPONENTIAL) {\n                    this._nextReconnectDelay = this._nextReconnectDelay * 2;\n                    // Truncated exponential backoff with a set limit unless disabled\n                    if (this.maxReconnectDelay !== 0) {\n                        this._nextReconnectDelay = Math.min(this._nextReconnectDelay, this.maxReconnectDelay);\n                    }\n                }\n                this._connect();\n            }, this._nextReconnectDelay);\n        }\n    }\n    /**\n     * Disconnect if connected and stop auto reconnect loop.\n     * Appropriate callbacks will be invoked if there is an underlying STOMP connection.\n     *\n     * This call is async. It will resolve immediately if there is no underlying active websocket,\n     * otherwise, it will resolve after the underlying websocket is properly disposed of.\n     *\n     * It is not an error to invoke this method more than once.\n     * Each of those would resolve on completion of deactivation.\n     *\n     * To reactivate, you can call [Client#activate]{@link Client#activate}.\n     *\n     * Experimental: pass `force: true` to immediately discard the underlying connection.\n     * This mode will skip both the STOMP and the Websocket shutdown sequences.\n     * In some cases, browsers take a long time in the Websocket shutdown\n     * if the underlying connection had gone stale.\n     * Using this mode can speed up.\n     * When this mode is used, the actual Websocket may linger for a while\n     * and the broker may not realize that the connection is no longer in use.\n     *\n     * It is possible to invoke this method initially without the `force` option\n     * and subsequently, say after a wait, with the `force` option.\n     */\n    async deactivate(options = {}) {\n        const force = options.force || false;\n        const needToDispose = this.active;\n        let retPromise;\n        if (this.state === _types_js__WEBPACK_IMPORTED_MODULE_0__.ActivationState.INACTIVE) {\n            this.debug(`Already INACTIVE, nothing more to do`);\n            return Promise.resolve();\n        }\n        this._changeState(_types_js__WEBPACK_IMPORTED_MODULE_0__.ActivationState.DEACTIVATING);\n        // Reset reconnection timer just to be safe\n        this._nextReconnectDelay = 0;\n        // Clear if a reconnection was scheduled\n        if (this._reconnector) {\n            clearTimeout(this._reconnector);\n            this._reconnector = undefined;\n        }\n        if (this._stompHandler &&\n            // @ts-ignore - if there is a _stompHandler, there is the webSocket\n            this.webSocket.readyState !== _types_js__WEBPACK_IMPORTED_MODULE_0__.StompSocketState.CLOSED) {\n            const origOnWebSocketClose = this._stompHandler.onWebSocketClose;\n            // we need to wait for the underlying websocket to close\n            retPromise = new Promise((resolve, reject) => {\n                // @ts-ignore - there is a _stompHandler\n                this._stompHandler.onWebSocketClose = evt => {\n                    origOnWebSocketClose(evt);\n                    resolve();\n                };\n            });\n        }\n        else {\n            // indicate that auto reconnect loop should terminate\n            this._changeState(_types_js__WEBPACK_IMPORTED_MODULE_0__.ActivationState.INACTIVE);\n            return Promise.resolve();\n        }\n        if (force) {\n            this._stompHandler?.discardWebsocket();\n        }\n        else if (needToDispose) {\n            this._disposeStompHandler();\n        }\n        return retPromise;\n    }\n    /**\n     * Force disconnect if there is an active connection by directly closing the underlying WebSocket.\n     * This is different from a normal disconnect where a DISCONNECT sequence is carried out with the broker.\n     * After forcing disconnect, automatic reconnect will be attempted.\n     * To stop further reconnects call [Client#deactivate]{@link Client#deactivate} as well.\n     */\n    forceDisconnect() {\n        if (this._stompHandler) {\n            this._stompHandler.forceDisconnect();\n        }\n    }\n    _disposeStompHandler() {\n        // Dispose STOMP Handler\n        if (this._stompHandler) {\n            this._stompHandler.dispose();\n        }\n    }\n    /**\n     * Send a message to a named destination. Refer to your STOMP broker documentation for types\n     * and naming of destinations.\n     *\n     * STOMP protocol specifies and suggests some headers and also allows broker-specific headers.\n     *\n     * `body` must be String.\n     * You will need to covert the payload to string in case it is not string (e.g. JSON).\n     *\n     * To send a binary message body, use `binaryBody` parameter. It should be a\n     * [Uint8Array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Uint8Array).\n     * Sometimes brokers may not support binary frames out of the box.\n     * Please check your broker documentation.\n     *\n     * `content-length` header is automatically added to the STOMP Frame sent to the broker.\n     * Set `skipContentLengthHeader` to indicate that `content-length` header should not be added.\n     * For binary messages, `content-length` header is always added.\n     *\n     * Caution: The broker will, most likely, report an error and disconnect\n     * if the message body has NULL octet(s) and `content-length` header is missing.\n     *\n     * ```javascript\n     *        client.publish({destination: \"/queue/test\", headers: {priority: 9}, body: \"Hello, STOMP\"});\n     *\n     *        // Only destination is mandatory parameter\n     *        client.publish({destination: \"/queue/test\", body: \"Hello, STOMP\"});\n     *\n     *        // Skip content-length header in the frame to the broker\n     *        client.publish({\"/queue/test\", body: \"Hello, STOMP\", skipContentLengthHeader: true});\n     *\n     *        var binaryData = generateBinaryData(); // This need to be of type Uint8Array\n     *        // setting content-type header is not mandatory, however a good practice\n     *        client.publish({destination: '/topic/special', binaryBody: binaryData,\n     *                         headers: {'content-type': 'application/octet-stream'}});\n     * ```\n     */\n    publish(params) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.publish(params);\n    }\n    _checkConnection() {\n        if (!this.connected) {\n            throw new TypeError('There is no underlying STOMP connection');\n        }\n    }\n    /**\n     * STOMP brokers may carry out operation asynchronously and allow requesting for acknowledgement.\n     * To request an acknowledgement, a `receipt` header needs to be sent with the actual request.\n     * The value (say receipt-id) for this header needs to be unique for each use.\n     * Typically, a sequence, a UUID, a random number or a combination may be used.\n     *\n     * A complaint broker will send a RECEIPT frame when an operation has actually been completed.\n     * The operation needs to be matched based on the value of the receipt-id.\n     *\n     * This method allows watching for a receipt and invoking the callback\n     *  when the corresponding receipt has been received.\n     *\n     * The actual {@link IFrame} will be passed as parameter to the callback.\n     *\n     * Example:\n     * ```javascript\n     *        // Subscribing with acknowledgement\n     *        let receiptId = randomText();\n     *\n     *        client.watchForReceipt(receiptId, function() {\n     *          // Will be called after server acknowledges\n     *        });\n     *\n     *        client.subscribe(TEST.destination, onMessage, {receipt: receiptId});\n     *\n     *\n     *        // Publishing with acknowledgement\n     *        receiptId = randomText();\n     *\n     *        client.watchForReceipt(receiptId, function() {\n     *          // Will be called after server acknowledges\n     *        });\n     *        client.publish({destination: TEST.destination, headers: {receipt: receiptId}, body: msg});\n     * ```\n     */\n    watchForReceipt(receiptId, callback) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.watchForReceipt(receiptId, callback);\n    }\n    /**\n     * Subscribe to a STOMP Broker location. The callback will be invoked for each\n     * received message with the {@link IMessage} as argument.\n     *\n     * Note: The library will generate a unique ID if there is none provided in the headers.\n     *       To use your own ID, pass it using the `headers` argument.\n     *\n     * ```javascript\n     *        callback = function(message) {\n     *        // called when the client receives a STOMP message from the server\n     *          if (message.body) {\n     *            alert(\"got message with body \" + message.body)\n     *          } else {\n     *            alert(\"got empty message\");\n     *          }\n     *        });\n     *\n     *        var subscription = client.subscribe(\"/queue/test\", callback);\n     *\n     *        // Explicit subscription id\n     *        var mySubId = 'my-subscription-id-001';\n     *        var subscription = client.subscribe(destination, callback, { id: mySubId });\n     * ```\n     */\n    subscribe(destination, callback, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        return this._stompHandler.subscribe(destination, callback, headers);\n    }\n    /**\n     * It is preferable to unsubscribe from a subscription by calling\n     * `unsubscribe()` directly on {@link StompSubscription} returned by `client.subscribe()`:\n     *\n     * ```javascript\n     *        var subscription = client.subscribe(destination, onmessage);\n     *        // ...\n     *        subscription.unsubscribe();\n     * ```\n     *\n     * See: https://stomp.github.com/stomp-specification-1.2.html#UNSUBSCRIBE UNSUBSCRIBE Frame\n     */\n    unsubscribe(id, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.unsubscribe(id, headers);\n    }\n    /**\n     * Start a transaction, the returned {@link ITransaction} has methods - [commit]{@link ITransaction#commit}\n     * and [abort]{@link ITransaction#abort}.\n     *\n     * `transactionId` is optional, if not passed the library will generate it internally.\n     */\n    begin(transactionId) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        return this._stompHandler.begin(transactionId);\n    }\n    /**\n     * Commit a transaction.\n     *\n     * It is preferable to commit a transaction by calling [commit]{@link ITransaction#commit} directly on\n     * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n     *\n     * ```javascript\n     *        var tx = client.begin(txId);\n     *        //...\n     *        tx.commit();\n     * ```\n     */\n    commit(transactionId) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.commit(transactionId);\n    }\n    /**\n     * Abort a transaction.\n     * It is preferable to abort a transaction by calling [abort]{@link ITransaction#abort} directly on\n     * {@link ITransaction} returned by [client.begin]{@link Client#begin}.\n     *\n     * ```javascript\n     *        var tx = client.begin(txId);\n     *        //...\n     *        tx.abort();\n     * ```\n     */\n    abort(transactionId) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.abort(transactionId);\n    }\n    /**\n     * ACK a message. It is preferable to acknowledge a message by calling [ack]{@link IMessage#ack} directly\n     * on the {@link IMessage} handled by a subscription callback:\n     *\n     * ```javascript\n     *        var callback = function (message) {\n     *          // process the message\n     *          // acknowledge it\n     *          message.ack();\n     *        };\n     *        client.subscribe(destination, callback, {'ack': 'client'});\n     * ```\n     */\n    ack(messageId, subscriptionId, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.ack(messageId, subscriptionId, headers);\n    }\n    /**\n     * NACK a message. It is preferable to acknowledge a message by calling [nack]{@link IMessage#nack} directly\n     * on the {@link IMessage} handled by a subscription callback:\n     *\n     * ```javascript\n     *        var callback = function (message) {\n     *          // process the message\n     *          // an error occurs, nack it\n     *          message.nack();\n     *        };\n     *        client.subscribe(destination, callback, {'ack': 'client'});\n     * ```\n     */\n    nack(messageId, subscriptionId, headers = {}) {\n        this._checkConnection();\n        // @ts-ignore - we already checked that there is a _stompHandler, and it is connected\n        this._stompHandler.nack(messageId, subscriptionId, headers);\n    }\n}\n//# sourceMappingURL=client.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0b21wL3N0b21wanMvZXNtNi9jbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrRDtBQUNvRDtBQUM3RDtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixzREFBZTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixrREFBUTtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhGQUE4Riw0QkFBNEI7QUFDMUgsOERBQThELGdDQUFnQztBQUM5RjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RCxnQ0FBZ0M7QUFDekY7QUFDQTtBQUNBLGlEQUFpRDtBQUNqRDtBQUNBO0FBQ0EsZ0RBQWdELGdDQUFnQztBQUNoRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLGlDQUFpQywyREFBb0I7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxxREFBYztBQUMvQztBQUNBO0FBQ0Esa0ZBQWtGLG1DQUFtQztBQUNySDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsOEJBQThCO0FBQ2hFLG9EQUFvRCwrQkFBK0I7QUFDbkY7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFFQUFxRSxvREFBb0Q7QUFDekg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQixzREFBZTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELHVCQUF1QixtQ0FBbUMsb0JBQW9CO0FBQ3BJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnRUFBZ0UsNEJBQTRCO0FBQzVGLDJFQUEyRTtBQUMzRTtBQUNBO0FBQ0EsZ0NBQWdDLHlCQUF5QjtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixzREFBZTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixzREFBZTtBQUMxQztBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNERBQTRELHVCQUF1QjtBQUNuRjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQywyREFBWTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxnREFBZ0Q7QUFDaEQsbUNBQW1DLHNEQUFlO0FBQ2xEO0FBQ0Esc0NBQXNDLHNEQUFlO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDREQUE0RCx5QkFBeUI7QUFDckY7QUFDQSwrQ0FBK0MsMkRBQW9CO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxzQkFBc0I7QUFDM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixzREFBZTtBQUMxQztBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsc0RBQWU7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLHVEQUFnQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixzREFBZTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELHlCQUF5QjtBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QixzQ0FBc0MsWUFBWSx1QkFBdUI7QUFDdkc7QUFDQTtBQUNBLDhCQUE4QixpREFBaUQ7QUFDL0U7QUFDQTtBQUNBLDhCQUE4QixtRUFBbUU7QUFDakc7QUFDQSxxREFBcUQ7QUFDckQ7QUFDQSw4QkFBOEI7QUFDOUIseUNBQXlDLDRDQUE0QztBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsY0FBYztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0EsNkRBQTZELG1CQUFtQjtBQUNoRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWU7QUFDZiw4QkFBOEIseUNBQXlDLG1CQUFtQixZQUFZO0FBQ3RHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxnQkFBZ0I7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJFQUEyRSxhQUFhO0FBQ3hGO0FBQ0E7QUFDQSxpREFBaUQ7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHlCQUF5QjtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQyxvQkFBb0IsdUJBQXVCO0FBQ3JGLG1CQUFtQix5QkFBeUI7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9FQUFvRSwyQkFBMkI7QUFDL0YsUUFBUSxvQkFBb0IsMkJBQTJCLG1CQUFtQjtBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0VBQWtFLDBCQUEwQjtBQUM1RixRQUFRLG9CQUFvQiwyQkFBMkIsbUJBQW1CO0FBQzFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUZBQWlGLG9CQUFvQjtBQUNyRyxlQUFlLGdCQUFnQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RCxnQkFBZ0I7QUFDdkU7QUFDQTtBQUNBLCtDQUErQztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUZBQW1GLHFCQUFxQjtBQUN4RyxlQUFlLGdCQUFnQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RCxnQkFBZ0I7QUFDdkU7QUFDQTtBQUNBLGdEQUFnRDtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aXNcXERvY3VtZW50c1xcUHJvamVjdHNcXFNwaWRleFxcc3BpZGV4X3dlYnVpMy4wXFxub2RlX21vZHVsZXNcXEBzdG9tcFxcc3RvbXBqc1xcZXNtNlxcY2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFN0b21wSGFuZGxlciB9IGZyb20gJy4vc3RvbXAtaGFuZGxlci5qcyc7XG5pbXBvcnQgeyBBY3RpdmF0aW9uU3RhdGUsIFJlY29ubmVjdGlvblRpbWVNb2RlLCBTdG9tcFNvY2tldFN0YXRlLCBUaWNrZXJTdHJhdGVneSwgfSBmcm9tICcuL3R5cGVzLmpzJztcbmltcG9ydCB7IFZlcnNpb25zIH0gZnJvbSAnLi92ZXJzaW9ucy5qcyc7XG4vKipcbiAqIFNUT01QIENsaWVudCBDbGFzcy5cbiAqXG4gKiBQYXJ0IG9mIGBAc3RvbXAvc3RvbXBqc2AuXG4gKi9cbmV4cG9ydCBjbGFzcyBDbGllbnQge1xuICAgIC8qKlxuICAgICAqIFVuZGVybHlpbmcgV2ViU29ja2V0IGluc3RhbmNlLCBSRUFET05MWS5cbiAgICAgKi9cbiAgICBnZXQgd2ViU29ja2V0KCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fc3RvbXBIYW5kbGVyPy5fd2ViU29ja2V0O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEaXNjb25uZWN0aW9uIGhlYWRlcnMuXG4gICAgICovXG4gICAgZ2V0IGRpc2Nvbm5lY3RIZWFkZXJzKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fZGlzY29ubmVjdEhlYWRlcnM7XG4gICAgfVxuICAgIHNldCBkaXNjb25uZWN0SGVhZGVycyh2YWx1ZSkge1xuICAgICAgICB0aGlzLl9kaXNjb25uZWN0SGVhZGVycyA9IHZhbHVlO1xuICAgICAgICBpZiAodGhpcy5fc3RvbXBIYW5kbGVyKSB7XG4gICAgICAgICAgICB0aGlzLl9zdG9tcEhhbmRsZXIuZGlzY29ubmVjdEhlYWRlcnMgPSB0aGlzLl9kaXNjb25uZWN0SGVhZGVycztcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBgdHJ1ZWAgaWYgdGhlcmUgaXMgYW4gYWN0aXZlIGNvbm5lY3Rpb24gdG8gU1RPTVAgQnJva2VyXG4gICAgICovXG4gICAgZ2V0IGNvbm5lY3RlZCgpIHtcbiAgICAgICAgcmV0dXJuICEhdGhpcy5fc3RvbXBIYW5kbGVyICYmIHRoaXMuX3N0b21wSGFuZGxlci5jb25uZWN0ZWQ7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIHZlcnNpb24gb2YgU1RPTVAgcHJvdG9jb2wgbmVnb3RpYXRlZCB3aXRoIHRoZSBzZXJ2ZXIsIFJFQURPTkxZXG4gICAgICovXG4gICAgZ2V0IGNvbm5lY3RlZFZlcnNpb24oKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9zdG9tcEhhbmRsZXIgPyB0aGlzLl9zdG9tcEhhbmRsZXIuY29ubmVjdGVkVmVyc2lvbiA6IHVuZGVmaW5lZDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogaWYgdGhlIGNsaWVudCBpcyBhY3RpdmUgKGNvbm5lY3RlZCBvciBnb2luZyB0byByZWNvbm5lY3QpXG4gICAgICovXG4gICAgZ2V0IGFjdGl2ZSgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuc3RhdGUgPT09IEFjdGl2YXRpb25TdGF0ZS5BQ1RJVkU7XG4gICAgfVxuICAgIF9jaGFuZ2VTdGF0ZShzdGF0ZSkge1xuICAgICAgICB0aGlzLnN0YXRlID0gc3RhdGU7XG4gICAgICAgIHRoaXMub25DaGFuZ2VTdGF0ZShzdGF0ZSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENyZWF0ZSBhbiBpbnN0YW5jZS5cbiAgICAgKi9cbiAgICBjb25zdHJ1Y3Rvcihjb25mID0ge30pIHtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIFNUT01QIHZlcnNpb25zIHRvIGF0dGVtcHQgZHVyaW5nIFNUT01QIGhhbmRzaGFrZS4gQnkgZGVmYXVsdCwgdmVyc2lvbnMgYDEuMmAsIGAxLjFgLCBhbmQgYDEuMGAgYXJlIGF0dGVtcHRlZC5cbiAgICAgICAgICpcbiAgICAgICAgICogRXhhbXBsZTpcbiAgICAgICAgICogYGBgamF2YXNjcmlwdFxuICAgICAgICAgKiAgICAgICAgLy8gVHJ5IG9ubHkgdmVyc2lvbnMgMS4xIGFuZCAxLjBcbiAgICAgICAgICogICAgICAgIGNsaWVudC5zdG9tcFZlcnNpb25zID0gbmV3IFZlcnNpb25zKFsnMS4xJywgJzEuMCddKVxuICAgICAgICAgKiBgYGBcbiAgICAgICAgICovXG4gICAgICAgIHRoaXMuc3RvbXBWZXJzaW9ucyA9IFZlcnNpb25zLmRlZmF1bHQ7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBXaWxsIHJldHJ5IGlmIFN0b21wIGNvbm5lY3Rpb24gaXMgbm90IGVzdGFibGlzaGVkIGluIHNwZWNpZmllZCBtaWxsaXNlY29uZHMuXG4gICAgICAgICAqIERlZmF1bHQgMCwgd2hpY2ggc3dpdGNoZXMgb2ZmIGF1dG9tYXRpYyByZWNvbm5lY3Rpb24uXG4gICAgICAgICAqL1xuICAgICAgICB0aGlzLmNvbm5lY3Rpb25UaW1lb3V0ID0gMDtcbiAgICAgICAgLyoqXG4gICAgICAgICAqICBhdXRvbWF0aWNhbGx5IHJlY29ubmVjdCB3aXRoIGRlbGF5IGluIG1pbGxpc2Vjb25kcywgc2V0IHRvIDAgdG8gZGlzYWJsZS5cbiAgICAgICAgICovXG4gICAgICAgIHRoaXMucmVjb25uZWN0RGVsYXkgPSA1MDAwO1xuICAgICAgICAvKipcbiAgICAgICAgICogdHJhY2tpbmcgdGhlIHRpbWUgdG8gdGhlIG5leHQgcmVjb25uZWN0aW9uLiBJbml0aWFsaXplZCB0byBbQ2xpZW50I3JlY29ubmVjdERlbGF5XXtAbGluayBDbGllbnQjcmVjb25uZWN0RGVsYXl9J3MgdmFsdWUgYW5kIGl0IG1heVxuICAgICAgICAgKiBjaGFuZ2UgZGVwZW5kaW5nIG9uIHRoZSBbQ2xpZW50I3JlY29ubmVjdFRpbWVNb2RlXXtAbGluayBDbGllbnQjcmVjb25uZWN0VGltZU1vZGV9IHNldHRpbmdcbiAgICAgICAgICovXG4gICAgICAgIHRoaXMuX25leHRSZWNvbm5lY3REZWxheSA9IDA7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBNYXhpbXVtIHRpbWUgdG8gd2FpdCBiZXR3ZWVuIHJlY29ubmVjdHMsIGluIG1pbGxpc2Vjb25kcy4gRGVmYXVsdHMgdG8gMTUgbWludXRlcy5cbiAgICAgICAgICogT25seSByZWxldmFudCB3aGVuIFtDbGllbnQjcmVjb25uZWN0VGltZU1vZGVde0BsaW5rIENsaWVudCNyZWNvbm5lY3RUaW1lTW9kZX0gbm90IExJTkVBUiAoZS5nLiwgRVhQT05FTlRJQUwpLlxuICAgICAgICAgKiBTZXQgdG8gMCBmb3Igbm8gbGltaXQgb24gd2FpdCB0aW1lLlxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy5tYXhSZWNvbm5lY3REZWxheSA9IDE1ICogNjAgKiAxMDAwOyAvLyAxNSBtaW51dGVzIGluIG1zXG4gICAgICAgIC8qKlxuICAgICAgICAgKiBSZWNvbm5lY3Rpb24gd2FpdCB0aW1lIG1vZGUsIGVpdGhlciBsaW5lYXIgKGRlZmF1bHQpIG9yIGV4cG9uZW50aWFsLlxuICAgICAgICAgKiBOb3RlOiBTZWUgW0NsaWVudCNtYXhSZWNvbm5lY3REZWxheV17QGxpbmsgQ2xpZW50I21heFJlY29ubmVjdERlbGF5fSBmb3Igc2V0dGluZyB0aGUgbWF4aW11bSBkZWxheSB3aGVuIGV4cG9uZW50aWFsXG4gICAgICAgICAqXG4gICAgICAgICAqIGBgYGphdmFzY3JpcHRcbiAgICAgICAgICogY2xpZW50LmNvbmZpZ3VyZSh7XG4gICAgICAgICAqICAgcmVjb25uZWN0VGltZU1vZGU6IFJlY29ubmVjdGlvblRpbWVNb2RlLkVYUE9ORU5USUFMLFxuICAgICAgICAgKiAgIHJlY29ubmVjdERlbGF5OiAyMDAsIC8vIEl0IHdpbGwgd2FpdCAyMDAsIDQwMCwgODAwIG1zLi4uXG4gICAgICAgICAqICAgbWF4UmVjb25uZWN0RGVsYXk6IDEwMDAwLCAvLyBPcHRpb25hbCwgd2hlbiBwcm92aWRlZCwgaXQgd2lsbCBub3Qgd2FpdCBtb3JlIHRoYXQgdGhlc2UgbXNcbiAgICAgICAgICogfSlcbiAgICAgICAgICogYGBgXG4gICAgICAgICAqL1xuICAgICAgICB0aGlzLnJlY29ubmVjdFRpbWVNb2RlID0gUmVjb25uZWN0aW9uVGltZU1vZGUuTElORUFSO1xuICAgICAgICAvKipcbiAgICAgICAgICogSW5jb21pbmcgaGVhcnRiZWF0IGludGVydmFsIGluIG1pbGxpc2Vjb25kcy4gU2V0IHRvIDAgdG8gZGlzYWJsZS5cbiAgICAgICAgICovXG4gICAgICAgIHRoaXMuaGVhcnRiZWF0SW5jb21pbmcgPSAxMDAwMDtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIE91dGdvaW5nIGhlYXJ0YmVhdCBpbnRlcnZhbCBpbiBtaWxsaXNlY29uZHMuIFNldCB0byAwIHRvIGRpc2FibGUuXG4gICAgICAgICAqL1xuICAgICAgICB0aGlzLmhlYXJ0YmVhdE91dGdvaW5nID0gMTAwMDA7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBPdXRnb2luZyBoZWFydGJlYXQgc3RyYXRlZ3kuXG4gICAgICAgICAqIFNlZSBodHRwczovL2dpdGh1Yi5jb20vc3RvbXAtanMvc3RvbXBqcy9wdWxsLzU3OVxuICAgICAgICAgKlxuICAgICAgICAgKiBDYW4gYmUgd29ya2VyIG9yIGludGVydmFsIHN0cmF0ZWd5LCBidXQgd2lsbCBhbHdheXMgdXNlIGBpbnRlcnZhbGBcbiAgICAgICAgICogaWYgd2ViIHdvcmtlcnMgYXJlIHVuYXZhaWxhYmxlLCBmb3IgZXhhbXBsZSwgaW4gYSBub24tYnJvd3NlciBlbnZpcm9ubWVudC5cbiAgICAgICAgICpcbiAgICAgICAgICogVXNpbmcgV2ViIFdvcmtlcnMgbWF5IHdvcmsgYmV0dGVyIG9uIGxvbmctcnVubmluZyBwYWdlc1xuICAgICAgICAgKiBhbmQgbW9iaWxlIGFwcHMsIGFzIHRoZSBicm93c2VyIG1heSBzdXNwZW5kIFRpbWVycyBpbiB0aGUgbWFpbiBwYWdlLlxuICAgICAgICAgKiBUcnkgdGhlIGBXb3JrZXJgIG1vZGUgaWYgeW91IGRpc2NvdmVyIGRpc2Nvbm5lY3RzIHdoZW4gdGhlIGJyb3dzZXIgdGFiIGlzIGluIHRoZSBiYWNrZ3JvdW5kLlxuICAgICAgICAgKlxuICAgICAgICAgKiBXaGVuIHVzZWQgaW4gYSBKUyBlbnZpcm9ubWVudCwgdXNlICd3b3JrZXInIG9yICdpbnRlcnZhbCcgYXMgdmFsaWQgdmFsdWVzLlxuICAgICAgICAgKlxuICAgICAgICAgKiBEZWZhdWx0cyB0byBgaW50ZXJ2YWxgIHN0cmF0ZWd5LlxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy5oZWFydGJlYXRTdHJhdGVneSA9IFRpY2tlclN0cmF0ZWd5LkludGVydmFsO1xuICAgICAgICAvKipcbiAgICAgICAgICogVGhpcyBzd2l0Y2hlcyBvbiBhIG5vbi1zdGFuZGFyZCBiZWhhdmlvciB3aGlsZSBzZW5kaW5nIFdlYlNvY2tldCBwYWNrZXRzLlxuICAgICAgICAgKiBJdCBzcGxpdHMgbGFyZ2VyICh0ZXh0KSBwYWNrZXRzIGludG8gY2h1bmtzIG9mIFttYXhXZWJTb2NrZXRDaHVua1NpemVde0BsaW5rIENsaWVudCNtYXhXZWJTb2NrZXRDaHVua1NpemV9LlxuICAgICAgICAgKiBPbmx5IEphdmEgU3ByaW5nIGJyb2tlcnMgc2VlbSB0byBzdXBwb3J0IHRoaXMgbW9kZS5cbiAgICAgICAgICpcbiAgICAgICAgICogV2ViU29ja2V0cywgYnkgaXRzZWxmLCBzcGxpdCBsYXJnZSAodGV4dCkgcGFja2V0cyxcbiAgICAgICAgICogc28gaXQgaXMgbm90IG5lZWRlZCB3aXRoIGEgdHJ1bHkgY29tcGxpYW50IFNUT01QL1dlYlNvY2tldCBicm9rZXIuXG4gICAgICAgICAqIFNldHRpbmcgaXQgZm9yIHN1Y2ggYSBicm9rZXIgd2lsbCBjYXVzZSBsYXJnZSBtZXNzYWdlcyB0byBmYWlsLlxuICAgICAgICAgKlxuICAgICAgICAgKiBgZmFsc2VgIGJ5IGRlZmF1bHQuXG4gICAgICAgICAqXG4gICAgICAgICAqIEJpbmFyeSBmcmFtZXMgYXJlIG5ldmVyIHNwbGl0LlxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy5zcGxpdExhcmdlRnJhbWVzID0gZmFsc2U7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBTZWUgW3NwbGl0TGFyZ2VGcmFtZXNde0BsaW5rIENsaWVudCNzcGxpdExhcmdlRnJhbWVzfS5cbiAgICAgICAgICogVGhpcyBoYXMgbm8gZWZmZWN0IGlmIFtzcGxpdExhcmdlRnJhbWVzXXtAbGluayBDbGllbnQjc3BsaXRMYXJnZUZyYW1lc30gaXMgYGZhbHNlYC5cbiAgICAgICAgICovXG4gICAgICAgIHRoaXMubWF4V2ViU29ja2V0Q2h1bmtTaXplID0gOCAqIDEwMjQ7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBVc3VhbGx5IHRoZVxuICAgICAgICAgKiBbdHlwZSBvZiBXZWJTb2NrZXQgZnJhbWVde0BsaW5rIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0FQSS9XZWJTb2NrZXQvc2VuZCNQYXJhbWV0ZXJzfVxuICAgICAgICAgKiBpcyBhdXRvbWF0aWNhbGx5IGRlY2lkZWQgYnkgdHlwZSBvZiB0aGUgcGF5bG9hZC5cbiAgICAgICAgICogRGVmYXVsdCBpcyBgZmFsc2VgLCB3aGljaCBzaG91bGQgd29yayB3aXRoIGFsbCBjb21wbGlhbnQgYnJva2Vycy5cbiAgICAgICAgICpcbiAgICAgICAgICogU2V0IHRoaXMgZmxhZyB0byBmb3JjZSBiaW5hcnkgZnJhbWVzLlxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy5mb3JjZUJpbmFyeVdTRnJhbWVzID0gZmFsc2U7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBBIGJ1ZyBpbiBSZWFjdE5hdGl2ZSBjaG9wcyBhIHN0cmluZyBvbiBvY2N1cnJlbmNlIG9mIGEgTlVMTC5cbiAgICAgICAgICogU2VlIGlzc3VlIFtodHRwczovL2dpdGh1Yi5jb20vc3RvbXAtanMvc3RvbXBqcy9pc3N1ZXMvODlde0BsaW5rIGh0dHBzOi8vZ2l0aHViLmNvbS9zdG9tcC1qcy9zdG9tcGpzL2lzc3Vlcy84OX0uXG4gICAgICAgICAqIFRoaXMgbWFrZXMgaW5jb21pbmcgV2ViU29ja2V0IG1lc3NhZ2VzIGludmFsaWQgU1RPTVAgcGFja2V0cy5cbiAgICAgICAgICogU2V0dGluZyB0aGlzIGZsYWcgYXR0ZW1wdHMgdG8gcmV2ZXJzZSB0aGUgZGFtYWdlIGJ5IGFwcGVuZGluZyBhIE5VTEwuXG4gICAgICAgICAqIElmIHRoZSBicm9rZXIgc3BsaXRzIGEgbGFyZ2UgbWVzc2FnZSBpbnRvIG11bHRpcGxlIFdlYlNvY2tldCBtZXNzYWdlcyxcbiAgICAgICAgICogdGhpcyBmbGFnIHdpbGwgY2F1c2UgZGF0YSBsb3NzIGFuZCBhYm5vcm1hbCB0ZXJtaW5hdGlvbiBvZiBjb25uZWN0aW9uLlxuICAgICAgICAgKlxuICAgICAgICAgKiBUaGlzIGlzIG5vdCBhbiBpZGVhbCBzb2x1dGlvbiwgYnV0IGEgc3RvcCBnYXAgdW50aWwgdGhlIHVuZGVybHlpbmcgaXNzdWUgaXMgZml4ZWQgYXQgUmVhY3ROYXRpdmUgbGlicmFyeS5cbiAgICAgICAgICovXG4gICAgICAgIHRoaXMuYXBwZW5kTWlzc2luZ05VTExvbkluY29taW5nID0gZmFsc2U7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBCcm93c2VycyBkbyBub3QgaW1tZWRpYXRlbHkgY2xvc2UgV2ViU29ja2V0cyB3aGVuIGAuY2xvc2VgIGlzIGlzc3VlZC5cbiAgICAgICAgICogVGhpcyBtYXkgY2F1c2UgcmVjb25uZWN0aW9uIHRvIHRha2UgYSBzaWduaWZpY2FudGx5IGxvbmcgdGltZSBpbiBjYXNlXG4gICAgICAgICAqICBvZiBzb21lIHR5cGVzIG9mIGZhaWx1cmVzLlxuICAgICAgICAgKiBJbiBjYXNlIG9mIGluY29taW5nIGhlYXJ0YmVhdCBmYWlsdXJlLCB0aGlzIGV4cGVyaW1lbnRhbCBmbGFnIGluc3RydWN0c1xuICAgICAgICAgKiB0aGUgbGlicmFyeSB0byBkaXNjYXJkIHRoZSBzb2NrZXQgaW1tZWRpYXRlbHlcbiAgICAgICAgICogKGV2ZW4gYmVmb3JlIGl0IGlzIGFjdHVhbGx5IGNsb3NlZCkuXG4gICAgICAgICAqL1xuICAgICAgICB0aGlzLmRpc2NhcmRXZWJzb2NrZXRPbkNvbW1GYWlsdXJlID0gZmFsc2U7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBBY3RpdmF0aW9uIHN0YXRlLlxuICAgICAgICAgKlxuICAgICAgICAgKiBJdCB3aWxsIHVzdWFsbHkgYmUgQUNUSVZFIG9yIElOQUNUSVZFLlxuICAgICAgICAgKiBXaGVuIGRlYWN0aXZhdGluZywgaXQgbWF5IGdvIGZyb20gQUNUSVZFIHRvIElOQUNUSVZFIHdpdGhvdXQgZW50ZXJpbmcgREVBQ1RJVkFUSU5HLlxuICAgICAgICAgKi9cbiAgICAgICAgdGhpcy5zdGF0ZSA9IEFjdGl2YXRpb25TdGF0ZS5JTkFDVElWRTtcbiAgICAgICAgLy8gTm8gb3AgY2FsbGJhY2tzXG4gICAgICAgIGNvbnN0IG5vT3AgPSAoKSA9PiB7IH07XG4gICAgICAgIHRoaXMuZGVidWcgPSBub09wO1xuICAgICAgICB0aGlzLmJlZm9yZUNvbm5lY3QgPSBub09wO1xuICAgICAgICB0aGlzLm9uQ29ubmVjdCA9IG5vT3A7XG4gICAgICAgIHRoaXMub25EaXNjb25uZWN0ID0gbm9PcDtcbiAgICAgICAgdGhpcy5vblVuaGFuZGxlZE1lc3NhZ2UgPSBub09wO1xuICAgICAgICB0aGlzLm9uVW5oYW5kbGVkUmVjZWlwdCA9IG5vT3A7XG4gICAgICAgIHRoaXMub25VbmhhbmRsZWRGcmFtZSA9IG5vT3A7XG4gICAgICAgIHRoaXMub25TdG9tcEVycm9yID0gbm9PcDtcbiAgICAgICAgdGhpcy5vbldlYlNvY2tldENsb3NlID0gbm9PcDtcbiAgICAgICAgdGhpcy5vbldlYlNvY2tldEVycm9yID0gbm9PcDtcbiAgICAgICAgdGhpcy5sb2dSYXdDb21tdW5pY2F0aW9uID0gZmFsc2U7XG4gICAgICAgIHRoaXMub25DaGFuZ2VTdGF0ZSA9IG5vT3A7XG4gICAgICAgIC8vIFRoZXNlIHBhcmFtZXRlcnMgd291bGQgdHlwaWNhbGx5IGdldCBwcm9wZXIgdmFsdWVzIGJlZm9yZSBjb25uZWN0IGlzIGNhbGxlZFxuICAgICAgICB0aGlzLmNvbm5lY3RIZWFkZXJzID0ge307XG4gICAgICAgIHRoaXMuX2Rpc2Nvbm5lY3RIZWFkZXJzID0ge307XG4gICAgICAgIC8vIEFwcGx5IGNvbmZpZ3VyYXRpb25cbiAgICAgICAgdGhpcy5jb25maWd1cmUoY29uZik7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFVwZGF0ZSBjb25maWd1cmF0aW9uLlxuICAgICAqL1xuICAgIGNvbmZpZ3VyZShjb25mKSB7XG4gICAgICAgIC8vIGJ1bGsgYXNzaWduIGFsbCBwcm9wZXJ0aWVzIHRvIHRoaXNcbiAgICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLCBjb25mKTtcbiAgICAgICAgLy8gV2FybiBvbiBpbmNvcnJlY3QgbWF4UmVjb25uZWN0RGVsYXkgc2V0dGluZ3NcbiAgICAgICAgaWYgKHRoaXMubWF4UmVjb25uZWN0RGVsYXkgPiAwICYmXG4gICAgICAgICAgICB0aGlzLm1heFJlY29ubmVjdERlbGF5IDwgdGhpcy5yZWNvbm5lY3REZWxheSkge1xuICAgICAgICAgICAgdGhpcy5kZWJ1ZyhgV2FybmluZzogbWF4UmVjb25uZWN0RGVsYXkgKCR7dGhpcy5tYXhSZWNvbm5lY3REZWxheX1tcykgaXMgbGVzcyB0aGFuIHJlY29ubmVjdERlbGF5ICgke3RoaXMucmVjb25uZWN0RGVsYXl9bXMpLiBVc2luZyByZWNvbm5lY3REZWxheSBhcyB0aGUgbWF4UmVjb25uZWN0RGVsYXkgZGVsYXkuYCk7XG4gICAgICAgICAgICB0aGlzLm1heFJlY29ubmVjdERlbGF5ID0gdGhpcy5yZWNvbm5lY3REZWxheTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBJbml0aWF0ZSB0aGUgY29ubmVjdGlvbiB3aXRoIHRoZSBicm9rZXIuXG4gICAgICogSWYgdGhlIGNvbm5lY3Rpb24gYnJlYWtzLCBhcyBwZXIgW0NsaWVudCNyZWNvbm5lY3REZWxheV17QGxpbmsgQ2xpZW50I3JlY29ubmVjdERlbGF5fSxcbiAgICAgKiBpdCB3aWxsIGtlZXAgdHJ5aW5nIHRvIHJlY29ubmVjdC4gSWYgdGhlIFtDbGllbnQjcmVjb25uZWN0VGltZU1vZGVde0BsaW5rIENsaWVudCNyZWNvbm5lY3RUaW1lTW9kZX1cbiAgICAgKiBpcyBzZXQgdG8gRVhQT05FTlRJQUwgaXQgd2lsbCBpbmNyZWFzZSB0aGUgd2FpdCB0aW1lIGV4cG9uZW50aWFsbHlcbiAgICAgKlxuICAgICAqIENhbGwgW0NsaWVudCNkZWFjdGl2YXRlXXtAbGluayBDbGllbnQjZGVhY3RpdmF0ZX0gdG8gZGlzY29ubmVjdCBhbmQgc3RvcCByZWNvbm5lY3Rpb24gYXR0ZW1wdHMuXG4gICAgICovXG4gICAgYWN0aXZhdGUoKSB7XG4gICAgICAgIGNvbnN0IF9hY3RpdmF0ZSA9ICgpID0+IHtcbiAgICAgICAgICAgIGlmICh0aGlzLmFjdGl2ZSkge1xuICAgICAgICAgICAgICAgIHRoaXMuZGVidWcoJ0FscmVhZHkgQUNUSVZFLCBpZ25vcmluZyByZXF1ZXN0IHRvIGFjdGl2YXRlJyk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5fY2hhbmdlU3RhdGUoQWN0aXZhdGlvblN0YXRlLkFDVElWRSk7XG4gICAgICAgICAgICB0aGlzLl9uZXh0UmVjb25uZWN0RGVsYXkgPSB0aGlzLnJlY29ubmVjdERlbGF5O1xuICAgICAgICAgICAgdGhpcy5fY29ubmVjdCgpO1xuICAgICAgICB9O1xuICAgICAgICAvLyBpZiBpdCBpcyBkZWFjdGl2YXRpbmcsIHdhaXQgZm9yIGl0IHRvIGNvbXBsZXRlIGJlZm9yZSBhY3RpdmF0aW5nLlxuICAgICAgICBpZiAodGhpcy5zdGF0ZSA9PT0gQWN0aXZhdGlvblN0YXRlLkRFQUNUSVZBVElORykge1xuICAgICAgICAgICAgdGhpcy5kZWJ1ZygnV2FpdGluZyBmb3IgZGVhY3RpdmF0aW9uIHRvIGZpbmlzaCBiZWZvcmUgYWN0aXZhdGluZycpO1xuICAgICAgICAgICAgdGhpcy5kZWFjdGl2YXRlKCkudGhlbigoKSA9PiB7XG4gICAgICAgICAgICAgICAgX2FjdGl2YXRlKCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIF9hY3RpdmF0ZSgpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGFzeW5jIF9jb25uZWN0KCkge1xuICAgICAgICBhd2FpdCB0aGlzLmJlZm9yZUNvbm5lY3QodGhpcyk7XG4gICAgICAgIGlmICh0aGlzLl9zdG9tcEhhbmRsZXIpIHtcbiAgICAgICAgICAgIHRoaXMuZGVidWcoJ1RoZXJlIGlzIGFscmVhZHkgYSBzdG9tcEhhbmRsZXIsIHNraXBwaW5nIHRoZSBjYWxsIHRvIGNvbm5lY3QnKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXRoaXMuYWN0aXZlKSB7XG4gICAgICAgICAgICB0aGlzLmRlYnVnKCdDbGllbnQgaGFzIGJlZW4gbWFya2VkIGluYWN0aXZlLCB3aWxsIG5vdCBhdHRlbXB0IHRvIGNvbm5lY3QnKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICAvLyBzZXR1cCBjb25uZWN0aW9uIHdhdGNoZXJcbiAgICAgICAgaWYgKHRoaXMuY29ubmVjdGlvblRpbWVvdXQgPiAwKSB7XG4gICAgICAgICAgICAvLyBjbGVhciBmaXJzdFxuICAgICAgICAgICAgaWYgKHRoaXMuX2Nvbm5lY3Rpb25XYXRjaGVyKSB7XG4gICAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuX2Nvbm5lY3Rpb25XYXRjaGVyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuX2Nvbm5lY3Rpb25XYXRjaGVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuY29ubmVjdGVkKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gQ29ubmVjdGlvbiBub3QgZXN0YWJsaXNoZWQsIGNsb3NlIHRoZSB1bmRlcmx5aW5nIHNvY2tldFxuICAgICAgICAgICAgICAgIC8vIGEgcmVjb25uZWN0aW9uIHdpbGwgYmUgYXR0ZW1wdGVkXG4gICAgICAgICAgICAgICAgdGhpcy5kZWJ1ZyhgQ29ubmVjdGlvbiBub3QgZXN0YWJsaXNoZWQgaW4gJHt0aGlzLmNvbm5lY3Rpb25UaW1lb3V0fW1zLCBjbG9zaW5nIHNvY2tldGApO1xuICAgICAgICAgICAgICAgIHRoaXMuZm9yY2VEaXNjb25uZWN0KCk7XG4gICAgICAgICAgICB9LCB0aGlzLmNvbm5lY3Rpb25UaW1lb3V0KTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmRlYnVnKCdPcGVuaW5nIFdlYiBTb2NrZXQuLi4nKTtcbiAgICAgICAgLy8gR2V0IHRoZSBhY3R1YWwgV2ViU29ja2V0IChvciBhIHNpbWlsYXIgb2JqZWN0KVxuICAgICAgICBjb25zdCB3ZWJTb2NrZXQgPSB0aGlzLl9jcmVhdGVXZWJTb2NrZXQoKTtcbiAgICAgICAgdGhpcy5fc3RvbXBIYW5kbGVyID0gbmV3IFN0b21wSGFuZGxlcih0aGlzLCB3ZWJTb2NrZXQsIHtcbiAgICAgICAgICAgIGRlYnVnOiB0aGlzLmRlYnVnLFxuICAgICAgICAgICAgc3RvbXBWZXJzaW9uczogdGhpcy5zdG9tcFZlcnNpb25zLFxuICAgICAgICAgICAgY29ubmVjdEhlYWRlcnM6IHRoaXMuY29ubmVjdEhlYWRlcnMsXG4gICAgICAgICAgICBkaXNjb25uZWN0SGVhZGVyczogdGhpcy5fZGlzY29ubmVjdEhlYWRlcnMsXG4gICAgICAgICAgICBoZWFydGJlYXRJbmNvbWluZzogdGhpcy5oZWFydGJlYXRJbmNvbWluZyxcbiAgICAgICAgICAgIGhlYXJ0YmVhdE91dGdvaW5nOiB0aGlzLmhlYXJ0YmVhdE91dGdvaW5nLFxuICAgICAgICAgICAgaGVhcnRiZWF0U3RyYXRlZ3k6IHRoaXMuaGVhcnRiZWF0U3RyYXRlZ3ksXG4gICAgICAgICAgICBzcGxpdExhcmdlRnJhbWVzOiB0aGlzLnNwbGl0TGFyZ2VGcmFtZXMsXG4gICAgICAgICAgICBtYXhXZWJTb2NrZXRDaHVua1NpemU6IHRoaXMubWF4V2ViU29ja2V0Q2h1bmtTaXplLFxuICAgICAgICAgICAgZm9yY2VCaW5hcnlXU0ZyYW1lczogdGhpcy5mb3JjZUJpbmFyeVdTRnJhbWVzLFxuICAgICAgICAgICAgbG9nUmF3Q29tbXVuaWNhdGlvbjogdGhpcy5sb2dSYXdDb21tdW5pY2F0aW9uLFxuICAgICAgICAgICAgYXBwZW5kTWlzc2luZ05VTExvbkluY29taW5nOiB0aGlzLmFwcGVuZE1pc3NpbmdOVUxMb25JbmNvbWluZyxcbiAgICAgICAgICAgIGRpc2NhcmRXZWJzb2NrZXRPbkNvbW1GYWlsdXJlOiB0aGlzLmRpc2NhcmRXZWJzb2NrZXRPbkNvbW1GYWlsdXJlLFxuICAgICAgICAgICAgb25Db25uZWN0OiBmcmFtZSA9PiB7XG4gICAgICAgICAgICAgICAgLy8gU3VjY2Vzc2Z1bGx5IGNvbm5lY3RlZCwgc3RvcCB0aGUgY29ubmVjdGlvbiB3YXRjaGVyXG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuX2Nvbm5lY3Rpb25XYXRjaGVyKSB7XG4gICAgICAgICAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aGlzLl9jb25uZWN0aW9uV2F0Y2hlcik7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX2Nvbm5lY3Rpb25XYXRjaGVyID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoIXRoaXMuYWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZGVidWcoJ1NUT01QIGdvdCBjb25uZWN0ZWQgd2hpbGUgZGVhY3RpdmF0ZSB3YXMgaXNzdWVkLCB3aWxsIGRpc2Nvbm5lY3Qgbm93Jyk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX2Rpc3Bvc2VTdG9tcEhhbmRsZXIoKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLm9uQ29ubmVjdChmcmFtZSk7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgb25EaXNjb25uZWN0OiBmcmFtZSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5vbkRpc2Nvbm5lY3QoZnJhbWUpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIG9uU3RvbXBFcnJvcjogZnJhbWUgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMub25TdG9tcEVycm9yKGZyYW1lKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBvbldlYlNvY2tldENsb3NlOiBldnQgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMuX3N0b21wSGFuZGxlciA9IHVuZGVmaW5lZDsgLy8gYSBuZXcgb25lIHdpbGwgYmUgY3JlYXRlZCBpbiBjYXNlIG9mIGEgcmVjb25uZWN0XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuc3RhdGUgPT09IEFjdGl2YXRpb25TdGF0ZS5ERUFDVElWQVRJTkcpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gTWFyayBkZWFjdGl2YXRpb24gY29tcGxldGVcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fY2hhbmdlU3RhdGUoQWN0aXZhdGlvblN0YXRlLklOQUNUSVZFKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gVGhlIGNhbGxiYWNrIGlzIGNhbGxlZCBiZWZvcmUgYXR0ZW1wdGluZyB0byByZWNvbm5lY3QsIHRoaXMgd291bGQgYWxsb3cgdGhlIGNsaWVudFxuICAgICAgICAgICAgICAgIC8vIHRvIGJlIGBkZWFjdGl2YXRlZGAgaW4gdGhlIGNhbGxiYWNrLlxuICAgICAgICAgICAgICAgIHRoaXMub25XZWJTb2NrZXRDbG9zZShldnQpO1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmFjdGl2ZSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLl9zY2hlZHVsZV9yZWNvbm5lY3QoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgb25XZWJTb2NrZXRFcnJvcjogZXZ0ID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLm9uV2ViU29ja2V0RXJyb3IoZXZ0KTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBvblVuaGFuZGxlZE1lc3NhZ2U6IG1lc3NhZ2UgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMub25VbmhhbmRsZWRNZXNzYWdlKG1lc3NhZ2UpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIG9uVW5oYW5kbGVkUmVjZWlwdDogZnJhbWUgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMub25VbmhhbmRsZWRSZWNlaXB0KGZyYW1lKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBvblVuaGFuZGxlZEZyYW1lOiBmcmFtZSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5vblVuaGFuZGxlZEZyYW1lKGZyYW1lKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLl9zdG9tcEhhbmRsZXIuc3RhcnQoKTtcbiAgICB9XG4gICAgX2NyZWF0ZVdlYlNvY2tldCgpIHtcbiAgICAgICAgbGV0IHdlYlNvY2tldDtcbiAgICAgICAgaWYgKHRoaXMud2ViU29ja2V0RmFjdG9yeSkge1xuICAgICAgICAgICAgd2ViU29ja2V0ID0gdGhpcy53ZWJTb2NrZXRGYWN0b3J5KCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodGhpcy5icm9rZXJVUkwpIHtcbiAgICAgICAgICAgIHdlYlNvY2tldCA9IG5ldyBXZWJTb2NrZXQodGhpcy5icm9rZXJVUkwsIHRoaXMuc3RvbXBWZXJzaW9ucy5wcm90b2NvbFZlcnNpb25zKCkpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFaXRoZXIgYnJva2VyVVJMIG9yIHdlYlNvY2tldEZhY3RvcnkgbXVzdCBiZSBwcm92aWRlZCcpO1xuICAgICAgICB9XG4gICAgICAgIHdlYlNvY2tldC5iaW5hcnlUeXBlID0gJ2FycmF5YnVmZmVyJztcbiAgICAgICAgcmV0dXJuIHdlYlNvY2tldDtcbiAgICB9XG4gICAgX3NjaGVkdWxlX3JlY29ubmVjdCgpIHtcbiAgICAgICAgaWYgKHRoaXMuX25leHRSZWNvbm5lY3REZWxheSA+IDApIHtcbiAgICAgICAgICAgIHRoaXMuZGVidWcoYFNUT01QOiBzY2hlZHVsaW5nIHJlY29ubmVjdGlvbiBpbiAke3RoaXMuX25leHRSZWNvbm5lY3REZWxheX1tc2ApO1xuICAgICAgICAgICAgdGhpcy5fcmVjb25uZWN0b3IgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5yZWNvbm5lY3RUaW1lTW9kZSA9PT0gUmVjb25uZWN0aW9uVGltZU1vZGUuRVhQT05FTlRJQUwpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fbmV4dFJlY29ubmVjdERlbGF5ID0gdGhpcy5fbmV4dFJlY29ubmVjdERlbGF5ICogMjtcbiAgICAgICAgICAgICAgICAgICAgLy8gVHJ1bmNhdGVkIGV4cG9uZW50aWFsIGJhY2tvZmYgd2l0aCBhIHNldCBsaW1pdCB1bmxlc3MgZGlzYWJsZWRcbiAgICAgICAgICAgICAgICAgICAgaWYgKHRoaXMubWF4UmVjb25uZWN0RGVsYXkgIT09IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX25leHRSZWNvbm5lY3REZWxheSA9IE1hdGgubWluKHRoaXMuX25leHRSZWNvbm5lY3REZWxheSwgdGhpcy5tYXhSZWNvbm5lY3REZWxheSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5fY29ubmVjdCgpO1xuICAgICAgICAgICAgfSwgdGhpcy5fbmV4dFJlY29ubmVjdERlbGF5KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBEaXNjb25uZWN0IGlmIGNvbm5lY3RlZCBhbmQgc3RvcCBhdXRvIHJlY29ubmVjdCBsb29wLlxuICAgICAqIEFwcHJvcHJpYXRlIGNhbGxiYWNrcyB3aWxsIGJlIGludm9rZWQgaWYgdGhlcmUgaXMgYW4gdW5kZXJseWluZyBTVE9NUCBjb25uZWN0aW9uLlxuICAgICAqXG4gICAgICogVGhpcyBjYWxsIGlzIGFzeW5jLiBJdCB3aWxsIHJlc29sdmUgaW1tZWRpYXRlbHkgaWYgdGhlcmUgaXMgbm8gdW5kZXJseWluZyBhY3RpdmUgd2Vic29ja2V0LFxuICAgICAqIG90aGVyd2lzZSwgaXQgd2lsbCByZXNvbHZlIGFmdGVyIHRoZSB1bmRlcmx5aW5nIHdlYnNvY2tldCBpcyBwcm9wZXJseSBkaXNwb3NlZCBvZi5cbiAgICAgKlxuICAgICAqIEl0IGlzIG5vdCBhbiBlcnJvciB0byBpbnZva2UgdGhpcyBtZXRob2QgbW9yZSB0aGFuIG9uY2UuXG4gICAgICogRWFjaCBvZiB0aG9zZSB3b3VsZCByZXNvbHZlIG9uIGNvbXBsZXRpb24gb2YgZGVhY3RpdmF0aW9uLlxuICAgICAqXG4gICAgICogVG8gcmVhY3RpdmF0ZSwgeW91IGNhbiBjYWxsIFtDbGllbnQjYWN0aXZhdGVde0BsaW5rIENsaWVudCNhY3RpdmF0ZX0uXG4gICAgICpcbiAgICAgKiBFeHBlcmltZW50YWw6IHBhc3MgYGZvcmNlOiB0cnVlYCB0byBpbW1lZGlhdGVseSBkaXNjYXJkIHRoZSB1bmRlcmx5aW5nIGNvbm5lY3Rpb24uXG4gICAgICogVGhpcyBtb2RlIHdpbGwgc2tpcCBib3RoIHRoZSBTVE9NUCBhbmQgdGhlIFdlYnNvY2tldCBzaHV0ZG93biBzZXF1ZW5jZXMuXG4gICAgICogSW4gc29tZSBjYXNlcywgYnJvd3NlcnMgdGFrZSBhIGxvbmcgdGltZSBpbiB0aGUgV2Vic29ja2V0IHNodXRkb3duXG4gICAgICogaWYgdGhlIHVuZGVybHlpbmcgY29ubmVjdGlvbiBoYWQgZ29uZSBzdGFsZS5cbiAgICAgKiBVc2luZyB0aGlzIG1vZGUgY2FuIHNwZWVkIHVwLlxuICAgICAqIFdoZW4gdGhpcyBtb2RlIGlzIHVzZWQsIHRoZSBhY3R1YWwgV2Vic29ja2V0IG1heSBsaW5nZXIgZm9yIGEgd2hpbGVcbiAgICAgKiBhbmQgdGhlIGJyb2tlciBtYXkgbm90IHJlYWxpemUgdGhhdCB0aGUgY29ubmVjdGlvbiBpcyBubyBsb25nZXIgaW4gdXNlLlxuICAgICAqXG4gICAgICogSXQgaXMgcG9zc2libGUgdG8gaW52b2tlIHRoaXMgbWV0aG9kIGluaXRpYWxseSB3aXRob3V0IHRoZSBgZm9yY2VgIG9wdGlvblxuICAgICAqIGFuZCBzdWJzZXF1ZW50bHksIHNheSBhZnRlciBhIHdhaXQsIHdpdGggdGhlIGBmb3JjZWAgb3B0aW9uLlxuICAgICAqL1xuICAgIGFzeW5jIGRlYWN0aXZhdGUob3B0aW9ucyA9IHt9KSB7XG4gICAgICAgIGNvbnN0IGZvcmNlID0gb3B0aW9ucy5mb3JjZSB8fCBmYWxzZTtcbiAgICAgICAgY29uc3QgbmVlZFRvRGlzcG9zZSA9IHRoaXMuYWN0aXZlO1xuICAgICAgICBsZXQgcmV0UHJvbWlzZTtcbiAgICAgICAgaWYgKHRoaXMuc3RhdGUgPT09IEFjdGl2YXRpb25TdGF0ZS5JTkFDVElWRSkge1xuICAgICAgICAgICAgdGhpcy5kZWJ1ZyhgQWxyZWFkeSBJTkFDVElWRSwgbm90aGluZyBtb3JlIHRvIGRvYCk7XG4gICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKCk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5fY2hhbmdlU3RhdGUoQWN0aXZhdGlvblN0YXRlLkRFQUNUSVZBVElORyk7XG4gICAgICAgIC8vIFJlc2V0IHJlY29ubmVjdGlvbiB0aW1lciBqdXN0IHRvIGJlIHNhZmVcbiAgICAgICAgdGhpcy5fbmV4dFJlY29ubmVjdERlbGF5ID0gMDtcbiAgICAgICAgLy8gQ2xlYXIgaWYgYSByZWNvbm5lY3Rpb24gd2FzIHNjaGVkdWxlZFxuICAgICAgICBpZiAodGhpcy5fcmVjb25uZWN0b3IpIHtcbiAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aGlzLl9yZWNvbm5lY3Rvcik7XG4gICAgICAgICAgICB0aGlzLl9yZWNvbm5lY3RvciA9IHVuZGVmaW5lZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5fc3RvbXBIYW5kbGVyICYmXG4gICAgICAgICAgICAvLyBAdHMtaWdub3JlIC0gaWYgdGhlcmUgaXMgYSBfc3RvbXBIYW5kbGVyLCB0aGVyZSBpcyB0aGUgd2ViU29ja2V0XG4gICAgICAgICAgICB0aGlzLndlYlNvY2tldC5yZWFkeVN0YXRlICE9PSBTdG9tcFNvY2tldFN0YXRlLkNMT1NFRCkge1xuICAgICAgICAgICAgY29uc3Qgb3JpZ09uV2ViU29ja2V0Q2xvc2UgPSB0aGlzLl9zdG9tcEhhbmRsZXIub25XZWJTb2NrZXRDbG9zZTtcbiAgICAgICAgICAgIC8vIHdlIG5lZWQgdG8gd2FpdCBmb3IgdGhlIHVuZGVybHlpbmcgd2Vic29ja2V0IHRvIGNsb3NlXG4gICAgICAgICAgICByZXRQcm9taXNlID0gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgIC8vIEB0cy1pZ25vcmUgLSB0aGVyZSBpcyBhIF9zdG9tcEhhbmRsZXJcbiAgICAgICAgICAgICAgICB0aGlzLl9zdG9tcEhhbmRsZXIub25XZWJTb2NrZXRDbG9zZSA9IGV2dCA9PiB7XG4gICAgICAgICAgICAgICAgICAgIG9yaWdPbldlYlNvY2tldENsb3NlKGV2dCk7XG4gICAgICAgICAgICAgICAgICAgIHJlc29sdmUoKTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAvLyBpbmRpY2F0ZSB0aGF0IGF1dG8gcmVjb25uZWN0IGxvb3Agc2hvdWxkIHRlcm1pbmF0ZVxuICAgICAgICAgICAgdGhpcy5fY2hhbmdlU3RhdGUoQWN0aXZhdGlvblN0YXRlLklOQUNUSVZFKTtcbiAgICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZm9yY2UpIHtcbiAgICAgICAgICAgIHRoaXMuX3N0b21wSGFuZGxlcj8uZGlzY2FyZFdlYnNvY2tldCgpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKG5lZWRUb0Rpc3Bvc2UpIHtcbiAgICAgICAgICAgIHRoaXMuX2Rpc3Bvc2VTdG9tcEhhbmRsZXIoKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmV0UHJvbWlzZTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRm9yY2UgZGlzY29ubmVjdCBpZiB0aGVyZSBpcyBhbiBhY3RpdmUgY29ubmVjdGlvbiBieSBkaXJlY3RseSBjbG9zaW5nIHRoZSB1bmRlcmx5aW5nIFdlYlNvY2tldC5cbiAgICAgKiBUaGlzIGlzIGRpZmZlcmVudCBmcm9tIGEgbm9ybWFsIGRpc2Nvbm5lY3Qgd2hlcmUgYSBESVNDT05ORUNUIHNlcXVlbmNlIGlzIGNhcnJpZWQgb3V0IHdpdGggdGhlIGJyb2tlci5cbiAgICAgKiBBZnRlciBmb3JjaW5nIGRpc2Nvbm5lY3QsIGF1dG9tYXRpYyByZWNvbm5lY3Qgd2lsbCBiZSBhdHRlbXB0ZWQuXG4gICAgICogVG8gc3RvcCBmdXJ0aGVyIHJlY29ubmVjdHMgY2FsbCBbQ2xpZW50I2RlYWN0aXZhdGVde0BsaW5rIENsaWVudCNkZWFjdGl2YXRlfSBhcyB3ZWxsLlxuICAgICAqL1xuICAgIGZvcmNlRGlzY29ubmVjdCgpIHtcbiAgICAgICAgaWYgKHRoaXMuX3N0b21wSGFuZGxlcikge1xuICAgICAgICAgICAgdGhpcy5fc3RvbXBIYW5kbGVyLmZvcmNlRGlzY29ubmVjdCgpO1xuICAgICAgICB9XG4gICAgfVxuICAgIF9kaXNwb3NlU3RvbXBIYW5kbGVyKCkge1xuICAgICAgICAvLyBEaXNwb3NlIFNUT01QIEhhbmRsZXJcbiAgICAgICAgaWYgKHRoaXMuX3N0b21wSGFuZGxlcikge1xuICAgICAgICAgICAgdGhpcy5fc3RvbXBIYW5kbGVyLmRpc3Bvc2UoKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZW5kIGEgbWVzc2FnZSB0byBhIG5hbWVkIGRlc3RpbmF0aW9uLiBSZWZlciB0byB5b3VyIFNUT01QIGJyb2tlciBkb2N1bWVudGF0aW9uIGZvciB0eXBlc1xuICAgICAqIGFuZCBuYW1pbmcgb2YgZGVzdGluYXRpb25zLlxuICAgICAqXG4gICAgICogU1RPTVAgcHJvdG9jb2wgc3BlY2lmaWVzIGFuZCBzdWdnZXN0cyBzb21lIGhlYWRlcnMgYW5kIGFsc28gYWxsb3dzIGJyb2tlci1zcGVjaWZpYyBoZWFkZXJzLlxuICAgICAqXG4gICAgICogYGJvZHlgIG11c3QgYmUgU3RyaW5nLlxuICAgICAqIFlvdSB3aWxsIG5lZWQgdG8gY292ZXJ0IHRoZSBwYXlsb2FkIHRvIHN0cmluZyBpbiBjYXNlIGl0IGlzIG5vdCBzdHJpbmcgKGUuZy4gSlNPTikuXG4gICAgICpcbiAgICAgKiBUbyBzZW5kIGEgYmluYXJ5IG1lc3NhZ2UgYm9keSwgdXNlIGBiaW5hcnlCb2R5YCBwYXJhbWV0ZXIuIEl0IHNob3VsZCBiZSBhXG4gICAgICogW1VpbnQ4QXJyYXldKGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0phdmFTY3JpcHQvUmVmZXJlbmNlL0dsb2JhbF9PYmplY3RzL1VpbnQ4QXJyYXkpLlxuICAgICAqIFNvbWV0aW1lcyBicm9rZXJzIG1heSBub3Qgc3VwcG9ydCBiaW5hcnkgZnJhbWVzIG91dCBvZiB0aGUgYm94LlxuICAgICAqIFBsZWFzZSBjaGVjayB5b3VyIGJyb2tlciBkb2N1bWVudGF0aW9uLlxuICAgICAqXG4gICAgICogYGNvbnRlbnQtbGVuZ3RoYCBoZWFkZXIgaXMgYXV0b21hdGljYWxseSBhZGRlZCB0byB0aGUgU1RPTVAgRnJhbWUgc2VudCB0byB0aGUgYnJva2VyLlxuICAgICAqIFNldCBgc2tpcENvbnRlbnRMZW5ndGhIZWFkZXJgIHRvIGluZGljYXRlIHRoYXQgYGNvbnRlbnQtbGVuZ3RoYCBoZWFkZXIgc2hvdWxkIG5vdCBiZSBhZGRlZC5cbiAgICAgKiBGb3IgYmluYXJ5IG1lc3NhZ2VzLCBgY29udGVudC1sZW5ndGhgIGhlYWRlciBpcyBhbHdheXMgYWRkZWQuXG4gICAgICpcbiAgICAgKiBDYXV0aW9uOiBUaGUgYnJva2VyIHdpbGwsIG1vc3QgbGlrZWx5LCByZXBvcnQgYW4gZXJyb3IgYW5kIGRpc2Nvbm5lY3RcbiAgICAgKiBpZiB0aGUgbWVzc2FnZSBib2R5IGhhcyBOVUxMIG9jdGV0KHMpIGFuZCBgY29udGVudC1sZW5ndGhgIGhlYWRlciBpcyBtaXNzaW5nLlxuICAgICAqXG4gICAgICogYGBgamF2YXNjcmlwdFxuICAgICAqICAgICAgICBjbGllbnQucHVibGlzaCh7ZGVzdGluYXRpb246IFwiL3F1ZXVlL3Rlc3RcIiwgaGVhZGVyczoge3ByaW9yaXR5OiA5fSwgYm9keTogXCJIZWxsbywgU1RPTVBcIn0pO1xuICAgICAqXG4gICAgICogICAgICAgIC8vIE9ubHkgZGVzdGluYXRpb24gaXMgbWFuZGF0b3J5IHBhcmFtZXRlclxuICAgICAqICAgICAgICBjbGllbnQucHVibGlzaCh7ZGVzdGluYXRpb246IFwiL3F1ZXVlL3Rlc3RcIiwgYm9keTogXCJIZWxsbywgU1RPTVBcIn0pO1xuICAgICAqXG4gICAgICogICAgICAgIC8vIFNraXAgY29udGVudC1sZW5ndGggaGVhZGVyIGluIHRoZSBmcmFtZSB0byB0aGUgYnJva2VyXG4gICAgICogICAgICAgIGNsaWVudC5wdWJsaXNoKHtcIi9xdWV1ZS90ZXN0XCIsIGJvZHk6IFwiSGVsbG8sIFNUT01QXCIsIHNraXBDb250ZW50TGVuZ3RoSGVhZGVyOiB0cnVlfSk7XG4gICAgICpcbiAgICAgKiAgICAgICAgdmFyIGJpbmFyeURhdGEgPSBnZW5lcmF0ZUJpbmFyeURhdGEoKTsgLy8gVGhpcyBuZWVkIHRvIGJlIG9mIHR5cGUgVWludDhBcnJheVxuICAgICAqICAgICAgICAvLyBzZXR0aW5nIGNvbnRlbnQtdHlwZSBoZWFkZXIgaXMgbm90IG1hbmRhdG9yeSwgaG93ZXZlciBhIGdvb2QgcHJhY3RpY2VcbiAgICAgKiAgICAgICAgY2xpZW50LnB1Ymxpc2goe2Rlc3RpbmF0aW9uOiAnL3RvcGljL3NwZWNpYWwnLCBiaW5hcnlCb2R5OiBiaW5hcnlEYXRhLFxuICAgICAqICAgICAgICAgICAgICAgICAgICAgICAgIGhlYWRlcnM6IHsnY29udGVudC10eXBlJzogJ2FwcGxpY2F0aW9uL29jdGV0LXN0cmVhbSd9fSk7XG4gICAgICogYGBgXG4gICAgICovXG4gICAgcHVibGlzaChwYXJhbXMpIHtcbiAgICAgICAgdGhpcy5fY2hlY2tDb25uZWN0aW9uKCk7XG4gICAgICAgIC8vIEB0cy1pZ25vcmUgLSB3ZSBhbHJlYWR5IGNoZWNrZWQgdGhhdCB0aGVyZSBpcyBhIF9zdG9tcEhhbmRsZXIsIGFuZCBpdCBpcyBjb25uZWN0ZWRcbiAgICAgICAgdGhpcy5fc3RvbXBIYW5kbGVyLnB1Ymxpc2gocGFyYW1zKTtcbiAgICB9XG4gICAgX2NoZWNrQ29ubmVjdGlvbigpIHtcbiAgICAgICAgaWYgKCF0aGlzLmNvbm5lY3RlZCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignVGhlcmUgaXMgbm8gdW5kZXJseWluZyBTVE9NUCBjb25uZWN0aW9uJyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogU1RPTVAgYnJva2VycyBtYXkgY2Fycnkgb3V0IG9wZXJhdGlvbiBhc3luY2hyb25vdXNseSBhbmQgYWxsb3cgcmVxdWVzdGluZyBmb3IgYWNrbm93bGVkZ2VtZW50LlxuICAgICAqIFRvIHJlcXVlc3QgYW4gYWNrbm93bGVkZ2VtZW50LCBhIGByZWNlaXB0YCBoZWFkZXIgbmVlZHMgdG8gYmUgc2VudCB3aXRoIHRoZSBhY3R1YWwgcmVxdWVzdC5cbiAgICAgKiBUaGUgdmFsdWUgKHNheSByZWNlaXB0LWlkKSBmb3IgdGhpcyBoZWFkZXIgbmVlZHMgdG8gYmUgdW5pcXVlIGZvciBlYWNoIHVzZS5cbiAgICAgKiBUeXBpY2FsbHksIGEgc2VxdWVuY2UsIGEgVVVJRCwgYSByYW5kb20gbnVtYmVyIG9yIGEgY29tYmluYXRpb24gbWF5IGJlIHVzZWQuXG4gICAgICpcbiAgICAgKiBBIGNvbXBsYWludCBicm9rZXIgd2lsbCBzZW5kIGEgUkVDRUlQVCBmcmFtZSB3aGVuIGFuIG9wZXJhdGlvbiBoYXMgYWN0dWFsbHkgYmVlbiBjb21wbGV0ZWQuXG4gICAgICogVGhlIG9wZXJhdGlvbiBuZWVkcyB0byBiZSBtYXRjaGVkIGJhc2VkIG9uIHRoZSB2YWx1ZSBvZiB0aGUgcmVjZWlwdC1pZC5cbiAgICAgKlxuICAgICAqIFRoaXMgbWV0aG9kIGFsbG93cyB3YXRjaGluZyBmb3IgYSByZWNlaXB0IGFuZCBpbnZva2luZyB0aGUgY2FsbGJhY2tcbiAgICAgKiAgd2hlbiB0aGUgY29ycmVzcG9uZGluZyByZWNlaXB0IGhhcyBiZWVuIHJlY2VpdmVkLlxuICAgICAqXG4gICAgICogVGhlIGFjdHVhbCB7QGxpbmsgSUZyYW1lfSB3aWxsIGJlIHBhc3NlZCBhcyBwYXJhbWV0ZXIgdG8gdGhlIGNhbGxiYWNrLlxuICAgICAqXG4gICAgICogRXhhbXBsZTpcbiAgICAgKiBgYGBqYXZhc2NyaXB0XG4gICAgICogICAgICAgIC8vIFN1YnNjcmliaW5nIHdpdGggYWNrbm93bGVkZ2VtZW50XG4gICAgICogICAgICAgIGxldCByZWNlaXB0SWQgPSByYW5kb21UZXh0KCk7XG4gICAgICpcbiAgICAgKiAgICAgICAgY2xpZW50LndhdGNoRm9yUmVjZWlwdChyZWNlaXB0SWQsIGZ1bmN0aW9uKCkge1xuICAgICAqICAgICAgICAgIC8vIFdpbGwgYmUgY2FsbGVkIGFmdGVyIHNlcnZlciBhY2tub3dsZWRnZXNcbiAgICAgKiAgICAgICAgfSk7XG4gICAgICpcbiAgICAgKiAgICAgICAgY2xpZW50LnN1YnNjcmliZShURVNULmRlc3RpbmF0aW9uLCBvbk1lc3NhZ2UsIHtyZWNlaXB0OiByZWNlaXB0SWR9KTtcbiAgICAgKlxuICAgICAqXG4gICAgICogICAgICAgIC8vIFB1Ymxpc2hpbmcgd2l0aCBhY2tub3dsZWRnZW1lbnRcbiAgICAgKiAgICAgICAgcmVjZWlwdElkID0gcmFuZG9tVGV4dCgpO1xuICAgICAqXG4gICAgICogICAgICAgIGNsaWVudC53YXRjaEZvclJlY2VpcHQocmVjZWlwdElkLCBmdW5jdGlvbigpIHtcbiAgICAgKiAgICAgICAgICAvLyBXaWxsIGJlIGNhbGxlZCBhZnRlciBzZXJ2ZXIgYWNrbm93bGVkZ2VzXG4gICAgICogICAgICAgIH0pO1xuICAgICAqICAgICAgICBjbGllbnQucHVibGlzaCh7ZGVzdGluYXRpb246IFRFU1QuZGVzdGluYXRpb24sIGhlYWRlcnM6IHtyZWNlaXB0OiByZWNlaXB0SWR9LCBib2R5OiBtc2d9KTtcbiAgICAgKiBgYGBcbiAgICAgKi9cbiAgICB3YXRjaEZvclJlY2VpcHQocmVjZWlwdElkLCBjYWxsYmFjaykge1xuICAgICAgICB0aGlzLl9jaGVja0Nvbm5lY3Rpb24oKTtcbiAgICAgICAgLy8gQHRzLWlnbm9yZSAtIHdlIGFscmVhZHkgY2hlY2tlZCB0aGF0IHRoZXJlIGlzIGEgX3N0b21wSGFuZGxlciwgYW5kIGl0IGlzIGNvbm5lY3RlZFxuICAgICAgICB0aGlzLl9zdG9tcEhhbmRsZXIud2F0Y2hGb3JSZWNlaXB0KHJlY2VpcHRJZCwgY2FsbGJhY2spO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTdWJzY3JpYmUgdG8gYSBTVE9NUCBCcm9rZXIgbG9jYXRpb24uIFRoZSBjYWxsYmFjayB3aWxsIGJlIGludm9rZWQgZm9yIGVhY2hcbiAgICAgKiByZWNlaXZlZCBtZXNzYWdlIHdpdGggdGhlIHtAbGluayBJTWVzc2FnZX0gYXMgYXJndW1lbnQuXG4gICAgICpcbiAgICAgKiBOb3RlOiBUaGUgbGlicmFyeSB3aWxsIGdlbmVyYXRlIGEgdW5pcXVlIElEIGlmIHRoZXJlIGlzIG5vbmUgcHJvdmlkZWQgaW4gdGhlIGhlYWRlcnMuXG4gICAgICogICAgICAgVG8gdXNlIHlvdXIgb3duIElELCBwYXNzIGl0IHVzaW5nIHRoZSBgaGVhZGVyc2AgYXJndW1lbnQuXG4gICAgICpcbiAgICAgKiBgYGBqYXZhc2NyaXB0XG4gICAgICogICAgICAgIGNhbGxiYWNrID0gZnVuY3Rpb24obWVzc2FnZSkge1xuICAgICAqICAgICAgICAvLyBjYWxsZWQgd2hlbiB0aGUgY2xpZW50IHJlY2VpdmVzIGEgU1RPTVAgbWVzc2FnZSBmcm9tIHRoZSBzZXJ2ZXJcbiAgICAgKiAgICAgICAgICBpZiAobWVzc2FnZS5ib2R5KSB7XG4gICAgICogICAgICAgICAgICBhbGVydChcImdvdCBtZXNzYWdlIHdpdGggYm9keSBcIiArIG1lc3NhZ2UuYm9keSlcbiAgICAgKiAgICAgICAgICB9IGVsc2Uge1xuICAgICAqICAgICAgICAgICAgYWxlcnQoXCJnb3QgZW1wdHkgbWVzc2FnZVwiKTtcbiAgICAgKiAgICAgICAgICB9XG4gICAgICogICAgICAgIH0pO1xuICAgICAqXG4gICAgICogICAgICAgIHZhciBzdWJzY3JpcHRpb24gPSBjbGllbnQuc3Vic2NyaWJlKFwiL3F1ZXVlL3Rlc3RcIiwgY2FsbGJhY2spO1xuICAgICAqXG4gICAgICogICAgICAgIC8vIEV4cGxpY2l0IHN1YnNjcmlwdGlvbiBpZFxuICAgICAqICAgICAgICB2YXIgbXlTdWJJZCA9ICdteS1zdWJzY3JpcHRpb24taWQtMDAxJztcbiAgICAgKiAgICAgICAgdmFyIHN1YnNjcmlwdGlvbiA9IGNsaWVudC5zdWJzY3JpYmUoZGVzdGluYXRpb24sIGNhbGxiYWNrLCB7IGlkOiBteVN1YklkIH0pO1xuICAgICAqIGBgYFxuICAgICAqL1xuICAgIHN1YnNjcmliZShkZXN0aW5hdGlvbiwgY2FsbGJhY2ssIGhlYWRlcnMgPSB7fSkge1xuICAgICAgICB0aGlzLl9jaGVja0Nvbm5lY3Rpb24oKTtcbiAgICAgICAgLy8gQHRzLWlnbm9yZSAtIHdlIGFscmVhZHkgY2hlY2tlZCB0aGF0IHRoZXJlIGlzIGEgX3N0b21wSGFuZGxlciwgYW5kIGl0IGlzIGNvbm5lY3RlZFxuICAgICAgICByZXR1cm4gdGhpcy5fc3RvbXBIYW5kbGVyLnN1YnNjcmliZShkZXN0aW5hdGlvbiwgY2FsbGJhY2ssIGhlYWRlcnMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBJdCBpcyBwcmVmZXJhYmxlIHRvIHVuc3Vic2NyaWJlIGZyb20gYSBzdWJzY3JpcHRpb24gYnkgY2FsbGluZ1xuICAgICAqIGB1bnN1YnNjcmliZSgpYCBkaXJlY3RseSBvbiB7QGxpbmsgU3RvbXBTdWJzY3JpcHRpb259IHJldHVybmVkIGJ5IGBjbGllbnQuc3Vic2NyaWJlKClgOlxuICAgICAqXG4gICAgICogYGBgamF2YXNjcmlwdFxuICAgICAqICAgICAgICB2YXIgc3Vic2NyaXB0aW9uID0gY2xpZW50LnN1YnNjcmliZShkZXN0aW5hdGlvbiwgb25tZXNzYWdlKTtcbiAgICAgKiAgICAgICAgLy8gLi4uXG4gICAgICogICAgICAgIHN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpO1xuICAgICAqIGBgYFxuICAgICAqXG4gICAgICogU2VlOiBodHRwczovL3N0b21wLmdpdGh1Yi5jb20vc3RvbXAtc3BlY2lmaWNhdGlvbi0xLjIuaHRtbCNVTlNVQlNDUklCRSBVTlNVQlNDUklCRSBGcmFtZVxuICAgICAqL1xuICAgIHVuc3Vic2NyaWJlKGlkLCBoZWFkZXJzID0ge30pIHtcbiAgICAgICAgdGhpcy5fY2hlY2tDb25uZWN0aW9uKCk7XG4gICAgICAgIC8vIEB0cy1pZ25vcmUgLSB3ZSBhbHJlYWR5IGNoZWNrZWQgdGhhdCB0aGVyZSBpcyBhIF9zdG9tcEhhbmRsZXIsIGFuZCBpdCBpcyBjb25uZWN0ZWRcbiAgICAgICAgdGhpcy5fc3RvbXBIYW5kbGVyLnVuc3Vic2NyaWJlKGlkLCBoZWFkZXJzKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU3RhcnQgYSB0cmFuc2FjdGlvbiwgdGhlIHJldHVybmVkIHtAbGluayBJVHJhbnNhY3Rpb259IGhhcyBtZXRob2RzIC0gW2NvbW1pdF17QGxpbmsgSVRyYW5zYWN0aW9uI2NvbW1pdH1cbiAgICAgKiBhbmQgW2Fib3J0XXtAbGluayBJVHJhbnNhY3Rpb24jYWJvcnR9LlxuICAgICAqXG4gICAgICogYHRyYW5zYWN0aW9uSWRgIGlzIG9wdGlvbmFsLCBpZiBub3QgcGFzc2VkIHRoZSBsaWJyYXJ5IHdpbGwgZ2VuZXJhdGUgaXQgaW50ZXJuYWxseS5cbiAgICAgKi9cbiAgICBiZWdpbih0cmFuc2FjdGlvbklkKSB7XG4gICAgICAgIHRoaXMuX2NoZWNrQ29ubmVjdGlvbigpO1xuICAgICAgICAvLyBAdHMtaWdub3JlIC0gd2UgYWxyZWFkeSBjaGVja2VkIHRoYXQgdGhlcmUgaXMgYSBfc3RvbXBIYW5kbGVyLCBhbmQgaXQgaXMgY29ubmVjdGVkXG4gICAgICAgIHJldHVybiB0aGlzLl9zdG9tcEhhbmRsZXIuYmVnaW4odHJhbnNhY3Rpb25JZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENvbW1pdCBhIHRyYW5zYWN0aW9uLlxuICAgICAqXG4gICAgICogSXQgaXMgcHJlZmVyYWJsZSB0byBjb21taXQgYSB0cmFuc2FjdGlvbiBieSBjYWxsaW5nIFtjb21taXRde0BsaW5rIElUcmFuc2FjdGlvbiNjb21taXR9IGRpcmVjdGx5IG9uXG4gICAgICoge0BsaW5rIElUcmFuc2FjdGlvbn0gcmV0dXJuZWQgYnkgW2NsaWVudC5iZWdpbl17QGxpbmsgQ2xpZW50I2JlZ2lufS5cbiAgICAgKlxuICAgICAqIGBgYGphdmFzY3JpcHRcbiAgICAgKiAgICAgICAgdmFyIHR4ID0gY2xpZW50LmJlZ2luKHR4SWQpO1xuICAgICAqICAgICAgICAvLy4uLlxuICAgICAqICAgICAgICB0eC5jb21taXQoKTtcbiAgICAgKiBgYGBcbiAgICAgKi9cbiAgICBjb21taXQodHJhbnNhY3Rpb25JZCkge1xuICAgICAgICB0aGlzLl9jaGVja0Nvbm5lY3Rpb24oKTtcbiAgICAgICAgLy8gQHRzLWlnbm9yZSAtIHdlIGFscmVhZHkgY2hlY2tlZCB0aGF0IHRoZXJlIGlzIGEgX3N0b21wSGFuZGxlciwgYW5kIGl0IGlzIGNvbm5lY3RlZFxuICAgICAgICB0aGlzLl9zdG9tcEhhbmRsZXIuY29tbWl0KHRyYW5zYWN0aW9uSWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBBYm9ydCBhIHRyYW5zYWN0aW9uLlxuICAgICAqIEl0IGlzIHByZWZlcmFibGUgdG8gYWJvcnQgYSB0cmFuc2FjdGlvbiBieSBjYWxsaW5nIFthYm9ydF17QGxpbmsgSVRyYW5zYWN0aW9uI2Fib3J0fSBkaXJlY3RseSBvblxuICAgICAqIHtAbGluayBJVHJhbnNhY3Rpb259IHJldHVybmVkIGJ5IFtjbGllbnQuYmVnaW5de0BsaW5rIENsaWVudCNiZWdpbn0uXG4gICAgICpcbiAgICAgKiBgYGBqYXZhc2NyaXB0XG4gICAgICogICAgICAgIHZhciB0eCA9IGNsaWVudC5iZWdpbih0eElkKTtcbiAgICAgKiAgICAgICAgLy8uLi5cbiAgICAgKiAgICAgICAgdHguYWJvcnQoKTtcbiAgICAgKiBgYGBcbiAgICAgKi9cbiAgICBhYm9ydCh0cmFuc2FjdGlvbklkKSB7XG4gICAgICAgIHRoaXMuX2NoZWNrQ29ubmVjdGlvbigpO1xuICAgICAgICAvLyBAdHMtaWdub3JlIC0gd2UgYWxyZWFkeSBjaGVja2VkIHRoYXQgdGhlcmUgaXMgYSBfc3RvbXBIYW5kbGVyLCBhbmQgaXQgaXMgY29ubmVjdGVkXG4gICAgICAgIHRoaXMuX3N0b21wSGFuZGxlci5hYm9ydCh0cmFuc2FjdGlvbklkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQUNLIGEgbWVzc2FnZS4gSXQgaXMgcHJlZmVyYWJsZSB0byBhY2tub3dsZWRnZSBhIG1lc3NhZ2UgYnkgY2FsbGluZyBbYWNrXXtAbGluayBJTWVzc2FnZSNhY2t9IGRpcmVjdGx5XG4gICAgICogb24gdGhlIHtAbGluayBJTWVzc2FnZX0gaGFuZGxlZCBieSBhIHN1YnNjcmlwdGlvbiBjYWxsYmFjazpcbiAgICAgKlxuICAgICAqIGBgYGphdmFzY3JpcHRcbiAgICAgKiAgICAgICAgdmFyIGNhbGxiYWNrID0gZnVuY3Rpb24gKG1lc3NhZ2UpIHtcbiAgICAgKiAgICAgICAgICAvLyBwcm9jZXNzIHRoZSBtZXNzYWdlXG4gICAgICogICAgICAgICAgLy8gYWNrbm93bGVkZ2UgaXRcbiAgICAgKiAgICAgICAgICBtZXNzYWdlLmFjaygpO1xuICAgICAqICAgICAgICB9O1xuICAgICAqICAgICAgICBjbGllbnQuc3Vic2NyaWJlKGRlc3RpbmF0aW9uLCBjYWxsYmFjaywgeydhY2snOiAnY2xpZW50J30pO1xuICAgICAqIGBgYFxuICAgICAqL1xuICAgIGFjayhtZXNzYWdlSWQsIHN1YnNjcmlwdGlvbklkLCBoZWFkZXJzID0ge30pIHtcbiAgICAgICAgdGhpcy5fY2hlY2tDb25uZWN0aW9uKCk7XG4gICAgICAgIC8vIEB0cy1pZ25vcmUgLSB3ZSBhbHJlYWR5IGNoZWNrZWQgdGhhdCB0aGVyZSBpcyBhIF9zdG9tcEhhbmRsZXIsIGFuZCBpdCBpcyBjb25uZWN0ZWRcbiAgICAgICAgdGhpcy5fc3RvbXBIYW5kbGVyLmFjayhtZXNzYWdlSWQsIHN1YnNjcmlwdGlvbklkLCBoZWFkZXJzKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTkFDSyBhIG1lc3NhZ2UuIEl0IGlzIHByZWZlcmFibGUgdG8gYWNrbm93bGVkZ2UgYSBtZXNzYWdlIGJ5IGNhbGxpbmcgW25hY2tde0BsaW5rIElNZXNzYWdlI25hY2t9IGRpcmVjdGx5XG4gICAgICogb24gdGhlIHtAbGluayBJTWVzc2FnZX0gaGFuZGxlZCBieSBhIHN1YnNjcmlwdGlvbiBjYWxsYmFjazpcbiAgICAgKlxuICAgICAqIGBgYGphdmFzY3JpcHRcbiAgICAgKiAgICAgICAgdmFyIGNhbGxiYWNrID0gZnVuY3Rpb24gKG1lc3NhZ2UpIHtcbiAgICAgKiAgICAgICAgICAvLyBwcm9jZXNzIHRoZSBtZXNzYWdlXG4gICAgICogICAgICAgICAgLy8gYW4gZXJyb3Igb2NjdXJzLCBuYWNrIGl0XG4gICAgICogICAgICAgICAgbWVzc2FnZS5uYWNrKCk7XG4gICAgICogICAgICAgIH07XG4gICAgICogICAgICAgIGNsaWVudC5zdWJzY3JpYmUoZGVzdGluYXRpb24sIGNhbGxiYWNrLCB7J2Fjayc6ICdjbGllbnQnfSk7XG4gICAgICogYGBgXG4gICAgICovXG4gICAgbmFjayhtZXNzYWdlSWQsIHN1YnNjcmlwdGlvbklkLCBoZWFkZXJzID0ge30pIHtcbiAgICAgICAgdGhpcy5fY2hlY2tDb25uZWN0aW9uKCk7XG4gICAgICAgIC8vIEB0cy1pZ25vcmUgLSB3ZSBhbHJlYWR5IGNoZWNrZWQgdGhhdCB0aGVyZSBpcyBhIF9zdG9tcEhhbmRsZXIsIGFuZCBpdCBpcyBjb25uZWN0ZWRcbiAgICAgICAgdGhpcy5fc3RvbXBIYW5kbGVyLm5hY2sobWVzc2FnZUlkLCBzdWJzY3JpcHRpb25JZCwgaGVhZGVycyk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2xpZW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stomp/stompjs/esm6/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stomp/stompjs/esm6/frame-impl.js":
/*!********************************************************!*\
  !*** ./node_modules/@stomp/stompjs/esm6/frame-impl.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FrameImpl: () => (/* binding */ FrameImpl)\n/* harmony export */ });\n/* harmony import */ var _byte_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./byte.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/byte.js\");\n\n/**\n * Frame class represents a STOMP frame.\n *\n * @internal\n */\nclass FrameImpl {\n    /**\n     * body of the frame\n     */\n    get body() {\n        if (!this._body && this.isBinaryBody) {\n            this._body = new TextDecoder().decode(this._binaryBody);\n        }\n        return this._body || '';\n    }\n    /**\n     * body as Uint8Array\n     */\n    get binaryBody() {\n        if (!this._binaryBody && !this.isBinaryBody) {\n            this._binaryBody = new TextEncoder().encode(this._body);\n        }\n        // At this stage it will definitely have a valid value\n        return this._binaryBody;\n    }\n    /**\n     * Frame constructor. `command`, `headers` and `body` are available as properties.\n     *\n     * @internal\n     */\n    constructor(params) {\n        const { command, headers, body, binaryBody, escapeHeaderValues, skipContentLengthHeader, } = params;\n        this.command = command;\n        this.headers = Object.assign({}, headers || {});\n        if (binaryBody) {\n            this._binaryBody = binaryBody;\n            this.isBinaryBody = true;\n        }\n        else {\n            this._body = body || '';\n            this.isBinaryBody = false;\n        }\n        this.escapeHeaderValues = escapeHeaderValues || false;\n        this.skipContentLengthHeader = skipContentLengthHeader || false;\n    }\n    /**\n     * deserialize a STOMP Frame from raw data.\n     *\n     * @internal\n     */\n    static fromRawFrame(rawFrame, escapeHeaderValues) {\n        const headers = {};\n        const trim = (str) => str.replace(/^\\s+|\\s+$/g, '');\n        // In case of repeated headers, as per standards, first value need to be used\n        for (const header of rawFrame.headers.reverse()) {\n            const idx = header.indexOf(':');\n            const key = trim(header[0]);\n            let value = trim(header[1]);\n            if (escapeHeaderValues &&\n                rawFrame.command !== 'CONNECT' &&\n                rawFrame.command !== 'CONNECTED') {\n                value = FrameImpl.hdrValueUnEscape(value);\n            }\n            headers[key] = value;\n        }\n        return new FrameImpl({\n            command: rawFrame.command,\n            headers,\n            binaryBody: rawFrame.binaryBody,\n            escapeHeaderValues,\n        });\n    }\n    /**\n     * @internal\n     */\n    toString() {\n        return this.serializeCmdAndHeaders();\n    }\n    /**\n     * serialize this Frame in a format suitable to be passed to WebSocket.\n     * If the body is string the output will be string.\n     * If the body is binary (i.e. of type Unit8Array) it will be serialized to ArrayBuffer.\n     *\n     * @internal\n     */\n    serialize() {\n        const cmdAndHeaders = this.serializeCmdAndHeaders();\n        if (this.isBinaryBody) {\n            return FrameImpl.toUnit8Array(cmdAndHeaders, this._binaryBody).buffer;\n        }\n        else {\n            return cmdAndHeaders + this._body + _byte_js__WEBPACK_IMPORTED_MODULE_0__.BYTE.NULL;\n        }\n    }\n    serializeCmdAndHeaders() {\n        const lines = [this.command];\n        if (this.skipContentLengthHeader) {\n            delete this.headers['content-length'];\n        }\n        for (const name of Object.keys(this.headers || {})) {\n            const value = this.headers[name];\n            if (this.escapeHeaderValues &&\n                this.command !== 'CONNECT' &&\n                this.command !== 'CONNECTED') {\n                lines.push(`${name}:${FrameImpl.hdrValueEscape(`${value}`)}`);\n            }\n            else {\n                lines.push(`${name}:${value}`);\n            }\n        }\n        if (this.isBinaryBody ||\n            (!this.isBodyEmpty() && !this.skipContentLengthHeader)) {\n            lines.push(`content-length:${this.bodyLength()}`);\n        }\n        return lines.join(_byte_js__WEBPACK_IMPORTED_MODULE_0__.BYTE.LF) + _byte_js__WEBPACK_IMPORTED_MODULE_0__.BYTE.LF + _byte_js__WEBPACK_IMPORTED_MODULE_0__.BYTE.LF;\n    }\n    isBodyEmpty() {\n        return this.bodyLength() === 0;\n    }\n    bodyLength() {\n        const binaryBody = this.binaryBody;\n        return binaryBody ? binaryBody.length : 0;\n    }\n    /**\n     * Compute the size of a UTF-8 string by counting its number of bytes\n     * (and not the number of characters composing the string)\n     */\n    static sizeOfUTF8(s) {\n        return s ? new TextEncoder().encode(s).length : 0;\n    }\n    static toUnit8Array(cmdAndHeaders, binaryBody) {\n        const uint8CmdAndHeaders = new TextEncoder().encode(cmdAndHeaders);\n        const nullTerminator = new Uint8Array([0]);\n        const uint8Frame = new Uint8Array(uint8CmdAndHeaders.length + binaryBody.length + nullTerminator.length);\n        uint8Frame.set(uint8CmdAndHeaders);\n        uint8Frame.set(binaryBody, uint8CmdAndHeaders.length);\n        uint8Frame.set(nullTerminator, uint8CmdAndHeaders.length + binaryBody.length);\n        return uint8Frame;\n    }\n    /**\n     * Serialize a STOMP frame as per STOMP standards, suitable to be sent to the STOMP broker.\n     *\n     * @internal\n     */\n    static marshall(params) {\n        const frame = new FrameImpl(params);\n        return frame.serialize();\n    }\n    /**\n     *  Escape header values\n     */\n    static hdrValueEscape(str) {\n        return str\n            .replace(/\\\\/g, '\\\\\\\\')\n            .replace(/\\r/g, '\\\\r')\n            .replace(/\\n/g, '\\\\n')\n            .replace(/:/g, '\\\\c');\n    }\n    /**\n     * UnEscape header values\n     */\n    static hdrValueUnEscape(str) {\n        return str\n            .replace(/\\\\r/g, '\\r')\n            .replace(/\\\\n/g, '\\n')\n            .replace(/\\\\c/g, ':')\n            .replace(/\\\\\\\\/g, '\\\\');\n    }\n}\n//# sourceMappingURL=frame-impl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stomp/stompjs/esm6/frame-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stomp/stompjs/esm6/parser.js":
/*!****************************************************!*\
  !*** ./node_modules/@stomp/stompjs/esm6/parser.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/**\n * @internal\n */\nconst NULL = 0;\n/**\n * @internal\n */\nconst LF = 10;\n/**\n * @internal\n */\nconst CR = 13;\n/**\n * @internal\n */\nconst COLON = 58;\n/**\n * This is an evented, rec descent parser.\n * A stream of Octets can be passed and whenever it recognizes\n * a complete Frame or an incoming ping it will invoke the registered callbacks.\n *\n * All incoming Octets are fed into _onByte function.\n * Depending on current state the _onByte function keeps changing.\n * Depending on the state it keeps accumulating into _token and _results.\n * State is indicated by current value of _onByte, all states are named as _collect.\n *\n * STOMP standards https://stomp.github.io/stomp-specification-1.2.html\n * imply that all lengths are considered in bytes (instead of string lengths).\n * So, before actual parsing, if the incoming data is String it is converted to Octets.\n * This allows faithful implementation of the protocol and allows NULL Octets to be present in the body.\n *\n * There is no peek function on the incoming data.\n * When a state change occurs based on an Octet without consuming the Octet,\n * the Octet, after state change, is fed again (_reinjectByte).\n * This became possible as the state change can be determined by inspecting just one Octet.\n *\n * There are two modes to collect the body, if content-length header is there then it by counting Octets\n * otherwise it is determined by NULL terminator.\n *\n * Following the standards, the command and headers are converted to Strings\n * and the body is returned as Octets.\n * Headers are returned as an array and not as Hash - to allow multiple occurrence of an header.\n *\n * This parser does not use Regular Expressions as that can only operate on Strings.\n *\n * It handles if multiple STOMP frames are given as one chunk, a frame is split into multiple chunks, or\n * any combination there of. The parser remembers its state (any partial frame) and continues when a new chunk\n * is pushed.\n *\n * Typically the higher level function will convert headers to Hash, handle unescaping of header values\n * (which is protocol version specific), and convert body to text.\n *\n * Check the parser.spec.js to understand cases that this parser is supposed to handle.\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nclass Parser {\n    constructor(onFrame, onIncomingPing) {\n        this.onFrame = onFrame;\n        this.onIncomingPing = onIncomingPing;\n        this._encoder = new TextEncoder();\n        this._decoder = new TextDecoder();\n        this._token = [];\n        this._initState();\n    }\n    parseChunk(segment, appendMissingNULLonIncoming = false) {\n        let chunk;\n        if (typeof segment === 'string') {\n            chunk = this._encoder.encode(segment);\n        }\n        else {\n            chunk = new Uint8Array(segment);\n        }\n        // See https://github.com/stomp-js/stompjs/issues/89\n        // Remove when underlying issue is fixed.\n        //\n        // Send a NULL byte, if the last byte of a Text frame was not NULL.F\n        if (appendMissingNULLonIncoming && chunk[chunk.length - 1] !== 0) {\n            const chunkWithNull = new Uint8Array(chunk.length + 1);\n            chunkWithNull.set(chunk, 0);\n            chunkWithNull[chunk.length] = 0;\n            chunk = chunkWithNull;\n        }\n        // tslint:disable-next-line:prefer-for-of\n        for (let i = 0; i < chunk.length; i++) {\n            const byte = chunk[i];\n            this._onByte(byte);\n        }\n    }\n    // The following implements a simple Rec Descent Parser.\n    // The grammar is simple and just one byte tells what should be the next state\n    _collectFrame(byte) {\n        if (byte === NULL) {\n            // Ignore\n            return;\n        }\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            // Incoming Ping\n            this.onIncomingPing();\n            return;\n        }\n        this._onByte = this._collectCommand;\n        this._reinjectByte(byte);\n    }\n    _collectCommand(byte) {\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            this._results.command = this._consumeTokenAsUTF8();\n            this._onByte = this._collectHeaders;\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _collectHeaders(byte) {\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            this._setupCollectBody();\n            return;\n        }\n        this._onByte = this._collectHeaderKey;\n        this._reinjectByte(byte);\n    }\n    _reinjectByte(byte) {\n        this._onByte(byte);\n    }\n    _collectHeaderKey(byte) {\n        if (byte === COLON) {\n            this._headerKey = this._consumeTokenAsUTF8();\n            this._onByte = this._collectHeaderValue;\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _collectHeaderValue(byte) {\n        if (byte === CR) {\n            // Ignore CR\n            return;\n        }\n        if (byte === LF) {\n            this._results.headers.push([\n                this._headerKey,\n                this._consumeTokenAsUTF8(),\n            ]);\n            this._headerKey = undefined;\n            this._onByte = this._collectHeaders;\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _setupCollectBody() {\n        const contentLengthHeader = this._results.headers.filter((header) => {\n            return header[0] === 'content-length';\n        })[0];\n        if (contentLengthHeader) {\n            this._bodyBytesRemaining = parseInt(contentLengthHeader[1], 10);\n            this._onByte = this._collectBodyFixedSize;\n        }\n        else {\n            this._onByte = this._collectBodyNullTerminated;\n        }\n    }\n    _collectBodyNullTerminated(byte) {\n        if (byte === NULL) {\n            this._retrievedBody();\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _collectBodyFixedSize(byte) {\n        // It is post decrement, so that we discard the trailing NULL octet\n        if (this._bodyBytesRemaining-- === 0) {\n            this._retrievedBody();\n            return;\n        }\n        this._consumeByte(byte);\n    }\n    _retrievedBody() {\n        this._results.binaryBody = this._consumeTokenAsRaw();\n        try {\n            this.onFrame(this._results);\n        }\n        catch (e) {\n            console.log(`Ignoring an exception thrown by a frame handler. Original exception: `, e);\n        }\n        this._initState();\n    }\n    // Rec Descent Parser helpers\n    _consumeByte(byte) {\n        this._token.push(byte);\n    }\n    _consumeTokenAsUTF8() {\n        return this._decoder.decode(this._consumeTokenAsRaw());\n    }\n    _consumeTokenAsRaw() {\n        const rawResult = new Uint8Array(this._token);\n        this._token = [];\n        return rawResult;\n    }\n    _initState() {\n        this._results = {\n            command: undefined,\n            headers: [],\n            binaryBody: undefined,\n        };\n        this._token = [];\n        this._headerKey = undefined;\n        this._onByte = this._collectFrame;\n    }\n}\n//# sourceMappingURL=parser.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stomp/stompjs/esm6/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stomp/stompjs/esm6/stomp-handler.js":
/*!***********************************************************!*\
  !*** ./node_modules/@stomp/stompjs/esm6/stomp-handler.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StompHandler: () => (/* binding */ StompHandler)\n/* harmony export */ });\n/* harmony import */ var _augment_websocket_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./augment-websocket.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/augment-websocket.js\");\n/* harmony import */ var _byte_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./byte.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/byte.js\");\n/* harmony import */ var _frame_impl_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./frame-impl.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/frame-impl.js\");\n/* harmony import */ var _parser_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/parser.js\");\n/* harmony import */ var _ticker_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ticker.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/ticker.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/types.js\");\n/* harmony import */ var _versions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./versions.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/versions.js\");\n\n\n\n\n\n\n\n/**\n * The STOMP protocol handler\n *\n * Part of `@stomp/stompjs`.\n *\n * @internal\n */\nclass StompHandler {\n    get connectedVersion() {\n        return this._connectedVersion;\n    }\n    get connected() {\n        return this._connected;\n    }\n    constructor(_client, _webSocket, config) {\n        this._client = _client;\n        this._webSocket = _webSocket;\n        this._connected = false;\n        this._serverFrameHandlers = {\n            // [CONNECTED Frame](https://stomp.github.com/stomp-specification-1.2.html#CONNECTED_Frame)\n            CONNECTED: frame => {\n                this.debug(`connected to server ${frame.headers.server}`);\n                this._connected = true;\n                this._connectedVersion = frame.headers.version;\n                // STOMP version 1.2 needs header values to be escaped\n                if (this._connectedVersion === _versions_js__WEBPACK_IMPORTED_MODULE_0__.Versions.V1_2) {\n                    this._escapeHeaderValues = true;\n                }\n                this._setupHeartbeat(frame.headers);\n                this.onConnect(frame);\n            },\n            // [MESSAGE Frame](https://stomp.github.com/stomp-specification-1.2.html#MESSAGE)\n            MESSAGE: frame => {\n                // the callback is registered when the client calls\n                // `subscribe()`.\n                // If there is no registered subscription for the received message,\n                // the default `onUnhandledMessage` callback is used that the client can set.\n                // This is useful for subscriptions that are automatically created\n                // on the browser side (e.g. [RabbitMQ's temporary\n                // queues](https://www.rabbitmq.com/stomp.html)).\n                const subscription = frame.headers.subscription;\n                const onReceive = this._subscriptions[subscription] || this.onUnhandledMessage;\n                // bless the frame to be a Message\n                const message = frame;\n                const client = this;\n                const messageId = this._connectedVersion === _versions_js__WEBPACK_IMPORTED_MODULE_0__.Versions.V1_2\n                    ? message.headers.ack\n                    : message.headers['message-id'];\n                // add `ack()` and `nack()` methods directly to the returned frame\n                // so that a simple call to `message.ack()` can acknowledge the message.\n                message.ack = (headers = {}) => {\n                    return client.ack(messageId, subscription, headers);\n                };\n                message.nack = (headers = {}) => {\n                    return client.nack(messageId, subscription, headers);\n                };\n                onReceive(message);\n            },\n            // [RECEIPT Frame](https://stomp.github.com/stomp-specification-1.2.html#RECEIPT)\n            RECEIPT: frame => {\n                const callback = this._receiptWatchers[frame.headers['receipt-id']];\n                if (callback) {\n                    callback(frame);\n                    // Server will acknowledge only once, remove the callback\n                    delete this._receiptWatchers[frame.headers['receipt-id']];\n                }\n                else {\n                    this.onUnhandledReceipt(frame);\n                }\n            },\n            // [ERROR Frame](https://stomp.github.com/stomp-specification-1.2.html#ERROR)\n            ERROR: frame => {\n                this.onStompError(frame);\n            },\n        };\n        // used to index subscribers\n        this._counter = 0;\n        // subscription callbacks indexed by subscriber's ID\n        this._subscriptions = {};\n        // receipt-watchers indexed by receipts-ids\n        this._receiptWatchers = {};\n        this._partialData = '';\n        this._escapeHeaderValues = false;\n        this._lastServerActivityTS = Date.now();\n        this.debug = config.debug;\n        this.stompVersions = config.stompVersions;\n        this.connectHeaders = config.connectHeaders;\n        this.disconnectHeaders = config.disconnectHeaders;\n        this.heartbeatIncoming = config.heartbeatIncoming;\n        this.heartbeatOutgoing = config.heartbeatOutgoing;\n        this.splitLargeFrames = config.splitLargeFrames;\n        this.maxWebSocketChunkSize = config.maxWebSocketChunkSize;\n        this.forceBinaryWSFrames = config.forceBinaryWSFrames;\n        this.logRawCommunication = config.logRawCommunication;\n        this.appendMissingNULLonIncoming = config.appendMissingNULLonIncoming;\n        this.discardWebsocketOnCommFailure = config.discardWebsocketOnCommFailure;\n        this.onConnect = config.onConnect;\n        this.onDisconnect = config.onDisconnect;\n        this.onStompError = config.onStompError;\n        this.onWebSocketClose = config.onWebSocketClose;\n        this.onWebSocketError = config.onWebSocketError;\n        this.onUnhandledMessage = config.onUnhandledMessage;\n        this.onUnhandledReceipt = config.onUnhandledReceipt;\n        this.onUnhandledFrame = config.onUnhandledFrame;\n    }\n    start() {\n        const parser = new _parser_js__WEBPACK_IMPORTED_MODULE_1__.Parser(\n        // On Frame\n        rawFrame => {\n            const frame = _frame_impl_js__WEBPACK_IMPORTED_MODULE_2__.FrameImpl.fromRawFrame(rawFrame, this._escapeHeaderValues);\n            // if this.logRawCommunication is set, the rawChunk is logged at this._webSocket.onmessage\n            if (!this.logRawCommunication) {\n                this.debug(`<<< ${frame}`);\n            }\n            const serverFrameHandler = this._serverFrameHandlers[frame.command] || this.onUnhandledFrame;\n            serverFrameHandler(frame);\n        }, \n        // On Incoming Ping\n        () => {\n            this.debug('<<< PONG');\n        });\n        this._webSocket.onmessage = (evt) => {\n            this.debug('Received data');\n            this._lastServerActivityTS = Date.now();\n            if (this.logRawCommunication) {\n                const rawChunkAsString = evt.data instanceof ArrayBuffer\n                    ? new TextDecoder().decode(evt.data)\n                    : evt.data;\n                this.debug(`<<< ${rawChunkAsString}`);\n            }\n            parser.parseChunk(evt.data, this.appendMissingNULLonIncoming);\n        };\n        this._webSocket.onclose = (closeEvent) => {\n            this.debug(`Connection closed to ${this._webSocket.url}`);\n            this._cleanUp();\n            this.onWebSocketClose(closeEvent);\n        };\n        this._webSocket.onerror = (errorEvent) => {\n            this.onWebSocketError(errorEvent);\n        };\n        this._webSocket.onopen = () => {\n            // Clone before updating\n            const connectHeaders = Object.assign({}, this.connectHeaders);\n            this.debug('Web Socket Opened...');\n            connectHeaders['accept-version'] = this.stompVersions.supportedVersions();\n            connectHeaders['heart-beat'] = [\n                this.heartbeatOutgoing,\n                this.heartbeatIncoming,\n            ].join(',');\n            this._transmit({ command: 'CONNECT', headers: connectHeaders });\n        };\n    }\n    _setupHeartbeat(headers) {\n        if (headers.version !== _versions_js__WEBPACK_IMPORTED_MODULE_0__.Versions.V1_1 &&\n            headers.version !== _versions_js__WEBPACK_IMPORTED_MODULE_0__.Versions.V1_2) {\n            return;\n        }\n        // It is valid for the server to not send this header\n        // https://stomp.github.io/stomp-specification-1.2.html#Heart-beating\n        if (!headers['heart-beat']) {\n            return;\n        }\n        // heart-beat header received from the server looks like:\n        //\n        //     heart-beat: sx, sy\n        const [serverOutgoing, serverIncoming] = headers['heart-beat']\n            .split(',')\n            .map((v) => parseInt(v, 10));\n        if (this.heartbeatOutgoing !== 0 && serverIncoming !== 0) {\n            const ttl = Math.max(this.heartbeatOutgoing, serverIncoming);\n            this.debug(`send PING every ${ttl}ms`);\n            this._pinger = new _ticker_js__WEBPACK_IMPORTED_MODULE_3__.Ticker(ttl, this._client.heartbeatStrategy, this.debug);\n            this._pinger.start(() => {\n                if (this._webSocket.readyState === _types_js__WEBPACK_IMPORTED_MODULE_4__.StompSocketState.OPEN) {\n                    this._webSocket.send(_byte_js__WEBPACK_IMPORTED_MODULE_5__.BYTE.LF);\n                    this.debug('>>> PING');\n                }\n            });\n        }\n        if (this.heartbeatIncoming !== 0 && serverOutgoing !== 0) {\n            const ttl = Math.max(this.heartbeatIncoming, serverOutgoing);\n            this.debug(`check PONG every ${ttl}ms`);\n            this._ponger = setInterval(() => {\n                const delta = Date.now() - this._lastServerActivityTS;\n                // We wait twice the TTL to be flexible on window's setInterval calls\n                if (delta > ttl * 2) {\n                    this.debug(`did not receive server activity for the last ${delta}ms`);\n                    this._closeOrDiscardWebsocket();\n                }\n            }, ttl);\n        }\n    }\n    _closeOrDiscardWebsocket() {\n        if (this.discardWebsocketOnCommFailure) {\n            this.debug('Discarding websocket, the underlying socket may linger for a while');\n            this.discardWebsocket();\n        }\n        else {\n            this.debug('Issuing close on the websocket');\n            this._closeWebsocket();\n        }\n    }\n    forceDisconnect() {\n        if (this._webSocket) {\n            if (this._webSocket.readyState === _types_js__WEBPACK_IMPORTED_MODULE_4__.StompSocketState.CONNECTING ||\n                this._webSocket.readyState === _types_js__WEBPACK_IMPORTED_MODULE_4__.StompSocketState.OPEN) {\n                this._closeOrDiscardWebsocket();\n            }\n        }\n    }\n    _closeWebsocket() {\n        this._webSocket.onmessage = () => { }; // ignore messages\n        this._webSocket.close();\n    }\n    discardWebsocket() {\n        if (typeof this._webSocket.terminate !== 'function') {\n            (0,_augment_websocket_js__WEBPACK_IMPORTED_MODULE_6__.augmentWebsocket)(this._webSocket, (msg) => this.debug(msg));\n        }\n        // @ts-ignore - this method will be there at this stage\n        this._webSocket.terminate();\n    }\n    _transmit(params) {\n        const { command, headers, body, binaryBody, skipContentLengthHeader } = params;\n        const frame = new _frame_impl_js__WEBPACK_IMPORTED_MODULE_2__.FrameImpl({\n            command,\n            headers,\n            body,\n            binaryBody,\n            escapeHeaderValues: this._escapeHeaderValues,\n            skipContentLengthHeader,\n        });\n        let rawChunk = frame.serialize();\n        if (this.logRawCommunication) {\n            this.debug(`>>> ${rawChunk}`);\n        }\n        else {\n            this.debug(`>>> ${frame}`);\n        }\n        if (this.forceBinaryWSFrames && typeof rawChunk === 'string') {\n            rawChunk = new TextEncoder().encode(rawChunk);\n        }\n        if (typeof rawChunk !== 'string' || !this.splitLargeFrames) {\n            this._webSocket.send(rawChunk);\n        }\n        else {\n            let out = rawChunk;\n            while (out.length > 0) {\n                const chunk = out.substring(0, this.maxWebSocketChunkSize);\n                out = out.substring(this.maxWebSocketChunkSize);\n                this._webSocket.send(chunk);\n                this.debug(`chunk sent = ${chunk.length}, remaining = ${out.length}`);\n            }\n        }\n    }\n    dispose() {\n        if (this.connected) {\n            try {\n                // clone before updating\n                const disconnectHeaders = Object.assign({}, this.disconnectHeaders);\n                if (!disconnectHeaders.receipt) {\n                    disconnectHeaders.receipt = `close-${this._counter++}`;\n                }\n                this.watchForReceipt(disconnectHeaders.receipt, frame => {\n                    this._closeWebsocket();\n                    this._cleanUp();\n                    this.onDisconnect(frame);\n                });\n                this._transmit({ command: 'DISCONNECT', headers: disconnectHeaders });\n            }\n            catch (error) {\n                this.debug(`Ignoring error during disconnect ${error}`);\n            }\n        }\n        else {\n            if (this._webSocket.readyState === _types_js__WEBPACK_IMPORTED_MODULE_4__.StompSocketState.CONNECTING ||\n                this._webSocket.readyState === _types_js__WEBPACK_IMPORTED_MODULE_4__.StompSocketState.OPEN) {\n                this._closeWebsocket();\n            }\n        }\n    }\n    _cleanUp() {\n        this._connected = false;\n        if (this._pinger) {\n            this._pinger.stop();\n            this._pinger = undefined;\n        }\n        if (this._ponger) {\n            clearInterval(this._ponger);\n            this._ponger = undefined;\n        }\n    }\n    publish(params) {\n        const { destination, headers, body, binaryBody, skipContentLengthHeader } = params;\n        const hdrs = Object.assign({ destination }, headers);\n        this._transmit({\n            command: 'SEND',\n            headers: hdrs,\n            body,\n            binaryBody,\n            skipContentLengthHeader,\n        });\n    }\n    watchForReceipt(receiptId, callback) {\n        this._receiptWatchers[receiptId] = callback;\n    }\n    subscribe(destination, callback, headers = {}) {\n        headers = Object.assign({}, headers);\n        if (!headers.id) {\n            headers.id = `sub-${this._counter++}`;\n        }\n        headers.destination = destination;\n        this._subscriptions[headers.id] = callback;\n        this._transmit({ command: 'SUBSCRIBE', headers });\n        const client = this;\n        return {\n            id: headers.id,\n            unsubscribe(hdrs) {\n                return client.unsubscribe(headers.id, hdrs);\n            },\n        };\n    }\n    unsubscribe(id, headers = {}) {\n        headers = Object.assign({}, headers);\n        delete this._subscriptions[id];\n        headers.id = id;\n        this._transmit({ command: 'UNSUBSCRIBE', headers });\n    }\n    begin(transactionId) {\n        const txId = transactionId || `tx-${this._counter++}`;\n        this._transmit({\n            command: 'BEGIN',\n            headers: {\n                transaction: txId,\n            },\n        });\n        const client = this;\n        return {\n            id: txId,\n            commit() {\n                client.commit(txId);\n            },\n            abort() {\n                client.abort(txId);\n            },\n        };\n    }\n    commit(transactionId) {\n        this._transmit({\n            command: 'COMMIT',\n            headers: {\n                transaction: transactionId,\n            },\n        });\n    }\n    abort(transactionId) {\n        this._transmit({\n            command: 'ABORT',\n            headers: {\n                transaction: transactionId,\n            },\n        });\n    }\n    ack(messageId, subscriptionId, headers = {}) {\n        headers = Object.assign({}, headers);\n        if (this._connectedVersion === _versions_js__WEBPACK_IMPORTED_MODULE_0__.Versions.V1_2) {\n            headers.id = messageId;\n        }\n        else {\n            headers['message-id'] = messageId;\n        }\n        headers.subscription = subscriptionId;\n        this._transmit({ command: 'ACK', headers });\n    }\n    nack(messageId, subscriptionId, headers = {}) {\n        headers = Object.assign({}, headers);\n        if (this._connectedVersion === _versions_js__WEBPACK_IMPORTED_MODULE_0__.Versions.V1_2) {\n            headers.id = messageId;\n        }\n        else {\n            headers['message-id'] = messageId;\n        }\n        headers.subscription = subscriptionId;\n        return this._transmit({ command: 'NACK', headers });\n    }\n}\n//# sourceMappingURL=stomp-handler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN0b21wL3N0b21wanMvZXNtNi9zdG9tcC1oYW5kbGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTBEO0FBQ3pCO0FBQ1c7QUFDUDtBQUNBO0FBQ1U7QUFDTjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QscUJBQXFCO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBLCtDQUErQyxrREFBUTtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELGtEQUFRO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDO0FBQzNDO0FBQ0E7QUFDQSw0Q0FBNEM7QUFDNUM7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsOENBQU07QUFDakM7QUFDQTtBQUNBLDBCQUEwQixxREFBUztBQUNuQztBQUNBO0FBQ0Esa0NBQWtDLE1BQU07QUFDeEM7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxpQkFBaUI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0Msb0JBQW9CO0FBQ25FO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQ7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLDZDQUE2QztBQUMxRTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msa0RBQVE7QUFDeEMsZ0NBQWdDLGtEQUFRO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQyxJQUFJO0FBQzlDLCtCQUErQiw4Q0FBTTtBQUNyQztBQUNBLG1EQUFtRCx1REFBZ0I7QUFDbkUseUNBQXlDLDBDQUFJO0FBQzdDO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLElBQUk7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRUFBK0UsTUFBTTtBQUNyRjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsdURBQWdCO0FBQy9ELCtDQUErQyx1REFBZ0I7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtDQUErQztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksdUVBQWdCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsOERBQThEO0FBQzlFLDBCQUEwQixxREFBUztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLDhCQUE4QixTQUFTO0FBQ3ZDO0FBQ0E7QUFDQSw4QkFBOEIsTUFBTTtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxhQUFhLGdCQUFnQixXQUFXO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMERBQTBEO0FBQzFEO0FBQ0EseURBQXlELGdCQUFnQjtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGlDQUFpQyxtREFBbUQ7QUFDcEY7QUFDQTtBQUNBLCtEQUErRCxNQUFNO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBLCtDQUErQyx1REFBZ0I7QUFDL0QsK0NBQStDLHVEQUFnQjtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixrRUFBa0U7QUFDbEYscUNBQXFDLGFBQWE7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlEO0FBQ2pELGtDQUFrQztBQUNsQztBQUNBLGdDQUFnQyxnQkFBZ0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLCtCQUErQjtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEMsa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQSx5QkFBeUIsaUNBQWlDO0FBQzFEO0FBQ0E7QUFDQSw0Q0FBNEMsZ0JBQWdCO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQSwrQ0FBK0M7QUFDL0Msa0NBQWtDO0FBQ2xDLHVDQUF1QyxrREFBUTtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIseUJBQXlCO0FBQ2xEO0FBQ0EsZ0RBQWdEO0FBQ2hELGtDQUFrQztBQUNsQyx1Q0FBdUMsa0RBQVE7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDBCQUEwQjtBQUMxRDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aXNcXERvY3VtZW50c1xcUHJvamVjdHNcXFNwaWRleFxcc3BpZGV4X3dlYnVpMy4wXFxub2RlX21vZHVsZXNcXEBzdG9tcFxcc3RvbXBqc1xcZXNtNlxcc3RvbXAtaGFuZGxlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhdWdtZW50V2Vic29ja2V0IH0gZnJvbSAnLi9hdWdtZW50LXdlYnNvY2tldC5qcyc7XG5pbXBvcnQgeyBCWVRFIH0gZnJvbSAnLi9ieXRlLmpzJztcbmltcG9ydCB7IEZyYW1lSW1wbCB9IGZyb20gJy4vZnJhbWUtaW1wbC5qcyc7XG5pbXBvcnQgeyBQYXJzZXIgfSBmcm9tICcuL3BhcnNlci5qcyc7XG5pbXBvcnQgeyBUaWNrZXIgfSBmcm9tICcuL3RpY2tlci5qcyc7XG5pbXBvcnQgeyBTdG9tcFNvY2tldFN0YXRlLCB9IGZyb20gJy4vdHlwZXMuanMnO1xuaW1wb3J0IHsgVmVyc2lvbnMgfSBmcm9tICcuL3ZlcnNpb25zLmpzJztcbi8qKlxuICogVGhlIFNUT01QIHByb3RvY29sIGhhbmRsZXJcbiAqXG4gKiBQYXJ0IG9mIGBAc3RvbXAvc3RvbXBqc2AuXG4gKlxuICogQGludGVybmFsXG4gKi9cbmV4cG9ydCBjbGFzcyBTdG9tcEhhbmRsZXIge1xuICAgIGdldCBjb25uZWN0ZWRWZXJzaW9uKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fY29ubmVjdGVkVmVyc2lvbjtcbiAgICB9XG4gICAgZ2V0IGNvbm5lY3RlZCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2Nvbm5lY3RlZDtcbiAgICB9XG4gICAgY29uc3RydWN0b3IoX2NsaWVudCwgX3dlYlNvY2tldCwgY29uZmlnKSB7XG4gICAgICAgIHRoaXMuX2NsaWVudCA9IF9jbGllbnQ7XG4gICAgICAgIHRoaXMuX3dlYlNvY2tldCA9IF93ZWJTb2NrZXQ7XG4gICAgICAgIHRoaXMuX2Nvbm5lY3RlZCA9IGZhbHNlO1xuICAgICAgICB0aGlzLl9zZXJ2ZXJGcmFtZUhhbmRsZXJzID0ge1xuICAgICAgICAgICAgLy8gW0NPTk5FQ1RFRCBGcmFtZV0oaHR0cHM6Ly9zdG9tcC5naXRodWIuY29tL3N0b21wLXNwZWNpZmljYXRpb24tMS4yLmh0bWwjQ09OTkVDVEVEX0ZyYW1lKVxuICAgICAgICAgICAgQ09OTkVDVEVEOiBmcmFtZSA9PiB7XG4gICAgICAgICAgICAgICAgdGhpcy5kZWJ1ZyhgY29ubmVjdGVkIHRvIHNlcnZlciAke2ZyYW1lLmhlYWRlcnMuc2VydmVyfWApO1xuICAgICAgICAgICAgICAgIHRoaXMuX2Nvbm5lY3RlZCA9IHRydWU7XG4gICAgICAgICAgICAgICAgdGhpcy5fY29ubmVjdGVkVmVyc2lvbiA9IGZyYW1lLmhlYWRlcnMudmVyc2lvbjtcbiAgICAgICAgICAgICAgICAvLyBTVE9NUCB2ZXJzaW9uIDEuMiBuZWVkcyBoZWFkZXIgdmFsdWVzIHRvIGJlIGVzY2FwZWRcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5fY29ubmVjdGVkVmVyc2lvbiA9PT0gVmVyc2lvbnMuVjFfMikge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLl9lc2NhcGVIZWFkZXJWYWx1ZXMgPSB0cnVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLl9zZXR1cEhlYXJ0YmVhdChmcmFtZS5oZWFkZXJzKTtcbiAgICAgICAgICAgICAgICB0aGlzLm9uQ29ubmVjdChmcmFtZSk7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgLy8gW01FU1NBR0UgRnJhbWVdKGh0dHBzOi8vc3RvbXAuZ2l0aHViLmNvbS9zdG9tcC1zcGVjaWZpY2F0aW9uLTEuMi5odG1sI01FU1NBR0UpXG4gICAgICAgICAgICBNRVNTQUdFOiBmcmFtZSA9PiB7XG4gICAgICAgICAgICAgICAgLy8gdGhlIGNhbGxiYWNrIGlzIHJlZ2lzdGVyZWQgd2hlbiB0aGUgY2xpZW50IGNhbGxzXG4gICAgICAgICAgICAgICAgLy8gYHN1YnNjcmliZSgpYC5cbiAgICAgICAgICAgICAgICAvLyBJZiB0aGVyZSBpcyBubyByZWdpc3RlcmVkIHN1YnNjcmlwdGlvbiBmb3IgdGhlIHJlY2VpdmVkIG1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgLy8gdGhlIGRlZmF1bHQgYG9uVW5oYW5kbGVkTWVzc2FnZWAgY2FsbGJhY2sgaXMgdXNlZCB0aGF0IHRoZSBjbGllbnQgY2FuIHNldC5cbiAgICAgICAgICAgICAgICAvLyBUaGlzIGlzIHVzZWZ1bCBmb3Igc3Vic2NyaXB0aW9ucyB0aGF0IGFyZSBhdXRvbWF0aWNhbGx5IGNyZWF0ZWRcbiAgICAgICAgICAgICAgICAvLyBvbiB0aGUgYnJvd3NlciBzaWRlIChlLmcuIFtSYWJiaXRNUSdzIHRlbXBvcmFyeVxuICAgICAgICAgICAgICAgIC8vIHF1ZXVlc10oaHR0cHM6Ly93d3cucmFiYml0bXEuY29tL3N0b21wLmh0bWwpKS5cbiAgICAgICAgICAgICAgICBjb25zdCBzdWJzY3JpcHRpb24gPSBmcmFtZS5oZWFkZXJzLnN1YnNjcmlwdGlvbjtcbiAgICAgICAgICAgICAgICBjb25zdCBvblJlY2VpdmUgPSB0aGlzLl9zdWJzY3JpcHRpb25zW3N1YnNjcmlwdGlvbl0gfHwgdGhpcy5vblVuaGFuZGxlZE1lc3NhZ2U7XG4gICAgICAgICAgICAgICAgLy8gYmxlc3MgdGhlIGZyYW1lIHRvIGJlIGEgTWVzc2FnZVxuICAgICAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2UgPSBmcmFtZTtcbiAgICAgICAgICAgICAgICBjb25zdCBjbGllbnQgPSB0aGlzO1xuICAgICAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2VJZCA9IHRoaXMuX2Nvbm5lY3RlZFZlcnNpb24gPT09IFZlcnNpb25zLlYxXzJcbiAgICAgICAgICAgICAgICAgICAgPyBtZXNzYWdlLmhlYWRlcnMuYWNrXG4gICAgICAgICAgICAgICAgICAgIDogbWVzc2FnZS5oZWFkZXJzWydtZXNzYWdlLWlkJ107XG4gICAgICAgICAgICAgICAgLy8gYWRkIGBhY2soKWAgYW5kIGBuYWNrKClgIG1ldGhvZHMgZGlyZWN0bHkgdG8gdGhlIHJldHVybmVkIGZyYW1lXG4gICAgICAgICAgICAgICAgLy8gc28gdGhhdCBhIHNpbXBsZSBjYWxsIHRvIGBtZXNzYWdlLmFjaygpYCBjYW4gYWNrbm93bGVkZ2UgdGhlIG1lc3NhZ2UuXG4gICAgICAgICAgICAgICAgbWVzc2FnZS5hY2sgPSAoaGVhZGVycyA9IHt9KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBjbGllbnQuYWNrKG1lc3NhZ2VJZCwgc3Vic2NyaXB0aW9uLCBoZWFkZXJzKTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIG1lc3NhZ2UubmFjayA9IChoZWFkZXJzID0ge30pID0+IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGNsaWVudC5uYWNrKG1lc3NhZ2VJZCwgc3Vic2NyaXB0aW9uLCBoZWFkZXJzKTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIG9uUmVjZWl2ZShtZXNzYWdlKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAvLyBbUkVDRUlQVCBGcmFtZV0oaHR0cHM6Ly9zdG9tcC5naXRodWIuY29tL3N0b21wLXNwZWNpZmljYXRpb24tMS4yLmh0bWwjUkVDRUlQVClcbiAgICAgICAgICAgIFJFQ0VJUFQ6IGZyYW1lID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBjYWxsYmFjayA9IHRoaXMuX3JlY2VpcHRXYXRjaGVyc1tmcmFtZS5oZWFkZXJzWydyZWNlaXB0LWlkJ11dO1xuICAgICAgICAgICAgICAgIGlmIChjYWxsYmFjaykge1xuICAgICAgICAgICAgICAgICAgICBjYWxsYmFjayhmcmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIC8vIFNlcnZlciB3aWxsIGFja25vd2xlZGdlIG9ubHkgb25jZSwgcmVtb3ZlIHRoZSBjYWxsYmFja1xuICAgICAgICAgICAgICAgICAgICBkZWxldGUgdGhpcy5fcmVjZWlwdFdhdGNoZXJzW2ZyYW1lLmhlYWRlcnNbJ3JlY2VpcHQtaWQnXV07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLm9uVW5oYW5kbGVkUmVjZWlwdChmcmFtZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIC8vIFtFUlJPUiBGcmFtZV0oaHR0cHM6Ly9zdG9tcC5naXRodWIuY29tL3N0b21wLXNwZWNpZmljYXRpb24tMS4yLmh0bWwjRVJST1IpXG4gICAgICAgICAgICBFUlJPUjogZnJhbWUgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMub25TdG9tcEVycm9yKGZyYW1lKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH07XG4gICAgICAgIC8vIHVzZWQgdG8gaW5kZXggc3Vic2NyaWJlcnNcbiAgICAgICAgdGhpcy5fY291bnRlciA9IDA7XG4gICAgICAgIC8vIHN1YnNjcmlwdGlvbiBjYWxsYmFja3MgaW5kZXhlZCBieSBzdWJzY3JpYmVyJ3MgSURcbiAgICAgICAgdGhpcy5fc3Vic2NyaXB0aW9ucyA9IHt9O1xuICAgICAgICAvLyByZWNlaXB0LXdhdGNoZXJzIGluZGV4ZWQgYnkgcmVjZWlwdHMtaWRzXG4gICAgICAgIHRoaXMuX3JlY2VpcHRXYXRjaGVycyA9IHt9O1xuICAgICAgICB0aGlzLl9wYXJ0aWFsRGF0YSA9ICcnO1xuICAgICAgICB0aGlzLl9lc2NhcGVIZWFkZXJWYWx1ZXMgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5fbGFzdFNlcnZlckFjdGl2aXR5VFMgPSBEYXRlLm5vdygpO1xuICAgICAgICB0aGlzLmRlYnVnID0gY29uZmlnLmRlYnVnO1xuICAgICAgICB0aGlzLnN0b21wVmVyc2lvbnMgPSBjb25maWcuc3RvbXBWZXJzaW9ucztcbiAgICAgICAgdGhpcy5jb25uZWN0SGVhZGVycyA9IGNvbmZpZy5jb25uZWN0SGVhZGVycztcbiAgICAgICAgdGhpcy5kaXNjb25uZWN0SGVhZGVycyA9IGNvbmZpZy5kaXNjb25uZWN0SGVhZGVycztcbiAgICAgICAgdGhpcy5oZWFydGJlYXRJbmNvbWluZyA9IGNvbmZpZy5oZWFydGJlYXRJbmNvbWluZztcbiAgICAgICAgdGhpcy5oZWFydGJlYXRPdXRnb2luZyA9IGNvbmZpZy5oZWFydGJlYXRPdXRnb2luZztcbiAgICAgICAgdGhpcy5zcGxpdExhcmdlRnJhbWVzID0gY29uZmlnLnNwbGl0TGFyZ2VGcmFtZXM7XG4gICAgICAgIHRoaXMubWF4V2ViU29ja2V0Q2h1bmtTaXplID0gY29uZmlnLm1heFdlYlNvY2tldENodW5rU2l6ZTtcbiAgICAgICAgdGhpcy5mb3JjZUJpbmFyeVdTRnJhbWVzID0gY29uZmlnLmZvcmNlQmluYXJ5V1NGcmFtZXM7XG4gICAgICAgIHRoaXMubG9nUmF3Q29tbXVuaWNhdGlvbiA9IGNvbmZpZy5sb2dSYXdDb21tdW5pY2F0aW9uO1xuICAgICAgICB0aGlzLmFwcGVuZE1pc3NpbmdOVUxMb25JbmNvbWluZyA9IGNvbmZpZy5hcHBlbmRNaXNzaW5nTlVMTG9uSW5jb21pbmc7XG4gICAgICAgIHRoaXMuZGlzY2FyZFdlYnNvY2tldE9uQ29tbUZhaWx1cmUgPSBjb25maWcuZGlzY2FyZFdlYnNvY2tldE9uQ29tbUZhaWx1cmU7XG4gICAgICAgIHRoaXMub25Db25uZWN0ID0gY29uZmlnLm9uQ29ubmVjdDtcbiAgICAgICAgdGhpcy5vbkRpc2Nvbm5lY3QgPSBjb25maWcub25EaXNjb25uZWN0O1xuICAgICAgICB0aGlzLm9uU3RvbXBFcnJvciA9IGNvbmZpZy5vblN0b21wRXJyb3I7XG4gICAgICAgIHRoaXMub25XZWJTb2NrZXRDbG9zZSA9IGNvbmZpZy5vbldlYlNvY2tldENsb3NlO1xuICAgICAgICB0aGlzLm9uV2ViU29ja2V0RXJyb3IgPSBjb25maWcub25XZWJTb2NrZXRFcnJvcjtcbiAgICAgICAgdGhpcy5vblVuaGFuZGxlZE1lc3NhZ2UgPSBjb25maWcub25VbmhhbmRsZWRNZXNzYWdlO1xuICAgICAgICB0aGlzLm9uVW5oYW5kbGVkUmVjZWlwdCA9IGNvbmZpZy5vblVuaGFuZGxlZFJlY2VpcHQ7XG4gICAgICAgIHRoaXMub25VbmhhbmRsZWRGcmFtZSA9IGNvbmZpZy5vblVuaGFuZGxlZEZyYW1lO1xuICAgIH1cbiAgICBzdGFydCgpIHtcbiAgICAgICAgY29uc3QgcGFyc2VyID0gbmV3IFBhcnNlcihcbiAgICAgICAgLy8gT24gRnJhbWVcbiAgICAgICAgcmF3RnJhbWUgPT4ge1xuICAgICAgICAgICAgY29uc3QgZnJhbWUgPSBGcmFtZUltcGwuZnJvbVJhd0ZyYW1lKHJhd0ZyYW1lLCB0aGlzLl9lc2NhcGVIZWFkZXJWYWx1ZXMpO1xuICAgICAgICAgICAgLy8gaWYgdGhpcy5sb2dSYXdDb21tdW5pY2F0aW9uIGlzIHNldCwgdGhlIHJhd0NodW5rIGlzIGxvZ2dlZCBhdCB0aGlzLl93ZWJTb2NrZXQub25tZXNzYWdlXG4gICAgICAgICAgICBpZiAoIXRoaXMubG9nUmF3Q29tbXVuaWNhdGlvbikge1xuICAgICAgICAgICAgICAgIHRoaXMuZGVidWcoYDw8PCAke2ZyYW1lfWApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3Qgc2VydmVyRnJhbWVIYW5kbGVyID0gdGhpcy5fc2VydmVyRnJhbWVIYW5kbGVyc1tmcmFtZS5jb21tYW5kXSB8fCB0aGlzLm9uVW5oYW5kbGVkRnJhbWU7XG4gICAgICAgICAgICBzZXJ2ZXJGcmFtZUhhbmRsZXIoZnJhbWUpO1xuICAgICAgICB9LCBcbiAgICAgICAgLy8gT24gSW5jb21pbmcgUGluZ1xuICAgICAgICAoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmRlYnVnKCc8PDwgUE9ORycpO1xuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5fd2ViU29ja2V0Lm9ubWVzc2FnZSA9IChldnQpID0+IHtcbiAgICAgICAgICAgIHRoaXMuZGVidWcoJ1JlY2VpdmVkIGRhdGEnKTtcbiAgICAgICAgICAgIHRoaXMuX2xhc3RTZXJ2ZXJBY3Rpdml0eVRTID0gRGF0ZS5ub3coKTtcbiAgICAgICAgICAgIGlmICh0aGlzLmxvZ1Jhd0NvbW11bmljYXRpb24pIHtcbiAgICAgICAgICAgICAgICBjb25zdCByYXdDaHVua0FzU3RyaW5nID0gZXZ0LmRhdGEgaW5zdGFuY2VvZiBBcnJheUJ1ZmZlclxuICAgICAgICAgICAgICAgICAgICA/IG5ldyBUZXh0RGVjb2RlcigpLmRlY29kZShldnQuZGF0YSlcbiAgICAgICAgICAgICAgICAgICAgOiBldnQuZGF0YTtcbiAgICAgICAgICAgICAgICB0aGlzLmRlYnVnKGA8PDwgJHtyYXdDaHVua0FzU3RyaW5nfWApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcGFyc2VyLnBhcnNlQ2h1bmsoZXZ0LmRhdGEsIHRoaXMuYXBwZW5kTWlzc2luZ05VTExvbkluY29taW5nKTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5fd2ViU29ja2V0Lm9uY2xvc2UgPSAoY2xvc2VFdmVudCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5kZWJ1ZyhgQ29ubmVjdGlvbiBjbG9zZWQgdG8gJHt0aGlzLl93ZWJTb2NrZXQudXJsfWApO1xuICAgICAgICAgICAgdGhpcy5fY2xlYW5VcCgpO1xuICAgICAgICAgICAgdGhpcy5vbldlYlNvY2tldENsb3NlKGNsb3NlRXZlbnQpO1xuICAgICAgICB9O1xuICAgICAgICB0aGlzLl93ZWJTb2NrZXQub25lcnJvciA9IChlcnJvckV2ZW50KSA9PiB7XG4gICAgICAgICAgICB0aGlzLm9uV2ViU29ja2V0RXJyb3IoZXJyb3JFdmVudCk7XG4gICAgICAgIH07XG4gICAgICAgIHRoaXMuX3dlYlNvY2tldC5vbm9wZW4gPSAoKSA9PiB7XG4gICAgICAgICAgICAvLyBDbG9uZSBiZWZvcmUgdXBkYXRpbmdcbiAgICAgICAgICAgIGNvbnN0IGNvbm5lY3RIZWFkZXJzID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5jb25uZWN0SGVhZGVycyk7XG4gICAgICAgICAgICB0aGlzLmRlYnVnKCdXZWIgU29ja2V0IE9wZW5lZC4uLicpO1xuICAgICAgICAgICAgY29ubmVjdEhlYWRlcnNbJ2FjY2VwdC12ZXJzaW9uJ10gPSB0aGlzLnN0b21wVmVyc2lvbnMuc3VwcG9ydGVkVmVyc2lvbnMoKTtcbiAgICAgICAgICAgIGNvbm5lY3RIZWFkZXJzWydoZWFydC1iZWF0J10gPSBbXG4gICAgICAgICAgICAgICAgdGhpcy5oZWFydGJlYXRPdXRnb2luZyxcbiAgICAgICAgICAgICAgICB0aGlzLmhlYXJ0YmVhdEluY29taW5nLFxuICAgICAgICAgICAgXS5qb2luKCcsJyk7XG4gICAgICAgICAgICB0aGlzLl90cmFuc21pdCh7IGNvbW1hbmQ6ICdDT05ORUNUJywgaGVhZGVyczogY29ubmVjdEhlYWRlcnMgfSk7XG4gICAgICAgIH07XG4gICAgfVxuICAgIF9zZXR1cEhlYXJ0YmVhdChoZWFkZXJzKSB7XG4gICAgICAgIGlmIChoZWFkZXJzLnZlcnNpb24gIT09IFZlcnNpb25zLlYxXzEgJiZcbiAgICAgICAgICAgIGhlYWRlcnMudmVyc2lvbiAhPT0gVmVyc2lvbnMuVjFfMikge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIC8vIEl0IGlzIHZhbGlkIGZvciB0aGUgc2VydmVyIHRvIG5vdCBzZW5kIHRoaXMgaGVhZGVyXG4gICAgICAgIC8vIGh0dHBzOi8vc3RvbXAuZ2l0aHViLmlvL3N0b21wLXNwZWNpZmljYXRpb24tMS4yLmh0bWwjSGVhcnQtYmVhdGluZ1xuICAgICAgICBpZiAoIWhlYWRlcnNbJ2hlYXJ0LWJlYXQnXSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIC8vIGhlYXJ0LWJlYXQgaGVhZGVyIHJlY2VpdmVkIGZyb20gdGhlIHNlcnZlciBsb29rcyBsaWtlOlxuICAgICAgICAvL1xuICAgICAgICAvLyAgICAgaGVhcnQtYmVhdDogc3gsIHN5XG4gICAgICAgIGNvbnN0IFtzZXJ2ZXJPdXRnb2luZywgc2VydmVySW5jb21pbmddID0gaGVhZGVyc1snaGVhcnQtYmVhdCddXG4gICAgICAgICAgICAuc3BsaXQoJywnKVxuICAgICAgICAgICAgLm1hcCgodikgPT4gcGFyc2VJbnQodiwgMTApKTtcbiAgICAgICAgaWYgKHRoaXMuaGVhcnRiZWF0T3V0Z29pbmcgIT09IDAgJiYgc2VydmVySW5jb21pbmcgIT09IDApIHtcbiAgICAgICAgICAgIGNvbnN0IHR0bCA9IE1hdGgubWF4KHRoaXMuaGVhcnRiZWF0T3V0Z29pbmcsIHNlcnZlckluY29taW5nKTtcbiAgICAgICAgICAgIHRoaXMuZGVidWcoYHNlbmQgUElORyBldmVyeSAke3R0bH1tc2ApO1xuICAgICAgICAgICAgdGhpcy5fcGluZ2VyID0gbmV3IFRpY2tlcih0dGwsIHRoaXMuX2NsaWVudC5oZWFydGJlYXRTdHJhdGVneSwgdGhpcy5kZWJ1Zyk7XG4gICAgICAgICAgICB0aGlzLl9waW5nZXIuc3RhcnQoKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLl93ZWJTb2NrZXQucmVhZHlTdGF0ZSA9PT0gU3RvbXBTb2NrZXRTdGF0ZS5PUEVOKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3dlYlNvY2tldC5zZW5kKEJZVEUuTEYpO1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmRlYnVnKCc+Pj4gUElORycpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLmhlYXJ0YmVhdEluY29taW5nICE9PSAwICYmIHNlcnZlck91dGdvaW5nICE9PSAwKSB7XG4gICAgICAgICAgICBjb25zdCB0dGwgPSBNYXRoLm1heCh0aGlzLmhlYXJ0YmVhdEluY29taW5nLCBzZXJ2ZXJPdXRnb2luZyk7XG4gICAgICAgICAgICB0aGlzLmRlYnVnKGBjaGVjayBQT05HIGV2ZXJ5ICR7dHRsfW1zYCk7XG4gICAgICAgICAgICB0aGlzLl9wb25nZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgZGVsdGEgPSBEYXRlLm5vdygpIC0gdGhpcy5fbGFzdFNlcnZlckFjdGl2aXR5VFM7XG4gICAgICAgICAgICAgICAgLy8gV2Ugd2FpdCB0d2ljZSB0aGUgVFRMIHRvIGJlIGZsZXhpYmxlIG9uIHdpbmRvdydzIHNldEludGVydmFsIGNhbGxzXG4gICAgICAgICAgICAgICAgaWYgKGRlbHRhID4gdHRsICogMikge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLmRlYnVnKGBkaWQgbm90IHJlY2VpdmUgc2VydmVyIGFjdGl2aXR5IGZvciB0aGUgbGFzdCAke2RlbHRhfW1zYCk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX2Nsb3NlT3JEaXNjYXJkV2Vic29ja2V0KCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSwgdHRsKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBfY2xvc2VPckRpc2NhcmRXZWJzb2NrZXQoKSB7XG4gICAgICAgIGlmICh0aGlzLmRpc2NhcmRXZWJzb2NrZXRPbkNvbW1GYWlsdXJlKSB7XG4gICAgICAgICAgICB0aGlzLmRlYnVnKCdEaXNjYXJkaW5nIHdlYnNvY2tldCwgdGhlIHVuZGVybHlpbmcgc29ja2V0IG1heSBsaW5nZXIgZm9yIGEgd2hpbGUnKTtcbiAgICAgICAgICAgIHRoaXMuZGlzY2FyZFdlYnNvY2tldCgpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5kZWJ1ZygnSXNzdWluZyBjbG9zZSBvbiB0aGUgd2Vic29ja2V0Jyk7XG4gICAgICAgICAgICB0aGlzLl9jbG9zZVdlYnNvY2tldCgpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGZvcmNlRGlzY29ubmVjdCgpIHtcbiAgICAgICAgaWYgKHRoaXMuX3dlYlNvY2tldCkge1xuICAgICAgICAgICAgaWYgKHRoaXMuX3dlYlNvY2tldC5yZWFkeVN0YXRlID09PSBTdG9tcFNvY2tldFN0YXRlLkNPTk5FQ1RJTkcgfHxcbiAgICAgICAgICAgICAgICB0aGlzLl93ZWJTb2NrZXQucmVhZHlTdGF0ZSA9PT0gU3RvbXBTb2NrZXRTdGF0ZS5PUEVOKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fY2xvc2VPckRpc2NhcmRXZWJzb2NrZXQoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBfY2xvc2VXZWJzb2NrZXQoKSB7XG4gICAgICAgIHRoaXMuX3dlYlNvY2tldC5vbm1lc3NhZ2UgPSAoKSA9PiB7IH07IC8vIGlnbm9yZSBtZXNzYWdlc1xuICAgICAgICB0aGlzLl93ZWJTb2NrZXQuY2xvc2UoKTtcbiAgICB9XG4gICAgZGlzY2FyZFdlYnNvY2tldCgpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB0aGlzLl93ZWJTb2NrZXQudGVybWluYXRlICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICBhdWdtZW50V2Vic29ja2V0KHRoaXMuX3dlYlNvY2tldCwgKG1zZykgPT4gdGhpcy5kZWJ1Zyhtc2cpKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBAdHMtaWdub3JlIC0gdGhpcyBtZXRob2Qgd2lsbCBiZSB0aGVyZSBhdCB0aGlzIHN0YWdlXG4gICAgICAgIHRoaXMuX3dlYlNvY2tldC50ZXJtaW5hdGUoKTtcbiAgICB9XG4gICAgX3RyYW5zbWl0KHBhcmFtcykge1xuICAgICAgICBjb25zdCB7IGNvbW1hbmQsIGhlYWRlcnMsIGJvZHksIGJpbmFyeUJvZHksIHNraXBDb250ZW50TGVuZ3RoSGVhZGVyIH0gPSBwYXJhbXM7XG4gICAgICAgIGNvbnN0IGZyYW1lID0gbmV3IEZyYW1lSW1wbCh7XG4gICAgICAgICAgICBjb21tYW5kLFxuICAgICAgICAgICAgaGVhZGVycyxcbiAgICAgICAgICAgIGJvZHksXG4gICAgICAgICAgICBiaW5hcnlCb2R5LFxuICAgICAgICAgICAgZXNjYXBlSGVhZGVyVmFsdWVzOiB0aGlzLl9lc2NhcGVIZWFkZXJWYWx1ZXMsXG4gICAgICAgICAgICBza2lwQ29udGVudExlbmd0aEhlYWRlcixcbiAgICAgICAgfSk7XG4gICAgICAgIGxldCByYXdDaHVuayA9IGZyYW1lLnNlcmlhbGl6ZSgpO1xuICAgICAgICBpZiAodGhpcy5sb2dSYXdDb21tdW5pY2F0aW9uKSB7XG4gICAgICAgICAgICB0aGlzLmRlYnVnKGA+Pj4gJHtyYXdDaHVua31gKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuZGVidWcoYD4+PiAke2ZyYW1lfWApO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLmZvcmNlQmluYXJ5V1NGcmFtZXMgJiYgdHlwZW9mIHJhd0NodW5rID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgcmF3Q2h1bmsgPSBuZXcgVGV4dEVuY29kZXIoKS5lbmNvZGUocmF3Q2h1bmspO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgcmF3Q2h1bmsgIT09ICdzdHJpbmcnIHx8ICF0aGlzLnNwbGl0TGFyZ2VGcmFtZXMpIHtcbiAgICAgICAgICAgIHRoaXMuX3dlYlNvY2tldC5zZW5kKHJhd0NodW5rKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGxldCBvdXQgPSByYXdDaHVuaztcbiAgICAgICAgICAgIHdoaWxlIChvdXQubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGNodW5rID0gb3V0LnN1YnN0cmluZygwLCB0aGlzLm1heFdlYlNvY2tldENodW5rU2l6ZSk7XG4gICAgICAgICAgICAgICAgb3V0ID0gb3V0LnN1YnN0cmluZyh0aGlzLm1heFdlYlNvY2tldENodW5rU2l6ZSk7XG4gICAgICAgICAgICAgICAgdGhpcy5fd2ViU29ja2V0LnNlbmQoY2h1bmspO1xuICAgICAgICAgICAgICAgIHRoaXMuZGVidWcoYGNodW5rIHNlbnQgPSAke2NodW5rLmxlbmd0aH0sIHJlbWFpbmluZyA9ICR7b3V0Lmxlbmd0aH1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBkaXNwb3NlKCkge1xuICAgICAgICBpZiAodGhpcy5jb25uZWN0ZWQpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgLy8gY2xvbmUgYmVmb3JlIHVwZGF0aW5nXG4gICAgICAgICAgICAgICAgY29uc3QgZGlzY29ubmVjdEhlYWRlcnMgPSBPYmplY3QuYXNzaWduKHt9LCB0aGlzLmRpc2Nvbm5lY3RIZWFkZXJzKTtcbiAgICAgICAgICAgICAgICBpZiAoIWRpc2Nvbm5lY3RIZWFkZXJzLnJlY2VpcHQpIHtcbiAgICAgICAgICAgICAgICAgICAgZGlzY29ubmVjdEhlYWRlcnMucmVjZWlwdCA9IGBjbG9zZS0ke3RoaXMuX2NvdW50ZXIrK31gO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLndhdGNoRm9yUmVjZWlwdChkaXNjb25uZWN0SGVhZGVycy5yZWNlaXB0LCBmcmFtZSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX2Nsb3NlV2Vic29ja2V0KCk7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX2NsZWFuVXAoKTtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5vbkRpc2Nvbm5lY3QoZnJhbWUpO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIHRoaXMuX3RyYW5zbWl0KHsgY29tbWFuZDogJ0RJU0NPTk5FQ1QnLCBoZWFkZXJzOiBkaXNjb25uZWN0SGVhZGVycyB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIHRoaXMuZGVidWcoYElnbm9yaW5nIGVycm9yIGR1cmluZyBkaXNjb25uZWN0ICR7ZXJyb3J9YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBpZiAodGhpcy5fd2ViU29ja2V0LnJlYWR5U3RhdGUgPT09IFN0b21wU29ja2V0U3RhdGUuQ09OTkVDVElORyB8fFxuICAgICAgICAgICAgICAgIHRoaXMuX3dlYlNvY2tldC5yZWFkeVN0YXRlID09PSBTdG9tcFNvY2tldFN0YXRlLk9QRU4pIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9jbG9zZVdlYnNvY2tldCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIF9jbGVhblVwKCkge1xuICAgICAgICB0aGlzLl9jb25uZWN0ZWQgPSBmYWxzZTtcbiAgICAgICAgaWYgKHRoaXMuX3Bpbmdlcikge1xuICAgICAgICAgICAgdGhpcy5fcGluZ2VyLnN0b3AoKTtcbiAgICAgICAgICAgIHRoaXMuX3BpbmdlciA9IHVuZGVmaW5lZDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5fcG9uZ2VyKSB7XG4gICAgICAgICAgICBjbGVhckludGVydmFsKHRoaXMuX3Bvbmdlcik7XG4gICAgICAgICAgICB0aGlzLl9wb25nZXIgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcHVibGlzaChwYXJhbXMpIHtcbiAgICAgICAgY29uc3QgeyBkZXN0aW5hdGlvbiwgaGVhZGVycywgYm9keSwgYmluYXJ5Qm9keSwgc2tpcENvbnRlbnRMZW5ndGhIZWFkZXIgfSA9IHBhcmFtcztcbiAgICAgICAgY29uc3QgaGRycyA9IE9iamVjdC5hc3NpZ24oeyBkZXN0aW5hdGlvbiB9LCBoZWFkZXJzKTtcbiAgICAgICAgdGhpcy5fdHJhbnNtaXQoe1xuICAgICAgICAgICAgY29tbWFuZDogJ1NFTkQnLFxuICAgICAgICAgICAgaGVhZGVyczogaGRycyxcbiAgICAgICAgICAgIGJvZHksXG4gICAgICAgICAgICBiaW5hcnlCb2R5LFxuICAgICAgICAgICAgc2tpcENvbnRlbnRMZW5ndGhIZWFkZXIsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICB3YXRjaEZvclJlY2VpcHQocmVjZWlwdElkLCBjYWxsYmFjaykge1xuICAgICAgICB0aGlzLl9yZWNlaXB0V2F0Y2hlcnNbcmVjZWlwdElkXSA9IGNhbGxiYWNrO1xuICAgIH1cbiAgICBzdWJzY3JpYmUoZGVzdGluYXRpb24sIGNhbGxiYWNrLCBoZWFkZXJzID0ge30pIHtcbiAgICAgICAgaGVhZGVycyA9IE9iamVjdC5hc3NpZ24oe30sIGhlYWRlcnMpO1xuICAgICAgICBpZiAoIWhlYWRlcnMuaWQpIHtcbiAgICAgICAgICAgIGhlYWRlcnMuaWQgPSBgc3ViLSR7dGhpcy5fY291bnRlcisrfWA7XG4gICAgICAgIH1cbiAgICAgICAgaGVhZGVycy5kZXN0aW5hdGlvbiA9IGRlc3RpbmF0aW9uO1xuICAgICAgICB0aGlzLl9zdWJzY3JpcHRpb25zW2hlYWRlcnMuaWRdID0gY2FsbGJhY2s7XG4gICAgICAgIHRoaXMuX3RyYW5zbWl0KHsgY29tbWFuZDogJ1NVQlNDUklCRScsIGhlYWRlcnMgfSk7XG4gICAgICAgIGNvbnN0IGNsaWVudCA9IHRoaXM7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBpZDogaGVhZGVycy5pZCxcbiAgICAgICAgICAgIHVuc3Vic2NyaWJlKGhkcnMpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gY2xpZW50LnVuc3Vic2NyaWJlKGhlYWRlcnMuaWQsIGhkcnMpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgdW5zdWJzY3JpYmUoaWQsIGhlYWRlcnMgPSB7fSkge1xuICAgICAgICBoZWFkZXJzID0gT2JqZWN0LmFzc2lnbih7fSwgaGVhZGVycyk7XG4gICAgICAgIGRlbGV0ZSB0aGlzLl9zdWJzY3JpcHRpb25zW2lkXTtcbiAgICAgICAgaGVhZGVycy5pZCA9IGlkO1xuICAgICAgICB0aGlzLl90cmFuc21pdCh7IGNvbW1hbmQ6ICdVTlNVQlNDUklCRScsIGhlYWRlcnMgfSk7XG4gICAgfVxuICAgIGJlZ2luKHRyYW5zYWN0aW9uSWQpIHtcbiAgICAgICAgY29uc3QgdHhJZCA9IHRyYW5zYWN0aW9uSWQgfHwgYHR4LSR7dGhpcy5fY291bnRlcisrfWA7XG4gICAgICAgIHRoaXMuX3RyYW5zbWl0KHtcbiAgICAgICAgICAgIGNvbW1hbmQ6ICdCRUdJTicsXG4gICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgdHJhbnNhY3Rpb246IHR4SWQsXG4gICAgICAgICAgICB9LFxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgY2xpZW50ID0gdGhpcztcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGlkOiB0eElkLFxuICAgICAgICAgICAgY29tbWl0KCkge1xuICAgICAgICAgICAgICAgIGNsaWVudC5jb21taXQodHhJZCk7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgYWJvcnQoKSB7XG4gICAgICAgICAgICAgICAgY2xpZW50LmFib3J0KHR4SWQpO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgY29tbWl0KHRyYW5zYWN0aW9uSWQpIHtcbiAgICAgICAgdGhpcy5fdHJhbnNtaXQoe1xuICAgICAgICAgICAgY29tbWFuZDogJ0NPTU1JVCcsXG4gICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgdHJhbnNhY3Rpb246IHRyYW5zYWN0aW9uSWQsXG4gICAgICAgICAgICB9LFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgYWJvcnQodHJhbnNhY3Rpb25JZCkge1xuICAgICAgICB0aGlzLl90cmFuc21pdCh7XG4gICAgICAgICAgICBjb21tYW5kOiAnQUJPUlQnLFxuICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgIHRyYW5zYWN0aW9uOiB0cmFuc2FjdGlvbklkLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIGFjayhtZXNzYWdlSWQsIHN1YnNjcmlwdGlvbklkLCBoZWFkZXJzID0ge30pIHtcbiAgICAgICAgaGVhZGVycyA9IE9iamVjdC5hc3NpZ24oe30sIGhlYWRlcnMpO1xuICAgICAgICBpZiAodGhpcy5fY29ubmVjdGVkVmVyc2lvbiA9PT0gVmVyc2lvbnMuVjFfMikge1xuICAgICAgICAgICAgaGVhZGVycy5pZCA9IG1lc3NhZ2VJZDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGhlYWRlcnNbJ21lc3NhZ2UtaWQnXSA9IG1lc3NhZ2VJZDtcbiAgICAgICAgfVxuICAgICAgICBoZWFkZXJzLnN1YnNjcmlwdGlvbiA9IHN1YnNjcmlwdGlvbklkO1xuICAgICAgICB0aGlzLl90cmFuc21pdCh7IGNvbW1hbmQ6ICdBQ0snLCBoZWFkZXJzIH0pO1xuICAgIH1cbiAgICBuYWNrKG1lc3NhZ2VJZCwgc3Vic2NyaXB0aW9uSWQsIGhlYWRlcnMgPSB7fSkge1xuICAgICAgICBoZWFkZXJzID0gT2JqZWN0LmFzc2lnbih7fSwgaGVhZGVycyk7XG4gICAgICAgIGlmICh0aGlzLl9jb25uZWN0ZWRWZXJzaW9uID09PSBWZXJzaW9ucy5WMV8yKSB7XG4gICAgICAgICAgICBoZWFkZXJzLmlkID0gbWVzc2FnZUlkO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaGVhZGVyc1snbWVzc2FnZS1pZCddID0gbWVzc2FnZUlkO1xuICAgICAgICB9XG4gICAgICAgIGhlYWRlcnMuc3Vic2NyaXB0aW9uID0gc3Vic2NyaXB0aW9uSWQ7XG4gICAgICAgIHJldHVybiB0aGlzLl90cmFuc21pdCh7IGNvbW1hbmQ6ICdOQUNLJywgaGVhZGVycyB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdG9tcC1oYW5kbGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stomp/stompjs/esm6/stomp-handler.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stomp/stompjs/esm6/ticker.js":
/*!****************************************************!*\
  !*** ./node_modules/@stomp/stompjs/esm6/ticker.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ticker: () => (/* binding */ Ticker)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types.js */ \"(ssr)/./node_modules/@stomp/stompjs/esm6/types.js\");\n\nclass Ticker {\n    constructor(_interval, _strategy = _types_js__WEBPACK_IMPORTED_MODULE_0__.TickerStrategy.Interval, _debug) {\n        this._interval = _interval;\n        this._strategy = _strategy;\n        this._debug = _debug;\n        this._workerScript = `\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `;\n    }\n    start(tick) {\n        this.stop();\n        if (this.shouldUseWorker()) {\n            this.runWorker(tick);\n        }\n        else {\n            this.runInterval(tick);\n        }\n    }\n    stop() {\n        this.disposeWorker();\n        this.disposeInterval();\n    }\n    shouldUseWorker() {\n        return typeof (Worker) !== 'undefined' && this._strategy === _types_js__WEBPACK_IMPORTED_MODULE_0__.TickerStrategy.Worker;\n    }\n    runWorker(tick) {\n        this._debug('Using runWorker for outgoing pings');\n        if (!this._worker) {\n            this._worker = new Worker(URL.createObjectURL(new Blob([this._workerScript], { type: 'text/javascript' })));\n            this._worker.onmessage = (message) => tick(message.data);\n        }\n    }\n    runInterval(tick) {\n        this._debug('Using runInterval for outgoing pings');\n        if (!this._timer) {\n            const startTime = Date.now();\n            this._timer = setInterval(() => {\n                tick(Date.now() - startTime);\n            }, this._interval);\n        }\n    }\n    disposeWorker() {\n        if (this._worker) {\n            this._worker.terminate();\n            delete this._worker;\n            this._debug('Outgoing ping disposeWorker');\n        }\n    }\n    disposeInterval() {\n        if (this._timer) {\n            clearInterval(this._timer);\n            delete this._timer;\n            this._debug('Outgoing ping disposeInterval');\n        }\n    }\n}\n//# sourceMappingURL=ticker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stomp/stompjs/esm6/ticker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stomp/stompjs/esm6/types.js":
/*!***************************************************!*\
  !*** ./node_modules/@stomp/stompjs/esm6/types.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActivationState: () => (/* binding */ ActivationState),\n/* harmony export */   ReconnectionTimeMode: () => (/* binding */ ReconnectionTimeMode),\n/* harmony export */   StompSocketState: () => (/* binding */ StompSocketState),\n/* harmony export */   TickerStrategy: () => (/* binding */ TickerStrategy)\n/* harmony export */ });\n/**\n * Possible states for the IStompSocket\n */\nvar StompSocketState;\n(function (StompSocketState) {\n    StompSocketState[StompSocketState[\"CONNECTING\"] = 0] = \"CONNECTING\";\n    StompSocketState[StompSocketState[\"OPEN\"] = 1] = \"OPEN\";\n    StompSocketState[StompSocketState[\"CLOSING\"] = 2] = \"CLOSING\";\n    StompSocketState[StompSocketState[\"CLOSED\"] = 3] = \"CLOSED\";\n})(StompSocketState || (StompSocketState = {}));\n/**\n * Possible activation state\n */\nvar ActivationState;\n(function (ActivationState) {\n    ActivationState[ActivationState[\"ACTIVE\"] = 0] = \"ACTIVE\";\n    ActivationState[ActivationState[\"DEACTIVATING\"] = 1] = \"DEACTIVATING\";\n    ActivationState[ActivationState[\"INACTIVE\"] = 2] = \"INACTIVE\";\n})(ActivationState || (ActivationState = {}));\n/**\n * Possible reconnection wait time modes\n */\nvar ReconnectionTimeMode;\n(function (ReconnectionTimeMode) {\n    ReconnectionTimeMode[ReconnectionTimeMode[\"LINEAR\"] = 0] = \"LINEAR\";\n    ReconnectionTimeMode[ReconnectionTimeMode[\"EXPONENTIAL\"] = 1] = \"EXPONENTIAL\";\n})(ReconnectionTimeMode || (ReconnectionTimeMode = {}));\n/**\n * Possible ticker strategies for outgoing heartbeat ping\n */\nvar TickerStrategy;\n(function (TickerStrategy) {\n    TickerStrategy[\"Interval\"] = \"interval\";\n    TickerStrategy[\"Worker\"] = \"worker\";\n})(TickerStrategy || (TickerStrategy = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stomp/stompjs/esm6/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@stomp/stompjs/esm6/versions.js":
/*!******************************************************!*\
  !*** ./node_modules/@stomp/stompjs/esm6/versions.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Versions: () => (/* binding */ Versions)\n/* harmony export */ });\n/**\n * Supported STOMP versions\n *\n * Part of `@stomp/stompjs`.\n */\nclass Versions {\n    /**\n     * Takes an array of versions, typical elements '1.2', '1.1', or '1.0'\n     *\n     * You will be creating an instance of this class if you want to override\n     * supported versions to be declared during STOMP handshake.\n     */\n    constructor(versions) {\n        this.versions = versions;\n    }\n    /**\n     * Used as part of CONNECT STOMP Frame\n     */\n    supportedVersions() {\n        return this.versions.join(',');\n    }\n    /**\n     * Used while creating a WebSocket\n     */\n    protocolVersions() {\n        return this.versions.map(x => `v${x.replace('.', '')}.stomp`);\n    }\n}\n/**\n * Indicates protocol version 1.0\n */\nVersions.V1_0 = '1.0';\n/**\n * Indicates protocol version 1.1\n */\nVersions.V1_1 = '1.1';\n/**\n * Indicates protocol version 1.2\n */\nVersions.V1_2 = '1.2';\n/**\n * @internal\n */\nVersions.default = new Versions([\n    Versions.V1_2,\n    Versions.V1_1,\n    Versions.V1_0,\n]);\n//# sourceMappingURL=versions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@stomp/stompjs/esm6/versions.js\n");

/***/ })

};
;