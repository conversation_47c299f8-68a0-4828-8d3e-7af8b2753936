import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import {
  Users,
  Shield,
  FileText,
  Key,
  ArrowRight,
  Building2,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

export const metadata: Metadata = {
  title: "User Management - Spidex",
  description: "Manage users, roles, permissions, and system access",
};

export default function UserManagementPage() {
  const managementSections = [
    {
      title: "Account Management",
      description: "Manage user accounts, profiles, and basic information",
      icon: Users,
      href: "/user-management/account",
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Tenant Management",
      description: "Manage tenant organizations, types, and settings",
      icon: Building2,
      href: "/user-management/tenant",
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
    },
    {
      title: "Role Management",
      description: "Create and manage user roles with specific permissions",
      icon: Shield,
      href: "/user-management/roles",
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Permission Management",
      description: "Configure role-page permissions and access control",
      icon: Key,
      href: "/user-management/permissions",
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "Page Management",
      description: "Manage system pages and their access requirements",
      icon: FileText,
      href: "/user-management/pages",
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
  ];

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>User Management</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Users className="h-8 w-8" />
          User Management
        </h1>
        <p className="text-muted-foreground text-lg">
          Comprehensive user, role, and permission management for your
          organization
        </p>
      </div>

      {/* Management Sections Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {managementSections.map((section) => {
          const IconComponent = section.icon;
          return (
            <Card
              key={section.href}
              className="hover:shadow-lg transition-shadow"
            >
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${section.bgColor}`}>
                    <IconComponent className={`h-6 w-6 ${section.color}`} />
                  </div>
                  <div>
                    <CardTitle className="text-xl">{section.title}</CardTitle>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <CardDescription className="text-base">
                  {section.description}
                </CardDescription>
                <Link href={section.href}>
                  <Button className="w-full group">
                    Access {section.title}
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Stats or Additional Info */}
      <Card>
        <CardHeader>
          <CardTitle>User Management Overview</CardTitle>
          <CardDescription>
            Manage all aspects of user access and permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <Users className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <h3 className="font-semibold">Accounts</h3>
              <p className="text-sm text-muted-foreground">
                User profiles & info
              </p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Building2 className="h-8 w-8 mx-auto mb-2 text-indigo-600" />
              <h3 className="font-semibold">Tenants</h3>
              <p className="text-sm text-muted-foreground">
                Organization management
              </p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Shield className="h-8 w-8 mx-auto mb-2 text-green-600" />
              <h3 className="font-semibold">Roles</h3>
              <p className="text-sm text-muted-foreground">
                User role definitions
              </p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <Key className="h-8 w-8 mx-auto mb-2 text-purple-600" />
              <h3 className="font-semibold">Permissions</h3>
              <p className="text-sm text-muted-foreground">
                Access control matrix
              </p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <FileText className="h-8 w-8 mx-auto mb-2 text-orange-600" />
              <h3 className="font-semibold">Pages</h3>
              <p className="text-sm text-muted-foreground">
                System page registry
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
