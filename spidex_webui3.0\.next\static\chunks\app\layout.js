/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./globals.css":
/*!*********************!*\
  !*** ./globals.css ***!
  \*********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"283f49b5fe03\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMjgzZjQ5YjVmZTAzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./globals.css */ \"(app-pages-browser)/./globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(app-pages-browser)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    cancelIdleCallback: function() {\n        return cancelIdleCallback;\n    },\n    requestIdleCallback: function() {\n        return requestIdleCallback;\n    }\n});\nconst requestIdleCallback = typeof self !== 'undefined' && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return self.setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nconst cancelIdleCallback = typeof self !== 'undefined' && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=request-idle-callback.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/script.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/client/script.js ***!
  \*************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    handleClientScriptLoad: function() {\n        return handleClientScriptLoad;\n    },\n    initScriptLoader: function() {\n        return initScriptLoader;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _setattributesfromprops = __webpack_require__(/*! ./set-attributes-from-props */ \"(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\");\nconst ScriptCache = new Map();\nconst LoadCache = new Set();\nconst insertStylesheets = (stylesheets)=>{\n    // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n    //\n    // Using ReactDOM.preinit to feature detect appDir and inject styles\n    // Stylesheets might have already been loaded if initialized with Script component\n    // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n    // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n    if (_reactdom.default.preinit) {\n        stylesheets.forEach((stylesheet)=>{\n            _reactdom.default.preinit(stylesheet, {\n                as: 'style'\n            });\n        });\n        return;\n    }\n    // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n    //\n    // We use this function to load styles when appdir is not detected\n    // TODO: Use React float APIs to load styles once available for pages dir\n    if (true) {\n        let head = document.head;\n        stylesheets.forEach((stylesheet)=>{\n            let link = document.createElement('link');\n            link.type = 'text/css';\n            link.rel = 'stylesheet';\n            link.href = stylesheet;\n            head.appendChild(link);\n        });\n    }\n};\nconst loadScript = (props)=>{\n    const { src, id, onLoad = ()=>{}, onReady = null, dangerouslySetInnerHTML, children = '', strategy = 'afterInteractive', onError, stylesheets } = props;\n    const cacheKey = id || src;\n    // Script has already loaded\n    if (cacheKey && LoadCache.has(cacheKey)) {\n        return;\n    }\n    // Contents of this script are already loading/loaded\n    if (ScriptCache.has(src)) {\n        LoadCache.add(cacheKey);\n        // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n        // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n        ScriptCache.get(src).then(onLoad, onError);\n        return;\n    }\n    /** Execute after the script first loaded */ const afterLoad = ()=>{\n        // Run onReady for the first time after load event\n        if (onReady) {\n            onReady();\n        }\n        // add cacheKey to LoadCache when load successfully\n        LoadCache.add(cacheKey);\n    };\n    const el = document.createElement('script');\n    const loadPromise = new Promise((resolve, reject)=>{\n        el.addEventListener('load', function(e) {\n            resolve();\n            if (onLoad) {\n                onLoad.call(this, e);\n            }\n            afterLoad();\n        });\n        el.addEventListener('error', function(e) {\n            reject(e);\n        });\n    }).catch(function(e) {\n        if (onError) {\n            onError(e);\n        }\n    });\n    if (dangerouslySetInnerHTML) {\n        // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n        el.innerHTML = dangerouslySetInnerHTML.__html || '';\n        afterLoad();\n    } else if (children) {\n        el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n        afterLoad();\n    } else if (src) {\n        el.src = src;\n        // do not add cacheKey into LoadCache for remote script here\n        // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n        ScriptCache.set(src, loadPromise);\n    }\n    (0, _setattributesfromprops.setAttributesFromProps)(el, props);\n    if (strategy === 'worker') {\n        el.setAttribute('type', 'text/partytown');\n    }\n    el.setAttribute('data-nscript', strategy);\n    // Load styles associated with this script\n    if (stylesheets) {\n        insertStylesheets(stylesheets);\n    }\n    document.body.appendChild(el);\n};\nfunction handleClientScriptLoad(props) {\n    const { strategy = 'afterInteractive' } = props;\n    if (strategy === 'lazyOnload') {\n        window.addEventListener('load', ()=>{\n            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n        });\n    } else {\n        loadScript(props);\n    }\n}\nfunction loadLazyScript(props) {\n    if (document.readyState === 'complete') {\n        (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n    } else {\n        window.addEventListener('load', ()=>{\n            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n        });\n    }\n}\nfunction addBeforeInteractiveToCache() {\n    const scripts = [\n        ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n        ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]')\n    ];\n    scripts.forEach((script)=>{\n        const cacheKey = script.id || script.getAttribute('src');\n        LoadCache.add(cacheKey);\n    });\n}\nfunction initScriptLoader(scriptLoaderItems) {\n    scriptLoaderItems.forEach(handleClientScriptLoad);\n    addBeforeInteractiveToCache();\n}\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */ function Script(props) {\n    const { id, src = '', onLoad = ()=>{}, onReady = null, strategy = 'afterInteractive', onError, stylesheets, ...restProps } = props;\n    // Context is available only during SSR\n    const { updateScripts, scripts, getIsSsr, appDir, nonce } = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */ const hasOnReadyEffectCalled = (0, _react.useRef)(false);\n    (0, _react.useEffect)(()=>{\n        const cacheKey = id || src;\n        if (!hasOnReadyEffectCalled.current) {\n            // Run onReady if script has loaded before but component is re-mounted\n            if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n                onReady();\n            }\n            hasOnReadyEffectCalled.current = true;\n        }\n    }, [\n        onReady,\n        id,\n        src\n    ]);\n    const hasLoadScriptEffectCalled = (0, _react.useRef)(false);\n    (0, _react.useEffect)(()=>{\n        if (!hasLoadScriptEffectCalled.current) {\n            if (strategy === 'afterInteractive') {\n                loadScript(props);\n            } else if (strategy === 'lazyOnload') {\n                loadLazyScript(props);\n            }\n            hasLoadScriptEffectCalled.current = true;\n        }\n    }, [\n        props,\n        strategy\n    ]);\n    if (strategy === 'beforeInteractive' || strategy === 'worker') {\n        if (updateScripts) {\n            scripts[strategy] = (scripts[strategy] || []).concat([\n                {\n                    id,\n                    src,\n                    onLoad,\n                    onReady,\n                    onError,\n                    ...restProps\n                }\n            ]);\n            updateScripts(scripts);\n        } else if (getIsSsr && getIsSsr()) {\n            // Script has already loaded during SSR\n            LoadCache.add(id || src);\n        } else if (getIsSsr && !getIsSsr()) {\n            loadScript(props);\n        }\n    }\n    // For the app directory, we need React Float to preload these scripts.\n    if (appDir) {\n        // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n        // For other strategies injecting here ensures correct stylesheet order\n        // ReactDOM.preinit handles loading the styles in the correct order,\n        // also ensures the stylesheet is loaded only once and in a consistent manner\n        //\n        // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n        // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n        // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n        // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n        if (stylesheets) {\n            stylesheets.forEach((styleSrc)=>{\n                _reactdom.default.preinit(styleSrc, {\n                    as: 'style'\n                });\n            });\n        }\n        // Before interactive scripts need to be loaded by Next.js' runtime instead\n        // of native <script> tags, because they no longer have `defer`.\n        if (strategy === 'beforeInteractive') {\n            if (!src) {\n                // For inlined scripts, we put the content in `children`.\n                if (restProps.dangerouslySetInnerHTML) {\n                    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n                    restProps.children = restProps.dangerouslySetInnerHTML.__html;\n                    delete restProps.dangerouslySetInnerHTML;\n                }\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: \"(self.__next_s=self.__next_s||[]).push(\" + JSON.stringify([\n                            0,\n                            {\n                                ...restProps,\n                                id\n                            }\n                        ]) + \")\"\n                    }\n                });\n            } else {\n                // @ts-ignore\n                _reactdom.default.preload(src, restProps.integrity ? {\n                    as: 'script',\n                    integrity: restProps.integrity,\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                } : {\n                    as: 'script',\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                });\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: \"(self.__next_s=self.__next_s||[]).push(\" + JSON.stringify([\n                            src,\n                            {\n                                ...restProps,\n                                id\n                            }\n                        ]) + \")\"\n                    }\n                });\n            }\n        } else if (strategy === 'afterInteractive') {\n            if (src) {\n                // @ts-ignore\n                _reactdom.default.preload(src, restProps.integrity ? {\n                    as: 'script',\n                    integrity: restProps.integrity,\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                } : {\n                    as: 'script',\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                });\n            }\n        }\n    }\n    return null;\n}\n_c = Script;\nObject.defineProperty(Script, '__nextScript', {\n    value: true\n});\nconst _default = Script;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=script.js.map\nvar _c;\n$RefreshReg$(_c, \"Script\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/script.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/set-attributes-from-props.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"setAttributesFromProps\", ({\n    enumerable: true,\n    get: function() {\n        return setAttributesFromProps;\n    }\n}));\nconst DOMAttributeNames = {\n    acceptCharset: 'accept-charset',\n    className: 'class',\n    htmlFor: 'for',\n    httpEquiv: 'http-equiv',\n    noModule: 'noModule'\n};\nconst ignoreProps = [\n    'onLoad',\n    'onReady',\n    'dangerouslySetInnerHTML',\n    'children',\n    'onError',\n    'strategy',\n    'stylesheets'\n];\nfunction isBooleanScriptAttribute(attr) {\n    return [\n        'async',\n        'defer',\n        'noModule'\n    ].includes(attr);\n}\nfunction setAttributesFromProps(el, props) {\n    for (const [p, value] of Object.entries(props)){\n        if (!props.hasOwnProperty(p)) continue;\n        if (ignoreProps.includes(p)) continue;\n        // we don't render undefined props to the DOM\n        if (value === undefined) {\n            continue;\n        }\n        const attr = DOMAttributeNames[p] || p.toLowerCase();\n        if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {\n            // Correctly assign boolean script attributes\n            // https://github.com/vercel/next.js/pull/20748\n            ;\n            el[attr] = !!value;\n        } else {\n            el.setAttribute(attr, String(value));\n        }\n        // Remove falsy non-zero boolean attributes so they are correctly interpreted\n        // (e.g. if we set them to false, this coerces to the string \"false\", which the browser interprets as true)\n        if (value === false || el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr) && (!value || value === 'false')) {\n            // Call setAttribute before, as we need to set and unset the attribute to override force async:\n            // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async\n            el.setAttribute(attr, '');\n            el.removeAttribute(attr);\n        }\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=set-attributes-from-props.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={529:(e,r,t)=>{var n=t(191);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},191:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(529);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \*********************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Inter', 'Inter Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\"};\n    if(true) {\n      // 1752136571003\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwXFxcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiSW50ZXJcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiaW50ZXJcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyw4REFBOEQ7QUFDekYsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQThJLGNBQWMsc0RBQXNEO0FBQ2hQLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aXNcXERvY3VtZW50c1xcUHJvamVjdHNcXFNwaWRleFxcc3BpZGV4X3dlYnVpMy4wXFxub2RlX21vZHVsZXNcXG5leHRcXGZvbnRcXGdvb2dsZVxcdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJhcHBcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiSW50ZXJcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiaW50ZXJcIn18YXBwLXBhZ2VzLWJyb3dzZXIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ0ludGVyJywgJ0ludGVyIEZhbGxiYWNrJ1wiLFwiZm9udFN0eWxlXCI6XCJub3JtYWxcIn0sXCJjbGFzc05hbWVcIjpcIl9fY2xhc3NOYW1lX2U4Y2UwY1wifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzUyMTM2NTcxMDAzXG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkM6L1VzZXJzL3JhdmlzL0RvY3VtZW50cy9Qcm9qZWN0cy9TcGlkZXgvc3BpZGV4X3dlYnVpMy4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravis%5C%5CDocuments%5C%5CProjects%5C%5CSpidex%5C%5Cspidex_webui3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);