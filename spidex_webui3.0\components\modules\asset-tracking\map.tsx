"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import Map, {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  NavigationControl,
  ScaleControl,
} from "react-map-gl";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  MapPin,
  Search,
  Filter,
  Layers,
  Maximize2,
  Minimize2,
  Battery,
  Wifi,
  WifiOff,
  Thermometer,
  Droplets,
  Signal,
  Eye,
} from "lucide-react";
import { useSpidexAssetTracking } from "@/hooks/use-spidex-asset-tracking";
import { Asset, MapAsset, SensorType } from "@/types/asset-tracking";
import { MAP_CONFIG } from "@/lib/constants/asset-tracking";
import { useRouter } from "next/navigation";

const SOCKET_URL = process.env.NEXT_PUBLIC_SPIDEX_SOCKET_URI;
const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MapboxAccessToken || "";

export default function AssetTrackingMapPage() {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);

  const [viewState, setViewState] = useState<any>({
    longitude: MAP_CONFIG.DEFAULT_CENTER.longitude,
    latitude: MAP_CONFIG.DEFAULT_CENTER.latitude,
    zoom: MAP_CONFIG.DEFAULT_ZOOM,
  });

  const [selectedAsset, setSelectedAsset] = useState<MapAsset | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showOfflineAssets, setShowOfflineAssets] = useState(true);
  const [showLowBatteryOnly, setShowLowBatteryOnly] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const [showControlsInFullscreen, setShowControlsInFullscreen] =
    useState(true);
  const [showSearchDropdown, setShowSearchDropdown] = useState(false);
  const [selectedSearchAssets, setSelectedSearchAssets] = useState<string[]>(
    []
  );
  const [gatewayFilter, setGatewayFilter] = useState<
    "transit" | "fixed" | "all"
  >("transit"); // Default to transit
  const [debugLogs, setDebugLogs] = useState<
    Array<{
      id: string;
      timestamp: string;
      type: "GPS" | "ASSET";
      name: string;
      gatewayId: string;
      latitude: number;
      longitude: number;
      previousLat?: number;
      previousLng?: number;
    }>
  >([]);
  const mapRef = useRef<any>(null);
  const searchRef = useRef<HTMLInputElement>(null);
  const previousGpsData = useRef<Record<string, any>>({});
  const previousAssetLocations = useRef<
    Record<string, { lat: number; lng: number }>
  >({});
  const logIdCounter = useRef<number>(0);

  const {
    gateways,
    currentTableData,
    sensorData,
    gpsData,
    selectedGateway,
    selectedLocation,
    isLoading,
    isSocketConnected,
    selectGateway,
  } = useSpidexAssetTracking({
    socketUrl: SOCKET_URL,
    autoConnect: true,
  });

  // Filter gateways based on category type
  const filteredGateways = React.useMemo(() => {
    if (gatewayFilter === "all") return gateways;
    return gateways.filter((gateway) => gateway.categoryType === gatewayFilter);
  }, [gateways, gatewayFilter]);

  // Set mounted state to prevent hydration issues
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Generate unique ID for debug logs (client-side only)
  const generateUniqueId = (prefix: string) => {
    if (!isMounted) return `${prefix}-temp`; // Temporary ID for SSR
    logIdCounter.current += 1;
    return `${prefix}-${logIdCounter.current}-${performance
      .now()
      .toString(36)}`;
  };

  // Auto-fit to assets when gateway filter changes
  React.useEffect(() => {
    if (!isMounted) return;
    // Small delay to ensure mapAssets are updated after filter change
    const timer = setTimeout(() => {
      fitToAssets();
    }, 100);

    return () => clearTimeout(timer);
  }, [gatewayFilter, isMounted]); // Trigger when gateway filter changes

  // GPS data is automatically loaded via WebSocket subscription in the hook
  // No manual loading needed

  // Track GPS data changes for debug logging
  React.useEffect(() => {
    if (!isMounted) return;

    Object.entries(gpsData).forEach(([gatewayId, gpsMessage]) => {
      if (
        gpsMessage &&
        gpsMessage.attributeName === "gps" &&
        gpsMessage.attributeValues
      ) {
        const currentLat = parseFloat(gpsMessage.attributeValues[0]);
        const currentLng = parseFloat(gpsMessage.attributeValues[1]);

        const previousData = previousGpsData.current[gatewayId];

        // Check if coordinates have changed
        if (
          !previousData ||
          previousData.lat !== currentLat ||
          previousData.lng !== currentLng
        ) {
          const gateway = gateways.find(
            (g) => g.id === gatewayId || g.id === gpsMessage.sourceId
          );

          if (gateway && !isNaN(currentLat) && !isNaN(currentLng)) {
            const newLog = {
              id: generateUniqueId(`gps-${gatewayId}`),
              timestamp: new Date().toLocaleTimeString(),
              type: "GPS" as const,
              name: gateway.name,
              gatewayId: gatewayId,
              latitude: currentLat,
              longitude: currentLng,
              previousLat: previousData?.lat,
              previousLng: previousData?.lng,
            };

            console.log("Adding GPS debug log:", newLog);
            setDebugLogs((prev) => [newLog, ...prev.slice(0, 19)]); // Keep last 20 logs

            // Update previous data
            previousGpsData.current[gatewayId] = {
              lat: currentLat,
              lng: currentLng,
            };
          }
        }
      }
    });
  }, [gpsData, gateways, isMounted]);

  // Log initial GPS data when component loads
  React.useEffect(() => {
    if (!isMounted || Object.keys(gpsData).length === 0) return;

    // Add initial GPS data to debug log
    Object.entries(gpsData).forEach(([gatewayId, gpsMessage]) => {
      if (gpsMessage?.attributeName === "gps" && gpsMessage?.attributeValues) {
        const gateway = gateways.find(
          (g) => g.id === gatewayId || g.id === gpsMessage.sourceId
        );
        if (gateway) {
          const initialLog = {
            id: generateUniqueId(`initial-gps-${gatewayId}`),
            timestamp: new Date().toLocaleTimeString(),
            type: "GPS" as const,
            name: gateway.name,
            gatewayId: gatewayId,
            latitude: parseFloat(gpsMessage.attributeValues[0]),
            longitude: parseFloat(gpsMessage.attributeValues[1]),
          };
          setDebugLogs((prev) => [initialLog, ...prev.slice(0, 19)]);
        }
      }
    });
  }, [isMounted, gpsData, gateways]); // Run when mounted and data changes

  // Define marker color function before it's used
  const getMarkerColor = (asset: MapAsset) => {
    // Priority 1: Offline status (red for all types)
    if (!asset.isOnline) {
      return "#ef4444"; // red
    }

    // Priority 2: Low battery (amber for all types)
    if ((asset.battery || 100) < 20) {
      return "#f59e0b"; // amber
    }

    // Priority 3: Gateway type based colors for online assets
    switch (asset.gatewayType) {
      case "transit":
        return "#3b82f6"; // blue for transit assets
      case "fixed":
        return "#10b981"; // green for fixed assets
      default:
        return "#6b7280"; // gray for unknown type
    }
  };

  // Convert assets to map assets with location data
  const mapAssets: MapAsset[] = React.useMemo(() => {
    // Only get assets from filtered gateways
    const filteredTableData: Record<string, Asset[]> = {};
    filteredGateways.forEach((gateway) => {
      if (currentTableData[gateway.id]) {
        filteredTableData[gateway.id] = currentTableData[gateway.id];
      }
    });

    const allAssets = Object.values(filteredTableData).flat() as Asset[];

    // Debug logging
    console.log("Map Assets Debug:", {
      filteredGateways: filteredGateways.length,
      transitGateways: filteredGateways.filter(
        (g) => g.categoryType === "transit"
      ).length,
      fixedGateways: filteredGateways.filter((g) => g.categoryType === "fixed")
        .length,
      allAssets: allAssets.length,
      gpsDataKeys: Object.keys(gpsData),
      currentTableDataKeys: Object.keys(currentTableData),
    });

    // Create map assets from actual assets
    const assetMapAssets = allAssets.map((asset) => {
      // Get location data from the asset's gateway or generate based on location
      let latitude: number = MAP_CONFIG.DEFAULT_CENTER.latitude;
      let longitude: number = MAP_CONFIG.DEFAULT_CENTER.longitude;

      // Priority 1: Try to get location from lintDtos attributeValues array
      // Check if asset has lintDtos with attributeValues containing lat/lng
      let lintLocation = null;
      if (asset.lintDtos && asset.lintDtos.length > 0) {
        // Find the most recent lintDto with attributeValues
        const lintWithLocation = asset.lintDtos
          .filter(
            (lint) => lint.attributeValues && lint.attributeValues.length >= 2
          )
          .sort((a, b) => b.lintTime - a.lintTime)[0]; // Most recent first

        if (lintWithLocation) {
          try {
            const lat = parseFloat(lintWithLocation.attributeValues![0]);
            const lng = parseFloat(lintWithLocation.attributeValues![1]);

            // Validate coordinates
            if (
              !isNaN(lat) &&
              !isNaN(lng) &&
              lat >= -90 &&
              lat <= 90 &&
              lng >= -180 &&
              lng <= 180
            ) {
              lintLocation = { latitude: lat, longitude: lng };
            }
          } catch (error) {
            console.warn("Error parsing lintDtos coordinates:", error);
          }
        }
      }
      if (lintLocation) {
        latitude = lintLocation.latitude;
        longitude = lintLocation.longitude;
      }
      // Priority 2: Try to get location from the asset's source gateway
      else {
        const gateway = filteredGateways.find((g) => g.id === asset.sourceId);
        if (gateway) {
          // For transit gateways, try GPS data first
          // Check both gateway.id and any GPS data that might have this gateway as sourceId
          const gpsDataForGateway =
            gpsData[gateway.id] ||
            Object.values(gpsData).find((gps) => gps.sourceId === gateway.id);

          if (gateway.categoryType === "transit" && gpsDataForGateway) {
            const gps = gpsDataForGateway;

            // GPS data comes from WebSocket with attributeValues: [lat, lng]
            if (
              gps.attributeName === "gps" &&
              gps.attributeValues &&
              gps.attributeValues.length >= 2
            ) {
              const lat = parseFloat(gps.attributeValues[0]);
              const lng = parseFloat(gps.attributeValues[1]);

              // Validate coordinates
              if (
                !isNaN(lat) &&
                !isNaN(lng) &&
                lat >= -90 &&
                lat <= 90 &&
                lng >= -180 &&
                lng <= 180
              ) {
                latitude = lat;
                longitude = lng;
              }
            }
          }
          // For fixed gateways or if GPS data is not available, use xyzCoordinates
          else if (gateway.xyzCoordinates) {
            const coords = gateway.xyzCoordinates;
            if (coords.latitude && coords.longitude) {
              latitude = coords.latitude;
              longitude = coords.longitude;
            }
          }
        }
        // Priority 3: Use location-based positioning with some spread
        else if (selectedLocation) {
          // Use location-based positioning with some spread
          const locationGateways = gateways.filter(
            (g) => g.locId === selectedLocation.id
          );
          if (locationGateways.length > 0) {
            // Try to get average coordinates from gateways with coordinates
            const gatewaysWithCoords = locationGateways.filter(
              (g) =>
                g.xyzCoordinates &&
                g.xyzCoordinates.latitude &&
                g.xyzCoordinates.longitude
            );

            if (gatewaysWithCoords.length > 0) {
              const avgLat =
                gatewaysWithCoords.reduce(
                  (sum, g) => sum + g.xyzCoordinates!.latitude,
                  0
                ) / gatewaysWithCoords.length;
              const avgLng =
                gatewaysWithCoords.reduce(
                  (sum, g) => sum + g.xyzCoordinates!.longitude,
                  0
                ) / gatewaysWithCoords.length;

              // Use average coordinates without random offset to avoid hydration issues
              latitude = avgLat;
              longitude = avgLng;
            } else {
              // Fallback to default center
              latitude = MAP_CONFIG.DEFAULT_CENTER.latitude;
              longitude = MAP_CONFIG.DEFAULT_CENTER.longitude;
            }
          }
        } else {
          // Fallback: use default center
          latitude = MAP_CONFIG.DEFAULT_CENTER.latitude;
          longitude = MAP_CONFIG.DEFAULT_CENTER.longitude;
        }
      }

      // Determine online status based on recent activity
      const lastSeen = asset.lastSeen ? new Date(asset.lastSeen) : new Date();
      const now = new Date();
      const timeDiff = now.getTime() - lastSeen.getTime();
      const isOnline = timeDiff < 5 * 60 * 1000; // 5 minutes

      // Get sensor data for this asset
      const assetSensorData = sensorData[asset.deviceLogId] || {};

      // Get gateway information for this asset
      const assetGateway = filteredGateways.find(
        (g) => g.id === asset.sourceId
      );

      return {
        ...asset,
        location: {
          latitude,
          longitude,
          accuracy: 10, // Fixed value to avoid hydration mismatch
          timestamp: lastSeen.getTime(),
        },
        isOnline,
        lastUpdate: lastSeen.toISOString(),
        sensorData: assetSensorData,
        // Add gateway type for marker styling
        gatewayType: assetGateway?.categoryType || "unknown",
        // Add battery level from sensor data or default
        battery: assetSensorData[SensorType.BATTERY]?.value || 85,
        // Add RSSI from sensor data or default
        rssi: assetSensorData[SensorType.RSSI]?.value || -45,
      } as MapAsset;
    });

    // Create gateway markers for transit gateways (especially those with no assets)
    const transitGatewayMarkers: MapAsset[] = filteredGateways
      .filter((gateway) => gateway.categoryType === "transit")
      .map((gateway) => {
        // Get GPS location for this transit gateway
        const gpsDataForGateway =
          gpsData[gateway.id] ||
          Object.values(gpsData).find((gps) => gps.sourceId === gateway.id);

        let latitude: number = MAP_CONFIG.DEFAULT_CENTER.latitude;
        let longitude: number = MAP_CONFIG.DEFAULT_CENTER.longitude;

        if (
          gpsDataForGateway?.attributeName === "gps" &&
          gpsDataForGateway?.attributeValues
        ) {
          const lat = parseFloat(gpsDataForGateway.attributeValues[0]);
          const lng = parseFloat(gpsDataForGateway.attributeValues[1]);

          if (
            !isNaN(lat) &&
            !isNaN(lng) &&
            lat >= -90 &&
            lat <= 90 &&
            lng >= -180 &&
            lng <= 180
          ) {
            latitude = lat;
            longitude = lng;
          }
        }

        // Create a virtual asset representing the gateway
        return {
          id: `gateway-${gateway.id}`,
          deviceId: `gateway-${gateway.id}`,
          deviceLogId: `gateway-${gateway.id}`,
          devicePhyId: gateway.id,
          name: `${gateway.name} (Gateway)`,
          tagName: `${gateway.name} Gateway`,
          macId: gateway.id,
          sourceId: gateway.id,
          areaId: gateway.areaId,
          areaName: gateway.areaName,
          locId: gateway.locId,
          locName: gateway.locName,
          zone: gateway.areaName || "Unknown Zone",
          key: `gateway-${gateway.id}`,
          tgAssetLink: {
            id: `gateway-${gateway.id}`,
            taggedAssetInfo: {
              assetExternalId: gateway.id,
              tagExternalId: gateway.id,
              tagName: gateway.name,
              assetName: `${gateway.name} Gateway`,
              assetType: "Gateway",
            },
            deleted: false,
            createdBy: "system",
            createdDate: "2024-01-01T00:00:00.000Z", // Fixed timestamp to avoid hydration issues
            modifiedBy: "system",
            modifiedDate: "2024-01-01T00:00:00.000Z", // Fixed timestamp to avoid hydration issues
          },
          lastSeen: "2024-01-01T00:00:00.000Z", // Fixed timestamp to avoid hydration issues
          location: {
            latitude,
            longitude,
            accuracy: 10,
            timestamp: 1704067200000, // Fixed timestamp (2024-01-01)
          },
          isOnline: true, // Gateways are considered online if they have GPS data
          lastUpdate: "2024-01-01T00:00:00.000Z", // Fixed timestamp to avoid hydration issues
          gatewayType: "transit",
          battery: 100, // Gateways don't have battery issues
          rssi: -30, // Good signal for gateways
        } as MapAsset;
      });

    // Combine asset markers with transit gateway markers
    // Remove duplicates where we already have assets for transit gateways
    const existingTransitGatewayIds = new Set(
      assetMapAssets
        .filter((asset) => asset.gatewayType === "transit")
        .map((asset) => asset.sourceId)
    );

    const uniqueTransitGatewayMarkers = transitGatewayMarkers.filter(
      (gatewayMarker) => !existingTransitGatewayIds.has(gatewayMarker.sourceId)
    );

    const mapAssets = [...assetMapAssets, ...uniqueTransitGatewayMarkers];

    return mapAssets;
  }, [
    currentTableData,
    filteredGateways,
    selectedLocation,
    sensorData,
    gpsData,
  ]);

  // Track asset location changes for debug logging
  React.useEffect(() => {
    if (!isMounted) return;

    mapAssets.forEach((asset) => {
      if (asset.location.latitude && asset.location.longitude) {
        const assetKey = asset.deviceLogId;
        const currentLat = asset.location.latitude;
        const currentLng = asset.location.longitude;

        const previousData = previousAssetLocations.current[assetKey];

        // Check if coordinates have changed significantly (more than 0.0001 degrees ~ 10 meters)
        if (
          !previousData ||
          Math.abs(previousData.lat - currentLat) > 0.0001 ||
          Math.abs(previousData.lng - currentLng) > 0.0001
        ) {
          const newLog = {
            id: generateUniqueId(`asset-${assetKey}`),
            timestamp: new Date().toLocaleTimeString(),
            type: "ASSET" as const,
            name: asset.name,
            gatewayId: asset.sourceId || "unknown",
            latitude: currentLat,
            longitude: currentLng,
            previousLat: previousData?.lat,
            previousLng: previousData?.lng,
          };

          setDebugLogs((prev) => [newLog, ...prev.slice(0, 19)]); // Keep last 20 logs

          // Update previous data
          previousAssetLocations.current[assetKey] = {
            lat: currentLat,
            lng: currentLng,
          };
        }
      }
    });
  }, [mapAssets, isMounted]);

  // Filter assets for search suggestions
  const searchSuggestions = React.useMemo(() => {
    if (!searchTerm) return mapAssets;

    return mapAssets.filter(
      (asset) =>
        asset.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.macId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        asset.deviceId?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [mapAssets, searchTerm]);

  // Filter assets based on search and filters
  const filteredAssets = React.useMemo(() => {
    let filtered = mapAssets;

    // If specific assets are selected from search, show only those
    if (selectedSearchAssets.length > 0) {
      filtered = filtered.filter((asset) =>
        selectedSearchAssets.includes(asset.id)
      );
    }
    // Otherwise apply search filter
    else if (searchTerm) {
      filtered = filtered.filter(
        (asset) =>
          asset.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          asset.macId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          asset.deviceId?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Offline filter
    if (!showOfflineAssets) {
      filtered = filtered.filter((asset) => asset.isOnline);
    }

    // Low battery filter
    if (showLowBatteryOnly) {
      filtered = filtered.filter((asset) => (asset.battery || 100) < 20);
    }

    return filtered;
  }, [
    mapAssets,
    searchTerm,
    showOfflineAssets,
    showLowBatteryOnly,
    selectedSearchAssets,
  ]);

  const getMarkerSize = (asset: MapAsset) => {
    if (selectedAsset?.id === asset.id) return MAP_CONFIG.MARKER_SIZE.LARGE;
    if (!asset.isOnline) return MAP_CONFIG.MARKER_SIZE.SMALL;
    return MAP_CONFIG.MARKER_SIZE.MEDIUM;
  };

  const handleAssetClick = (asset: MapAsset) => {
    setSelectedAsset(asset);

    // Center map on selected asset
    setViewState((prev: any) => ({
      ...prev,
      longitude: asset.location.longitude,
      latitude: asset.location.latitude,
      zoom: Math.max(prev.zoom, 16),
    }));
  };

  const handleMapClick = useCallback(() => {
    setSelectedAsset(null);
  }, []);

  const handleSearchFocus = () => {
    setShowSearchDropdown(true);
  };

  const handleSearchBlur = () => {
    // Delay hiding dropdown to allow for clicks
    setTimeout(() => setShowSearchDropdown(false), 200);
  };

  const handleAssetSelect = (asset: MapAsset) => {
    setSelectedSearchAssets([asset.id]);
    setSearchTerm(asset.name || asset.deviceId || "");
    setShowSearchDropdown(false);

    // Center map on selected asset
    setViewState((prev: any) => ({
      ...prev,
      longitude: asset.location.longitude,
      latitude: asset.location.latitude,
      zoom: Math.max(prev.zoom, 16),
    }));
  };

  const clearSearch = () => {
    setSearchTerm("");
    setSelectedSearchAssets([]);
    setShowSearchDropdown(false);
  };

  const handleDeviceDetails = (asset: MapAsset) => {
    // Navigate to analytics page with device details
    router.push(`/asset-tracking/analytics?device=${asset.deviceId}`);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);

    // Trigger map resize after state change
    setTimeout(() => {
      if (mapRef.current) {
        mapRef.current.resize();
      }
    }, 100);
  };

  const fitToAssets = () => {
    // Use mapAssets instead of filteredAssets to get all visible assets based on gateway filter
    const assetsToFit = mapAssets.filter(
      (asset) =>
        asset.location.latitude &&
        asset.location.longitude &&
        // Exclude assets that are still at default center (no real location)
        !(
          asset.location.latitude === MAP_CONFIG.DEFAULT_CENTER.latitude &&
          asset.location.longitude === MAP_CONFIG.DEFAULT_CENTER.longitude
        )
    );

    if (assetsToFit.length === 0 || !mapRef.current) {
      return;
    }

    const bounds = assetsToFit.reduce(
      (bounds, asset) => {
        return [
          Math.min(bounds[0], asset.location.longitude),
          Math.min(bounds[1], asset.location.latitude),
          Math.max(bounds[2], asset.location.longitude),
          Math.max(bounds[3], asset.location.latitude),
        ];
      },
      [Infinity, Infinity, -Infinity, -Infinity]
    );

    mapRef.current.fitBounds(bounds, {
      padding: 50,
      duration: 1000,
    });
  };

  // Effect to handle map resize when fullscreen or controls visibility changes
  useEffect(() => {
    const resizeMap = () => {
      if (mapRef.current) {
        // Force map to recalculate dimensions
        mapRef.current.resize();
        // Trigger a second resize after a short delay to ensure proper sizing
        setTimeout(() => {
          if (mapRef.current) {
            mapRef.current.resize();
          }
        }, 100);
      }
    };

    // Multiple resize attempts to ensure proper sizing
    const timer1 = setTimeout(resizeMap, 50);
    const timer2 = setTimeout(resizeMap, 200);
    const timer3 = setTimeout(resizeMap, 500);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, [isFullscreen, showControlsInFullscreen]);

  // Effect to handle window resize and container size changes
  useEffect(() => {
    const handleResize = () => {
      if (mapRef.current) {
        mapRef.current.resize();
      }
    };

    window.addEventListener("resize", handleResize);

    // Also observe the map container for size changes
    let resizeObserver: ResizeObserver | null = null;

    if (mapRef.current && typeof ResizeObserver !== "undefined") {
      const mapContainer = mapRef.current.getContainer();
      if (mapContainer) {
        resizeObserver = new ResizeObserver(() => {
          handleResize();
        });
        resizeObserver.observe(mapContainer);
      }
    }

    return () => {
      window.removeEventListener("resize", handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, []);

  // Effect to handle clicking outside search dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setShowSearchDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Prevent hydration mismatch by not rendering on server
  if (!isMounted) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading map...</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading map data...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <style jsx global>{`
        .custom-popup .mapboxgl-popup-close-button {
          background: rgba(0, 0, 0, 0.8) !important;
          color: white !important;
          border-radius: 50% !important;
          width: 20px !important;
          height: 20px !important;
          font-size: 15px !important;
          font-weight: bold !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          right: 4px !important;
          top: 4px !important;
          border: 2px solid white !important;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
        }
        .custom-popup .mapboxgl-popup-close-button:hover {
          background: rgba(239, 68, 68, 0.9) !important;
          transform: scale(1.1) !important;
        }
      `}</style>
      <div
        className={`${
          isFullscreen
            ? "fixed inset-0 z-50 bg-background p-4"
            : "container mx-auto p-6"
        }`}
      >
        {/* Header */}
        <div
          className={`flex items-center justify-between ${
            isFullscreen ? "mb-4" : "mb-6"
          }`}
        >
          <div>
            <h1
              className={`${isFullscreen ? "text-2xl" : "text-3xl"} font-bold`}
            >
              Asset Map View
            </h1>
            {!isFullscreen && (
              <p className="text-muted-foreground">
                Real-time location tracking of assets across your facilities
              </p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={isSocketConnected ? "default" : "destructive"}>
              {isSocketConnected ? (
                <Wifi className="h-3 w-3 mr-1" />
              ) : (
                <WifiOff className="h-3 w-3 mr-1" />
              )}
              {isSocketConnected ? "Connected" : "Disconnected"}
            </Badge>
            {isFullscreen && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setShowControlsInFullscreen(!showControlsInFullscreen);
                  // Trigger immediate map resize
                  setTimeout(() => {
                    if (mapRef.current) {
                      mapRef.current.resize();
                    }
                  }, 50);
                }}
              >
                <Filter className="h-4 w-4" />
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={toggleFullscreen}>
              {isFullscreen ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Main Content Grid */}
        <div
          className={`grid ${
            isFullscreen ? "grid-cols-1" : "grid-cols-4"
          } gap-6 h-full`}
        >
          {/* Left Sidebar - Gateway Selection */}
          {!isFullscreen && (
            <div className="col-span-1 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Gateway Selection</CardTitle>
                  <CardDescription>
                    Select gateway type and specific gateways
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Gateway Type Filter */}
                  <div>
                    <span className="text-sm font-medium mb-2 block">
                      Gateway Type:
                    </span>
                    <div className="flex flex-col space-y-2">
                      <Button
                        variant={
                          gatewayFilter === "transit" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => setGatewayFilter("transit")}
                        className="justify-start"
                      >
                        Transit (
                        {
                          gateways.filter((g) => g.categoryType === "transit")
                            .length
                        }
                        )
                      </Button>
                      <Button
                        variant={
                          gatewayFilter === "fixed" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => setGatewayFilter("fixed")}
                        className="justify-start"
                      >
                        Fixed (
                        {
                          gateways.filter((g) => g.categoryType === "fixed")
                            .length
                        }
                        )
                      </Button>
                      <Button
                        variant={
                          gatewayFilter === "all" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => setGatewayFilter("all")}
                        className="justify-start"
                      >
                        All ({gateways.length})
                      </Button>
                    </div>
                  </div>

                  {/* Gateway List */}
                  <div>
                    <span className="text-sm font-medium mb-2 block">
                      Available Gateways ({filteredGateways.length})
                    </span>
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {filteredGateways.map((gateway) => (
                        <Card
                          key={gateway.id}
                          className={`cursor-pointer transition-colors hover:bg-accent ${
                            selectedGateway?.id === gateway.id
                              ? "ring-2 ring-primary"
                              : ""
                          }`}
                          onClick={() => selectGateway(gateway)}
                        >
                          <CardContent className="p-3">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <h4 className="font-medium text-sm">
                                    {gateway.name}
                                  </h4>
                                  <Badge variant="outline" className="text-xs">
                                    {gateway.categoryType?.toUpperCase() ||
                                      "UNKNOWN"}
                                  </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground">
                                  {currentTableData[gateway.id]?.length || 0}{" "}
                                  assets
                                </p>
                              </div>
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  gateway.active ||
                                  (gateway.categoryType === "transit" &&
                                    (gpsData[gateway.id] ||
                                      Object.values(gpsData).find(
                                        (gps) => gps.sourceId === gateway.id
                                      )))
                                    ? "bg-green-500"
                                    : "bg-red-500"
                                }`}
                              />
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Debug Log Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Debug Log</CardTitle>
                  <CardDescription className="text-xs">
                    Real-time GPS & Asset coordinate changes
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-3">
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {debugLogs.length === 0 ? (
                      <p className="text-xs text-muted-foreground text-center py-4">
                        No coordinate changes detected yet...
                      </p>
                    ) : (
                      debugLogs.map((log) => (
                        <div
                          key={log.id}
                          className="border rounded p-2 text-xs space-y-1"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  log.type === "GPS"
                                    ? "bg-blue-500"
                                    : "bg-green-500"
                                }`}
                              />
                              <span className="font-medium">{log.type}</span>
                              <span className="text-muted-foreground">
                                {log.timestamp}
                              </span>
                            </div>
                          </div>
                          <div>
                            <p className="font-medium">{log.name}</p>
                            <p className="text-muted-foreground">
                              Gateway: {log.gatewayId.slice(0, 8)}...
                            </p>
                          </div>
                          <div className="grid grid-cols-2 gap-1 text-xs">
                            <div>
                              <span className="text-muted-foreground">
                                New:
                              </span>
                              <p className="font-mono">
                                {log.latitude.toFixed(6)},{" "}
                                {log.longitude.toFixed(6)}
                              </p>
                            </div>
                            {log.previousLat && log.previousLng && (
                              <div>
                                <span className="text-muted-foreground">
                                  Prev:
                                </span>
                                <p className="font-mono">
                                  {log.previousLat.toFixed(6)},{" "}
                                  {log.previousLng.toFixed(6)}
                                </p>
                              </div>
                            )}
                          </div>
                          {log.previousLat && log.previousLng && (
                            <div className="text-xs text-muted-foreground">
                              Δ:{" "}
                              {Math.abs(log.latitude - log.previousLat).toFixed(
                                6
                              )}
                              ,{" "}
                              {Math.abs(
                                log.longitude - log.previousLng
                              ).toFixed(6)}
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                  {debugLogs.length > 0 && (
                    <div className="mt-2 pt-2 border-t flex justify-end">
                      <button
                        onClick={() => setDebugLogs([])}
                        className="text-xs text-muted-foreground hover:text-foreground"
                      >
                        Clear logs
                      </button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Right Content - Map and Controls */}
          <div
            className={`${
              isFullscreen ? "col-span-1" : "col-span-3"
            } space-y-4`}
          >
            {/* Controls */}
            {(!isFullscreen || showControlsInFullscreen) && (
              <Card>
                <CardContent className={isFullscreen ? "pt-4" : "pt-6"}>
                  <div className="flex flex-wrap items-center gap-4">
                    {/* Gateway Type Filter for Fullscreen */}
                    {isFullscreen && (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">Type:</span>
                        <Button
                          variant={
                            gatewayFilter === "transit" ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => setGatewayFilter("transit")}
                        >
                          Transit
                        </Button>
                        <Button
                          variant={
                            gatewayFilter === "fixed" ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => setGatewayFilter("fixed")}
                        >
                          Fixed
                        </Button>
                        <Button
                          variant={
                            gatewayFilter === "all" ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => setGatewayFilter("all")}
                        >
                          All
                        </Button>
                      </div>
                    )}

                    {/* Search */}
                    <div className="relative flex-1 min-w-64" ref={searchRef}>
                      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground z-10" />
                      <Input
                        placeholder="Search assets..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onFocus={handleSearchFocus}
                        onBlur={handleSearchBlur}
                        className="pl-10"
                      />
                      {selectedSearchAssets.length > 0 && (
                        <button
                          onClick={clearSearch}
                          className="absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-foreground z-10"
                        >
                          ×
                        </button>
                      )}

                      {/* Search Dropdown */}
                      {showSearchDropdown && (
                        <div className="absolute top-full left-0 right-0 mt-1 bg-background border border-border rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
                          {(searchTerm ? searchSuggestions : mapAssets)
                            .length === 0 ? (
                            <div className="p-3 text-sm text-muted-foreground">
                              No assets found
                            </div>
                          ) : (
                            (searchTerm ? searchSuggestions : mapAssets).map(
                              (asset) => (
                                <div
                                  key={asset.id}
                                  onClick={() => handleAssetSelect(asset)}
                                  className="p-3 hover:bg-accent cursor-pointer border-b border-border last:border-b-0"
                                >
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <div className="font-medium text-sm">
                                        {asset.name || asset.deviceId}
                                      </div>
                                      <div className="text-xs text-muted-foreground">
                                        {asset.macId} •{" "}
                                        {asset.areaName || "Unknown Area"}
                                      </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <div
                                        className="w-2 h-2 rounded-full"
                                        style={{
                                          backgroundColor:
                                            getMarkerColor(asset),
                                        }}
                                      />
                                      <span className="text-xs text-muted-foreground">
                                        {asset.isOnline ? "Online" : "Offline"}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              )
                            )
                          )}
                        </div>
                      )}
                    </div>

                    {/* Filters */}
                    <div className="flex items-center space-x-2">
                      <Button
                        variant={showOfflineAssets ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowOfflineAssets(!showOfflineAssets)}
                      >
                        Show Offline
                      </Button>
                      <Button
                        variant={showLowBatteryOnly ? "default" : "outline"}
                        size="sm"
                        onClick={() =>
                          setShowLowBatteryOnly(!showLowBatteryOnly)
                        }
                      >
                        <Battery className="h-4 w-4 mr-1" />
                        Low Battery
                      </Button>
                      <Button variant="outline" size="sm" onClick={fitToAssets}>
                        <Layers className="h-4 w-4 mr-1" />
                        Fit to Assets
                      </Button>
                    </div>
                  </div>

                  {/* Stats */}
                  <div
                    className={`flex items-center space-x-6 ${
                      isFullscreen ? "mt-2" : "mt-4"
                    } text-sm text-muted-foreground`}
                  >
                    <span>Total: {mapAssets.length}</span>
                    <span>
                      Online: {mapAssets.filter((a) => a.isOnline).length}
                    </span>
                    <span>
                      Offline: {mapAssets.filter((a) => !a.isOnline).length}
                    </span>
                    <span>
                      Low Battery:{" "}
                      {mapAssets.filter((a) => (a.battery || 100) < 20).length}
                    </span>
                    <span>Showing: {filteredAssets.length}</span>
                  </div>

                  {/* Marker Legend */}
                  <div
                    className={`flex items-center space-x-4 ${
                      isFullscreen ? "mt-2" : "mt-2"
                    } text-xs text-muted-foreground`}
                  >
                    <span className="font-medium">Markers:</span>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <span>Transit</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span>Fixed</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <span>Offline</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-amber-500"></div>
                      <span>Low Battery</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Map */}
            <Card
              className={
                isFullscreen
                  ? showControlsInFullscreen
                    ? "h-[calc(100vh-120px)]"
                    : "h-[calc(100vh-80px)]"
                  : "h-[600px]"
              }
            >
              <CardContent className="p-0 h-full">
                <Map
                  ref={mapRef}
                  {...viewState}
                  onMove={(evt) => setViewState(evt.viewState)}
                  onClick={handleMapClick}
                  mapboxAccessToken={MAPBOX_TOKEN}
                  style={{ width: "100%", height: "100%" }}
                  mapStyle="mapbox://styles/mapbox/streets-v12"
                  minZoom={MAP_CONFIG.MIN_ZOOM}
                  maxZoom={MAP_CONFIG.MAX_ZOOM}
                >
                  {/* Navigation Controls */}
                  <NavigationControl position="top-right" />
                  <ScaleControl position="bottom-left" />

                  {/* Asset Markers */}
                  {filteredAssets.map((asset) => (
                    <Marker
                      key={asset.id}
                      longitude={asset.location.longitude}
                      latitude={asset.location.latitude}
                      onClick={(e) => {
                        e.originalEvent.stopPropagation();
                        handleAssetClick(asset);
                      }}
                    >
                      <div
                        className="cursor-pointer transition-transform hover:scale-110"
                        style={{
                          width: getMarkerSize(asset),
                          height: getMarkerSize(asset),
                        }}
                      >
                        <div
                          className="w-full h-full rounded-full border-2 border-white shadow-lg flex items-center justify-center"
                          style={{ backgroundColor: getMarkerColor(asset) }}
                        >
                          <MapPin className="h-4 w-4 text-white" />
                        </div>
                      </div>
                    </Marker>
                  ))}

                  {/* Asset Popup */}
                  {selectedAsset && (
                    <Popup
                      longitude={selectedAsset.location.longitude}
                      latitude={selectedAsset.location.latitude}
                      onClose={() => setSelectedAsset(null)}
                      closeButton={true}
                      closeOnClick={false}
                      anchor="left"
                      offset={[10, 0]}
                      className="custom-popup"
                    >
                      <div className="p-2 min-w-64 max-w-80">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold text-sm truncate pr-2">
                            {selectedAsset.name}
                          </h3>
                          <Badge
                            variant={
                              selectedAsset.isOnline ? "default" : "destructive"
                            }
                            className="text-xs px-2 py-0.5"
                          >
                            {selectedAsset.isOnline ? "Online" : "Offline"}
                          </Badge>
                        </div>

                        <div className="space-y-2 text-xs">
                          {/* Basic Info */}
                          {/* <div className="grid grid-cols-2 gap-2">
                          <div>
                            <span className="text-muted-foreground">
                              Device ID:
                            </span>
                            <p className="font-mono text-xs">
                              {selectedAsset.deviceId}
                            </p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">
                              MAC ID:
                            </span>
                            <p className="font-mono text-xs">
                              {selectedAsset.macId}
                            </p>
                          </div>
                        </div> */}

                          {/* Location Info */}
                          <div className="grid grid-cols-2 gap-1">
                            <div>
                              <span className="text-muted-foreground text-xs">
                                Zone:
                              </span>
                              <p className="font-medium text-xs truncate">
                                {selectedAsset.zone || selectedAsset.areaName}
                              </p>
                            </div>
                            <div>
                              <span className="text-muted-foreground text-xs">
                                Gateway Type:
                              </span>
                              <div className="flex items-center gap-1">
                                <div
                                  className="w-2 h-2 rounded-full"
                                  style={{
                                    backgroundColor:
                                      getMarkerColor(selectedAsset),
                                  }}
                                />
                                <span className="font-medium capitalize text-xs">
                                  {selectedAsset.gatewayType || "Unknown"}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Accuracy Info */}
                          <div className="grid grid-cols-2 gap-1">
                            <div>
                              <span className="text-muted-foreground text-xs">
                                Accuracy:
                              </span>
                              <p className="text-xs">
                                ±{selectedAsset.location.accuracy?.toFixed(1)}m
                              </p>
                            </div>
                            <div>
                              <span className="text-muted-foreground text-xs">
                                Coordinates:
                              </span>
                              <p className="text-xs font-mono">
                                {selectedAsset.location.latitude.toFixed(6)},{" "}
                                {selectedAsset.location.longitude.toFixed(6)}
                              </p>
                            </div>
                          </div>

                          {/* Sensor Data */}
                          <div className="grid grid-cols-2 gap-1">
                            {selectedAsset.battery && (
                              <div>
                                <span className="text-muted-foreground text-xs">
                                  Battery:
                                </span>
                                <div className="flex items-center gap-1">
                                  <Battery className="h-3 w-3" />
                                  <span
                                    className={`text-xs ${
                                      selectedAsset.battery < 20
                                        ? "text-red-500"
                                        : ""
                                    }`}
                                  >
                                    {selectedAsset.battery}%
                                  </span>
                                </div>
                              </div>
                            )}

                            {selectedAsset.rssi && (
                              <div>
                                <span className="text-muted-foreground text-xs">
                                  Signal:
                                </span>
                                <div className="flex items-center gap-1">
                                  <Signal className="h-3 w-3" />
                                  <span className="text-xs">
                                    {selectedAsset.rssi}dBm
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Temperature & Humidity if available */}
                          {selectedAsset.sensorData && (
                            <div className="grid grid-cols-2 gap-1">
                              {selectedAsset.sensorData[
                                SensorType.TEMPERATURE
                              ] && (
                                <div>
                                  <span className="text-muted-foreground text-xs">
                                    Temperature:
                                  </span>
                                  <div className="flex items-center gap-1">
                                    <Thermometer className="h-3 w-3" />
                                    <span className="text-xs">
                                      {
                                        selectedAsset.sensorData[
                                          SensorType.TEMPERATURE
                                        ].value
                                      }
                                      °C
                                    </span>
                                  </div>
                                </div>
                              )}

                              {selectedAsset.sensorData[
                                SensorType.HUMIDITY
                              ] && (
                                <div>
                                  <span className="text-muted-foreground text-xs">
                                    Humidity:
                                  </span>
                                  <div className="flex items-center gap-1">
                                    <Droplets className="h-3 w-3" />
                                    <span className="text-xs">
                                      {
                                        selectedAsset.sensorData[
                                          SensorType.HUMIDITY
                                        ].value
                                      }
                                      %
                                    </span>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}

                          <div className="flex justify-between items-center pt-1 border-t">
                            <div>
                              <span className="text-muted-foreground text-xs">
                                Last Update:
                              </span>
                              <p className="text-xs">
                                {new Date(
                                  selectedAsset.lastUpdate
                                ).toLocaleString()}
                              </p>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDeviceDetails(selectedAsset)}
                              className="h-6 px-2 text-xs"
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Popup>
                  )}
                </Map>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}

// Minimal, reusable Mapbox map view for timeline+map layout
export function MapboxMapView({
  markers,
  initialViewState,
  height = 400,
  width = "100%",
}: {
  markers: {
    id: string;
    latitude: number;
    longitude: number;
    color?: string;
  }[];
  initialViewState?: { latitude: number; longitude: number; zoom: number };
  height?: number | string;
  width?: number | string;
}) {
  const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MapboxAccessToken || "";
  return (
    <div style={{ width, height }}>
      <Map
        initialViewState={
          initialViewState || {
            latitude: 17.435784,
            longitude: 78.461947,
            zoom: 15,
          }
        }
        mapboxAccessToken={MAPBOX_TOKEN}
        style={{ width: "100%", height: "100%" }}
        mapStyle="mapbox://styles/mapbox/streets-v12"
        minZoom={10}
        maxZoom={20}
      >
        <NavigationControl position="top-right" />
        <ScaleControl position="bottom-left" />
        {markers.map((marker) => (
          <Marker
            key={marker.id}
            longitude={marker.longitude}
            latitude={marker.latitude}
            anchor="bottom"
          >
            <div
              className="w-4 h-4 rounded-full border-2 border-white shadow-lg"
              style={{ backgroundColor: marker.color || "#3b82f6" }}
            />
          </Marker>
        ))}
      </Map>
    </div>
  );
}
