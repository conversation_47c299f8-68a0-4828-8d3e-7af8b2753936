import React from "react";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import CountUp from "react-countup";

export default function MetricCard({
  icon,
  label,
  value,
  color,
  onClick,
  tooltip,
  active,
}: {
  icon: React.ReactNode;
  label: string;
  value: number;
  color: string;
  onClick?: () => void;
  tooltip?: string;
  active?: boolean;
}) {
  const colorMap: Record<
    string,
    {
      text: string;
      bg: string;
      bgActive: string;
      border: string;
      borderActive: string;
    }
  > = {
    green: {
      text: "text-green-800",
      bg: "bg-green-100",
      bgActive: "bg-green-200",
      border: "border-green-200",
      borderActive: "border-green-400",
    },
    red: {
      text: "text-red-800",
      bg: "bg-red-100",
      bgActive: "bg-red-200",
      border: "border-red-200",
      borderActive: "border-red-400",
    },
    yellow: {
      text: "text-yellow-800",
      bg: "bg-yellow-100",
      bgActive: "bg-yellow-200",
      border: "border-yellow-200",
      borderActive: "border-yellow-400",
    },
    blue: {
      text: "text-blue-800",
      bg: "bg-blue-100",
      bgActive: "bg-blue-200",
      border: "border-blue-200",
      borderActive: "border-blue-400",
    },
  };
  const style = colorMap[color] || colorMap.blue;
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <button
          className={`flex flex-col items-center justify-center rounded-lg border px-4 py-4 shadow-sm w-full transition
            ${
              active
                ? `${style.bgActive} ${style.borderActive} border-2 scale-105 shadow-md`
                : `${style.bg} ${style.border}`
            }
            hover:shadow-md
          `}
          onClick={onClick}
          aria-label={label}
          type="button"
        >
          <span className={`mb-2 ${style.text}`}>{icon}</span>
          <span className={`text-3xl font-bold ${style.text}`}>
            <CountUp end={value} duration={0.8} separator="," />
          </span>
          <span className="text-xs text-muted-foreground mt-1">{label}</span>
        </button>
      </TooltipTrigger>
      {tooltip && <TooltipContent>{tooltip}</TooltipContent>}
    </Tooltip>
  );
}
