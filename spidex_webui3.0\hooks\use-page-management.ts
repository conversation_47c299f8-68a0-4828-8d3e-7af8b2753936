"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  Page,
  CreatePageFormData,
  UpdatePageFormData,
  PageSearchFilters,
  RbacPaginationParams,
  RbacPageSize,
  DEFAULT_RBAC_PAGE_SIZE,
  RBAC_PAGE_SIZES,
} from "@/types/rbac";

interface UsePageManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: RbacPageSize;
}

export function usePageManagement(options: UsePageManagementOptions = {}) {
  const { autoLoad = true, defaultPageSize = DEFAULT_RBAC_PAGE_SIZE } = options;
  const { data: session } = useSession();
  const spidexApi = useSpidexApi();

  // Extract page key from pageName (e.g., "device management-configure-area" -> "AREA")
  const getPageKey = (pageName: string): string => {
    // Map of pageName patterns to page keys based on old application
    const pageNameMap: Record<string, string> = {
      "device management-configure-area": "AREA",
      "device management-configure-asset": "ASSET",
      "device management-configure-branch": "BRANCH",
      "device management-configure-gateway": "GATEWAY",
      "device management-configure-transit": "TRANSIT",
      "device management-configure-location": "LOCATION",
      "device management-configure-tag": "TAG",
      "device management-configure-taggedasset": "TAGGED_ASSET",
      "device management-onboard-onboard": "ONBOARD",
      "device configure-configure-eventattribute": "EVENT_ATTRIBUTE",
      "device configure-configure-gatewayconfig": "GATEWAY_CONFIG",
      "device configure-configure-tagconfig": "TAG_CONFIG",
      "user management-user-account": "ACCOUNT",
      "user management-user-tenant": "TENANT",
      "user management-user-rbac": "RBAC",
      "master-register-vendor": "VENDOR",
      "master-register-worker": "WORKER",
      "master-register-vehicle": "VEHICLE",
      "master-register-washroom": "WASHROOM",
      "master-register-ecu": "ECU",
      "master-register-vin": "VIN",
      "master-register-vehiclemodel": "VEHICLE_MODEL",
      "master-register-ecumodel": "ECU_MODEL",
    };

    // Return mapped value or extract last part as fallback
    if (pageNameMap[pageName]) {
      return pageNameMap[pageName];
    }

    // Fallback: extract the last part after the last dash and convert to uppercase
    const parts = pageName.split("-");
    const lastPart = parts[parts.length - 1];
    return lastPart.toUpperCase();
  };

  // State
  const [pages, setPages] = useState<Page[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);
  const [searchFilters, setSearchFilters] = useState<PageSearchFilters>({});
  const [pagination, setPagination] = useState<RbacPaginationParams>({
    page: 1,
    pageSize: defaultPageSize === "all" ? 1000 : defaultPageSize,
  });

  // Available page sizes
  const availablePageSizes = useMemo(() => RBAC_PAGE_SIZES, []);

  // Filter pages based on search criteria and deleted status
  const filteredPages = useMemo(() => {
    let filtered = pages.filter((page) => {
      if (!showDeleted && page.deleted) return false;
      if (showDeleted && !page.deleted) return false;
      return true;
    });

    // Apply search filters
    if (searchFilters.pageName) {
      const nameFilter = searchFilters.pageName.toLowerCase();
      filtered = filtered.filter((page) => {
        // Search in both the actual pageName and the display key
        const pageName = page.pageName.toLowerCase();
        const displayKey = getPageKey(page.pageName).toLowerCase();

        return pageName.includes(nameFilter) || displayKey.includes(nameFilter);
      });
    }

    return filtered;
  }, [pages, showDeleted, searchFilters]);

  // Paginated pages
  const paginatedPages = useMemo(() => {
    if (pagination.pageSize === 1000) {
      // Show all
      return filteredPages;
    }

    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredPages.slice(startIndex, endIndex);
  }, [filteredPages, pagination]);

  // Pagination calculations
  const totalRecords = filteredPages.length;
  const totalPages = Math.ceil(totalRecords / pagination.pageSize);
  const totalAllRecords = pages.length;
  const activeRecordsCount = pages.filter((page) => !page.deleted).length;
  const inactiveRecordsCount = pages.filter((page) => page.deleted).length;

  // Load pages data
  const loadData = useCallback(async () => {
    if (!session?.user?.token) {
      console.log("No session token available for loading pages");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("Loading pages data...");
      const pagesData = await spidexApi.getAllPages();
      console.log("Pages data loaded:", pagesData);
      setPages(pagesData || []);
    } catch (err) {
      console.error("Error loading pages:", err);
      setError(err instanceof Error ? err.message : "Failed to load pages");
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.token, spidexApi]);

  // Create page
  const createPage = useCallback(
    async (formData: CreatePageFormData): Promise<boolean> => {
      if (!session?.user?.token) {
        throw new Error("No authentication token available");
      }

      try {
        console.log("Creating page:", formData);

        const dateTimeNow = new Date().toISOString();
        const newPage = {
          pageName: formData.pageName,
          description: formData.description || "",
          deleted: false,
          createdTime: dateTimeNow,
          modifiedTime: dateTimeNow,
          createdBy: session.user.userId || "system",
          modifiedBy: session.user.userId || "system",
        };

        await spidexApi.createPage(newPage);
        await loadData();
        return true;
      } catch (err) {
        console.error("Error creating page:", err);
        throw err;
      }
    },
    [session?.user?.token, session?.user?.userId, spidexApi, loadData]
  );

  // Update page
  const updatePage = useCallback(
    async (formData: UpdatePageFormData): Promise<boolean> => {
      if (!session?.user?.token) {
        throw new Error("No authentication token available");
      }

      try {
        console.log("Updating page:", formData);

        // Note: The old API doesn't seem to have an update endpoint,
        // so we might need to implement this differently
        console.warn("Page update not implemented in API");
        await loadData();
        return true;
      } catch (err) {
        console.error("Error updating page:", err);
        throw err;
      }
    },
    [session?.user?.token, session?.user?.userId, spidexApi, loadData]
  );

  // Delete page
  const deletePage = useCallback(
    async (pageId: string): Promise<boolean> => {
      if (!session?.user?.token) {
        throw new Error("No authentication token available");
      }

      try {
        console.log("Deleting page:", pageId);
        await spidexApi.deletePage(pageId);
        await loadData();
        return true;
      } catch (err) {
        console.error("Error deleting page:", err);
        throw err;
      }
    },
    [session?.user?.token, spidexApi, loadData]
  );

  // Pagination functions
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  }, []);

  const changePageSize = useCallback((pageSize: RbacPageSize) => {
    setPagination({
      page: 1,
      pageSize: pageSize === "all" ? 1000 : pageSize,
    });
  }, []);

  // Search functions
  const updateSearchFilters = useCallback(
    (filters: Partial<PageSearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({});
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  // Auto-load data on mount (only on initial mount)
  useEffect(() => {
    if (autoLoad && session?.user?.token) {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad, session?.user?.token]); // Removed loadData dependency to prevent reloading on session changes

  return {
    // Data
    pages: paginatedPages,
    filteredPages,
    isLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    totalAllRecords,
    activeRecordsCount,
    inactiveRecordsCount,
    availablePageSizes,

    // Actions
    loadData,
    createPage,
    updatePage,
    deletePage,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
  };
}
