import React, { useState, useRef } from 'react';
import { Row, Col, message } from 'antd';
import { InboxOutlined, DownOutlined } from '@ant-design/icons';
import { ContextMenu, MenuItem, ContextMenuTrigger } from 'react-contextmenu';
import { Button, Select, Drawer, Upload, Menu, InputNumber, Popover, Space } from '../../../../components';
import { BreadcrumbList } from '../../../../shared';
import COLORS from '../../../../Colors';
import s from './index.module.less';

const { Option } = Select;
const { Dragger } = Upload;

const ACTIONS = {
  BLOCKED: { name: 'Blocked', backgroundColor: COLORS.RED_HEX, contextOnly: false },
  WALKWAY: { name: 'Walkway', backgroundColor: COLORS.DARK_GRAY_HEX, contextOnly: false },
  ENTRY_EXIT: { name: 'Entry / Exit', backgroundColor: COLORS.GREEN_HEX, contextOnly: false },
  OBJECT: { name: 'Object', backgroundColor: COLORS.WARNING_HEX, contextOnly: false },
  ERASE: { name: 'Erase', backgroundColor: COLORS.WHITE_HEX, contextOnly: false },
  RECT_START_CELL: { name: 'Rectangle Start', backgroundColor: COLORS.BLUE_HEX, contextOnly: true },
  RECT_END_CELL: { name: 'Rectangle End', backgroundColor: COLORS.BLUE_HEX, contextOnly: true },
};

const MapIndoorSetup = () => {
  const [currentSelector, setCurrentSelector] = useState(ACTIONS.BLOCKED.name);
  const [brushSelector, setBrushSelector] = useState('Big');
  const [userInputAttributes, setUserInputAttributes] = useState({
    areaMetric: 'sqft',
    offsets: { left: 0, right: 0, top: 0, bottom: 0 },
  });
  const [clickedAttributes, setClickedAttributes] = useState({});
  const [rectangleActionDetails, setRectangleActionDetails] = useState({ start: null, stop: null });
  const [helpModal, setHelpModal] = useState(false);
  const imageUploaded = useRef({ src: null, height: 0, width: 0, heightBlocks: 10, widthBlocks: 10 });
  const [isUploaded, setIsUploaded] = useState(false);
  const containerRef = useRef(null);
  const [gridState, setGridState] = useState(true);
  const borderStringCurry = (value, color = '#d2d2d2') => `${value}px solid ${color}`;
  const borderStringBig = borderStringCurry('0.6');
  const borderStringMedium = borderStringCurry('0.4', '#d8d8d8');
  const borderStringSmall = borderStringCurry('0.2', '#e4e4e4');

  const allowedImageTypes = ['image/png', 'image/webp', 'image/jpeg', 'image/jpg'];

  const processUpload = (e) => {
    if (!allowedImageTypes.includes(e.file.type)) {
      message.error("Only 'png', 'jpg', 'jpeg' and 'webp' images are allowed.");
      return false;
    }
    var reader = new FileReader();
    reader.readAsDataURL(e.file);
    reader.onload = function (x) {
      let img = new Image();
      img.src = x.target.result;
      img.onload = function () {
        const heightBlocks = Math.ceil(this.height / 60);
        const widthBlocks = Math.ceil(this.width / 60);
        imageUploaded.current = { src: img.src, height: this.height, width: this.width, heightBlocks, widthBlocks };
        setIsUploaded(true);
        return true;
      };
    };
  };

  const props = {
    action: null,
    name: 'file',
    multiple: false,
    showUploadList: false,
    previewFile: false,
    progress: { strokeWidth: 2, showInfo: false },

    beforeUpload: () => false,
    onChange(e) {
      processUpload(e);
    },
  };

  const hexToRgbA = (hex) => {
    let c;
    if (/^#([A-Fa-f0-9]{3}){1,2}$/.test(hex)) {
      c = hex.substring(1).split('');
      if (c.length === 3) {
        c = [c[0], c[0], c[1], c[1], c[2], c[2]];
      }
      c = '0x' + c.join('');
      return [(c >> 16) & 255, (c >> 8) & 255, c & 255].join(',');
    }
    throw new Error('Bad Hex');
  };

  const gridColor = (cell) => {
    switch (clickedAttributes[cell]) {
      case ACTIONS.BLOCKED.name:
        return hexToRgbA(ACTIONS.BLOCKED.backgroundColor);
      case ACTIONS.WALKWAY.name:
        return hexToRgbA(ACTIONS.WALKWAY.backgroundColor);
      case ACTIONS.ENTRY_EXIT.name:
        return hexToRgbA(ACTIONS.ENTRY_EXIT.backgroundColor);
      case ACTIONS.OBJECT.name:
        return hexToRgbA(ACTIONS.OBJECT.backgroundColor);
      case ACTIONS.RECT_START_CELL.name:
        return hexToRgbA(ACTIONS.RECT_START_CELL.backgroundColor);
      default:
        return hexToRgbA(COLORS.WHITE_HEX);
    }
  };

  const gridClass = (cell) => {
    switch (clickedAttributes[cell]) {
      case ACTIONS.RECT_START_CELL.name:
        return 'rectAnimation';
      default:
        return 'empty';
    }
  };

  const directions = ['tl', 'tr', 'bl', 'br'];

  const gridClick = (i, j, allowIfNotBig = false, type = null) => {
    if (brushSelector !== 'Big' && !allowIfNotBig) return;
    let cellMediumList = [];
    directions.forEach((direction) => {
      directions.forEach((smallDirection) => {
        cellMediumList.push(`${i}-${j}-${direction}-${smallDirection}`);
      });
    });
    if (currentSelector === ACTIONS.ERASE.name) {
      setClickedAttributes((ps) => {
        const temp = { ...ps };
        cellMediumList.forEach((cell) => {
          delete temp[cell];
        });
        return temp;
      });
      return;
    }

    if (rectangleActionDetails.start && type !== ACTIONS.RECT_END_CELL.name) {
      let cellMediumListRect = [];
      directions.forEach((direction) => {
        directions.forEach((smallDirection) => {
          cellMediumListRect.push(
            `${rectangleActionDetails.start.i}-${rectangleActionDetails.start.j}-${direction}-${smallDirection}`
          );
        });
      });
      setRectangleActionDetails({ start: null, stop: null });
      setClickedAttributes((ps) => {
        const temp = { ...ps };
        cellMediumListRect.forEach((cell) => {
          delete temp[cell];
        });
        return temp;
      });
      message.warn('Invalid Selection, try again.');
    }
    if (rectangleActionDetails.start && type === ACTIONS.RECT_END_CELL.name) {
      const rectStartI = parseInt(rectangleActionDetails.start.i);
      const rectStartJ = parseInt(rectangleActionDetails.start.j);
      const currentI = parseInt(i);
      const currentJ = parseInt(j);
      const lengthI =
        rectStartI < currentI ? Math.abs(rectStartI - (currentI + 1)) : Math.abs(currentI - (rectStartI + 1));
      const lengthJ =
        rectStartJ < currentJ ? Math.abs(rectStartJ - (currentJ + 1)) : Math.abs(currentJ - (rectStartJ + 1));
      let cellMediumListRectSelection = [];
      Array.from({ length: lengthI }, (_, iCell) => iCell).forEach((iCell) => {
        Array.from({ length: lengthJ }, (_, jCell) => jCell).forEach((jCell) => {
          const parityAddI = rectStartI < currentI ? iCell + rectStartI : iCell + currentI;
          const parityAddJ = rectStartJ < currentJ ? jCell + rectStartJ : jCell + currentJ;
          directions.forEach((direction) => {
            directions.forEach((smallDirection) => {
              cellMediumListRectSelection.push(`${parityAddI}-${parityAddJ}-${direction}-${smallDirection}`);
            });
          });
        });
      });
      setRectangleActionDetails({ start: null, stop: null });
      setClickedAttributes((ps) => {
        const temp = { ...ps };
        cellMediumListRectSelection.forEach((cell) => {
          temp[cell] = currentSelector;
        });
        return temp;
      });
    }
    if (type === ACTIONS.RECT_START_CELL.name) {
      setRectangleActionDetails({ start: { i, j } });
    }
    if (type !== ACTIONS.RECT_END_CELL.name) {
      setClickedAttributes((ps) => {
        const temp = { ...ps };
        cellMediumList.forEach((cell) => {
          temp[cell] = type || currentSelector;
        });
        return temp;
      });
    }
  };

  const gridClickMedium = (i, j, block) => {
    if (brushSelector !== 'Medium') return;
    let cellSmallList = [];
    directions.forEach((direction) => {
      cellSmallList.push(`${i}-${j}-${block}-${direction}`);
    });
    if (currentSelector === ACTIONS.ERASE.name) {
      setClickedAttributes((ps) => {
        const temp = { ...ps };
        cellSmallList.forEach((cell) => {
          delete temp[cell];
        });
        return temp;
      });
      return;
    }
    setClickedAttributes((ps) => {
      return {
        ...ps,
        [`${i}-${j}-${block}-tl`]: currentSelector,
        [`${i}-${j}-${block}-tr`]: currentSelector,
        [`${i}-${j}-${block}-bl`]: currentSelector,
        [`${i}-${j}-${block}-br`]: currentSelector,
      };
    });
  };

  const gridClickSmall = (i, j, blockMedium, blockSmall) => {
    if (brushSelector !== 'Small') return;
    const block = `${i}-${j}-${blockMedium}-${blockSmall}`;
    if (currentSelector === ACTIONS.ERASE.name) {
      setClickedAttributes((ps) => {
        const temp = { ...ps };
        delete temp[`${i}-${j}-${blockMedium}-${blockSmall}`];
        return temp;
      });
      return;
    }
    setClickedAttributes((ps) => {
      return {
        ...ps,
        [block]: currentSelector,
      };
    });
  };

  const handleClick = (_, i) => {
    const [iCell, jCell] = i.targetCell.split('-');
    gridClick(iCell, jCell, true, i.type);
  };

  const handleReset = () => {
    setClickedAttributes({});
  };

  const onSelectorChange = (e) => {
    setCurrentSelector(e);
  };

  const onBrushChange = (e) => {
    setBrushSelector(e);
  };

  const handleClose = () => {
    setClickedAttributes({});
    setIsUploaded(false);
  };

  const onUserInputAttributesArea = (e) => {
    setUserInputAttributes((ps) => ({ ...ps, areaMetric: e }));
  };

  const Block = ({ i, j, position, smallPosition = null, height = 30, ...props }) => {
    let block = `${i}-${j}-${position}`;
    let border = borderStringMedium;
    let fnClick = () => gridClickMedium(i, j, position);
    let pos = position;
    let bgColor = 'none';
    if (smallPosition) {
      block = `${i}-${j}-${position}-${smallPosition}`;
      border = borderStringSmall;
      pos = smallPosition;
      fnClick = () => gridClickSmall(i, j, position, smallPosition);
      bgColor = clickedAttributes[block] ? `rgba(${gridColor(block)}, 0.5)` : 'none';
    }
    let top = 0;
    let left = 0;
    if (pos === 'tr') left = height;
    if (pos === 'bl') top = height;
    if (pos === 'br') {
      top = height;
      left = height;
    }
    return (
      <div
        onClick={fnClick}
        style={{
          zIndex: 100,
          position: 'absolute',
          width: gridState ? height - 0.5 : height,
          height: gridState ? height - 0.5 : height,
          top: top,
          left: left,
          border: gridState ? border : 'none',
          backgroundColor: bgColor,
        }}
        className={`${s[gridClass(block)]}`}
      >
        {props.children}
      </div>
    );
  };

  const updateOffset = (e, direction) => {
    setUserInputAttributes((ps) => ({ ...ps, offsets: { ...ps.offsets, [direction]: e } }));
  };

  const offsetMenu = (
    <Menu>
      <Menu.Item key="left">
        <Space>
          <span style={{ color: '#000' }}>Left</span>
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            placeholder="Left"
            value={userInputAttributes.offsets.left}
            onChange={(e) => updateOffset(e, 'left')}
          />
        </Space>
      </Menu.Item>
      <Menu.Item key="right">
        <Space>
          <span style={{ color: '#000' }}>Right</span>
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            placeholder="Right"
            value={userInputAttributes.offsets.right}
            onChange={(e) => updateOffset(e, 'right')}
          />
        </Space>
      </Menu.Item>
      <Menu.Item key="top">
        <Space>
          <span style={{ color: '#000' }}>Top</span>
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            placeholder="Top"
            value={userInputAttributes.offsets.top}
            onChange={(e) => updateOffset(e, 'top')}
          />
        </Space>
      </Menu.Item>
      <Menu.Item key="bottom">
        <Space>
          <span style={{ color: '#000' }}>Bottom</span>
          <InputNumber
            style={{ width: '100%' }}
            min={0}
            placeholder="Bottom"
            value={userInputAttributes.offsets.bottom}
            onChange={(e) => updateOffset(e, 'bottom')}
          />
        </Space>
      </Menu.Item>
    </Menu>
  );

  const CommonOptions = () => {
    return (
      <Row>
        {isUploaded && (
          <Col>
            <Button type="primary" className="mr-2" disabled>
              Map it
            </Button>
          </Col>
        )}
        <Col>
          <Button disabled={true}>Control Panel</Button>
        </Col>
        {isUploaded && (
          <Col>
            <Button type="primary" danger className="ml-2" onClick={handleClose}>
              Close
            </Button>
          </Col>
        )}
        <Col>
          <Button className="mx-2" onClick={() => setHelpModal(true)}>
            Help
          </Button>
        </Col>
      </Row>
    );
  };

  if (!isUploaded) {
    return (
      <>
        <Row justify="space-between">
          <Col>
            <BreadcrumbList list={['Map Indoor', 'Setup']} />
          </Col>
        </Row>
        <Row justify="end">
          <CommonOptions />
        </Row>
        <Row className="mt-5" justify="center">
          <Col className="text-center">
            <h3 className="my-5">To get started upload.</h3>
            <Dragger {...props} className="p-2">
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">Click or drag file to this area to upload</p>
              <p className="ant-upload-hint">
                Supported formats: PNG, JPG, JPEG and WEBP. Use highest quality available for best experience.
              </p>
            </Dragger>
          </Col>
        </Row>
        <Drawer title="How to use this tool?" visible={helpModal} onClose={() => setHelpModal(false)}>
          <p>Coming Soon</p>
        </Drawer>
      </>
    );
  }

  return (
    <div className="pb-5">
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Map Indoor', 'Setup']} />
        </Col>
      </Row>
      <div>
        <ContextMenu
          id="context-manager"
          className="shadow-lg rounded-lg"
          style={{ zIndex: 300, backgroundColor: 'white' }}
          hideOnLeave={true}
        >
          <div>
            <div className="px-3 py-2">Brush Size: Big (Only)</div>
            {Object.keys(ACTIONS).map((actionKey) => (
              <MenuItem
                key={ACTIONS[actionKey].name}
                data={{ type: ACTIONS[actionKey].name, color: ACTIONS[actionKey].backgroundColor }}
                onClick={handleClick}
                className={`${s.menuItem}`}
              >
                <Row gutter={4} className="px-3 py-2">
                  <Col>
                    <div
                      style={{
                        backgroundColor: ACTIONS[actionKey].backgroundColor,
                        height: 20,
                        width: 20,
                        border: `1px solid ${COLORS.BLACK_HEX}`,
                      }}
                    />
                  </Col>
                  <Col>{ACTIONS[actionKey].name}</Col>
                </Row>
              </MenuItem>
            ))}
          </div>
        </ContextMenu>
        <Row justify="space-between" gutter={[0, 4]}>
          <Col>
            <Button onClick={() => setGridState(!gridState)}>Toggle Grid</Button>
            <Button className="ml-2" onClick={handleReset}>
              Reset
            </Button>
            <Select
              className="ml-2"
              allowClear={false}
              onChange={onSelectorChange}
              value={`Current Selector: ${currentSelector}`}
            >
              {Object.keys(ACTIONS).map(
                (action) =>
                  !ACTIONS[action].contextOnly && (
                    <Option key={ACTIONS[action].name} value={ACTIONS[action].name}>
                      <Row align="middle">
                        <Col>
                          <div
                            style={{
                              backgroundColor: ACTIONS[action].backgroundColor,
                              height: 20,
                              width: 20,
                              border: `1px solid ${COLORS.BLACK_HEX}`,
                            }}
                          ></div>
                        </Col>
                        <Col>
                          <span style={{ marginLeft: 10 }}>{ACTIONS[action].name}</span>
                        </Col>
                      </Row>
                    </Option>
                  )
              )}
            </Select>
            <Select
              style={{ width: 150 }}
              className="ml-2"
              allowClear={false}
              onChange={onBrushChange}
              value={`Brush: ${brushSelector}`}
            >
              <Option key="big" value="Big">
                <Row justify="space-between" align="middle">
                  <Col>
                    <span className="mr-2 center">Big</span>
                  </Col>
                  <Col>
                    <div style={{ display: 'inline-block', height: 30, width: 30, border: '1px solid black' }} />
                  </Col>
                </Row>
              </Option>
              <Option key="medium" value="Medium">
                <Row justify="space-between">
                  <Col>
                    <span className="mr-2 center">Medium</span>
                  </Col>
                  <Col>
                    <div style={{ display: 'inline-block', height: 15, width: 15, border: '1px solid black' }} />
                  </Col>
                </Row>
              </Option>
              <Option key="small" value="Small">
                <Row justify="space-between">
                  <Col>
                    <span className="mr-2 center">Small</span>
                  </Col>
                  <Col>
                    <div style={{ display: 'inline-block', height: 7.5, width: 7.5, border: '1px solid black' }} />
                  </Col>
                </Row>
              </Option>
            </Select>
            <Select
              style={{ width: 150 }}
              className="ml-2"
              allowClear={false}
              onChange={onUserInputAttributesArea}
              value={`Metric: ${userInputAttributes.areaMetric}`}
            >
              <Option value="sqft">Square Feet (Sqft.)</Option>
              <Option value="yard">Yard(s)</Option>
              <Option value="meter">Meter(s)</Option>
            </Select>
            <Popover
              className="ml-2"
              content={offsetMenu}
              style={{ width: 200 }}
              overlayStyle={{ width: 200 }}
              placement="bottom"
              trigger={['click']}
            >
              <Button>
                <Space>
                  Offsets
                  <DownOutlined />
                </Space>
              </Button>
            </Popover>
          </Col>
          <Col>
            <CommonOptions />
          </Col>
        </Row>
        <div
          style={{
            margin: 20,
            overflow: 'auto',
          }}
        >
          <div
            ref={containerRef}
            style={{
              height: imageUploaded.current.heightBlocks * 60,
              width: imageUploaded.current.widthBlocks * 60,
              position: 'relative',
            }}
          >
            {Array.from({ length: imageUploaded.current.heightBlocks }, (_, i) => i).map((i) =>
              Array.from({ length: imageUploaded.current.widthBlocks }, (_, j) => j).map((j) => (
                <ContextMenuTrigger
                  attributes={{ key: `${i}-${j}` }}
                  key={`${i}-${j}`}
                  id="context-manager"
                  style={{ zIndex: 200, backgroundColor: 'white' }}
                  collect={() => ({ targetCell: `${i}-${j}` })}
                >
                  <div
                    onClick={() => gridClick(i, j)}
                    key={`${i}-${j}`}
                    style={{
                      zIndex: 200,
                      position: 'absolute',
                      top: 60 * i,
                      left: 60 * j,
                      height: 60,
                      width: 60,
                      border: gridState ? borderStringBig : 'none',
                    }}
                  >
                    <Block i={i} j={j} position="tl">
                      <Block i={i} j={j} position="tl" smallPosition="tl" height={15} />
                      <Block i={i} j={j} position="tl" smallPosition="tr" height={15} />
                      <Block i={i} j={j} position="tl" smallPosition="bl" height={15} />
                      <Block i={i} j={j} position="tl" smallPosition="br" height={15} />
                    </Block>
                    <Block i={i} j={j} position="tr">
                      <Block i={i} j={j} position="tr" smallPosition="tl" height={15} />
                      <Block i={i} j={j} position="tr" smallPosition="tr" height={15} />
                      <Block i={i} j={j} position="tr" smallPosition="bl" height={15} />
                      <Block i={i} j={j} position="tr" smallPosition="br" height={15} />
                    </Block>
                    <Block i={i} j={j} position="bl">
                      <Block i={i} j={j} position="bl" smallPosition="tl" height={15} />
                      <Block i={i} j={j} position="bl" smallPosition="tr" height={15} />
                      <Block i={i} j={j} position="bl" smallPosition="bl" height={15} />
                      <Block i={i} j={j} position="bl" smallPosition="br" height={15} />
                    </Block>
                    <Block i={i} j={j} position="br">
                      <Block i={i} j={j} position="br" smallPosition="tl" height={15} />
                      <Block i={i} j={j} position="br" smallPosition="tr" height={15} />
                      <Block i={i} j={j} position="br" smallPosition="bl" height={15} />
                      <Block i={i} j={j} position="br" smallPosition="br" height={15} />
                    </Block>
                  </div>
                </ContextMenuTrigger>
              ))
            )}
            <div>
              <img
                alt=""
                src={imageUploaded.current.src}
                style={{
                  zIndex: 10,
                  top: 0,
                  left: 0,
                  position: 'absolute',
                  height: imageUploaded.current.height,
                  width: imageUploaded.current.width,
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <Drawer title="How to use this tool?" visible={helpModal} onClose={() => setHelpModal(false)}>
        <p>Coming Soon</p>
      </Drawer>
    </div>
  );
};

export default MapIndoorSetup;
