"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect } from "react";
import { Branch } from "@/types/branch";
import {
  CreateBranchSchema,
  UpdateBranchSchema,
  CreateBranchFormData,
  UpdateBranchFormData,
} from "@/lib/schemas";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MapPin, Loader2, Building } from "lucide-react";
import { GoogleMapModal } from "@/components/ui/google-map-modal";
import { MapIcon } from "@/components/ui/map-icon";

interface BranchFormProps {
  branch?: Branch;
  isLoading?: boolean;
  onSubmit: (
    data: CreateBranchFormData | UpdateBranchFormData
  ) => Promise<void>;
  onCancel: () => void;
  onClear?: () => void;
}

export function BranchForm({
  branch,
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: BranchFormProps) {
  const isEditing = !!branch;
  const schema = isEditing ? UpdateBranchSchema : CreateBranchSchema;
  const [isMapOpen, setIsMapOpen] = useState(false);

  const form = useForm<CreateBranchFormData | UpdateBranchFormData>({
    resolver: zodResolver(schema),
    defaultValues: isEditing
      ? {
          id: branch.id,
          name: branch.name,
          address: branch.address,
          gpsPoint: {
            latitude: branch.gpsPoint.latitude,
            longitude: branch.gpsPoint.longitude,
          },
          geoJson: branch.geoJson || "",
        }
      : {
          name: "",
          address: "",
          gpsPoint: {
            latitude: "",
            longitude: "",
          },
          geoJson: "",
        },
  });

  // Reset form when branch data changes (for editing)
  useEffect(() => {
    if (branch) {
      console.log("🔄 Resetting form with branch data:", branch);
      form.reset({
        id: branch.id,
        name: branch.name,
        address: branch.address,
        gpsPoint: {
          latitude: branch.gpsPoint.latitude,
          longitude: branch.gpsPoint.longitude,
        },
        geoJson: branch.geoJson || "",
      });
    } else {
      console.log("🆕 Resetting form for new branch");
      form.reset({
        name: "",
        address: "",
        gpsPoint: {
          latitude: "",
          longitude: "",
        },
        geoJson: "",
      });
    }
  }, [branch, form]);

  // Reset map modal state when component unmounts or branch changes
  useEffect(() => {
    return () => {
      setIsMapOpen(false);
    };
  }, [branch]);

  const handleSubmit = async (
    data: CreateBranchFormData | UpdateBranchFormData
  ) => {
    try {
      console.log("📝 Form submission data:", data);
      console.log("🔧 Is editing:", isEditing);
      await onSubmit(data);
      if (!isEditing) {
        form.reset();
      }
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  const handleClear = () => {
    form.reset();
    setIsMapOpen(false);
    if (onClear) {
      onClear();
    }
  };

  const handleCancel = () => {
    form.reset();
    setIsMapOpen(false);
    onCancel();
  };

  const handleLocationSelect = (location: {
    latitude: number;
    longitude: number;
    geoJson?: string;
  }) => {
    form.setValue("gpsPoint.latitude", location.latitude.toString());
    form.setValue("gpsPoint.longitude", location.longitude.toString());
    if (location.geoJson) {
      form.setValue("geoJson", location.geoJson);
    }
    setIsMapOpen(false);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardContent className="space-y-4 pt-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Branch Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="(1-200 characters)"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="(1-200 characters)"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Geo Input */}
            <FormField
              control={form.control}
              name="geoJson"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Geo</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="(1-2000 characters)"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex items-end gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1">
                <FormField
                  control={form.control}
                  name="gpsPoint.latitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Latitude</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="(-90 to 90)"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="gpsPoint.longitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Longitude</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="(-180 to 180)"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Map Icon Button */}
              <div className="flex-shrink-0">
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => setIsMapOpen(true)}
                  disabled={isLoading}
                  className="h-10 w-10"
                >
                  <MapIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
          <Button
            type="submit"
            disabled={isLoading}
            className="flex-1 sm:flex-none bg-green-700 text-white hover:bg-green-800 rounded"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? "Update Branch" : "Create Branch"}
          </Button>

          {!isEditing && (
            <Button
              type="button"
              variant="outline"
              onClick={handleClear}
              disabled={isLoading}
              className="flex-1 sm:flex-none border bg-white border-gray-700 text-black hover:bg-green-20 rounded"
            >
              Clear
            </Button>
          )}

          <Button
            type="button"
            variant="secondary"
            onClick={handleCancel}
            disabled={isLoading}
            className="border bg-white border-red-700 text-black hover:bg-green-20 rounded"
          >
            Close
          </Button>
        </div>
      </form>

      {/* Google Maps Modal */}
      <GoogleMapModal
        isOpen={isMapOpen}
        onClose={() => setIsMapOpen(false)}
        onLocationSelect={handleLocationSelect}
        initialLocation={{
          latitude:
            parseFloat(form.getValues("gpsPoint.latitude")) || undefined,
          longitude:
            parseFloat(form.getValues("gpsPoint.longitude")) || undefined,
        }}
      />
    </Form>
  );
}
