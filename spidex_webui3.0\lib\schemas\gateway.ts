import { z } from "zod";
import { CATEGORY_TYPES } from "@/types/gateway";

// MAC ID validation regex (matches old application pattern)
const MAC_ID_REGEX = /^[a-fA-F0-9:]+$/;

// XYZ Coordinates schema (matches old application structure)
const XyzCoordinatesSchema = z.object({
  additionalProp1: z.string().optional(),
  additionalProp2: z.string().optional(),
  additionalProp3: z.string().optional(),
});

// Antenna Configuration schema for RFID Fixed Reader
const AntennaConfigSchema = z.object({
  numericValue: z.number().optional(),
  name: z.string().optional(),
});

// Gateway Lint schema for lint type gateways
const GatewayLintSchema = z.object({
  areaId: z.string().min(1, "Area is required"),
  areaName: z.string().optional(),
  externalId: z.string().optional(),
  modelId: z.string().optional(),
  antenna: z.number().optional(),
  coverageMin: z.number().optional(),
  coverageMax: z.number().optional(),
  radioSelection: z.enum(["antenna", "macId"]).optional(),
  modelType: z.enum(["gatewayModel", "tagModel"]).optional(),
  xyzCoordinates: XyzCoordinatesSchema.optional(),
});

// Base gateway schema
const BaseGatewaySchema = z.object({
  name: z
    .string()
    .min(1, "Gateway name is required")
    .max(200, "Gateway name must be at most 200 characters")
    .trim(),
  description: z
    .string()
    .max(500, "Description must be at most 500 characters")
    .optional(),
  categoryType: z.enum([CATEGORY_TYPES.FIXED, CATEGORY_TYPES.TRANSIT, "lint"], {
    required_error: "Category type is required",
  }),
  modelId: z.string().min(1, "Model is required"),
  externalId: z
    .string()
    .min(1, "MAC ID is required")
    .max(24, "MAC ID must be at most 24 characters")
    .regex(MAC_ID_REGEX, "Enter a valid MAC ID"),
  xyzCoordinates: XyzCoordinatesSchema.optional(),
  coverageMin: z.number().min(0).optional(),
  coverageMax: z.number().min(0).optional(),
  communicationType: z.string().optional(),
  parentId: z.string().optional(),
  locationType: z.string().optional(),
});

// Base schema for create gateway (without validation)
const CreateGatewayBaseSchema = BaseGatewaySchema.omit({
  modelId: true,
}).extend({
  modelId: z.string().optional().or(z.literal("")),
  areaId: z.string().optional(),
  locId: z.string().optional(),
  gatewayLint: z.array(GatewayLintSchema).optional(),
  communicationType: z.string().default("BLE"),
  antenna: z.number().optional(),
  antennaConfigs: z.array(AntennaConfigSchema).optional(),
});

// Create Gateway Schema with conditional validation
export const CreateGatewaySchema = CreateGatewayBaseSchema.refine(
  (data) => {
    // ModelId is always required
    return !!data.modelId && data.modelId.trim() !== "";
  },
  {
    message: "Model is required",
    path: ["modelId"],
  }
)
  .refine(
    (data) => {
      // For fixed and lint gateways, areaId is required
      if (data.categoryType === "fixed" || data.categoryType === "lint") {
        return !!data.areaId;
      }
      // For transit gateways, locId is required
      if (data.categoryType === "transit") {
        return !!data.locId;
      }
      return true;
    },
    {
      message:
        "Area is required for fixed/lint gateways, Location is required for transit gateways",
      path: ["areaId"],
    }
  )
  .refine(
    (data) => {
      // For lint gateways, gatewayLint array is required
      if (data.categoryType === "lint") {
        return data.gatewayLint && data.gatewayLint.length > 0;
      }
      return true;
    },
    {
      message: "At least one lint configuration is required for lint gateways",
      path: ["gatewayLint"],
    }
  );

// Update Gateway Schema (extend the base schema, not the refined one)
export const UpdateGatewaySchema = CreateGatewayBaseSchema.extend({
  id: z.string().min(1, "Gateway ID is required"),
})
  .refine(
    (data) => {
      // ModelId is always required
      return !!data.modelId && data.modelId.trim() !== "";
    },
    {
      message: "Model is required",
      path: ["modelId"],
    }
  )
  .refine(
    (data) => {
      // For fixed and lint gateways, areaId is required
      if (data.categoryType === "fixed" || data.categoryType === "lint") {
        return !!data.areaId;
      }
      // For transit gateways, locId is required
      if (data.categoryType === "transit") {
        return !!data.locId;
      }
      return true;
    },
    {
      message:
        "Area is required for fixed/lint gateways, Location is required for transit gateways",
      path: ["areaId"],
    }
  )
  .refine(
    (data) => {
      // For lint gateways, gatewayLint array is required
      if (data.categoryType === "lint") {
        return data.gatewayLint && data.gatewayLint.length > 0;
      }
      return true;
    },
    {
      message: "At least one lint configuration is required for lint gateways",
      path: ["gatewayLint"],
    }
  );

// Search filters schema
export const GatewaySearchFiltersSchema = z.object({
  searchTerm: z.string().default(""),
  categoryType: z.string().optional(),
  areaId: z.string().optional(),
  locationId: z.string().optional(),
  modelId: z.string().optional(),
  active: z.boolean().optional(),
});

// Pagination schema
export const GatewayPaginationSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z
    .union([z.literal(10), z.literal(25), z.literal(50), z.literal(100)])
    .default(25),
});

// Export types
export type CreateGatewayFormData = z.infer<typeof CreateGatewaySchema>;
export type UpdateGatewayFormData = z.infer<typeof UpdateGatewaySchema>;
export type GatewaySearchFilters = z.infer<typeof GatewaySearchFiltersSchema>;
export type GatewayPaginationParams = z.infer<typeof GatewayPaginationSchema>;
