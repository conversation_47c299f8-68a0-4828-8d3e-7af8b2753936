"use client";

import { useState } from "react";
import { Plus, Tag as Tag<PERSON><PERSON>, <PERSON><PERSON><PERSON>, RefreshCw } from "lucide-react";
import { useSession } from "next-auth/react";
import { useTagManagement } from "@/hooks/use-tag-management";
import { getTableConfig } from "@/config/table-config";
import {
  Tag,
  CreateTagFormData,
  UpdateTagFormData,
  TagPageSize,
} from "@/types/tag";
import { TagDataTable } from "./tag-data-table";
import { TagSearchPagination } from "./tag-search-pagination";
import { TagForm } from "./tag-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>age,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";

import { toast } from "sonner";

export default function TagManagement() {
  const { data: session, status } = useSession();

  // Get table configuration for this page
  const tableConfig = getTableConfig("tag-management");

  const {
    tags: paginatedTags,
    filteredTags,
    tagModels,
    isLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    totalAllRecords,
    activeRecordsCount,
    inactiveRecordsCount,
    loadData,
    createTag,
    updateTag,
    deleteTag,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useTagManagement({
    initialPageSize: tableConfig.defaultPageSize,
    availablePageSizes: tableConfig.availablePageSizes as TagPageSize[],
  });

  // State hooks must be called before any early returns
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingTag, setEditingTag] = useState<Tag | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // Prevent rendering until searchFilters is properly initialized
  if (!searchFilters || typeof searchFilters.searchTerm === "undefined") {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Ensure searchFilters is properly initialized to prevent controlled/uncontrolled input errors
  const safeSearchFilters = {
    searchTerm: searchFilters.searchTerm || "",
    modelId: searchFilters.modelId || "",
    status: searchFilters.status,
    deleted: searchFilters.deleted ?? false,
    totalRecords: totalRecords,
    activeCount: activeRecordsCount,
    inactiveCount: inactiveRecordsCount,
  };

  const openCreateForm = () => {
    setEditingTag(null);
    setIsFormOpen(true);
  };

  const openEditForm = (tag: Tag) => {
    setEditingTag(tag);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setEditingTag(null);
    setFormLoading(false);
  };

  const handleFormSubmit = async (
    data: CreateTagFormData | UpdateTagFormData
  ) => {
    try {
      setFormLoading(true);

      if (editingTag) {
        // Update existing tag
        await updateTag(data as UpdateTagFormData);
        console.log("Tag updated successfully");
      } else {
        // Create new tag
        await createTag(data as CreateTagFormData);
        console.log("Tag created successfully");
      }

      closeForm();
    } catch (error) {
      console.error("Form submission error:", error);
      // Error is already handled in the hook
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteTag = async (tagId: string) => {
    try {
      await deleteTag(tagId);
      console.log("Tag deleted successfully");
    } catch (error) {
      console.error("Delete error:", error);
      // Error is already handled in the hook
    }
  };

  const handleToggleActive = async (tag: Tag) => {
    try {
      // Create an update object that includes the deleted property
      // The updateTag function will merge this with the existing tag data
      const updateData = {
        id: tag.id,
        name: tag.name,
        description: tag.description,
        modelId: tag.modelId,
        externalId: tag.externalId,
        status: tag.status,
        deleted: !tag.deleted, // Toggle the deleted status
      } as any; // Use 'as any' to bypass TypeScript restrictions

      await updateTag(updateData);
      console.log(
        `Tag ${tag.deleted ? "activated" : "deactivated"} successfully`
      );
    } catch (error) {
      console.error("Toggle active error:", error);
    }
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="text-center">
        <p className="text-muted-foreground">
          Please log in to access tag management.
        </p>
      </div>
    );
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/digital-carpet">
                Digital Carpet
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Tag Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={openCreateForm} className="gap-2 ml-auto">
          <Plus className="h-4 w-4" />
          Add Tag
        </Button>
      </header>
      <main>
        {/* Search and Filters */}
        <div className="p-4">
          <TagSearchPagination
            searchFilters={safeSearchFilters}
            showDeleted={showDeleted}
            tagModels={tagModels}
            totalRecords={totalRecords}
            totalAllRecords={totalAllRecords}
            activeRecordsCount={activeRecordsCount}
            inactiveRecordsCount={inactiveRecordsCount}
            onSearchChange={updateSearchFilters}
            onClearSearch={clearSearch}
            onToggleShowDeleted={toggleShowDeleted}
          />
        </div>

        {/* Data Table */}
        <div className="p-4 pt-0">
          <TagDataTable
            data={paginatedTags}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onEdit={openEditForm}
            onDelete={handleDeleteTag}
            onToggleActive={handleToggleActive}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
          />
        </div>
      </main>

      {/* Tag Form Sheet */}
      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-1/4 sm:max-w-4xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <TagIcon className="h-5 w-5" />
              {editingTag ? "Edit Tag" : "Create New Tag"}
            </SheetTitle>
            <SheetDescription>
              {editingTag
                ? "Update the tag information below."
                : "Fill in the details to create a new tag."}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <TagForm
              key={editingTag?.id || "new"}
              tag={editingTag || undefined}
              tagModels={tagModels}
              isLoading={formLoading}
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
