"use client";

import { FileText } from "lucide-react";
import { Page, RbacPaginationParams, RbacPageSize } from "@/types/rbac";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { RbacTablePagination } from "./rbac-table-pagination";

interface PageDataTableProps {
  data: Page[];
  pagination: RbacPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: RbacPageSize[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: RbacPageSize) => void;
  isLoading?: boolean;
}

export function PageDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
}: PageDataTableProps) {
  const getStatusBadge = (page: Page) => {
    if (page.deleted) {
      return <Badge variant="destructive">Deleted</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };

  // Extract page key from pageName (e.g., "device management-configure-area" -> "AREA")
  const getPageKey = (pageName: string): string => {
    // Map of pageName patterns to page keys based on old application
    const pageNameMap: Record<string, string> = {
      "device management-configure-area": "AREA",
      "device management-configure-asset": "ASSET",
      "device management-configure-branch": "BRANCH",
      "device management-configure-gateway": "GATEWAY",
      "device management-configure-transit": "TRANSIT",
      "device management-configure-location": "LOCATION",
      "device management-configure-tag": "TAG",
      "device management-configure-taggedasset": "TAGGED_ASSET",
      "device management-onboard-onboard": "ONBOARD",
      "device configure-configure-eventattribute": "EVENT_ATTRIBUTE",
      "device configure-configure-gatewayconfig": "GATEWAY_CONFIG",
      "device configure-configure-tagconfig": "TAG_CONFIG",
      "user management-user-account": "ACCOUNT",
      "user management-user-tenant": "TENANT",
      "user management-user-rbac": "RBAC",
      "master-register-vendor": "VENDOR",
      "master-register-worker": "WORKER",
      "master-register-vehicle": "VEHICLE",
      "master-register-washroom": "WASHROOM",
      "master-register-ecu": "ECU",
      "master-register-vin": "VIN",
      "master-register-vehiclemodel": "VEHICLE_MODEL",
      "master-register-ecumodel": "ECU_MODEL",
    };

    // Return mapped value or extract last part as fallback
    if (pageNameMap[pageName]) {
      return pageNameMap[pageName];
    }

    // Fallback: extract the last part after the last dash and convert to uppercase
    const parts = pageName.split("-");
    const lastPart = parts[parts.length - 1];
    return lastPart.toUpperCase();
  };

  if (isLoading) {
    return (
      <>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[180px]">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Page
                  </div>
                </TableHead>
                <TableHead className="w-[300px]">Description</TableHead>
                <TableHead className="w-[100px]">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="h-4 w-[140px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[250px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-[60px]" />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <RbacTablePagination
          pagination={pagination}
          totalRecords={0}
          currentPageRecords={0}
          totalPages={0}
          availablePageSizes={availablePageSizes}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          entityName="page"
        />
      </>
    );
  }

  return (
    <>
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[180px]">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Page
                </div>
              </TableHead>
              <TableHead className="w-[300px]">Description</TableHead>
              <TableHead className="w-[100px]">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center">
                  No pages found.
                </TableCell>
              </TableRow>
            ) : (
              data.map((page) => (
                <TableRow
                  key={page.id}
                  className={page.deleted ? "opacity-50" : ""}
                >
                  <TableCell className="font-medium">
                    <div className="truncate" title={getPageKey(page.pageName)}>
                      {getPageKey(page.pageName)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div
                      className="truncate sm:whitespace-normal"
                      title={page.pageName}
                    >
                      {page.pageName}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(page)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <RbacTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        entityName="page"
      />
    </>
  );
}
