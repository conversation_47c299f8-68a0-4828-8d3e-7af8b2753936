import { Suspense } from "react";
import { Metadata } from "next";
import AreaManagement from "@/components/modules/area/area-management";
import { Skeleton } from "@/components/ui/skeleton";
import {
  TableSkeleton,
  AREA_TABLE_COLUMNS,
} from "@/components/ui/table-skeleton";
import { Card, CardContent } from "@/components/ui/card";

export const metadata: Metadata = {
  title: "Area Management - Spidex",
  description: "Create and manage area configurations for your organization",
};

function AreaPageSkeleton() {
  return (
    <div className="space-y-6">
      {/* Breadcrumb skeleton */}
      <div className="flex items-center space-x-2">
        <Skeleton className="h-4 w-12" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-4 w-32" />
      </div>

      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-28" />
        </div>
      </div>

      {/* Search and filter skeleton */}
      <div className="space-y-4 p-6 border rounded-lg">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-6 w-18" />
          </div>
        </div>

        <div className="flex gap-4">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-20" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-10 w-full" />
        </div>

        <div className="flex items-center gap-4 pt-2 border-t">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-48" />
        </div>
      </div>

      {/* Data Table Skeleton */}
      <Card>
        <CardContent className="p-0">
          <TableSkeleton
            columns={AREA_TABLE_COLUMNS}
            rows={10}
            showPagination={true}
          />
        </CardContent>
      </Card>
    </div>
  );
}

export default function AreaPage() {
  return (
    <Suspense fallback={<AreaPageSkeleton />}>
      <AreaManagement />
    </Suspense>
  );
}
