"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  Location,
  LocationSearchFilters,
  LocationPaginationParams,
  LocationPageSize,
  DEFAULT_LOCATION_PAGE_SIZE,
  LOCATION_PAGE_SIZES,
  LOCATION_PAGE_SIZE_ALL,
  CreateLocationFormData,
  UpdateLocationFormData,
} from "@/types/location";

interface UseLocationManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: LocationPageSize;
  branchId?: string;
}

export const useLocationManagement = ({
  autoLoad = true,
  defaultPageSize = DEFAULT_LOCATION_PAGE_SIZE,
  branchId,
}: UseLocationManagementOptions = {}) => {
  // Get authenticated API instance and session
  const spidexApi = useSpidexApi();
  const { data: session, status } = useSession();

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);

  // Data cache - store all locations for client-side pagination
  const [allLocationsCache, setAllLocationsCache] = useState<Location[]>([]);

  // Search and pagination state
  const [searchFilters, setSearchFilters] = useState<LocationSearchFilters>({
    searchTerm: "",
    branchId: branchId || "",
    deleted: false,
  });

  const [pagination, setPagination] = useState<LocationPaginationParams>({
    pageNumber: 1,
    pageSize: defaultPageSize,
  });

  // Safe search filters with defaults
  const safeSearchFilters = useMemo(
    () => ({
      searchTerm: searchFilters?.searchTerm || "",
      branchId: searchFilters?.branchId || "",
      deleted: searchFilters?.deleted || false,
    }),
    [searchFilters]
  );

  // Filter locations based on search criteria and deleted status
  const filteredLocations = useMemo(() => {
    let filtered = allLocationsCache;

    // Filter by deleted status
    if (showDeleted) {
      filtered = filtered.filter((location) => location.deleted === true);
    } else {
      filtered = filtered.filter((location) => location.deleted !== true);
    }

    // Apply search filters
    if (safeSearchFilters.searchTerm) {
      const searchTerm = safeSearchFilters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (location) =>
          location.name.toLowerCase().includes(searchTerm) ||
          location.address.toLowerCase().includes(searchTerm) ||
          location.branchName?.toLowerCase().includes(searchTerm) ||
          location.geoJson.toLowerCase().includes(searchTerm)
      );
    }

    if (safeSearchFilters.branchId) {
      filtered = filtered.filter(
        (location) => location.branchId === safeSearchFilters.branchId
      );
    }

    return filtered;
  }, [allLocationsCache, showDeleted, safeSearchFilters]);

  // Calculate pagination
  const totalPages = useMemo(() => {
    if (pagination.pageSize === LOCATION_PAGE_SIZE_ALL) return 1;
    const pageSize = pagination.pageSize as number;
    return Math.ceil(filteredLocations.length / pageSize);
  }, [filteredLocations.length, pagination.pageSize]);

  // Get paginated locations
  const paginatedLocations = useMemo(() => {
    if (pagination.pageSize === LOCATION_PAGE_SIZE_ALL) {
      return filteredLocations;
    }

    const pageSize = pagination.pageSize as number;
    const startIndex = (pagination.pageNumber - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredLocations.slice(startIndex, endIndex);
  }, [filteredLocations, pagination]);

  // Calculate record counts
  const activeRecordsCount = useMemo(
    () => allLocationsCache.filter((location) => !location.deleted).length,
    [allLocationsCache]
  );

  const inactiveRecordsCount = useMemo(
    () => allLocationsCache.filter((location) => location.deleted).length,
    [allLocationsCache]
  );

  // Available page sizes
  const availablePageSizes = LOCATION_PAGE_SIZES;

  // Load data function
  const loadData = useCallback(async () => {
    if (!spidexApi || status === "loading") return;

    try {
      setIsLoading(true);
      setError(null);

      console.log("Loading locations data...");

      // Fetch locations and branches in parallel
      const [locations, branches] = await Promise.all([
        spidexApi.getAllLocations(1, 1000),
        spidexApi.getAllBranches(1, 1000),
      ]);

      console.log(
        `Loaded ${locations.length} locations and ${branches.length} branches`
      );

      // Create a map of branch IDs to branch names for quick lookup
      const branchMap = new Map(
        branches.map((branch) => [branch.id, branch.name])
      );

      // Populate branchName for each location
      const locationsWithBranchNames = locations.map((location) => ({
        ...location,
        branchName: branchMap.get(location.branchId) || "Unknown Branch",
      }));

      setAllLocationsCache(locationsWithBranchNames || []);
    } catch (err) {
      console.error("Error loading locations:", err);
      setError(err instanceof Error ? err.message : "Failed to load locations");
    } finally {
      setIsLoading(false);
    }
  }, [spidexApi, status]);

  // Auto-load data when dependencies are ready (only on initial mount)
  useEffect(() => {
    if (autoLoad && spidexApi && status === "authenticated") {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad, spidexApi, status]); // Removed loadData dependency to prevent reloading on session changes

  // CRUD Operations
  const createLocation = useCallback(
    async (locationData: CreateLocationFormData): Promise<Location> => {
      if (!spidexApi) throw new Error("API not available");

      try {
        setIsLoading(true);
        const newLocation = await spidexApi.createLocation(locationData);

        // Fetch branch name for the new location
        const branches = await spidexApi.getAllBranches(1, 1000);
        const branch = branches.find((b) => b.id === newLocation.branchId);
        const locationWithBranchName = {
          ...newLocation,
          branchName: branch?.name || "Unknown Branch",
        };

        // Add to cache
        setAllLocationsCache((prev) => [locationWithBranchName, ...prev]);

        return locationWithBranchName;
      } catch (err) {
        console.error("Error creating location:", err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi]
  );

  const updateLocation = useCallback(
    async (locationData: UpdateLocationFormData): Promise<Location> => {
      if (!spidexApi) throw new Error("API not available");

      try {
        setIsLoading(true);
        const updatedLocation = await spidexApi.updateLocation(locationData);

        // Fetch branch name for the updated location
        const branches = await spidexApi.getAllBranches(1, 1000);
        const branch = branches.find((b) => b.id === updatedLocation.branchId);
        const locationWithBranchName = {
          ...updatedLocation,
          branchName: branch?.name || "Unknown Branch",
        };

        // Update in cache
        setAllLocationsCache((prev) =>
          prev.map((location) =>
            location.id === updatedLocation.id
              ? locationWithBranchName
              : location
          )
        );

        return locationWithBranchName;
      } catch (err) {
        console.error("Error updating location:", err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi]
  );

  const deleteLocation = useCallback(
    async (locationId: string): Promise<void> => {
      if (!spidexApi) throw new Error("API not available");

      try {
        setIsLoading(true);
        await spidexApi.deleteLocation(locationId);

        // Mark as deleted in cache
        setAllLocationsCache((prev) =>
          prev.map((location) =>
            location.id === locationId
              ? { ...location, deleted: true }
              : location
          )
        );
      } catch (err) {
        console.error("Error deleting location:", err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi]
  );

  // Pagination controls
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, pageNumber: page }));
  }, []);

  const changePageSize = useCallback((size: LocationPageSize) => {
    setPagination({ pageNumber: 1, pageSize: size });
  }, []);

  // Search controls
  const updateSearchFilters = useCallback(
    (filters: Partial<LocationSearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      // Reset to first page when search changes
      setPagination((prev) => ({ ...prev, pageNumber: 1 }));
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({
      searchTerm: "",
      branchId: "",
      deleted: false,
    });
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
  }, []);

  const toggleShowDeleted = useCallback((show: boolean) => {
    setShowDeleted(show);
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
  }, []);

  return {
    // Data
    locations: paginatedLocations,
    allLocations: allLocationsCache,
    filteredLocations,

    // State
    isLoading,
    error,
    showDeleted,

    // Search and pagination
    searchFilters: safeSearchFilters,
    pagination,
    totalPages,
    totalRecords: filteredLocations.length, // Use filtered count for display
    totalAllRecords: allLocationsCache.length, // Total count including deleted
    activeRecordsCount, // Count of active (not deleted) records
    inactiveRecordsCount, // Count of inactive (deleted) records
    hasNextPage: pagination.pageNumber < totalPages,
    hasPreviousPage: pagination.pageNumber > 1,

    // Actions
    loadData,
    createLocation,
    updateLocation,
    deleteLocation,

    // Pagination controls
    goToPage,
    changePageSize,
    availablePageSizes,

    // Search controls
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
  };
};
