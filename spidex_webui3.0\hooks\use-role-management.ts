"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  Role,
  CreateRoleFormData,
  UpdateRoleFormData,
  RoleSearchFilters,
  RbacPaginationParams,
  RbacPageSize,
  DEFAULT_RBAC_PAGE_SIZE,
  RBAC_PAGE_SIZES,
} from "@/types/rbac";

interface UseRoleManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: RbacPageSize;
}

export function useRoleManagement(options: UseRoleManagementOptions = {}) {
  const { autoLoad = true, defaultPageSize = DEFAULT_RBAC_PAGE_SIZE } = options;
  const { data: session } = useSession();
  const spidexApi = useSpidexApi();

  // State
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);
  const [searchFilters, setSearchFilters] = useState<RoleSearchFilters>({});
  const [pagination, setPagination] = useState<RbacPaginationParams>({
    page: 1,
    pageSize: defaultPageSize === "all" ? 1000 : defaultPageSize,
  });

  // Available page sizes
  const availablePageSizes = useMemo(() => RBAC_PAGE_SIZES, []);

  // Filter roles based on search criteria and deleted status
  const filteredRoles = useMemo(() => {
    let filtered = roles.filter((role) => {
      if (!showDeleted && role.deleted) return false;
      if (showDeleted && !role.deleted) return false;
      return true;
    });

    // Apply search filters
    if (searchFilters.name) {
      const nameFilter = searchFilters.name.toLowerCase();
      filtered = filtered.filter((role) =>
        role.name.toLowerCase().includes(nameFilter)
      );
    }

    if (searchFilters.enable !== undefined) {
      filtered = filtered.filter(
        (role) => role.enable === searchFilters.enable
      );
    }

    return filtered;
  }, [roles, showDeleted, searchFilters]);

  // Paginated roles
  const paginatedRoles = useMemo(() => {
    if (pagination.pageSize === 1000) {
      // Show all
      return filteredRoles;
    }

    const startIndex = (pagination.page - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredRoles.slice(startIndex, endIndex);
  }, [filteredRoles, pagination]);

  // Pagination calculations
  const totalRecords = filteredRoles.length;
  const totalPages = Math.ceil(totalRecords / pagination.pageSize);
  const totalAllRecords = roles.length;
  const activeRecordsCount = roles.filter((role) => !role.deleted).length;
  const inactiveRecordsCount = roles.filter((role) => role.deleted).length;

  // Load roles data
  const loadData = useCallback(async () => {
    if (!session?.user?.token) {
      console.log("No session token available for loading roles");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("Loading roles data...");
      const rolesData = await spidexApi.getAllRolesForRbac(1, 1000);
      console.log("Roles data loaded:", rolesData);
      setRoles(rolesData || []);
    } catch (err) {
      console.error("Error loading roles:", err);
      setError(err instanceof Error ? err.message : "Failed to load roles");
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.token, spidexApi]);

  // Create role
  const createRole = useCallback(
    async (formData: CreateRoleFormData): Promise<boolean> => {
      if (!session?.user?.token) {
        throw new Error("No authentication token available");
      }

      try {
        console.log("Creating role:", formData);

        const dateTimeNow = new Date().toISOString();
        const newRole = {
          name: formData.name,
          id: formData.name,
          enable: true,
          deleted: false,
          createdTime: dateTimeNow,
          modifiedTime: dateTimeNow,
          createdBy: session.user.userId || "system",
          modifiedBy: session.user.userId || "system",
        };

        await spidexApi.createRole(newRole);

        // If role creation successful and has page permissions, create the links
        if (formData.pagePermissions) {
          const selectedPagesArray: any[] = [];
          Object.keys(formData.pagePermissions).forEach((pageId) => {
            const permissions = Object.keys(
              formData.pagePermissions[pageId]
            ).filter(
              (permission) =>
                formData.pagePermissions[pageId][
                  permission as keyof (typeof formData.pagePermissions)[typeof pageId]
                ]
            );
            if (permissions.length > 0) {
              selectedPagesArray.push({
                pageName: pageId, // This should be mapped to actual page name
                permissions: permissions,
              });
            }
          });

          if (selectedPagesArray.length > 0) {
            const linkData = {
              id: formData.name,
              pagePermissions: selectedPagesArray,
              createdTime: dateTimeNow,
              modifiedTime: dateTimeNow,
              createdBy: session.user.userId || "system",
              modifiedBy: session.user.userId || "system",
            };

            await spidexApi.linkPageRole(linkData);
          }
        }

        await loadData();
        return true;
      } catch (err) {
        console.error("Error creating role:", err);
        throw err;
      }
    },
    [session?.user?.token, session?.user?.userId, spidexApi, loadData]
  );

  // Delete role
  const deleteRole = useCallback(
    async (roleId: string): Promise<boolean> => {
      if (!session?.user?.token) {
        throw new Error("No authentication token available");
      }

      try {
        console.log("Deleting role:", roleId);
        await spidexApi.deleteRole(roleId);
        await loadData();
        return true;
      } catch (err) {
        console.error("Error deleting role:", err);
        throw err;
      }
    },
    [session?.user?.token, spidexApi, loadData]
  );

  // Pagination functions
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  }, []);

  const changePageSize = useCallback((pageSize: RbacPageSize) => {
    setPagination({
      page: 1,
      pageSize: pageSize === "all" ? 1000 : pageSize,
    });
  }, []);

  // Search functions
  const updateSearchFilters = useCallback(
    (filters: Partial<RoleSearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({});
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  // Auto-load data on mount (only on initial mount)
  useEffect(() => {
    if (autoLoad && session?.user?.token) {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad, session?.user?.token]); // Removed loadData dependency to prevent reloading on session changes

  return {
    // Data
    roles: paginatedRoles,
    filteredRoles,
    isLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    totalAllRecords,
    activeRecordsCount,
    inactiveRecordsCount,
    availablePageSizes,

    // Actions
    loadData,
    createRole,
    deleteRole,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
  };
}
