"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AccountPaginationParams, AccountPageSize } from "@/types/account";

interface AccountTablePaginationProps {
  pagination: AccountPaginationParams;
  totalRecords: number;
  currentPageRecords: number; // Number of records on current page
  totalPages?: number;
  availablePageSizes: AccountPageSize[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: AccountPageSize) => void;
}

export function AccountTablePagination({
  pagination,
  totalRecords,
  currentPageRecords,
  totalPages = 1,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
}: AccountTablePaginationProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center justify-between p-4 border-t">
        <div className="text-sm text-muted-foreground">Loading...</div>
        <div className="flex items-center gap-2">
          <div className="w-20 h-8 bg-muted rounded animate-pulse" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between p-4 border-t">
      {/* Results Summary */}
      <div className="text-sm text-muted-foreground">
        {pagination.pageSize === "all" ? (
          <>
            Showing all {totalRecords} account{totalRecords !== 1 ? "s" : ""}
          </>
        ) : (
          <>
            Showing {currentPageRecords} of {totalRecords} account
            {totalRecords !== 1 ? "s" : ""} on page {pagination.pageNumber} of{" "}
            {totalPages}
          </>
        )}
      </div>

      <div className="flex items-center gap-4">
        {/* Page Size Selector */}
        <div className="flex items-center gap-2">
          <Label htmlFor="page-size">Show:</Label>
          <Select
            value={pagination.pageSize.toString()}
            onValueChange={(value) => {
              console.log("Page size select changed to:", value);
              const newSize =
                value === "all" ? "all" : (parseInt(value) as AccountPageSize);
              console.log("Calling onPageSizeChange with:", newSize);
              onPageSizeChange(newSize);
            }}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availablePageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size === "all" ? "All" : size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pagination Controls */}
        {pagination.pageSize !== "all" && (
          <div className="flex items-center gap-2">
            {/* First Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={pagination.pageNumber <= 1}
              className="px-3"
            >
              First
            </Button>

            {/* Previous Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.pageNumber - 1)}
              disabled={pagination.pageNumber <= 1}
              className="px-3"
            >
              ‹ Prev
            </Button>

            {/* Page Numbers */}
            <div className="flex items-center gap-1">
              {(() => {
                const currentPage = pagination.pageNumber;
                const pages = [];
                const maxVisiblePages = 5;

                let startPage = Math.max(
                  1,
                  currentPage - Math.floor(maxVisiblePages / 2)
                );
                let endPage = Math.min(
                  totalPages,
                  startPage + maxVisiblePages - 1
                );

                // Adjust start if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                // Show first page if not in range
                if (startPage > 1) {
                  pages.push(
                    <Button
                      key={1}
                      variant={1 === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => onPageChange(1)}
                      className="w-8 h-8 p-0"
                    >
                      1
                    </Button>
                  );
                  if (startPage > 2) {
                    pages.push(
                      <span
                        key="ellipsis1"
                        className="px-2 text-muted-foreground"
                      >
                        ...
                      </span>
                    );
                  }
                }

                // Show page range
                for (let i = startPage; i <= endPage; i++) {
                  pages.push(
                    <Button
                      key={i}
                      variant={i === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => onPageChange(i)}
                      className="w-8 h-8 p-0"
                    >
                      {i}
                    </Button>
                  );
                }

                // Show last page if not in range
                if (endPage < totalPages) {
                  if (endPage < totalPages - 1) {
                    pages.push(
                      <span
                        key="ellipsis2"
                        className="px-2 text-muted-foreground"
                      >
                        ...
                      </span>
                    );
                  }
                  pages.push(
                    <Button
                      key={totalPages}
                      variant={
                        totalPages === currentPage ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => onPageChange(totalPages)}
                      className="w-8 h-8 p-0"
                    >
                      {totalPages}
                    </Button>
                  );
                }

                return pages;
              })()}
            </div>

            {/* Next Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.pageNumber + 1)}
              disabled={pagination.pageNumber >= totalPages}
              className="px-3"
            >
              Next ›
            </Button>

            {/* Last Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={pagination.pageNumber >= totalPages}
              className="px-3"
            >
              Last
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
