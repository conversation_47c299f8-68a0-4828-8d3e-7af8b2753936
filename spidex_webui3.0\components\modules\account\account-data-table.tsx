"use client";

import { useMemo } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { MoreH<PERSON>zontal, Edit, Trash2, Eye, UserCheck } from "lucide-react";
import {
  User,
  AccountPaginationParams,
  AccountPageSize,
} from "@/types/account";
import { AccountTablePagination } from "./account-table-pagination";
import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useState } from "react";

interface AccountDataTableProps {
  data: User[];
  pagination: AccountPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: AccountPageSize[];
  onEdit?: (user: User) => void;
  onDelete?: (userId: string) => void;
  // onResetPassword?: (userId: string) => void; // COMMENTED OUT
  onView?: (user: User) => void;
  onToggleActive?: (userId: string, active: boolean) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: AccountPageSize) => void;
}

export function AccountDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onEdit,
  onDelete,
  // onResetPassword, // COMMENTED OUT
  onView,
  onToggleActive,
  onPageChange,
  onPageSizeChange,
}: AccountDataTableProps) {
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [forceRender, setForceRender] = useState(0);

  const handleDeleteClick = (user: User) => {
    // Force a clean state first
    setUserToDelete(null);
    // Then set the new user after a brief delay
    setTimeout(() => {
      setUserToDelete(user);
    }, 10);
  };

  const handleDeleteConfirm = () => {
    console.log("Delete confirmed for user:", userToDelete?.name);
    if (userToDelete && onDelete) {
      onDelete(userToDelete.id);
    }
    setUserToDelete(null);
    // Force re-render to ensure clean state
    setForceRender((prev) => prev + 1);
  };

  const handleDeleteCancel = () => {
    setUserToDelete(null);
    // Force re-render to ensure clean state
    setForceRender((prev) => prev + 1);
  };

  const columns: ColumnDef<User>[] = useMemo(
    () => [
      {
        id: "actions",
        header: "Actions",
        meta: {
          className: "text-left",
        },
        cell: ({ row }) => {
          const user = row.original;

          return (
            <div className="flex items-center justify-start gap-2">
              {/* More actions dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>

                  {user.deleted ? (
                    // For inactive users, show only Activate button
                    onToggleActive && (
                      <DropdownMenuItem
                        onClick={() => onToggleActive(user.id, true)}
                      >
                        <UserCheck className="mr-2 h-4 w-4" />
                        Activate
                      </DropdownMenuItem>
                    )
                  ) : (
                    // For active users, show all available options
                    <>
                      {onView && (
                        <DropdownMenuItem onClick={() => onView(user)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                      )}

                      {onEdit && (
                        <DropdownMenuItem onClick={() => onEdit(user)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Account
                        </DropdownMenuItem>
                      )}

                      {/* Reset Password option - COMMENTED OUT */}
                      {/* {onResetPassword && (
                        <DropdownMenuItem onClick={() => onResetPassword(user.id)}>
                          <Key className="mr-2 h-4 w-4" />
                          Reset Password
                        </DropdownMenuItem>
                      )} */}

                      <DropdownMenuSeparator />

                      {onDelete && (
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(user)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete Account
                        </DropdownMenuItem>
                      )}
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          );
        },
      },
      {
        accessorKey: "name",
        header: "Name",
        cell: ({ row }) => {
          const user = row.original;
          return (
            <div className="flex flex-col">
              <span className="font-medium">{user.name}</span>
              {user.email && (
                <span className="text-sm text-muted-foreground">
                  {user.email}
                </span>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "userId",
        header: "Username",
        cell: ({ row }) => {
          const user = row.original;
          return (
            <div className="flex items-center gap-2">
              <span className="font-mono text-sm">{user.userId}</span>
              {user.deleted && (
                <Badge variant="secondary" className="text-xs">
                  Inactive
                </Badge>
              )}
            </div>
          );
        },
      },
      {
        accessorKey: "tenantName",
        header: "Tenant",
        cell: ({ row }) => {
          const user = row.original;
          return (
            <span className="text-sm">{user.tenantName || "Unknown"}</span>
          );
        },
      },
      {
        accessorKey: "roles",
        header: "Roles",
        cell: ({ row }) => {
          const user = row.original;
          return (
            <div className="flex flex-wrap gap-1">
              {user.roles.length > 0 ? (
                user.roles.slice(0, 2).map((role) => (
                  <Badge key={role.id} variant="outline" className="text-xs">
                    {role.name}
                  </Badge>
                ))
              ) : (
                <span className="text-sm text-muted-foreground">No roles</span>
              )}
              {user.roles.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{user.roles.length - 2} more
                </Badge>
              )}
            </div>
          );
        },
      },

      {
        accessorKey: "modifiedTime",
        header: "Modified",
        cell: ({ row }) => {
          const user = row.original;
          return (
            <div className="flex flex-col text-sm">
              <span>{new Date(user.modifiedTime).toLocaleDateString()}</span>
              <span className="text-muted-foreground">
                by {user.modifiedBy}
              </span>
            </div>
          );
        },
      },
    ],
    [onEdit, onDelete, onView, onToggleActive]
  );

  return (
    <>
      <DataTable
        columns={columns}
        data={data}
        showPagination={false}
        rowClassName={(row: any) => (row.deleted ? "opacity-50" : "")}
      />

      {/* Pagination at bottom of table */}
      <AccountTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />

      {/* Delete confirmation dialog */}
      <AlertDialog
        key={`delete-dialog-${forceRender}`}
        open={!!userToDelete}
        onOpenChange={(open) => {
          if (!open) {
            handleDeleteCancel();
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Account</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the account for{" "}
              <strong>{userToDelete?.name}</strong> ({userToDelete?.userId})?
              This action cannot be undone and will permanently remove the user
              account and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleDeleteCancel}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Account
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
