import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Select,
  GoogleMapW3W,
  Popconfirm,
  Modal,
  CommonCompactView,
  message,
  Row,
  Col,
  Input,
  Checkbox,
} from '../../../../components';
import { Link } from 'react-router-dom';
import {
  getAllGateways,
  updateGateway,
  getAllLocations,
  getAllBranches,
  getAllAreas,
  addArea,
  updateArea,
  deleteArea,
} from '../../../../services';
import Context from '../../../../context';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import { PlusOutlined } from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import { buildCommonApiValues } from '../../../../utils';
import mapSvg from '../../../../assets/images/gmap.svg';
import CommonDrawer from '../../../../components/CommonDrawer';
import { get } from 'lodash';
import { CRUD, Pages, ModuleNames } from '../../../../constants';

const { Option } = Select;

const Area = () => {
  const params = useParams();
  const [context, setContext] = useContext(Context);
  const [branches, setBranches] = useState([]);
  const [locations, setLocations] = useState([]);
  const [areas, setAreas] = useState([]);
  const [area, setArea] = useState({});
  const [gatewayModalVisible, setGatewayModalVisible] = useState(false);
  const [gateway, setGateway] = useState({});
  const [gateways, setGateways] = useState([]);
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');
  const [isMaps, setIsMaps] = useState(false);
  const [mapValues, setMapValues] = useState({});
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);

  const locationId = params.id;

  const [form] = Form.useForm();
  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    form.resetFields();
  };

  const makeActive = (data) => {
    updateAreaCall({ ...data, deleted: false });
  };

  const switchTableData = () => {
    showDeleted ? setAreas(tableData) : setAreas(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveAreaAction({ ...commonValues, ...values, properties: {} });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateAreaAction(values);
      setVisible(false);
    }
  };

  const saveAreaAction = async (location) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addArea(location)
      .then((res) => {
        res.data.branchName = branches.filter((x) => x.id === location.branchId)[0]?.name;
        res.data.locationName = locations.filter((x) => x.id === location.locationId)[0]?.name;
        setAreas((state) => [res.data, ...state]);
        message.success('Succesfully Added area');
        form.resetFields();
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Area, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editAreaAction = (area) => {
    form.setFieldsValue({ ...area });
    setArea(area);
    setAction('edit');
    setVisible(true);
  };

  const updateAreaCall = (values) => {
    const data = { ...area, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateArea(data)
      .then((res) => {
        setAreas((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              res.data.branchName = branches.filter((x) => x.id === res.data.branchId)[0]?.name || i.branchName;
              res.data.locationName = locations.filter((x) => x.id === res.data.locationId)[0]?.name || i.locationName;
              Object.assign(i, { ...i, ...res.data });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated area');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Area, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateAreaAction = async (values) => {
    updateAreaCall(values);
  };

  const setDeleteAreaAction = (areaId, visible) => {
    setAreas((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === areaId).visible = visible;
      tempData
        .filter((x) => x.id !== areaId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteAreaAction = (areaId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteArea(areaId)
      .then(() => {
        setAreas((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === areaId).visible = false;
          tempState.find((x) => x.id === areaId).deleted = true;
          return [...state].filter((x) => x.id !== areaId);
        });
        message.success('Succesfully Deleted area');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Area, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  useEffect(() => {
    const init = async () => {
      if (context.profile.tenantId) {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
        try {
          let res;
          let locationRes;
          let gatewayRes;
          try {
            gatewayRes = await getAllGateways(context.profile.tenantId);
            setGateways(gatewayRes.data.filter((x) => x.deleted === false));
          } catch (e) {
            console.log(e);
            message.error('Unable to get Gateway details, try again later');
          }
          res = await getAllBranches(context.profile.tenantId);
          setBranches(res.data.filter((x) => x.deleted === false));
          locationRes = await getAllLocations(context.profile.tenantId);
          setLocations(locationRes.data.filter((x) => x.deleted === false));
          const areaRes = await getAllAreas(context.profile.tenantId);
          areaRes.data.forEach((l) => {
            try {
              l.branchName = res.data.filter((x) => x.id === l.branchId)[0]?.name;
            } catch {
              l.branchName = 'Unknown';
            }
          });
          areaRes.data.forEach((l) => {
            try {
              l.locationName = locationRes.data.filter((x) => x.id === l.locationId)[0]?.name;
            } catch {
              l.locationName = 'Unknown';
            }
          });
          setTableData(areaRes.data);
          setAreas(areaRes.data.filter((x) => x.deleted === false));
        } catch (e) {
          console.log(e);
          message.error('Unable to get Area details, try again later');
        } finally {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        }
      }
    };
    init();
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    form.setFieldsValue({ ...form.getFieldsValue(), ...mapValues });
    // eslint-disable-next-line
  }, [mapValues]);

  const clearForm = () => {
    form.resetFields();
  };

  const bindGateway = (area) => {
    setArea(area);
    setGatewayModalVisible(true);
  };

  const onGatewaySelect = (gatewayId) => {
    setGateway(gateways.find((x) => x.id === gatewayId));
  };

  const onGatewayModalOk = () => {
    if (gateway) {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      updateGateway({ ...gateway, areaId: area.id, modifiedTime: new Date() })
        .then(() => {
          message.success('Succesfully Updated gateway');
          setGatewayModalVisible(false);
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to update Gateway, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
    }
  };

  const tableCols = [
    {
      title: <strong> Name </strong>,
      render: (record) => (
        <PermissionContainer page={Pages.GATEWAY} permission={CRUD.VIEW}>
          <Link to={`/device/management/configure/gateway/${record.id}`}>{record.name}</Link>
        </PermissionContainer>
      ),
    },
    { title: <strong> Branch </strong>, key: 'branchName', dataIndex: 'branchName' },
    {
      title: <strong> Location </strong>,
      key: 'locationName',
      dataIndex: 'locationName',
      filters: locations.map((x) => ({ text: x.name, value: x.id })) || [],
      defaultFilteredValue: locationId ? [locationId] : [],
      onFilter: (value, record) => {
        return record.locationId === value;
      },
    },
    { title: <strong> Address </strong>, key: 'address', dataIndex: 'address', width: 200 },
    { title: <strong> Geo </strong>, key: 'geoJson', dataIndex: 'geoJson', width: 150 },
    {
      title: <strong> GPS </strong>,
      width: 150,
      key: 'gpsPoint',
      render: (record) => (
        <Row>
          <Col>{record.gpsPoint?.latitude},</Col>
          <Col>{record.gpsPoint?.longitude}</Col>
        </Row>
      ),
    },
    {
      title: <strong> Level, Max, Min </strong>,
      width: 140,
      key: 'level',
      render: (record) => (
        <>
          {record.level ? `${record.level}, ` : ''}
          {record.max ? `${record.max}, ` : ''}
          {record.min}
        </>
      ),
    },
    {
      title: <strong> Actions </strong>,
      width: 300,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <PermissionContainer page={Pages.AREA} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                  Activate
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <Row>
            <Col>
              <PermissionContainer page={Pages.AREA} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => editAreaAction(record)} className="actionButton">
                  Edit
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <PermissionContainer page={Pages.AREA} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => bindGateway(record)} className="actionButton">
                  Bind Gateway
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <PermissionContainer page={Pages.AREA} permission={CRUD.DELETE}>
                <Popconfirm
                  title={`Are you sure to delete area ${record.name}?`}
                  visible={record.visible || false}
                  onConfirm={() => deleteAreaAction(record.id)}
                  onCancel={() => setDeleteAreaAction(record.id, false)}
                >
                  <Button type="link" onClick={() => setDeleteAreaAction(record.id, true)} className="actionButton">
                    Delete
                  </Button>
                </Popconfirm>
              </PermissionContainer>
            </Col>
          </Row>
        ),
    },
  ];
  const areaBreadcrumbsName = get(context.tenantProfile, `moduleNames[${Pages.AREA}].displayName`, ModuleNames.AREA);

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.CONFIGURE, areaBreadcrumbsName]} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.AREA} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Area
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="id"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={areas}
          rowClassName={(record) => record.deleted && 'rowInactive'}
        />
      ) : (
        <CommonCompactView
          GPS={true}
          data={areas}
          onEdit={editAreaAction}
          onDelete={deleteAreaAction}
          permissions={[
            { pageName: Pages.AREA, permission: CRUD.UPDATE, label: 'Edit' },
            { pageName: Pages.AREA, permission: CRUD.DELETE, label: 'Delete' },
          ]}
          title="name"
          dataList={[
            { label: 'Branch', value: 'branchName' },
            { label: 'GPS', value: 'gpsPoint', type: 'gps' },
          ]}
        />
      )}
      <CommonDrawer title="Area" visible={visible} closeAdd={closeAdd}>
        <Form
          scrollToFirstError={true}
          layout="horizontal"
          initialValues={{}}
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          <Form.Item
            hasFeedback
            label="Branch"
            name="branchId"
            rules={[
              {
                required: true,
                message: 'Please select Area Branch!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Area Branch"
            >
              {branches.map((b) => (
                <Option title={b.name} key={b.id} value={b.id}>
                  {b.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Location"
            name="locationId"
            rules={[
              {
                required: true,
                message: 'Please select Area Location!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Area Location"
            >
              {locations.map((b) => (
                <Option title={b.name} key={b.id} value={b.id}>
                  {b.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input area Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Area Name" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Address"
            name="address"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input area Address!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Area Address" />
          </Form.Item>
          <Form.Item label="Geo" required>
            <Row justify="space-between" className="geoLocation">
              <Col xs={{ span: 21 }} span={22}>
                <Form.Item
                  shouldUpdate={true}
                  hasFeedback
                  name="geoJson"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: 'Please input branch Geo Location JSON!',
                      min: 1,
                      max: 2000,
                    },
                  ]}
                >
                  <Input placeholder="Branch Geo" />
                </Form.Item>
              </Col>
              <Col className="locationSelector">
                <img src={mapSvg} alt="mapSvg" onClick={() => setIsMaps(true)} />
              </Col>
            </Row>
          </Form.Item>
          <Form.Item label="GPS" required>
            <Input.Group compact>
              <Form.Item
                name={['gpsPoint', 'latitude']}
                noStyle
                hasFeedback
                rules={[{ required: true, message: 'Latitude is required' }]}
              >
                <Input style={{ width: '50%' }} placeholder="Latitude" />
              </Form.Item>
              <Form.Item
                name={['gpsPoint', 'longitude']}
                noStyle
                hasFeedback
                rules={[{ required: true, message: 'Longitude is required' }]}
              >
                <Input style={{ width: '50%' }} placeholder="Longitude" />
              </Form.Item>
            </Input.Group>
          </Form.Item>
          <Form.Item label="Level, Max, Min" required>
            <Input.Group compact>
              <Form.Item
                name={['level']}
                noStyle
                hasFeedback
                rules={[{ required: true, whitespace: true, message: 'Level is required' }]}
              >
                <Input style={{ width: '33%' }} placeholder="Level" />
              </Form.Item>
              <Form.Item
                name={['max']}
                noStyle
                hasFeedback
                rules={[{ required: true, whitespace: true, message: 'Max is required' }]}
              >
                <Input style={{ width: '33%' }} placeholder="Max" />
              </Form.Item>
              <Form.Item
                name={['min']}
                noStyle
                hasFeedback
                rules={[{ required: true, whitespace: true, message: 'Min is required' }]}
              >
                <Input style={{ width: '33%' }} placeholder="Min" />
              </Form.Item>
            </Input.Group>
          </Form.Item>
          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" type="primary" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
        {isMaps && <GoogleMapW3W setIsMaps={setIsMaps} onCloseUpdate={setMapValues}></GoogleMapW3W>}
      </CommonDrawer>
      <Modal
        title={`Bind Area ${area.name} to Gateway?`}
        visible={gatewayModalVisible}
        onCancel={() => setGatewayModalVisible(false)}
        onOk={onGatewayModalOk}
        okText={'Save'}
      >
        <Select
          onSelect={(e) => onGatewaySelect(e)}
          showSearch
          filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          placeholder="Select Gateway"
        >
          {gateways.map((b) => (
            <Option title={b.name} key={b.id} value={b.id}>
              {b.name}
            </Option>
          ))}
        </Select>
      </Modal>
    </>
  );
};

export default Area;
