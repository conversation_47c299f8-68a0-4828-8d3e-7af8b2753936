"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Tag as TagIcon,
  Cpu,
  Calendar,
} from "lucide-react";
import { Tag, TagPaginationParams, TagPageSize } from "@/types/tag";
import { TagTablePagination } from "./tag-table-pagination";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface TagDataTableProps {
  data: Tag[];
  pagination: TagPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: TagPageSize[];
  onEdit: (tag: Tag) => void;
  onDelete: (tagId: string) => void;
  onToggleActive?: (tag: Tag) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: TagPageSize) => void;
}

export function TagDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onEdit,
  onDelete,
  onToggleActive,
  onPageChange,
  onPageSizeChange,
}: TagDataTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [tagToDelete, setTagToDelete] = useState<Tag | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Reset dropdown state when data changes
  useEffect(() => {
    setOpenDropdownId(null);
  }, [data]);

  const handleDeleteClick = (tag: Tag) => {
    setTagToDelete(tag);
    setDeleteDialogOpen(true);
    setOpenDropdownId(null); // Close dropdown
  };

  const handleDeleteConfirm = () => {
    if (tagToDelete && onDelete) {
      onDelete(tagToDelete.id);
    }
    setDeleteDialogOpen(false);
    setTagToDelete(null);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Name</TableHead>
              <TableHead className="hidden md:table-cell">
                Description
              </TableHead>
              <TableHead className="hidden lg:table-cell">Model</TableHead>
              <TableHead className="hidden lg:table-cell">
                External ID
              </TableHead>
              <TableHead className="hidden xl:table-cell">Type</TableHead>
              <TableHead className="hidden xl:table-cell">Battery</TableHead>
              <TableHead className="hidden xl:table-cell">Status</TableHead>
              <TableHead className="hidden xl:table-cell">Created</TableHead>
              <TableHead className="text-right w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <TagIcon className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">No tags found</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              data.map((tag) => (
                <TableRow
                  key={tag.id}
                  className={tag.deleted ? "opacity-50" : ""}
                >
                  {/* Name */}
                  <TableCell className="font-medium">{tag.name}</TableCell>

                  {/* Description */}
                  <TableCell className="hidden md:table-cell">
                    <span className="text-sm text-muted-foreground">
                      {tag.description || "No description"}
                    </span>
                  </TableCell>

                  {/* Model */}
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center gap-1">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                        {tag.modelId || "No model"}
                      </span>
                    </div>
                  </TableCell>

                  {/* External ID */}
                  <TableCell className="hidden lg:table-cell">
                    <span className="text-sm text-muted-foreground">
                      {tag.externalId || "N/A"}
                    </span>
                  </TableCell>

                  {/* Type */}
                  <TableCell className="hidden xl:table-cell">
                    <span className="text-sm">Asset Tag</span>
                  </TableCell>

                  {/* Battery */}
                  <TableCell className="hidden xl:table-cell">
                    <span className="text-sm">N/A</span>
                  </TableCell>

                  {/* Status */}
                  <TableCell className="hidden xl:table-cell">
                    <Badge
                      variant={tag.deleted ? "destructive" : "default"}
                      className="w-fit"
                    >
                      {tag.deleted ? "Inactive" : "Active"}
                    </Badge>
                  </TableCell>

                  {/* Created */}
                  <TableCell className="hidden xl:table-cell">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      {tag.createdTime
                        ? formatDate(tag.createdTime)
                        : "Unknown"}
                    </div>
                  </TableCell>
                  <TableCell className="text-right pr-6">
                    <DropdownMenu
                      open={openDropdownId === tag.id}
                      onOpenChange={(open) =>
                        setOpenDropdownId(open ? tag.id : null)
                      }
                    >
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        {tag.deleted ? (
                          // For inactive tags, show only Activate button
                          onToggleActive && (
                            <DropdownMenuItem
                              onClick={() => {
                                onToggleActive(tag);
                                setOpenDropdownId(null);
                              }}
                            >
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Activate
                            </DropdownMenuItem>
                          )
                        ) : (
                          // For active tags, show Edit, Deactivate, and Delete options
                          <>
                            <DropdownMenuItem
                              onClick={() => {
                                onEdit(tag);
                                setOpenDropdownId(null);
                              }}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Tag
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteClick(tag)}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Tag
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination at bottom of table */}
      <TagTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Tag</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the tag "{tagToDelete?.name}"?
              This action cannot be undone and will remove the tag from all
              associated assets.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Tag
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
