import { useEffect, useState, useContext } from 'react';
import {
  Input,
  Checkbox,
  Upload,
  Table,
  Button,
  Form,
  Select,
  Popconfirm,
  CommonDrawer,
  CommonCompactView,
  message,
  Row,
  Col,
  Pagination,
} from '../../../components';
import {
  updateVehicle,
  deleteVehicle,
  getAllVehiclesByPagination,
  addVehicle,
  getAllVendors,
  getSearchVehicles,
} from '../../../services';
import Context from '../../../context';
import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { buildCommonApiValues, csvFile, normFile } from '../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../shared';
import { get } from 'lodash';
import { country, CRUD, InputRegex, Pages, state, ModuleNames, MastersPageSizeDefault } from '../../../constants';

const { Search } = Input;
const { Option } = Select;

const Vehicle = () => {
  const [context, setContext] = useContext(Context);
  const [totalVehicles, setTotalVehicles] = useState({ items: 0, current: 0, pageSize: MastersPageSizeDefault });
  const [vehicles, setVehicles] = useState([]);
  const [vehiclesOriginal, setVehiclesOriginal] = useState([]);
  const [vehicle, setVehicle] = useState({});
  const [visible, setVisible] = useState(false);
  const [vehicleInfo, setVehicleInfo] = useState(false);
  const [action, setAction] = useState('new');
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [vendorData, setVendorData] = useState([]);

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setVehicles(tableData) : setVehicles(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const formInitState = {
    attributes: [{ type: null, value: null, name: null }],
  };

  const [formInitValues, setFormInitValues] = useState({ ...formInitState });

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    setVehicleInfo(false);
    if (action === 'edit') form.resetFields();
  };

  const dummyCustomRequest = (e) => {
    e.onSuccess(null, e.file);
  };

  const [tableModeNormal, setTableModeNormal] = useState(true);

  const onVehicleSearch = async (e) => {
    if (e) {
      try {
        const vehicles = await getSearchVehicles(context.profile.tenantId, e);
        setVehicles(vehicles.data.filter((x) => x.deleted === false));
        setTableModeNormal(false);
      } catch {
        message.error('Could not retrieve vehicles');
      }
    } else {
      setTableModeNormal(true);
      setVehicles(vehiclesOriginal);
    }
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();

    const commonValues = buildCommonApiValues(context.profile);
    const vendorRes = vendorData.find((e) => e.id === values.vendorId);
    if (action === 'new') {
      await saveVehicleAction({
        registrationTime: commonValues.createdTime,
        lastModified: commonValues.modifiedTime,
        modifiedBy: commonValues.modifiedBy,
        createdBy: commonValues.createdBy,
        tenantId: commonValues.tenantId,
        deleted: commonValues.deleted,
        vendorName: vendorRes.vendorName,
        ...values,
      });
      if (type === 'save') setVisible(false);
      if (type === 'add') {
        form.resetFields();
        setFormInitValues({ ...formInitState, vendorId: vendorRes.id });
      }
    }
    if (action === 'edit') {
      updateVehicleAction(values);
      if (type === 'add') form.resetFields({});
      setFormInitValues({ ...formInitState });
      setVisible(false);
    }
  };

  const saveVehicleAction = async (vehicle) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const newVehicle = { ...vehicle, vehicleIdImage: vehicle.vehicleIdImageUpload };
    delete newVehicle.vehicleIdImage;
    addVehicle(newVehicle)
      .then((res) => {
        setVehicles((state) => [res.data, ...state]);
        message.success('Succesfully Added Vehicle');
        form.resetFields();
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Vehicle, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editVehicleAction = (vehicle) => {
    form.setFieldsValue({ ...vehicle });
    setAction('edit');
    setVehicle(vehicle);
    setVisible(true);
  };

  const updateVehicleCall = (values) => {
    const data = { ...vehicle, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateVehicle(data)
      .then((res) => {
        setVehicles((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, res.data);
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        message.success('Succesfully Updated Vehicle');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Vehicle, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateVehicleAction = (values) => {
    updateVehicleCall(values);
  };

  const makeActive = (data) => {
    updateVehicleCall({ ...data, deleted: false });
  };

  const setDeleteVehicleAction = (userId, visible) => {
    setVehicles((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === userId).visible = visible;
      tempData
        .filter((x) => x.id !== userId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteVehicleAction = (userId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteVehicle(userId)
      .then(() => {
        setVehicles((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === userId).visible = false;
          tempState.find((x) => x.id === userId).deleted = true;
          return [...state].filter((x) => x.id !== userId);
        });
        message.success('Succesfully Deleted Vehicle');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Vehicle, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const clearForm = () => {
    form.resetFields();
  };

  const onPaginationChange = (e) => {
    init(+e - 1);
  };

  const init = async (page = 0) => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: true,
        };
      });
      const pagination = {
        size: totalVehicles.pageSize,
        page: page,
      };

      try {
        const vehicleRes = await getAllVehiclesByPagination(context.profile.tenantId, pagination);
        const vendorRes = await getAllVendors(context.profile.tenantId);
        const vehicles = (vehicleRes.data?.content || []).filter((x) => x.deleted === false);
        setTableData(vehicleRes.data.content);
        setVendorData(vendorRes.data.filter((x) => x.deleted === false));
        setVehicles(vehicles);
        setVehiclesOriginal(vehicles);
        setTotalVehicles((ps) => ({ ...ps, current: page, items: vehicleRes.data.totalElements }));
      } catch (e) {
        console.log(e);
        message.error('Unable to get Vehicles details, try again later');
      } finally {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      }
    }
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, []);

  const viewVehicleInfo = (e) => {
    setVehicle(e);
    setVehicleInfo(true);
  };

  const csvFileDownload = () => {
    const exportData = tableData.map(({ vechicleNumber, vendorName, vehicleType }) => {
      return { vechicleNumber, vendorName, vehicleType };
    });
    csvFile({ data: exportData, fileName: `vechiles_${new Date().toDateString()}`.replaceAll(' ', '_').toLowerCase() });
  };

  const vehicleType = [
    'Two wheeler',
    'Auto',
    'Tempo',
    'Car',
    'Pickup Truck',
    'Bus',
    'Truck',
    'Tractor',
    'Earth Mover',
    'Crane',
    'Others',
  ];

  const tableCols = [
    { title: <strong> Number </strong>, key: 'vechicleNumber', dataIndex: 'vechicleNumber' },
    { title: <strong> Vendor </strong>, key: 'vendorName', dataIndex: 'vendorName' },
    { title: <strong> Type </strong>, key: 'vehicleType', dataIndex: 'vehicleType' },
    {
      title: <strong> Actions </strong>,
      width: 350,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                Activate
              </Button>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.VEHICLE} permission={CRUD.VIEW}>
              <Button type="link" onClick={() => viewVehicleInfo(record)} className="actionButton">
                View
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.VEHICLE} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editVehicleAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.VEHICLE} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete user ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteVehicleAction(record.id)}
                onCancel={() => setDeleteVehicleAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteVehicleAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const vehicleBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.VEHICLE}].displayName`,
    ModuleNames.VEHICLE
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.MASTERS, vehicleBreadcrumbsName, 'Register Vehicle']} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.VEHICLE} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Vehicle
            </Button>
          </PermissionContainer>
          <PermissionContainer page={Pages.VEHICLE} permission={CRUD.VIEW}>
            <Button size="small" onClick={csvFileDownload} type="primary" className="downloadButton">
              Export <DownloadOutlined />
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <Row className="searchDiv" justify="end">
        <Col xs={24} lg={6}>
          <Search placeholder="Enter Search" allowClear enterButton onSearch={onVehicleSearch} />
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <>
            {tableModeNormal ? (
              <>
                <Table
                  size="small"
                  scroll={{ x: true }}
                  rowKey="id"
                  columns={tableCols}
                  dataSource={vehicles}
                  rowClassName={(record) => record.deleted && 'rowInactive'}
                  pagination={false}
                />
                <Row justify="end">
                  <Col>
                    <div className="m-2">
                      <Pagination
                        onChange={onPaginationChange}
                        current={totalVehicles.current + 1}
                        total={totalVehicles.items}
                        defaultPageSize={totalVehicles.pageSize}
                        showSizeChanger={false}
                        showQuickJumper={false}
                      />
                    </div>
                  </Col>
                </Row>
              </>
            ) : (
              <Table
                size="small"
                scroll={{ x: true }}
                rowKey="id"
                columns={tableCols}
                dataSource={vehicles}
                rowClassName={(record) => record.deleted && 'rowInactive'}
              />
            )}
          </>
        ) : (
          <>
            <CommonCompactView
              data={vehicles}
              onEdit={editVehicleAction}
              onDelete={deleteVehicleAction}
              permissions={[
                { pageName: Pages.VEHICLE, permission: CRUD.UPDATE, label: 'Edit' },
                { pageName: Pages.VEHICLE, permission: CRUD.DELETE, label: 'Delete' },
              ]}
              title="vechicleNumber"
              dataList={[
                { label: 'Vendor', value: 'vendorName' },
                { label: 'ID Proof', value: 'idProof' },
              ]}
            />
            <Row justify="end">
              <Col>
                <div className="m-2">
                  <Pagination
                    onChange={onPaginationChange}
                    current={totalVehicles.current + 1}
                    total={totalVehicles.items}
                    defaultPageSize={totalVehicles.pageSize}
                    showSizeChanger={false}
                    showQuickJumper={false}
                  />
                </div>
              </Col>
            </Row>
          </>
        )}
      </>

      <CommonDrawer title="Vehicle Information" visible={vehicleInfo} closeAdd={closeAdd}>
        <div className="infoDrawer">
          {vehicle ? (
            <Row className="infoContainer" span={24}>
              <Col className="info" span={24}>
                <Row className="infoImg" justify="center" span={24}>
                  <Col>
                    <img
                      src={`data:image/jpg;base64,${vehicle.photoImage}`}
                      onError={({ currentTarget }) => {
                        currentTarget.onerror = null; // prevents looping
                        currentTarget.src = '/assets/images/notFound.png';
                      }}
                      alt="Not Found"
                    />
                  </Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Number :
                  </Col>
                  <Col>{vehicle.vechicleNumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Vendor
                  </Col>
                  <Col>{vehicle.vendorName}</Col>
                </Row>

                <Row>
                  <Col className="infoTitle" span={10}>
                    Address 1 :
                  </Col>
                  <Col>{vehicle.vechicleAddress?.address1}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Address 2 :
                  </Col>
                  <Col>{vehicle.vechicleAddress?.address2}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    City :
                  </Col>
                  <Col>{vehicle.vechicleAddress?.city}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    State :
                  </Col>
                  <Col>{vehicle.vechicleAddress?.state}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Country :
                  </Col>
                  <Col>{vehicle.vechicleAddress?.country}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Pincode :
                  </Col>
                  <Col>{vehicle.vechicleAddress?.pincode}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Phone Number :
                  </Col>
                  <Col>{vehicle.vechicleAddress?.phonenumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created By :
                  </Col>
                  <Col>{vehicle.createdBy}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created Date :
                  </Col>
                  <Col>{new Date(vehicle.registrationTime).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified By :
                  </Col>
                  <Col>{vehicle.modifiedBy}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified Date :
                  </Col>
                  <Col>{new Date(vehicle.lastModified).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    ID Proof:
                  </Col>
                  <Col>{vehicle.idProof}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Properties 1 :
                  </Col>
                  <Col>{vehicle.properties?.additionalProp1}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Properties 2 :
                  </Col>
                  <Col>{vehicle.properties?.additionalProp2}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Properties 3 :
                  </Col>
                  <Col>{vehicle.properties?.additionalProp3}</Col>
                </Row>
                {vehicle.attributes && (
                  <Row>
                    <Col className="infoTitle" span={10}>
                      Attribute :
                    </Col>
                    <Col>
                      {
                        <div className="attributes">
                          <Row gutter={20} justify="space-between">
                            <Col> Name </Col>
                            <Col> Value </Col>
                            <Col> Type </Col>
                          </Row>
                          {vehicle.attributes?.map((i) => (
                            <Row gutter={20} key={i.name} justify="space-between">
                              <Col>{i.name}</Col>
                              <Col>{i.value}</Col>
                              <Col>{i.type}</Col>
                            </Row>
                          ))}
                        </div>
                      }
                    </Col>
                  </Row>
                )}
                <br />
                <Row justify="center">
                  <Button type="primary" onClick={() => editVehicleAction(vehicle)}>
                    Edit
                  </Button>
                </Row>
              </Col>
            </Row>
          ) : (
            <></>
          )}
        </div>
      </CommonDrawer>

      <CommonDrawer title="Vehicle" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          autoComplete={'new-password'}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 16 }}
          labelCol={{ span: 8 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Registration Number"
            name="vechicleNumber"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Vehicle Registration Number!',
                min: 10,
                max: 20,
              },
              { pattern: `^[A-Za-z0-9]+$`, message: 'Use Numbers and Letters only!' },
            ]}
          >
            <Input placeholder="Vehicle Registration Number" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Vehicle Type"
            name="vehicleType"
            rules={[
              {
                required: true,
                message: 'Please select Vehicle Type!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Vehicle Type"
            >
              {vehicleType.map((type, index) => (
                <Option title={type} key={index} value={type}>
                  {type}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Vehicle Make"
            name="vehicleMake"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Vehicle Make!',
                min: 4,
                max: 15,
              },
              { pattern: `^[a-zA-Z0-9 ]+$`, message: 'Use Letters and Numbers only!' },
            ]}
          >
            <Input placeholder="Vehicle Make" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Vehicle Model"
            name="vehicleModel"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Vehicle Model!',
              },
              { pattern: `^[a-zA-Z0-9 ]+$`, message: 'Use Letters and Numbers only!' },
            ]}
          >
            <Input placeholder="Vehicle Model" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Vendor"
            name="vendorId"
            rules={[
              {
                required: true,
                message: 'Please select Vendor!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Vendor"
            >
              {vendorData.map((b) => (
                <Option title={b.vendorName} key={b.id} value={b.id}>
                  {b.vendorName}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            label="ID Proof"
            name="idProof"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Vehicle RC Number!',
                min: 10,
                max: 20,
              },
            ]}
          >
            <Input placeholder="Vehicle RC Number" />
          </Form.Item>

          <Form.Item valuePropName="file" name="idProofImage" label="Image of RC" getValueFromEvent={normFile}>
            <Upload maxCount={1} customRequest={dummyCustomRequest} name="idProofImage">
              <Button icon={<UploadOutlined />}>Upload</Button>
            </Upload>
          </Form.Item>

          <Form.Item valuePropName="file" name="photoImage" label="Image of Vehicle" getValueFromEvent={normFile}>
            <Upload maxCount={1} customRequest={dummyCustomRequest} name="idProofImage">
              <Button icon={<UploadOutlined />}>Upload</Button>
            </Upload>
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Owned or Rentel"
            name="rental"
            rules={[
              {
                required: true,
                message: 'Please input State!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="State"
            >
              <Option value={false}>Owned</Option>
              <Option value={true}>Rental</Option>
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Address Line 1"
            name={['vechicleAddress', 'address1']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Locality!',
                min: 4,
              },
            ]}
          >
            <Input placeholder="Locality" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Address Line 2"
            name={['vechicleAddress', 'address2']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Street!',
                min: 4,
              },
            ]}
          >
            <Input placeholder="Street" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="City"
            name={['vechicleAddress', 'city']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input City!',
                min: 3,
              },
              { pattern: `^[a-zA-Z ]+$`, message: 'Use Letters only!' },
            ]}
          >
            <Input placeholder="City" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="State"
            name={['vechicleAddress', 'state']}
            rules={[
              {
                required: true,
                message: 'Please input State!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="State"
            >
              {Object.keys(state).map((b) => (
                <Option title={state[b]} key={state[b]} value={state[b]}>
                  {state[b]}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            initialValue="India"
            label="Country"
            name={['vechicleAddress', 'country']}
            rules={[
              {
                required: true,
                message: 'Please input Country!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Country"
            >
              {Object.keys(country).map((b) => (
                <Option title={country[b]} key={country[b]} value={country[b]}>
                  {country[b]}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Pincode"
            name={['vechicleAddress', 'pincode']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Pincode!',
                min: 6,
              },
              { pattern: `^[0-9]+$`, message: 'Use Numbers only!' },
            ]}
          >
            <Input placeholder="Pincode" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Office Phone"
            name="officePhno"
            rules={[
              {
                whitespace: true,
                message: 'Please input Office Phone Number!',
              },
              { pattern: InputRegex.Mobile, message: 'Enter a valid Mobile Number' },
            ]}
          >
            <Input placeholder="Office Phone Number" />
          </Form.Item>

          {/* WIP */}
          {/* <Form.Item
            label="Additional Property 1"
            name={['properties', 'additionalProp1']}
            rules={[
              {
                whitespace: true,
                message: 'Please Remove White Space',
              },
            ]}
          >
            <Input placeholder="Property 1" />
          </Form.Item>
          <Form.Item
            label="Additional Property 2"
            name={['properties', 'additionalProp2']}
            rules={[
              {
                whitespace: true,
                message: 'Please Remove White Space',
              },
            ]}
          >
            <Input placeholder="Property 2" />
          </Form.Item>
          <Form.Item
            label="Additional Property 3"
            name={['properties', 'additionalProp3']}
            rules={[
              {
                whitespace: true,
                message: 'Please Remove White Space',
              },
            ]}
          >
            <Input placeholder="Property 3" />
          </Form.Item> */}

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </>
  );
};

export default Vehicle;
