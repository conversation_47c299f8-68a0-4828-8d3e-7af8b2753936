"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Location,
  LocationPaginationParams,
  LocationPageSize,
} from "@/types/location";
import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { LocationTablePagination } from "./location-table-pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  MoreH<PERSON>zon<PERSON>,
  Edit,
  Trash2,
  <PERSON><PERSON>teCcw,
  MapPin,
  Calendar,
  Building2,
} from "lucide-react";

interface LocationDataTableProps {
  data: Location[];
  pagination: LocationPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: LocationPageSize[];
  onEdit: (location: Location) => void;
  onDelete: (locationId: string) => void;
  onToggleActive?: (location: Location) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: LocationPageSize) => void;
}

export function LocationDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onEdit,
  onDelete,
  onToggleActive,
  onPageChange,
  onPageSizeChange,
}: LocationDataTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [locationToDelete, setLocationToDelete] = useState<Location | null>(
    null
  );
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Reset dropdown state when data changes
  useEffect(() => {
    setOpenDropdownId(null);
  }, [data]);

  const handleDeleteClick = (location: Location) => {
    setLocationToDelete(location);
    setDeleteDialogOpen(true);
    setOpenDropdownId(null); // Close dropdown
  };

  const handleDeleteConfirm = () => {
    if (locationToDelete && onDelete) {
      onDelete(locationToDelete.id);
    }
    setDeleteDialogOpen(false);
    setLocationToDelete(null);
  };

  const handleEditClick = (location: Location) => {
    onEdit(location);
    setOpenDropdownId(null); // Close dropdown
  };

  const handleToggleActiveClick = (location: Location) => {
    if (onToggleActive) {
      onToggleActive(location);
    }
    setOpenDropdownId(null); // Close dropdown
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCoordinates = (gpsPoint: {
    latitude: string;
    longitude: string;
  }) => {
    const lat = parseFloat(gpsPoint.latitude);
    const lng = parseFloat(gpsPoint.longitude);
    if (isNaN(lat) || isNaN(lng)) return "Invalid coordinates";
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
  };

  return (
    <div>
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Name</TableHead>
              <TableHead className="hidden md:table-cell">Branch</TableHead>
              <TableHead className="hidden lg:table-cell">Address</TableHead>
              <TableHead className="hidden lg:table-cell">
                GPS Coordinates
              </TableHead>
              <TableHead className="hidden xl:table-cell">Status</TableHead>
              <TableHead className="hidden xl:table-cell">Created</TableHead>
              <TableHead className="hidden xl:table-cell">Modified</TableHead>
              <TableHead className="text-left">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <MapPin className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">No locations found</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              data.map((location) => (
                <TableRow
                  key={location.id}
                  className={location.deleted ? "opacity-50" : ""}
                >
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <Link
                        href={`/device/management/configure/area/${location.id}`}
                        className="font-semibold text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {location.name}
                      </Link>
                      <span className="text-sm text-muted-foreground md:hidden">
                        {location.branchName || "Unknown Branch"}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <div className="flex items-center gap-1">
                      <Building2 className="h-3 w-3 text-muted-foreground" />
                      <span>{location.branchName || "Unknown Branch"}</span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div
                      className="max-w-[200px] truncate"
                      title={location.address}
                    >
                      {location.address}
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span className="text-xs font-mono">
                        {formatCoordinates(location.gpsPoint)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <Badge
                      variant={location.deleted ? "destructive" : "default"}
                      className="text-xs"
                    >
                      {location.deleted ? "Inactive" : "Active"}
                    </Badge>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {formatDate(location.createdTime)}
                    </div>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {formatDate(location.modifiedTime)}
                    </div>
                  </TableCell>
                  <TableCell className="text-left">
                    <DropdownMenu
                      open={openDropdownId === location.id}
                      onOpenChange={(open) =>
                        setOpenDropdownId(open ? location.id : null)
                      }
                    >
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {location.deleted ? (
                          // For deleted locations, only show Activate
                          onToggleActive && (
                            <DropdownMenuItem
                              onClick={() => handleToggleActiveClick(location)}
                              className="flex items-center gap-2"
                            >
                              <RotateCcw className="h-4 w-4" />
                              Activate
                            </DropdownMenuItem>
                          )
                        ) : (
                          // For active locations, show Edit and Delete
                          <>
                            <DropdownMenuItem
                              onClick={() => handleEditClick(location)}
                              className="flex items-center gap-2"
                            >
                              <Edit className="h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteClick(location)}
                              className="flex items-center gap-2 text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination at bottom of table */}
      <LocationTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Location</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{locationToDelete?.name}"? This
              will mark the location as inactive.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
