@import '../../../Colors.less';

.cardContainer {
  padding: 20px;

  .greenSign {
    filter: invert(63%) sepia(26%) saturate(730%) hue-rotate(69deg) brightness(99%) contrast(93%);
  }

  .redSign {
    filter: invert(49%) sepia(78%) saturate(3787%) hue-rotate(335deg) brightness(100%) contrast(102%);
  }

  .sanitaryCard {
    margin: 10px;
    width: 200px;
    border-radius: 5px;
    box-shadow: 1px 1px 5px 1px rgba(0, 0, 0, 0.2);
    border: 1px solid @light-gray-hex;
    .cardImage {
      padding: 10px 30px 10px 10px;
      height: 90px;
    }
    .greenSignSvg {
      width: 20px;
    }
    .cardTitle {
      padding: 5px 0 5px 0;
      border-bottom: 1px solid @light-gray-hex;
      border-top-right-radius: 5px;
      border-top-left-radius: 5px;
    }
    .cardInfo {
      border-top: 1px solid @light-gray-hex;
      border-bottom-right-radius: 5px;
      border-bottom-left-radius: 5px;
      padding: 10px;
    }
    .cardStatus {
      border-top: 1px solid @light-gray-hex;
      padding: 5px 15px 5px 15px;
    }
  }
}

.svg45 {
  width: 45px;
  cursor: pointer;
}
.svg35 {
  width: 35px;
  cursor: pointer;
}
.svg65 {
  width: 65px;
  cursor: pointer;
}
.svg30 {
  width: 30px;
  cursor: pointer;
}

.selectArea {
  width: 200px !important;
}

.form {
  row-gap: 10px;
}
