'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Building, Square, Navigation, Wifi, Tag, Package, Settings, Users } from 'lucide-react';
import Link from 'next/link';

const DigitalCarpetPage = () => {
  const menuItems = [
    {
      title: 'Setup Indoor Map',
      description: 'Configure indoor mapping and layout',
      icon: MapPin,
      href: '/digital-carpet/setup-indoor-map',
      color: 'text-blue-600'
    },
    {
      title: 'Create Branch',
      description: 'Manage branch locations and settings',
      icon: Building,
      href: '/digital-carpet/create-branch',
      color: 'text-green-600'
    },
    {
      title: 'Create Location',
      description: 'Define and manage location points',
      icon: Navigation,
      href: '/digital-carpet/create-location',
      color: 'text-purple-600'
    },
    {
      title: 'Create Area',
      description: 'Set up areas and zones',
      icon: Square,
      href: '/digital-carpet/create-area',
      color: 'text-orange-600'
    },
    {
      title: 'Create Transit Area',
      description: 'Configure transit and movement areas',
      icon: Navigation,
      href: '/digital-carpet/create-transit-area',
      color: 'text-indigo-600'
    },
    {
      title: 'Create Gateway',
      description: 'Manage gateway devices and connections',
      icon: Wifi,
      href: '/digital-carpet/create-gateway',
      color: 'text-cyan-600'
    },
    {
      title: 'Create Tag',
      description: 'Configure tags and identifiers',
      icon: Tag,
      href: '/digital-carpet/create-tag',
      color: 'text-pink-600'
    },
    {
      title: 'Create Asset',
      description: 'Manage assets and inventory',
      icon: Package,
      href: '/digital-carpet/create-asset',
      color: 'text-emerald-600'
    },
    {
      title: 'Provision',
      description: 'Device provisioning and setup',
      icon: Settings,
      href: '/digital-carpet/provision',
      color: 'text-amber-600'
    },
    {
      title: 'Onboard',
      description: 'Onboard new devices and assets',
      icon: Users,
      href: '/digital-carpet/onboard',
      color: 'text-red-600'
    }
  ];

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Digital Carpet</h1>
        <p className="text-gray-600">Manage your digital infrastructure and assets</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {menuItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Link key={item.href} href={item.href}>
              <Card className="h-full hover:shadow-lg transition-shadow duration-200 cursor-pointer group">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg bg-gray-50 group-hover:bg-gray-100 transition-colors`}>
                      <IconComponent className={`h-6 w-6 ${item.color}`} />
                    </div>
                    <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-gray-700">
                      {item.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default DigitalCarpetPage;
