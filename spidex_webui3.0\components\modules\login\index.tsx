import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import LoginForm from "@/components/forms/login-form";
import Image from "next/image";
import dynamic from "next/dynamic";

const ParticlesDynamic = dynamic(() => import("@/components/particles"));

export default function Page() {
  return (
    <div className="flex min-h-svh bg-gradient-to-r from-[#0d47a1] via-[#1976d2] to-[#42a5f5] bg-[length:150%_150%] animate-gradient-slow w-full items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm z-10">
        <div className="flex flex-col gap-6">
          <Card className="bg-white/80 backdrop-blur-md">
            <CardHeader>
              <CardTitle>
                <div className="grid gap-2 text-center justify-center">
                  <Image
                    priority
                    src="/logo.png"
                    alt="logo"
                    width={270}
                    height={90}
                    className="object-cover dark:brightness-[100]"
                  />
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <LoginForm />
            </CardContent>
          </Card>
        </div>
      </div>
      <ParticlesDynamic />
    </div>
  );
}
