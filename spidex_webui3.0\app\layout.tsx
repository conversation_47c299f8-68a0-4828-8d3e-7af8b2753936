import "../globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import Script from "next/script";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Spidex",
  description: "Spidex Asset Tracking Platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        {children}
        <Script
          src="https://assets.what3words.com/sdk/v3/what3words.js?key=YNTBQ7JF"
          strategy="beforeInteractive"
        />
        <Script
          src="https://assets.what3words.com/sdk/v3/autosuggest/what3words-autosuggest.js"
          strategy="beforeInteractive"
        />
      </body>
    </html>
  );
}
