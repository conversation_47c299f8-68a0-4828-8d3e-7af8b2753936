"use client";

import { useState, useEffect } from "react";
import { Search, X } from "lucide-react";
import { TaggedAssetSearchFilters } from "@/types/tagged-asset";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

import { Badge } from "@/components/ui/badge";

interface TaggedAssetSearchPaginationProps {
  searchFilters: TaggedAssetSearchFilters;
  onSearchChange: (filters: Partial<TaggedAssetSearchFilters>) => void;
  onClearSearch: () => void;
  showDeleted: boolean;
  onToggleShowDeleted: () => void;
  onRefresh: () => void;

  // Loading state
  isLoading?: boolean;
}

export function TaggedAssetSearchPagination({
  searchFilters,
  onSearchChange,
  onClearSearch,
  showDeleted,
  onToggleShowDeleted,
  onRefresh,
  isLoading = false,
}: TaggedAssetSearchPaginationProps) {
  const [localSearchTerm, setLocalSearchTerm] = useState(
    searchFilters.searchTerm || ""
  );

  // Update local search term when filters change
  useEffect(() => {
    setLocalSearchTerm(searchFilters.searchTerm || "");
  }, [searchFilters.searchTerm]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange({ searchTerm: localSearchTerm });
  };

  const handleClearSearch = () => {
    setLocalSearchTerm("");
    onClearSearch();
  };

  const hasActiveFilters =
    searchFilters.searchTerm ||
    searchFilters.status !== undefined ||
    searchFilters.provisioned !== undefined;

  const getActiveFilterCount = () => {
    let count = 0;
    if (searchFilters.searchTerm) count++;
    if (searchFilters.status !== undefined) count++;
    if (searchFilters.provisioned !== undefined) count++;
    return count;
  };

  return (
    <div className="space-y-4">
      {/* Search Form */}
      <form onSubmit={handleSearchSubmit} className="space-y-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1">
            <Label htmlFor="search" className="sr-only">
              Search tagged assets
            </Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                type="text"
                placeholder="Search by asset name, tag name, external ID..."
                value={localSearchTerm}
                onChange={(e) => {
                  const value = e.target.value;
                  setLocalSearchTerm(value);
                  // Trigger search immediately on input change
                  onSearchChange({ searchTerm: value });
                }}
                className="pl-10 pr-10"
                disabled={isLoading}
              />
              {localSearchTerm && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                  onClick={() => {
                    setLocalSearchTerm("");
                    onSearchChange({ searchTerm: "" });
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Search Button */}
          <Button type="submit" className="shrink-0" disabled={isLoading}>
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </div>
      </form>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 pt-2 border-t">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="showDeleted"
            checked={showDeleted}
            onCheckedChange={onToggleShowDeleted}
          />
          <Label
            htmlFor="showDeleted"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Show Inactive Tagged Assets
          </Label>
        </div>

        {/* Status Filter */}
        <div className="flex items-center space-x-2">
          <Label className="text-sm font-medium">Status:</Label>
          <div className="flex gap-1">
            <Button
              variant={
                searchFilters.status === undefined ? "default" : "outline"
              }
              size="sm"
              onClick={() => onSearchChange({ status: undefined })}
              className="h-8 px-3 text-xs"
            >
              All
            </Button>
            <Button
              variant={
                searchFilters.status === "ACTIVE" ||
                searchFilters.status === "TAGGED"
                  ? "default"
                  : "outline"
              }
              size="sm"
              onClick={() => onSearchChange({ status: "ACTIVE" })}
              className="h-8 px-3 text-xs"
            >
              Active
            </Button>
            <Button
              variant={
                searchFilters.status === "INACTIVE" ? "default" : "outline"
              }
              size="sm"
              onClick={() => onSearchChange({ status: "INACTIVE" })}
              className="h-8 px-3 text-xs"
            >
              Inactive
            </Button>
          </div>
        </div>

        {/* Provisioned Filter */}
        <div className="flex items-center space-x-2">
          <Label className="text-sm font-medium">Provisioned:</Label>
          <div className="flex gap-1">
            <Button
              variant={
                searchFilters.provisioned === undefined ? "default" : "outline"
              }
              size="sm"
              onClick={() => onSearchChange({ provisioned: undefined })}
              className="h-8 px-3 text-xs"
            >
              All
            </Button>
            <Button
              variant={
                searchFilters.provisioned === true ? "default" : "outline"
              }
              size="sm"
              onClick={() => onSearchChange({ provisioned: true })}
              className="h-8 px-3 text-xs"
            >
              Yes
            </Button>
            <Button
              variant={
                searchFilters.provisioned === false ? "default" : "outline"
              }
              size="sm"
              onClick={() => onSearchChange({ provisioned: false })}
              className="h-8 px-3 text-xs"
            >
              No
            </Button>
          </div>
        </div>

        {/* Current Results Info */}
        <div className="text-sm text-muted-foreground">
          {hasActiveFilters && (
            <>
              Showing filtered results
              {searchFilters.searchTerm && (
                <span className="ml-1">
                  matching &quot;{searchFilters.searchTerm}&quot;
                </span>
              )}
              {showDeleted && <span className="ml-1">(inactive only)</span>}
            </>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 pt-2 border-t">
          <span className="text-sm font-medium">Active filters:</span>

          {searchFilters.searchTerm && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: &quot;{searchFilters.searchTerm}&quot;
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSearchChange({ searchTerm: "" })}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {searchFilters.status !== undefined && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Status: {searchFilters.status}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSearchChange({ status: undefined })}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {searchFilters.provisioned !== undefined && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Provisioned: {searchFilters.provisioned ? "Yes" : "No"}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSearchChange({ provisioned: undefined })}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {showDeleted && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Show Inactive
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleShowDeleted}
                className="h-4 w-4 p-0 ml-1"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
