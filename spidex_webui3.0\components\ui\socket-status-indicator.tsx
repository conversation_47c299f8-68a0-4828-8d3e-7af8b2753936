import { Loader2, Wifi, <PERSON>Circle } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import clsx from "clsx";

export function SocketStatusIndicator({
  connected,
  connecting,
  className = "",
}: {
  connected: boolean;
  connecting: boolean;
  className?: string;
}) {
  let color = "bg-red-100 border-red-300 text-red-700";
  let icon = <XCircle className="w-4 h-4" />;
  let label = "Offline";
  let animate = false;

  if (connecting) {
    color = "bg-yellow-100 border-yellow-300 text-yellow-800";
    icon = <Loader2 className="w-4 h-4 animate-spin" />;
    label = "Connecting";
    animate = true;
  } else if (connected) {
    color = "bg-green-100 border-green-300 text-green-800";
    icon = <Wifi className="w-4 h-4" />;
    label = "Live";
    animate = false;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span
            className={clsx(
              "inline-flex items-center gap-1 px-2 py-0.5 rounded-full border text-xs font-medium shadow-sm select-none transition-all",
              color,
              animate && "animate-pulse",
              className
            )}
            aria-live="polite"
            aria-label={`Socket status: ${label}`}
            title={label}
          >
            {icon}
            <span>{label}</span>
          </span>
        </TooltipTrigger>
        <TooltipContent>
          {label === "Live"
            ? "Connected to live updates"
            : label === "Connecting"
            ? "Attempting to connect to live updates"
            : "Not connected to live updates"}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
