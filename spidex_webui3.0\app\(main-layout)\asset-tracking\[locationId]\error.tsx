"use client";
import { Alert<PERSON>riangle, RefreshCw } from "lucide-react";

export default function Error() {
  const handleRetry = () => {
    if (typeof window !== "undefined") {
      window.location.reload();
    }
  };

  return (
    <div className="flex flex-col items-center justify-center h-[60vh] w-full">
      <AlertTriangle className="text-destructive mb-4" size={48} />
      <div className="text-lg font-semibold text-destructive mb-2">
        Something went wrong
      </div>
      <div className="text-sm text-muted-foreground mb-4">
        An unexpected error occurred. Please try again or contact support if the
        problem persists.
      </div>
      <button
        onClick={handleRetry}
        className="inline-flex items-center gap-2 px-4 py-2 rounded bg-primary text-white hover:bg-primary/90 transition-colors"
      >
        <RefreshCw className="w-4 h-4" /> Retry
      </button>
    </div>
  );
}
