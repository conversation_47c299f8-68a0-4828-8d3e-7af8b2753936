"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect } from "react";
import { Asset } from "@/types/asset";
import {
  CreateAssetSchema,
  UpdateAssetSchema,
  CreateAssetFormData,
  UpdateAssetFormData,
  ASSET_TYPES,
} from "@/lib/schemas/asset";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Loader2,
  Package,
  User,
  Car,
  Building,
  Wrench,
  RockingChair,
} from "lucide-react";

interface AssetFormProps {
  asset?: Asset;
  isLoading?: boolean;
  onSubmit: (data: CreateAssetFormData | UpdateAssetFormData) => Promise<void>;
  onCancel: () => void;
  onClear?: () => void;
}

export function AssetForm({
  asset,
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: AssetFormProps) {
  const isEditing = !!asset;
  const schema = isEditing ? UpdateAssetSchema : CreateAssetSchema;
  const [selectedAssetType, setSelectedAssetType] = useState<string>("");

  const form = useForm<CreateAssetFormData | UpdateAssetFormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: "",
      externalId: "",
      assetType: ASSET_TYPES.WORKER,
      status: false,
      type: "",
    },
  });

  // Reset form when asset data changes (for editing)
  useEffect(() => {
    if (asset) {
      console.log("🔄 Resetting asset form with data:", asset);
      const formData = {
        id: asset.id,
        name: asset.name,
        externalId: asset.externalId,
        assetType: asset.assetType,
        status: asset.status === "true",
        type: asset.assetType === ASSET_TYPES.OTHERS ? asset.assetType : "",
      };
      console.log("🔄 Asset form data being set:", formData);
      form.reset(formData);
      setSelectedAssetType(asset.assetType);
    } else {
      console.log("🆕 Resetting asset form for new asset");
      const emptyFormData = {
        name: "",
        externalId: "",
        assetType: ASSET_TYPES.WORKER,
        status: false,
        type: "",
      };
      console.log("🆕 Empty asset form data being set:", emptyFormData);
      form.reset(emptyFormData);
      setSelectedAssetType(ASSET_TYPES.WORKER);
    }
  }, [asset, form]);

  // Watch asset type changes
  const watchedAssetType = form.watch("assetType");

  useEffect(() => {
    setSelectedAssetType(watchedAssetType);
    // Clear the type field when asset type changes from Others to something else
    if (watchedAssetType !== ASSET_TYPES.OTHERS) {
      form.setValue("type", "");
    }
  }, [watchedAssetType, form]);

  const handleSubmit = async (
    data: CreateAssetFormData | UpdateAssetFormData
  ) => {
    try {
      // Trigger validation manually to ensure all fields are validated
      const isValid = await form.trigger();

      if (!isValid) {
        console.error("❌ Form validation failed:", form.formState.errors);
        // Show validation errors to user
        Object.keys(form.formState.errors).forEach((field) => {
          const error =
            form.formState.errors[field as keyof typeof form.formState.errors];
          if (error?.message) {
            console.error(`❌ Validation error for ${field}:`, error.message);
          }
        });
        return;
      }

      // Clean up the data before submission
      const cleanedData = {
        ...data,
        // If asset type is not "Others", clear the type field
        type: data.assetType === ASSET_TYPES.OTHERS ? data.type : "",
      };

      console.log("🔄 Cleaned data for submission:", cleanedData);
      await onSubmit(cleanedData);

      if (!isEditing) {
        form.reset();
        setSelectedAssetType("");
      }
    } catch (error) {
      console.error("❌ Asset form submission error:", error);
      console.error("❌ Error details:", error);
      throw error;
    }
  };

  const handleClear = () => {
    form.reset();
    setSelectedAssetType("");
    if (onClear) {
      onClear();
    }
  };

  const handleCancel = () => {
    form.reset();
    setSelectedAssetType("");
    onCancel();
  };

  const getAssetTypeIcon = (type: string) => {
    switch (type) {
      case ASSET_TYPES.WORKER:
        return <User className="h-4 w-4" />;
      case ASSET_TYPES.VENDOR:
        return <Building className="h-4 w-4" />;
      case ASSET_TYPES.VEHICLE:
        return <Car className="h-4 w-4" />;
      case ASSET_TYPES.SANITARY:
        return <Wrench className="h-4 w-4" />;
      case ASSET_TYPES.OTHERS:
        return <Package className="h-4 w-4" />;
      case ASSET_TYPES.CHAIR:
        return <RockingChair className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardContent className="space-y-4 pt-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Asset Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="(1-200 characters)"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="externalId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>External ID</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="(1-200 characters)"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="assetType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Asset Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select the type of asset you want to create" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(ASSET_TYPES).map((type) => (
                        <SelectItem key={type} value={type}>
                          <div className="flex items-center gap-2">
                            {getAssetTypeIcon(type)}
                            {type}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedAssetType === ASSET_TYPES.OTHERS && (
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custom Asset Type</FormLabel>
                    <FormControl>
                      <Input
                        placeholder=" Specify the custom asset type (1-200 characters)"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Status</FormLabel>
                    <FormDescription>
                      Enable or disable this asset
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 ml-2 border-t">
          <Button
            type="submit"
            disabled={isLoading}
            className="flex-1 sm:flex-none bg-green-700 text-white hover:bg-green-800 rounded"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? "Update Asset" : "Create Asset"}
          </Button>

          {!isEditing && (
            <Button
              type="button"
              variant="outline"
              onClick={handleClear}
              disabled={isLoading}
              className="border bg-white border-gray-700 text-black hover:bg-green-20 rounded"
            >
              Clear
            </Button>
          )}

          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
            className="border bg-white border-red-700 text-black hover:bg-green-20 rounded"
          >
            Close
          </Button>
        </div>
      </form>
    </Form>
  );
}
