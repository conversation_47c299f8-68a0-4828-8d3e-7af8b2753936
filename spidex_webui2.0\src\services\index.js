import {
  getAllBranches,
  addBranch,
  updateBranch,
  deleteBranch,
  getAllLocations,
  addLocation,
  updateLocation,
  deleteLocation,
  getAllAreas,
  addArea,
  updateArea,
  deleteArea,
  getAllTags,
  addTag,
  addAllTags,
  updateTag,
  deleteTag,
  getAllAssets,
  addAsset,
  updateAsset,
  deleteAsset,
  getAssetInfo,
  getAllTaggedAssets,
  addTaggedAsset,
  updateTaggedAsset,
  deleteTaggedAsset,
  getAllGateways,
  addGateway,
  updateGateway,
  deleteGateway,
  getAllTransitGateways,
  addTransitGateway,
  updateTransitGateway,
  deleteTransitGateway,
  getAllTgassetAssets,
  getAllTgassetTags,
  getAllTaggedAssetsByPagination,
  getAllAssetsByPagination,
  getAllTagsByPagination,
} from './deviceManagement';

import {
  getAllEventAttrs,
  addEventAttr,
  updateEventAttr,
  deleteEventAttr,
  getAllGatewayConfigs,
  addGatewayConfig,
  deleteGatewayConfig,
  getAllTagConfigs,
  addTagConfig,
  deleteTagConfig,
} from './deviceConfigure';

import { getTenant, getAllTenants, addTenant, updateTenant, deleteTenant } from './common';

import {
  getAllAccounts,
  getAccount,
  addAccount,
  updateAccount,
  deleteAccount,
  passwordReset,
  forgotPassword,
} from './userManagement';

import {
  addRole,
  getAllRoles,
  deleteRole,
  addPage,
  deletePage,
  getAllPages,
  linkPageRole,
  deletePageRoleLink,
  getPagesRolesLinksByRole,
  getAllPagesRolesLinks,
} from './authorization';

import {
  getAmblightSensorInitialData,
  getHumiditySensorInitialData,
  getSensorProximityInitial,
  getTempSensorInitialData,
  getSensorInitialData,
  getSensorDataByDate,
  getGatewaySensorInitialData,
  getRfidReport,
  getPressureSensorInitialData,
  getTempSensorMetrics,
  getHumiditySensorMetrics,
  getAmblightSensorMetrics,
  getPressureSensorMetrics,
  getSensorProximityByDate,
  getSensorHealthByDate,
  getTempSensorByDate,
  getPressureSensorByDate,
  getHumidSensorByDate,
  getAmblSensorByDate,
  getSensorProximityInitialByLocation,
} from './sensor';

import {
  getAllVendors,
  getVendor,
  addVendor,
  updateVendor,
  deleteVendor,
  getAllWorkers,
  getWorker,
  addWorker,
  updateWorker,
  deleteWorker,
  getAllVehicles,
  getVehicle,
  addVehicle,
  updateVehicle,
  deleteVehicle,
  getSearchVehicles,
  getSearchWorkers,
  getSearchVendors,
  getAllVendorsByPagination,
  getAllWorkersByPagination,
  getAllVehiclesByPagination,
  getVendorsCount,
  getWorkersCount,
  getVehiclesCount,
  vendorWiseReport,
  workerVendorWiseReport,
  vehicleWiseReport,
  areaWiseReport,
  monthlyBillingReport,
  vendorWiseReportInRange,
  dayBillingReport,
  washroomPost,
  washroomGetOne,
  washroomsGetAll,
  getEcu,
  getAllEcu,
  getAllEcuByPagination,
  addEcu,
  updateEcu,
  deleteEcu,
  getVin,
  getAllVin,
  getAllVinByPagination,
  addVin,
  updateVin,
  deleteVin,
  getVehicleModel,
  getAllVehicleModel,
  getSearchVehicleManufacturer,
  getAllVehicleModelByPagination,
  addVehicleModel,
  updateVehicleModel,
  getSearchVehicleModel,
  deleteVehicleModel,
  getModelEcu,
  getAllModelEcu,
  getAllModelEcuByPagination,
  addModelEcu,
  updateModelEcu,
  deleteModelEcu,
  getAllEcuData,
  getVinInfoByVinNumber,
  addAllVin,
  getVinEcu,
  getAllVinEcu,
  getAllVinEcuByPagination,
  getVinEcuByVinNumberAndModel,
  addVinEcu,
  updateVinEcu,
  deleteVinEcu,
  addAllVins,
  addBulkVinEcus,
  getAllVehicleModelsForCampaign,
  getAllCampaignsByPagination,
  getSchedulesUpdateCall,
  getVinEcusByDate,
  getVinCountByManufacture,
  getModelCountByManufacture,
  getEcusCountByManufacture,
  getvehiclemodelVariantByManufacture,
} from './masterManagemant';

import { addTenantProfile, getAllTenantProfile, getTenantProfile, deleteTenantProfile } from './applicationManagement';

export {
  getAllBranches,
  addBranch,
  updateBranch,
  deleteBranch,
  getAllLocations,
  addLocation,
  updateLocation,
  deleteLocation,
  getAllAreas,
  addArea,
  updateArea,
  deleteArea,
  getAllTags,
  addTag,
  addAllTags,
  updateTag,
  deleteTag,
  getAllAssets,
  addAsset,
  updateAsset,
  deleteAsset,
  getAllTaggedAssets,
  addTaggedAsset,
  updateTaggedAsset,
  deleteTaggedAsset,
  getAllGateways,
  addGateway,
  updateGateway,
  deleteGateway,
  getAllEventAttrs,
  addEventAttr,
  updateEventAttr,
  deleteEventAttr,
  getAllGatewayConfigs,
  addGatewayConfig,
  deleteGatewayConfig,
  getAllTagConfigs,
  addTagConfig,
  deleteTagConfig,
  getAllTransitGateways,
  addTransitGateway,
  updateTransitGateway,
  deleteTransitGateway,
  getTenant,
  getAllTenants,
  addTenant,
  updateTenant,
  deleteTenant,
  getAllAccounts,
  getAccount,
  addAccount,
  updateAccount,
  deleteAccount,
  passwordReset,
  forgotPassword,
  addRole,
  getAllRoles,
  deleteRole,
  addPage,
  deletePage,
  getAllPages,
  linkPageRole,
  deletePageRoleLink,
  getPagesRolesLinksByRole,
  getAllPagesRolesLinks,
  getSensorInitialData,
  getSensorDataByDate,
  getTempSensorMetrics,
  getHumiditySensorMetrics,
  getAmblightSensorMetrics,
  getPressureSensorMetrics,
  getSensorProximityByDate,
  getGatewaySensorInitialData,
  getAmblightSensorInitialData,
  getHumiditySensorInitialData,
  getSensorProximityInitial,
  getTempSensorInitialData,
  getPressureSensorInitialData,
  getAllVendors,
  getVendor,
  addVendor,
  updateVendor,
  deleteVendor,
  getAllWorkers,
  getWorker,
  addWorker,
  updateWorker,
  deleteWorker,
  getAllVehicles,
  getVehicle,
  addVehicle,
  updateVehicle,
  deleteVehicle,
  getSearchVehicles,
  getSearchWorkers,
  getSearchVendors,
  getAllVendorsByPagination,
  getAllWorkersByPagination,
  getAllVehiclesByPagination,
  getRfidReport,
  getVendorsCount,
  getWorkersCount,
  getVehiclesCount,
  vendorWiseReport,
  workerVendorWiseReport,
  vehicleWiseReport,
  areaWiseReport,
  monthlyBillingReport,
  getAllTgassetAssets,
  getAllTgassetTags,
  vendorWiseReportInRange,
  dayBillingReport,
  washroomPost,
  washroomGetOne,
  washroomsGetAll,
  getAssetInfo,
  addTenantProfile,
  getAllTenantProfile,
  getTenantProfile,
  deleteTenantProfile,
  getAllTaggedAssetsByPagination,
  getAllAssetsByPagination,
  getAllTagsByPagination,
  getSensorHealthByDate,
  getTempSensorByDate,
  getPressureSensorByDate,
  getHumidSensorByDate,
  getAmblSensorByDate,
  getSensorProximityInitialByLocation,
  getEcu,
  getAllEcu,
  getAllEcuByPagination,
  addEcu,
  updateEcu,
  deleteEcu,
  getVin,
  getAllVin,
  getAllVinByPagination,
  addVin,
  updateVin,
  deleteVin,
  getVehicleModel,
  getSearchVehicleManufacturer,
  getAllVehicleModelByPagination,
  addVehicleModel,
  updateVehicleModel,
  deleteVehicleModel,
  getSearchVehicleModel,
  getModelEcu,
  getAllModelEcu,
  getAllModelEcuByPagination,
  addModelEcu,
  updateModelEcu,
  deleteModelEcu,
  getAllVehicleModel,
  getAllEcuData,
  getVinInfoByVinNumber,
  addAllVin,
  getVinEcu,
  getAllVinEcu,
  getAllVinEcuByPagination,
  getVinEcuByVinNumberAndModel,
  addVinEcu,
  updateVinEcu,
  deleteVinEcu,
  addAllVins,
  addBulkVinEcus,
  getAllVehicleModelsForCampaign,
  getAllCampaignsByPagination,
  getSchedulesUpdateCall,
  getVinEcusByDate,
  getVinCountByManufacture,
  getModelCountByManufacture,
  getEcusCountByManufacture,
  getvehiclemodelVariantByManufacture,
};
