"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  Gateway,
  GatewaySearchFilters,
  GatewayPaginationParams,
  GatewayPageSize,
  CreateGatewayFormData,
  UpdateGatewayFormData,
  GatewayModel,
} from "@/types/gateway";
import { Area, Location } from "@/types/asset-tracking";

// Default values
const DEFAULT_GATEWAY_PAGE_SIZE: GatewayPageSize = 25;
const GATEWAY_PAGE_SIZES: GatewayPageSize[] = [10, 25, 50, 100];

// Default gateway models as fallback when API fails
const getDefaultGatewayModels = (): GatewayModel[] => [
  {
    modelId: "default-fixed",
    modelName: "Default Fixed Gateway",
    deviceType: "gateway",
    gatewayConfig: { type: "fixed" },
  },
  {
    modelId: "default-transit",
    modelName: "Default Transit Gateway",
    deviceType: "gateway",
    gatewayConfig: { type: "transit" },
  },
  {
    modelId: "default-lint",
    modelName: "Default Lint Gateway",
    deviceType: "gateway",
    gatewayConfig: { type: "lint" },
  },
];

interface UseGatewayManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: GatewayPageSize;
  initialPageSize?: number;
  availablePageSizes?: GatewayPageSize[];
  areaId?: string;
}

export const useGatewayManagement = ({
  autoLoad = true,
  defaultPageSize = DEFAULT_GATEWAY_PAGE_SIZE,
  initialPageSize,
  availablePageSizes,
  areaId,
}: UseGatewayManagementOptions = {}) => {
  // Get authenticated API instance and session
  const spidexApi = useSpidexApi();
  const { data: session, status } = useSession();

  // State
  const [isLoading, setIsLoading] = useState(false); // For initial data loading
  const [isCrudLoading, setIsCrudLoading] = useState(false); // For CRUD operations
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);

  // Data cache - store all gateways for client-side pagination
  const [allGatewaysCache, setAllGatewaysCache] = useState<Gateway[]>([]);
  const [areasCache, setAreasCache] = useState<Area[]>([]);
  const [locationsCache, setLocationsCache] = useState<Location[]>([]);
  const [modelsCache, setModelsCache] = useState<GatewayModel[]>([]);

  // Search and pagination state
  const [searchFilters, setSearchFilters] = useState<GatewaySearchFilters>({
    searchTerm: "",
    categoryType: undefined,
    areaId: areaId || undefined,
    locationId: undefined,
    modelId: undefined,
    active: undefined,
  });

  const [pagination, setPagination] = useState<GatewayPaginationParams>({
    page: 1,
    pageSize: (initialPageSize as GatewayPageSize) || defaultPageSize,
  });

  // Safe search filters to prevent undefined issues
  const safeSearchFilters = useMemo(() => {
    return {
      searchTerm: searchFilters.searchTerm || "",
      categoryType: searchFilters.categoryType,
      areaId: searchFilters.areaId,
      locationId: searchFilters.locationId,
      modelId: searchFilters.modelId,
      active: searchFilters.active,
    };
  }, [searchFilters]);

  // Filter gateways based on search criteria
  const filteredGateways = useMemo(() => {
    let filtered = [...allGatewaysCache];

    // Filter by deleted status
    if (!showDeleted) {
      filtered = filtered.filter((gateway) => !gateway.deleted);
    } else {
      filtered = filtered.filter((gateway) => gateway.deleted);
    }

    // Filter by search term
    if (safeSearchFilters.searchTerm) {
      const searchTerm = safeSearchFilters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (gateway) =>
          gateway.name.toLowerCase().includes(searchTerm) ||
          gateway.description?.toLowerCase().includes(searchTerm) ||
          gateway.externalId?.toLowerCase().includes(searchTerm) ||
          gateway.areaName?.toLowerCase().includes(searchTerm) ||
          gateway.locationName?.toLowerCase().includes(searchTerm)
      );
    }

    // Filter by category type
    if (safeSearchFilters.categoryType) {
      filtered = filtered.filter(
        (gateway) => gateway.categoryType === safeSearchFilters.categoryType
      );
    }

    // Filter by category type
    if (safeSearchFilters.categoryType) {
      filtered = filtered.filter(
        (gateway) => gateway.categoryType === safeSearchFilters.categoryType
      );
    }

    // Filter by area
    if (safeSearchFilters.areaId) {
      filtered = filtered.filter(
        (gateway) => gateway.areaId === safeSearchFilters.areaId
      );
    }

    // Filter by location
    if (safeSearchFilters.locationId) {
      filtered = filtered.filter(
        (gateway) => gateway.locId === safeSearchFilters.locationId
      );
    }

    // Filter by model
    if (safeSearchFilters.modelId) {
      filtered = filtered.filter(
        (gateway) => gateway.modelId === safeSearchFilters.modelId
      );
    }

    // Filter by active status
    if (safeSearchFilters.active !== undefined) {
      filtered = filtered.filter(
        (gateway) => gateway.active === safeSearchFilters.active
      );
    }

    return filtered;
  }, [allGatewaysCache, showDeleted, safeSearchFilters]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredGateways.length / pagination.pageSize);
  const startIndex = (pagination.page - 1) * pagination.pageSize;
  const endIndex = startIndex + pagination.pageSize;
  const paginatedGateways = filteredGateways.slice(startIndex, endIndex);

  // Calculate record counts
  const activeRecordsCount = allGatewaysCache.filter((g) => !g.deleted).length;
  const inactiveRecordsCount = allGatewaysCache.filter((g) => g.deleted).length;

  // Available page sizes
  const finalAvailablePageSizes: GatewayPageSize[] =
    availablePageSizes || GATEWAY_PAGE_SIZES;

  // Internal function to refresh data without setting loading state
  const refreshData = useCallback(async () => {
    if (status !== "authenticated" || !session?.user?.token) {
      console.log("Not authenticated, skipping gateway data refresh");
      return;
    }

    // Set authentication in API service
    spidexApi.setAuthToken(session.user.token);
    spidexApi.setTenantId(session.user.tenantId);

    console.log("Refreshing gateway management data");

    // Load all data in parallel with individual error handling
    const [gatewaysData, areasData, locationsData, modelsData] =
      await Promise.allSettled([
        spidexApi.getAllGateways(session.user.tenantId, 1, 1000),
        spidexApi.getAllAreas(session.user.tenantId, 1, 1000),
        spidexApi.fetchLocations(),
        spidexApi.getAllGatewayConfigs(),
      ]).then((results) => {
        // Log any failed requests
        if (results[0].status === "rejected") {
          console.error("Failed to load gateways:", results[0].reason);
        }
        if (results[1].status === "rejected") {
          console.error("Failed to load areas:", results[1].reason);
        }
        if (results[2].status === "rejected") {
          console.error("Failed to load locations:", results[2].reason);
        }
        if (results[3].status === "rejected") {
          console.error("Failed to load gateway models:", results[3].reason);
          console.warn("Using default gateway models as fallback");
        }

        return [
          results[0].status === "fulfilled" ? results[0].value : [],
          results[1].status === "fulfilled" ? results[1].value : [],
          results[2].status === "fulfilled" ? results[2].value : [],
          results[3].status === "fulfilled"
            ? results[3].value
            : getDefaultGatewayModels(),
        ];
      });

    setAllGatewaysCache(gatewaysData);
    setAreasCache(areasData);
    setLocationsCache(locationsData);
    setModelsCache(modelsData);

    console.log(
      `Refreshed ${gatewaysData.length} gateways, ${areasData.length} areas, ${locationsData.length} locations, ${modelsData.length} models`
    );
  }, [spidexApi, session, status]);

  // Load all data with loading state
  const loadData = useCallback(async () => {
    if (status !== "authenticated" || !session?.user?.token) {
      console.log("Not authenticated, skipping gateway data load");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      await refreshData();
    } catch (error) {
      console.error("Error loading gateway data:", error);
      setError(
        error instanceof Error ? error.message : "Failed to load gateway data"
      );
    } finally {
      setIsLoading(false);
    }
  }, [refreshData, status, session]);

  // Auto-load data when session is ready (only on initial mount)
  useEffect(() => {
    if (autoLoad && status === "authenticated") {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad, status]); // Removed loadData dependency to prevent reloading on session changes

  // CRUD Operations
  const createGateway = useCallback(
    async (gatewayData: CreateGatewayFormData) => {
      try {
        setIsCrudLoading(true);
        setError(null);

        // Find area and location names for the payload
        const area = areasCache.find((a) => a.id === gatewayData.areaId);
        const location = locationsCache.find((l) => l.id === gatewayData.locId);

        // Transform form data to match API format like old application
        const basePayload = {
          id: null,
          createdBy: session?.user?.userId || "admin",
          createdTime: new Date().toISOString(),
          modifiedBy: session?.user?.userId || "admin",
          modifiedTime: new Date().toISOString(),
          deleted: false,
          tenantId: session?.user?.tenantId,
          categoryType: gatewayData.categoryType,
          name: gatewayData.name,
          description: gatewayData.description || "",
          modelId: parseInt(gatewayData.modelId),
          externalId: gatewayData.externalId,
          coverageMin: gatewayData.coverageMin || 1,
          coverageMax: gatewayData.coverageMax || 1,
          communicationType: gatewayData.communicationType || "BLE",
          areaId: gatewayData.areaId || null,
          xyzCoordinates: {
            additionalProp1: gatewayData.xyzCoordinates?.additionalProp1 || "1",
            additionalProp2: gatewayData.xyzCoordinates?.additionalProp2 || "1",
            additionalProp3: gatewayData.xyzCoordinates?.additionalProp3 || "1",
          },
          locId: gatewayData.locId || null,
          locName: location?.name || null,
          areaName: area?.name || "",
          ownerTenantId: session?.user?.tenantId,
          properties: {},
        };

        // Add category-specific fields only when needed
        const apiPayload: any = { ...basePayload };

        // Only add gatewayLint for lint category
        if (gatewayData.categoryType === "lint") {
          apiPayload.gatewayLint = gatewayData.gatewayLint || [];
          apiPayload.parentId = (gatewayData as any).parentId || null;
          apiPayload.locType = (gatewayData as any).locationType || null;
        }

        // Only add RFID-specific fields for RFID Fixed Reader
        if (gatewayData.communicationType === "RFID Fixed Reader") {
          apiPayload.antennaConfigs = gatewayData.antennaConfigs || [];
        }

        const newGateway = await spidexApi.createGateway(apiPayload);

        // Refresh data to get updated list
        await refreshData();

        return newGateway;
      } catch (error) {
        console.error("Error creating gateway:", error);
        setError(
          error instanceof Error ? error.message : "Failed to create gateway"
        );
        throw error;
      } finally {
        setIsCrudLoading(false);
      }
    },
    [spidexApi, session, refreshData, areasCache, locationsCache]
  );

  const updateGateway = useCallback(
    async (gatewayData: UpdateGatewayFormData) => {
      try {
        setIsCrudLoading(true);
        setError(null);

        // Find the existing gateway to get all required fields
        const existingGateway = allGatewaysCache.find(
          (g) => g.id === gatewayData.id
        );
        if (!existingGateway) {
          throw new Error("Gateway not found");
        }

        // Find area and location names for the payload
        const area = areasCache.find((a) => a.id === gatewayData.areaId);
        const location = locationsCache.find((l) => l.id === gatewayData.locId);

        const updatedGateway = await spidexApi.updateGateway({
          ...existingGateway, // Include all existing fields
          ...gatewayData, // Override with form data
          areaName: area?.name || "",
          locationName: location?.name || "",
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        });

        // Refresh data to get updated list
        await refreshData();

        return updatedGateway;
      } catch (error) {
        console.error("Error updating gateway:", error);
        setError(
          error instanceof Error ? error.message : "Failed to update gateway"
        );
        throw error;
      } finally {
        setIsCrudLoading(false);
      }
    },
    [
      spidexApi,
      session,
      refreshData,
      areasCache,
      locationsCache,
      allGatewaysCache,
    ]
  );

  const deleteGateway = useCallback(
    async (gatewayId: string) => {
      try {
        setIsCrudLoading(true);
        setError(null);

        await spidexApi.deleteGateway(gatewayId);

        // Refresh data to get updated list
        await refreshData();

        console.log("Gateway deleted successfully");
      } catch (error) {
        console.error("Error deleting gateway:", error);
        setError(
          error instanceof Error ? error.message : "Failed to delete gateway"
        );
        throw error;
      } finally {
        setIsCrudLoading(false);
      }
    },
    [spidexApi, refreshData]
  );

  // Pagination controls
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  }, []);

  const changePageSize = useCallback((size: GatewayPageSize) => {
    setPagination((prev) => ({ ...prev, pageSize: size, page: 1 }));
  }, []);

  // Search and filter controls
  const updateSearchFilters = useCallback(
    (filters: Partial<GatewaySearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      // Reset to first page when search changes
      setPagination((prev) => ({ ...prev, page: 1 }));
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({
      searchTerm: "",
      categoryType: undefined,
      areaId: undefined,
      locationId: undefined,
      modelId: undefined,
      active: undefined,
    });
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  return {
    // Data
    gateways: paginatedGateways,
    allGateways: allGatewaysCache,
    filteredGateways,
    areas: areasCache,
    locations: locationsCache,
    models: modelsCache,

    // State
    isLoading,
    isCrudLoading,
    error,
    showDeleted,

    // Search and pagination
    searchFilters: safeSearchFilters,
    pagination,
    totalPages,
    totalRecords: filteredGateways.length, // Use filtered count for display
    totalAllRecords: allGatewaysCache.length, // Total count including deleted
    activeRecordsCount, // Count of active (not deleted) records
    inactiveRecordsCount, // Count of inactive (deleted) records
    hasNextPage: pagination.page < totalPages,
    hasPreviousPage: pagination.page > 1,

    // Actions
    loadData,
    createGateway,
    updateGateway,
    deleteGateway,

    // Pagination controls
    goToPage,
    changePageSize,
    availablePageSizes: finalAvailablePageSizes,

    // Search and filter controls
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
  };
};
