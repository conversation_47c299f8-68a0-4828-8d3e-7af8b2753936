import React from 'react';

class ChangingProgressProvider extends React.Component {
  constructor(props) {
    super(props);
    this.intervalObj = null;
  }
  static defaultProps = {
    interval: 1000,
  };

  state = {
    valuesIndex: 0,
  };
  componentDidMount() {
    this.intervalObj = setInterval(() => {
      this.setState({
        valuesIndex: (this.state.valuesIndex + 1) % this.props.values.length,
      });
    }, this.props.interval);
  }

  componentWillUnmount() {
    clearInterval(this.intervalObj);
  }

  render() {
    return this.props.children(this.props.values[this.state.valuesIndex]);
  }
}

export default ChangingProgressProvider;
