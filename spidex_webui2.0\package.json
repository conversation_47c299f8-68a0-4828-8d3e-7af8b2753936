{"name": "spidex-cra", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons": "^4.6.4", "@craco/craco": "^6.2.0", "@googlemaps/js-api-loader": "^1.12.3", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "antd": "4.21.4", "axios": "^0.21.3", "craco-less": "2.0.0", "dotenv": "^10.0.0", "esm": "^3.2.25", "event-source-polyfill": "^1.0.25", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "https": "^1.0.0", "jsonwebtoken": "^8.5.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "react": "^17.0.2", "react-circular-progressbar": "^2.0.4", "react-color": "^2.19.3", "react-contextmenu": "^2.14.0", "react-dom": "^17.0.2", "react-router-dom": "^5.3.0", "react-scripts": "4.0.3", "recharts": "^2.1.6", "serve": "^12.0.0", "sockjs-client": "^1.1.4", "stompjs": "^2.3.3", "sunburst-chart": "^1.15.2", "tinycolor2": "^1.4.2", "web-vitals": "^1.0.1"}, "scripts": {"start": "serve -s build", "dev": "PORT=3006 craco start -v", "dev-local": "HOST=spidex-dev.io PORT=3006 craco start -v", "dev-ssl": "PORT=3006 HTTPS=true SSL_CRT_FILE=localhost.pem SSL_KEY_FILE=localhost-key.pem craco start", "build": "craco build", "heroku-postbuild": "yarn build", "test": "craco test"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": "16.x"}, "devDependencies": {"eslint": "^8.25.0", "eslint-plugin-react": "^7.31.10", "prettier": "2.3.2"}}