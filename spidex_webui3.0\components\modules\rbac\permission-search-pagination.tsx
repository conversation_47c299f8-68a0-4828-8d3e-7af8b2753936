"use client";

import { useState, useEffect } from "react";
import { Search, X, Filter, RotateCcw } from "lucide-react";
import {
  PermissionSearchFilters,
  Role,
  Page,
  CrudPermission,
  CRUD_PERMISSIONS,
} from "@/types/rbac";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

interface PermissionSearchPaginationProps {
  searchFilters: PermissionSearchFilters;
  totalRecords: number;
  roles: Role[];
  pages: Page[];
  onUpdateSearchFilters: (filters: Partial<PermissionSearchFilters>) => void;
  onClearSearch: () => void;
}

export function PermissionSearchPagination({
  searchFilters,
  totalRecords,
  roles,
  pages,
  onUpdateSearchFilters,
  onClearSearch,
}: PermissionSearchPaginationProps) {
  const [localFilters, setLocalFilters] =
    useState<PermissionSearchFilters>(searchFilters);

  // Sync local filters with parent filters
  useEffect(() => {
    setLocalFilters(searchFilters);
  }, [searchFilters]);

  const handleFilterChange = (
    key: keyof PermissionSearchFilters,
    value: any
  ) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onUpdateSearchFilters({ [key]: value });
  };

  const handleClearFilters = () => {
    setLocalFilters({});
    onClearSearch();
  };

  const hasActiveFilters = Object.values(searchFilters).some(
    (value) => value !== undefined && value !== "" && value !== null
  );

  return (
    <div className="space-y-4">
      {/* Search Controls */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Role Filter */}
        <div className="space-y-2">
          <Label htmlFor="role-filter">Role</Label>
          <Select
            value={localFilters.role || "all"}
            onValueChange={(value) =>
              handleFilterChange("role", value === "all" ? undefined : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All roles" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              {roles.map((role) => (
                <SelectItem key={role.id} value={role.id}>
                  {role.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Page Filter */}
        <div className="space-y-2">
          <Label htmlFor="page-filter">Page</Label>
          <Select
            value={localFilters.page || "all"}
            onValueChange={(value) =>
              handleFilterChange("page", value === "all" ? undefined : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All pages" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Pages</SelectItem>
              {pages.map((page) => (
                <SelectItem key={page.id} value={page.pageName}>
                  {page.pageName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Permission Filter */}
        <div className="space-y-2">
          <Label htmlFor="permission-filter">Permission</Label>
          <Select
            value={localFilters.permission || "all"}
            onValueChange={(value) =>
              handleFilterChange(
                "permission",
                value === "all" ? undefined : (value as CrudPermission)
              )
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All permissions" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Permissions</SelectItem>
              {Object.entries(CRUD_PERMISSIONS).map(([key, permission]) => (
                <SelectItem key={permission} value={permission}>
                  {key} ({permission})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <Label>&nbsp;</Label>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleClearFilters}
              disabled={!hasActiveFilters}
              className="flex-1"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear
            </Button>
          </div>
        </div>
      </div>

      {/* Filter Status and Stats */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {hasActiveFilters && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Filter className="h-3 w-3" />
              Filters Active
            </Badge>
          )}
        </div>

        {/* Record Counts */}
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <span>Showing: {totalRecords} permissions</span>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium">Active filters:</span>
          {searchFilters.role && (
            <Badge variant="outline" className="flex items-center gap-1">
              Role:{" "}
              {roles.find((r) => r.id === searchFilters.role)?.name ||
                searchFilters.role}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => handleFilterChange("role", undefined)}
              />
            </Badge>
          )}
          {searchFilters.page && (
            <Badge variant="outline" className="flex items-center gap-1">
              Page: {searchFilters.page}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => handleFilterChange("page", undefined)}
              />
            </Badge>
          )}
          {searchFilters.permission && (
            <Badge variant="outline" className="flex items-center gap-1">
              Permission: {searchFilters.permission}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => handleFilterChange("permission", undefined)}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
