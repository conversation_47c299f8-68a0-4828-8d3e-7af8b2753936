"use client";

import * as React from "react";
import {
  GalleryVerticalEnd,
  Gauge,
  Locate,
  PackagePlus,
  Settings2,
  Users,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";

// This is sample data.
const data = {
  user: {
    name: "batman",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  teams: [
    {
      name: "Spidex Inc",
      logo: GalleryVerticalEnd,
      plan: "Enterprise",
    },
  ],
  navMain: [
    {
      title: "Asset Tracking",
      url: "/asset-tracking",
      icon: Locate,
      isActive: true,
      items: [
        {
          title: "Dashboard",
          url: "/asset-tracking",
        },
        {
          title: "Map View",
          url: "/asset-tracking/map",
        },
        {
          title: "Analytics",
          url: "/asset-tracking/analytics",
        },
      ],
    },
    {
      title: "Device Management",
      url: "#",
      icon: Gauge,
      items: [
        {
          title: "Dashboard",
          url: "#",
        },
        {
          title: "Analytics",
          url: "#",
        },
        {
          title: "Settings",
          url: "#",
        },
      ],
    },
    {
      title: "User Management",
      url: "/user-management",
      icon: Users,
      items: [
        {
          title: "Accounts",
          url: "/user-management/account",
        },
        {
          title: "Tenants",
          url: "/user-management/tenant",
        },
        {
          title: "Roles",
          url: "/user-management/roles",
        },
        {
          title: "Permissions",
          url: "/user-management/permissions",
        },
        {
          title: "Pages",
          url: "/user-management/pages",
        },
      ],
    },
    {
      title: "Settings",
      url: "#",
      icon: Settings2,
      items: [
        {
          title: "General",
          url: "#",
        },
        {
          title: "Team",
          url: "#",
        },
        {
          title: "Billing",
          url: "#",
        },
        {
          title: "Limits",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Digital Carpet",
      url: "#",
      icon: PackagePlus,
      isActive: false,
      items: [
        {
          title: "Setup Indoor Map",
          url: "#",
        },
        {
          title: "Create Branch",
          url: "/device/management/configure/branch",
        },
        {
          title: "Create Location",
          url: "/device/management/configure/location",
        },
        {
          title: "Create Area",
          url: "/device/management/configure/area",
        },
        {
          title: "Create Transit Area",
          url: "#",
        },
        {
          title: "Create Gateway",
          url: "/device/management/configure/gateway",
        },
        {
          title: "Create Tag",
          url: "/device/management/configure/tag",
        },
        {
          title: "Create Asset",
          url: "/device/management/configure/asset",
        },
        {
          title: "Provision",
          url: "/device/management/onboard/asset/tagged",
        },
        {
          title: "Onboard",
          url: "/device/management/onboard/onboard",
        },
      ],
    },
    {
      title: "Device Configuration",
      url: "#",
      icon: Gauge,
      items: [
        {
          title: "Event Attribute",
          url: "/device-config/event-attribute",
        },
        {
          title: "Gateway Config",
          url: "/device-config/gateway",
        },
        {
          title: "Tag Config",
          url: "/device-config/tag",
        },
      ],
    },
    {
      title: "Masters",
      url: "#",
      icon: Settings2,
      items: [
        {
          title: "Vendor",
          url: "#",
        },
        {
          title: "Worker",
          url: "#",
        },
        {
          title: "Vehicle",
          url: "#",
        },
        {
          title: "Sanitary",
          url: "#",
        },
      ],
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} title="Platform" />
        <NavMain items={data.navSecondary} title="Onboard" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
