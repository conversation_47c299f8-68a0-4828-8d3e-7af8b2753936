"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AssetPaginationParams, AssetPageSize } from "@/types/asset";

interface AssetTablePaginationProps {
  pagination: AssetPaginationParams;
  totalRecords: number;
  currentPageRecords: number; // Number of records on current page
  totalPages?: number;
  availablePageSizes: AssetPageSize[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: AssetPageSize) => void;
}

export function AssetTablePagination({
  pagination,
  totalRecords,
  currentPageRecords,
  totalPages = 1,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
}: AssetTablePaginationProps) {
  const [inputPage, setInputPage] = useState(pagination.page.toString());

  // Update input when pagination changes
  useEffect(() => {
    setInputPage(pagination.page.toString());
  }, [pagination.page]);

  const handlePageSizeChange = (value: string) => {
    const size = parseInt(value, 10);
    onPageSizeChange(size as AssetPageSize);
  };

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputPage(e.target.value);
  };

  const handlePageInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const page = parseInt(inputPage, 10);
      if (page >= 1 && page <= totalPages) {
        onPageChange(page);
      } else {
        setInputPage(pagination.page.toString());
      }
    }
  };

  const handlePageInputBlur = () => {
    const page = parseInt(inputPage, 10);
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    } else {
      setInputPage(pagination.page.toString());
    }
  };

  const getVisiblePageNumbers = () => {
    const current = pagination.page;
    const total = totalPages;
    const delta = 2; // Number of pages to show on each side of current page

    let start = Math.max(1, current - delta);
    let end = Math.min(total, current + delta);

    // Adjust if we're near the beginning or end
    if (current <= delta + 1) {
      end = Math.min(total, 2 * delta + 1);
    }
    if (current >= total - delta) {
      start = Math.max(1, total - 2 * delta);
    }

    const pages = [];
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  };

  const visiblePages = getVisiblePageNumbers();

  // Calculate record range
  const startRecord = (pagination.page - 1) * pagination.size + 1;
  const endRecord = Math.min(pagination.page * pagination.size, totalRecords);

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 px-4 py-2 border-t">
      {/* Records info */}
      <div className="text-sm text-muted-foreground">
        Showing {startRecord} to {endRecord} of {totalRecords} entries
      </div>

      {/* Pagination controls */}
      <div className="flex flex-col sm:flex-row items-center gap-4">
        {/* Page size selector */}
        <div className="flex items-center gap-2">
          <Label htmlFor="pageSize" className="text-sm">
            Show:
          </Label>
          <Select
            value={pagination.size.toString()}
            onValueChange={handlePageSizeChange}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availablePageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size.toString()}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Page navigation - only show if more than 1 page */}
        {totalPages > 1 && (
          <div className="flex items-center gap-2">
            {/* First page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={pagination.page === 1}
            >
              First
            </Button>

            {/* Previous page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              Previous
            </Button>

            {/* Page numbers */}
            <div className="flex items-center gap-1">
              {visiblePages.map((page) => (
                <Button
                  key={page}
                  variant={page === pagination.page ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(page)}
                  className="w-8 h-8 p-0"
                >
                  {page}
                </Button>
              ))}
            </div>

            {/* Next page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page === totalPages}
            >
              Next
            </Button>

            {/* Last page */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={pagination.page === totalPages}
            >
              Last
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
