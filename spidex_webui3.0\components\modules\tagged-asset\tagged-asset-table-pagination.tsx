"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface TaggedAssetTablePaginationProps {
  pagination: { page: number; pageSize: number };
  totalRecords: number;
  currentPageRecords: number; // Number of records on current page
  totalPages?: number;
  availablePageSizes: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

export function TaggedAssetTablePagination({
  pagination,
  totalRecords,
  currentPageRecords,
  totalPages = 1,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
}: TaggedAssetTablePaginationProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center justify-between p-4 border-t">
        <div className="text-sm text-muted-foreground">Loading...</div>
        <div className="flex items-center gap-2">
          <div className="w-20 h-8 bg-muted rounded animate-pulse" />
        </div>
      </div>
    );
  }

  const handlePageSizeChange = (value: string) => {
    const size = parseInt(value, 10);
    onPageSizeChange(size);
  };

  return (
    <div className="flex items-center justify-between p-4 border-t">
      {/* Results Summary */}
      <div className="text-sm text-muted-foreground">
        Showing {currentPageRecords} of {totalRecords} tagged asset
        {totalRecords !== 1 ? "s" : ""} on page {pagination.page} of{" "}
        {totalPages}
      </div>

      <div className="flex items-center gap-4">
        {/* Page Size Selector */}
        <div className="flex items-center gap-2">
          <Label htmlFor="page-size">Show:</Label>
          <Select
            value={pagination.pageSize.toString()}
            onValueChange={handlePageSizeChange}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availablePageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex items-center gap-2">
            {/* First Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={pagination.page <= 1}
            >
              First
            </Button>

            {/* Previous Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>

            {/* Page Numbers */}
            <div className="flex items-center gap-1">
              {/* Show page numbers around current page */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (pagination.page <= 3) {
                  pageNum = i + 1;
                } else if (pagination.page >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = pagination.page - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={
                      pageNum === pagination.page ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => onPageChange(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            {/* Next Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page >= totalPages}
            >
              Next
            </Button>

            {/* Last Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={pagination.page >= totalPages}
            >
              Last
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
