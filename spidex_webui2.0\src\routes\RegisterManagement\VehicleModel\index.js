import { useEffect, useState, useContext } from 'react';
import {
  Input,
  Checkbox,
  Table,
  Button,
  Form,
  Popconfirm,
  CommonDrawer,
  CommonCompactView,
  message,
  Row,
  Col,
  Pagination,
  Select,
} from '../../../components';
import {
  getAllVehicleModelByPagination,
  addVehicleModel,
  updateVehicleModel,
  deleteVehicleModel,
  getSearchVehicleModel,
} from '../../../services';
import Context from '../../../context';
import { PlusOutlined } from '@ant-design/icons';
import { BreadcrumbList, PermissionContainer } from '../../../shared';
import { buildCommonApiValues } from '../../../utils';
import { get } from 'lodash';
import { CRUD, Pages, ModuleNames, MastersPageSizeDefault, VehicleManufacturers } from '../../../constants';

const { Search } = Input;
const { Option } = Select;

const VehicleModel = () => {
  const [context, setContext] = useContext(Context);
  const [action, setAction] = useState('new');
  const [totalVehicleModels, setTotalVehicleModels] = useState({
    items: 0,
    current: 0,
    pageSize: MastersPageSizeDefault,
  });
  const [showDeleted, setShowDeleted] = useState(false);
  const [visible, setVisible] = useState(false);
  const [vehicleModel, setVehicleModel] = useState([]);
  const [vehicleModelOriginal, setVehicleModelOriginal] = useState([]);
  const [vehicleModelsInfo, setVehicleModelsInfo] = useState({});
  const [vehicleModelInfo, setVehicleModelInfo] = useState(false);

  const [form] = Form.useForm();

  const formInitState = { vehicleManufacturer: VehicleManufacturers[0] };
  const [formInitValues, setFormInitValues] = useState({ ...formInitState });

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();

    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveVehicleModelAction({
        createdTime: commonValues.createdTime,
        modifiedTime: commonValues.modifiedTime,
        modifiedBy: commonValues.modifiedBy,
        createdBy: commonValues.createdBy,
        deleted: commonValues.deleted,
        ...values,
      });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      updateVehicleModelCall(values);
      if (type === 'add') form.resetFields({});
      setFormInitValues(null);
      setVisible(false);
    }
  };

  //Save vehicle model data
  const saveVehicleModelAction = async (vehicleModel) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addVehicleModel(vehicleModel)
      .then((res) => {
        setVehicleModel((state) => [res.data, ...state]);
        message.success('Succesfully Added Vehicle Model');
        form.resetFields();
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add vehicle model, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  //update vehicle model data
  const editVehicleModelAction = (vehicleModel) => {
    form.setFieldsValue({ ...vehicleModel });
    setAction('edit');
    setVehicleModelsInfo(vehicleModel);
    setVisible(true);
  };

  const updateVehicleModelCall = (values) => {
    const data = { ...vehicleModelsInfo, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateVehicleModel(data)
      .then((res) => {
        setVehicleModel((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, res.data);
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        message.success('Succesfully Updated Vehicle Model');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update vehicle model, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const makeActive = (data) => {
    updateVehicleModelCall({ ...data, deleted: false });
  };

  //delete the vehicle modal data
  const setDeleteVehicleModelAction = (vehicleModelId, visible) => {
    setVehicleModel((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === vehicleModelId).visible = visible;
      tempData
        .filter((x) => x.id !== vehicleModelId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteVehicleModelAction = (vehicleModelId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteVehicleModel(vehicleModelId)
      .then(() => {
        setVehicleModel((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === vehicleModelId).visible = false;
          tempState.find((x) => x.id === vehicleModelId).deleted = true;
          return [...state].filter((x) => x.id !== vehicleModelId);
        });
        message.success('Succesfully Deleted Vehicle Model');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete vehicle model, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const closeAdd = () => {
    setVisible(false);
    setVehicleModelInfo(false);
    if (action === 'edit') form.resetFields();
  };

  const clearForm = () => {
    form.resetFields();
  };

  //view vehicle model information
  const viewVehicleModelInfo = (e) => {
    setVehicleModelsInfo(e);
    setVehicleModelInfo(true);
  };

  //get all vehicle model data by pagination
  const onPaginationChange = (e) => {
    init(+e - 1);
  };

  const init = async (page = 0) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const pagination = {
      size: totalVehicleModels.pageSize,
      page: page,
    };

    try {
      const vehicleModelRes = await getAllVehicleModelByPagination(pagination, showDeleted);
      const vehicleModelData = vehicleModelRes.data?.content || [];
      setVehicleModel(vehicleModelData);
      setVehicleModelOriginal(vehicleModelData);
      setTotalVehicleModels((ps) => ({ ...ps, current: page, items: vehicleModelRes.data.totalElements }));
    } catch (e) {
      console.log(e);
      message.error('Unable to get vehicle model details, try again later');
    } finally {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
    }
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, [showDeleted]);

  //table mode normal data changes based on search results
  const [tableModeNormal, setTableModeNormal] = useState(true);
  const onVehicleModelSearch = async (e) => {
    if (e) {
      try {
        const vehicleModel = await getSearchVehicleModel(e);
        setVehicleModel(vehicleModel.data.filter((x) => x.deleted === false));
        setTableModeNormal(false);
      } catch {
        message.error('Could not retrieve vehicle model');
      }
    } else {
      setTableModeNormal(true);
      setVehicleModel(vehicleModelOriginal);
    }
  };
  const tableCols = [
    { title: <strong> Model </strong>, key: 'model', dataIndex: 'model' },
    { title: <strong> Variant </strong>, key: 'variant', dataIndex: 'variant' },
    { title: <strong> Vehicle Manufacturer </strong>, key: 'vehicleManufacturer', dataIndex: 'vehicleManufacturer' },
    {
      title: <strong> Actions </strong>,
      width: 350,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <Button type="link" className="actionButton" onClick={() => makeActive(record)}>
                Activate
              </Button>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.VEHICLE_MODEL} permission={CRUD.VIEW}>
              <Button type="link" className="actionButton" onClick={() => viewVehicleModelInfo(record)}>
                View
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.VEHICLE_MODEL} permission={CRUD.UPDATE}>
              <Button type="link" className="actionButton" onClick={() => editVehicleModelAction(record)}>
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.VEHICLE_MODEL} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete vehicle model ${record.model}?`}
                visible={record.visible || false}
                onConfirm={() => deleteVehicleModelAction(record.id)}
                onCancel={() => setDeleteVehicleModelAction(record.id, false)}
              >
                <Button
                  type="link"
                  className="actionButton"
                  onClick={() => setDeleteVehicleModelAction(record.id, true)}
                >
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const vehicleModelBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.VEHICLE_MODEL}].displayName`,
    ModuleNames.VEHICLE_MODEL
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.MASTERS, vehicleModelBreadcrumbsName, 'Register Vehicle Model']} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.VEHICLE_MODEL} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Vehicle Model
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <Row className="searchDiv" justify="end">
        <Col xs={24} lg={6}>
          <Search placeholder="Enter Model" allowClear enterButton onSearch={onVehicleModelSearch} />
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <>
            {tableModeNormal ? (
              <>
                <Table
                  size="small"
                  scroll={{ x: true }}
                  rowKey="id"
                  columns={tableCols}
                  dataSource={vehicleModel}
                  rowClassName={(record) => record.deleted && 'rowInactive'}
                  pagination={false}
                />
                <Row justify="end">
                  <Col>
                    <div className="m-2">
                      <Pagination
                        onChange={onPaginationChange}
                        current={totalVehicleModels.current + 1}
                        total={totalVehicleModels.items}
                        defaultPageSize={totalVehicleModels.pageSize}
                        showSizeChanger={false}
                        showQuickJumper={false}
                      />
                    </div>
                  </Col>
                </Row>
              </>
            ) : (
              <Table
                size="small"
                scroll={{ x: true }}
                rowKey="id"
                columns={tableCols}
                dataSource={vehicleModel}
                rowClassName={(record) => record.deleted && 'rowInactive'}
              />
            )}
          </>
        ) : (
          <>
            <CommonCompactView
              data={vehicleModel}
              onEdit={editVehicleModelAction}
              onDelete={deleteVehicleModelAction}
              permissions={[
                { pageName: Pages.VEHICLE_MODEL, permission: CRUD.UPDATE, label: 'Edit' },
                { pageName: Pages.VEHICLE_MODEL, permission: CRUD.DELETE, label: 'Delete' },
              ]}
              title="model"
              dataList={[{ label: 'Model', value: 'model' }]}
            />
            <Row justify="end">
              <Col>
                <div className="m-2">
                  <Pagination
                    onChange={onPaginationChange}
                    current={totalVehicleModels.current + 1}
                    total={totalVehicleModels.items}
                    defaultPageSize={totalVehicleModels.pageSize}
                    showSizeChanger={false}
                    showQuickJumper={false}
                  />
                </div>
              </Col>
            </Row>
          </>
        )}
      </>

      <CommonDrawer title="Vehicle Model Information" visible={vehicleModelInfo} closeAdd={closeAdd}>
        <div className="infoDrawer">
          {vehicleModelsInfo ? (
            <Row className="infoContainer" span={24}>
              <Col className="info" span={24}>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Vehicle Manufacturer :
                  </Col>
                  <Col>{vehicleModelsInfo.vehicleManufacturer}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Variant :
                  </Col>
                  <Col>{vehicleModelsInfo.variant}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Model :
                  </Col>
                  <Col>{vehicleModelsInfo.model}</Col>
                </Row>

                <Row>
                  <Col className="infoTitle" span={10}>
                    Deleted :
                  </Col>
                  <Col>{String(vehicleModelsInfo.deleted)}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created By :
                  </Col>
                  <Col>{vehicleModelsInfo.createdBy}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Created Time :
                  </Col>
                  <Col>{new Date(vehicleModelsInfo.createdTime).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified Time :
                  </Col>
                  <Col>{new Date(vehicleModelsInfo.modifiedTime).toLocaleString()}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Modified By :
                  </Col>
                  <Col>{vehicleModelsInfo.modifiedBy}</Col>
                </Row>
                <br />
                <Row justify="center">
                  <Button type="primary" onClick={() => editVehicleModelAction(vehicleModelsInfo)}>
                    Edit
                  </Button>
                </Row>
              </Col>
            </Row>
          ) : (
            <></>
          )}
        </div>
      </CommonDrawer>

      <CommonDrawer title="Vehicle Model" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          autoComplete={'new-password'}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 16 }}
          labelCol={{ span: 8 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Vehicle Manufacturer"
            name="vehicleManufacturer"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input vehicle manufacturer!',
              },
            ]}
          >
            <Select placeholder="Vehicle Manufacturer">
              {VehicleManufacturers.map((m) => (
                <Option title={m} key={m} value={m}>
                  {m}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Model"
            name="model"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input model !',
              },
            ]}
          >
            <Input placeholder="Model" />
          </Form.Item>
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Variant"
            name="variant"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input variant !',
              },
            ]}
          >
            <Input placeholder="Variant" />
          </Form.Item>
          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </>
  );
};

export default VehicleModel;
