"use client";

import { FileText } from "lucide-react";
import { useSession } from "next-auth/react";
import { usePageManagement } from "@/hooks/use-page-management";

import { PageDataTable } from "./page-data-table";
import { PageSearchPagination } from "./page-search-pagination";

import { Button } from "@/components/ui/button";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";

export default function PageManagement() {
  const { data: session, status } = useSession();
  const {
    pages: paginatedPages,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = usePageManagement();

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  if (!session) {
    return <div>Please log in to access page management.</div>;
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/user-management">
                User Management
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Page Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="p-4">
        {/* Search and Pagination Controls */}
        <PageSearchPagination
          searchFilters={searchFilters}
          showDeleted={showDeleted}
          totalRecords={totalRecords}
          onUpdateSearchFilters={updateSearchFilters}
          onClearSearch={clearSearch}
          onToggleShowDeleted={toggleShowDeleted}
        />

        {/* Data Table */}
        <div className="mt-6">
          <PageDataTable
            data={paginatedPages}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
          />
        </div>
      </main>
    </>
  );
}
