"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useSession } from "next-auth/react";
import { useSpidexApi } from "@/hooks/use-spidex-api";
import {
  Area,
  AreaSearchFilters,
  AreaPaginationParams,
  AreaPageSize,
  DEFAULT_AREA_PAGE_SIZE,
  AREA_PAGE_SIZES,
  AREA_PAGE_SIZE_ALL,
  CreateAreaFormData,
  UpdateAreaFormData,
  BranchOption,
  LocationOption,
} from "@/types/area";
import { Gateway } from "@/types/gateway";

interface UseAreaManagementOptions {
  autoLoad?: boolean;
  defaultPageSize?: AreaPageSize;
  initialPageSize?: number;
  availablePageSizes?: AreaPageSize[];
  locationId?: string;
}

export const useAreaManagement = ({
  autoLoad = true,
  defaultPageSize = DEFAULT_AREA_PAGE_SIZE,
  initialPageSize,
  availablePageSizes,
  locationId,
}: UseAreaManagementOptions = {}) => {
  // Get authenticated API instance and session
  const spidexApi = useSpidexApi();
  const { data: session, status } = useSession();

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleted, setShowDeleted] = useState(false);

  // Data cache - store all areas for client-side pagination
  const [allAreasCache, setAllAreasCache] = useState<Area[]>([]);
  const [branchesCache, setBranchesCache] = useState<BranchOption[]>([]);
  const [locationsCache, setLocationsCache] = useState<LocationOption[]>([]);
  const [gatewaysCache, setGatewaysCache] = useState<Gateway[]>([]);

  // Search and pagination state
  const [searchFilters, setSearchFilters] = useState<AreaSearchFilters>({
    searchTerm: "",
    locationId: locationId || "",
    deleted: false,
  });

  const [pagination, setPagination] = useState<AreaPaginationParams>({
    pageNumber: 1,
    pageSize: initialPageSize
      ? (initialPageSize as AreaPageSize)
      : defaultPageSize === "all"
      ? 1000
      : defaultPageSize,
  });

  // Safe search filters with defaults
  const safeSearchFilters = useMemo(
    () => ({
      searchTerm: searchFilters.searchTerm || "",
      branchId: searchFilters.branchId || "",
      locationId: searchFilters.locationId || "",
      deleted: showDeleted,
    }),
    [searchFilters, showDeleted]
  );

  // Filter areas based on search criteria
  const filteredAreas = useMemo(() => {
    let filtered = allAreasCache;

    // Filter by deleted status
    if (!showDeleted) {
      filtered = filtered.filter((area) => !area.deleted);
    }

    // Filter by search term (name, address)
    if (safeSearchFilters.searchTerm) {
      const searchTerm = safeSearchFilters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        (area) =>
          area.name.toLowerCase().includes(searchTerm) ||
          area.address.toLowerCase().includes(searchTerm) ||
          area.branchName?.toLowerCase().includes(searchTerm) ||
          area.locationName?.toLowerCase().includes(searchTerm)
      );
    }

    // Filter by branch
    if (safeSearchFilters.branchId) {
      filtered = filtered.filter(
        (area) => area.branchId === safeSearchFilters.branchId
      );
    }

    // Filter by location
    if (safeSearchFilters.locationId) {
      filtered = filtered.filter(
        (area) => area.locationId === safeSearchFilters.locationId
      );
    }

    return filtered;
  }, [allAreasCache, safeSearchFilters, showDeleted]);

  // Calculate pagination
  const totalPages = useMemo(() => {
    const pageSize = pagination.pageSize === "all" ? 1000 : pagination.pageSize;
    if (pageSize >= filteredAreas.length) return 1;
    return Math.ceil(filteredAreas.length / pageSize);
  }, [filteredAreas.length, pagination.pageSize]);

  // Get paginated areas
  const paginatedAreas = useMemo(() => {
    const pageSize = pagination.pageSize === "all" ? 1000 : pagination.pageSize;
    if (pageSize >= filteredAreas.length) {
      return filteredAreas;
    }

    const startIndex = (pagination.pageNumber - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredAreas.slice(startIndex, endIndex);
  }, [filteredAreas, pagination]);

  // Calculate record counts
  const activeRecordsCount = useMemo(
    () => allAreasCache.filter((area) => !area.deleted).length,
    [allAreasCache]
  );

  const inactiveRecordsCount = useMemo(
    () => allAreasCache.filter((area) => area.deleted).length,
    [allAreasCache]
  );

  // Available page sizes
  const finalAvailablePageSizes = useMemo((): AreaPageSize[] => {
    if (availablePageSizes) {
      return availablePageSizes;
    }
    const sizes: AreaPageSize[] = [...AREA_PAGE_SIZES];
    if (filteredAreas.length > 100) {
      sizes.push(AREA_PAGE_SIZE_ALL);
    }
    return sizes;
  }, [availablePageSizes, filteredAreas.length]);

  // Load all data
  const loadData = useCallback(async () => {
    if (!session?.user?.tenantId) return;

    try {
      setIsLoading(true);
      setError(null);

      // Load areas, branches, locations, and gateways in parallel
      const [
        areasResponse,
        branchesResponse,
        locationsResponse,
        gatewaysResponse,
      ] = await Promise.all([
        spidexApi.getAllAreas(),
        spidexApi.getAllBranches(),
        spidexApi.getAllLocations(),
        spidexApi.getAllGateways(),
      ]);

      // Process branches
      const branches: BranchOption[] = branchesResponse.map((branch: any) => ({
        id: branch.id,
        name: branch.name,
      }));

      // Process locations with branch names
      // Filter out deleted locations first
      const activeLocations = locationsResponse.filter(
        (location: any) => !location.deleted
      );
      const locations: LocationOption[] = activeLocations.map(
        (location: any) => {
          const branch = branches.find((b) => b.id === location.branchId);
          return {
            id: location.id,
            name: location.name,
            branchId: location.branchId,
            branchName: branch?.name || "Unknown Branch",
          };
        }
      );

      // Process areas with branch and location names
      const areas: Area[] = areasResponse.map((area: any) => {
        const branch = branches.find((b) => b.id === area.branchId);
        const location = locations.find((l) => l.id === area.locationId);
        return {
          ...area,
          branchName: branch?.name || "Unknown Branch",
          locationName: location?.name || "Unknown Location",
        };
      });

      // Process gateways with area names
      const gateways: Gateway[] = gatewaysResponse.map((gateway: any) => {
        const area = areasResponse.find((a: any) => a.id === gateway.areaId);
        return {
          ...gateway,
          areaName: area?.name || undefined,
        };
      });

      setAllAreasCache(areas);
      setBranchesCache(branches);
      setLocationsCache(locations);
      setGatewaysCache(gateways);

      console.log(
        `Loaded ${areas.length} areas, ${branches.length} branches, ${locations.length} locations, ${gateways.length} gateways`
      );
    } catch (error) {
      console.error("Error loading area data:", error);
      setError(error instanceof Error ? error.message : "Failed to load data");
    } finally {
      setIsLoading(false);
    }
  }, [spidexApi, session]);

  // Auto-load data when session is ready (only on initial mount)
  useEffect(() => {
    if (autoLoad && status === "authenticated") {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad, status]); // Removed loadData dependency to prevent reloading on session changes

  // CRUD Operations
  const createArea = useCallback(
    async (areaData: CreateAreaFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        // Transform form data to match API format
        const apiPayload = {
          ...areaData,
          id: null, // Required by API for new areas
          tenantId: session?.user?.tenantId,
          properties: {},
          deleted: false,
          createdBy: session?.user?.userId || "system",
          createdTime: Date.now(),
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        };

        const newArea = await spidexApi.createArea(apiPayload);

        // Reload data to get updated list
        await loadData();

        console.log("Area created successfully");
        return newArea;
      } catch (error) {
        console.error("Error creating area:", error);
        setError(
          error instanceof Error ? error.message : "Failed to create area"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData]
  );

  const updateArea = useCallback(
    async (areaData: UpdateAreaFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        // Find the existing area to get all required fields
        const existingArea = allAreasCache.find((a) => a.id === areaData.id);
        if (!existingArea) {
          throw new Error("Area not found");
        }

        const updatedArea = await spidexApi.updateArea({
          ...existingArea, // Include all existing fields
          ...areaData, // Override with form data
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        });

        // Reload data to get updated list
        await loadData();

        console.log("Area updated successfully");
        return updatedArea;
      } catch (error) {
        console.error("Error updating area:", error);
        setError(
          error instanceof Error ? error.message : "Failed to update area"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, session, loadData, allAreasCache]
  );

  const deleteArea = useCallback(
    async (areaId: string) => {
      try {
        setIsLoading(true);
        setError(null);

        await spidexApi.deleteArea(areaId);

        // Reload data to get updated list
        await loadData();

        console.log("Area deleted successfully");
      } catch (error) {
        console.error("Error deleting area:", error);
        setError(
          error instanceof Error ? error.message : "Failed to delete area"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, loadData]
  );

  const bindGateway = useCallback(
    async (gatewayId: string, areaId: string) => {
      try {
        setIsLoading(true);
        setError(null);

        // Find the gateway to update
        const gateway = gatewaysCache.find((g) => g.id === gatewayId);
        if (!gateway) {
          throw new Error("Gateway not found");
        }

        // Update gateway with area binding
        const updatedGateway = {
          ...gateway,
          areaId: areaId,
          modifiedBy: session?.user?.userId || "system",
          modifiedTime: Date.now(),
        };

        await spidexApi.updateGateway(updatedGateway);

        // Reload data to get updated list
        await loadData();

        console.log("Gateway bound to area successfully");
      } catch (error) {
        console.error("Error binding gateway:", error);
        setError(
          error instanceof Error ? error.message : "Failed to bind gateway"
        );
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [spidexApi, gatewaysCache, session, loadData]
  );

  // Pagination controls
  const goToPage = useCallback((page: number) => {
    setPagination((prev) => ({ ...prev, pageNumber: page }));
  }, []);

  const changePageSize = useCallback((newPageSize: AreaPageSize) => {
    const pageSize = newPageSize === "all" ? 1000 : newPageSize;
    setPagination({
      pageNumber: 1,
      pageSize,
    });
  }, []);

  // Search controls
  const updateSearchFilters = useCallback(
    (filters: Partial<AreaSearchFilters>) => {
      setSearchFilters((prev) => ({ ...prev, ...filters }));
      setPagination((prev) => ({ ...prev, pageNumber: 1 })); // Reset to first page
    },
    []
  );

  const clearSearch = useCallback(() => {
    setSearchFilters({
      searchTerm: "",
      deleted: false,
    });
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
  }, []);

  const toggleShowDeleted = useCallback(() => {
    setShowDeleted((prev) => !prev);
    setPagination((prev) => ({ ...prev, pageNumber: 1 }));
  }, []);

  return {
    // Data
    areas: paginatedAreas,
    allAreas: allAreasCache,
    filteredAreas,
    branches: branchesCache,
    locations: locationsCache,
    gateways: gatewaysCache,

    // State
    isLoading,
    error,
    showDeleted,

    // Search and pagination
    searchFilters: safeSearchFilters,
    pagination,
    totalPages,
    totalRecords: filteredAreas.length, // Use filtered count for display
    totalAllRecords: allAreasCache.length, // Total count including deleted
    activeRecordsCount, // Count of active (not deleted) records
    inactiveRecordsCount, // Count of inactive (deleted) records
    hasNextPage: pagination.pageNumber < totalPages,
    hasPreviousPage: pagination.pageNumber > 1,

    // Actions
    loadData,
    createArea,
    updateArea,
    deleteArea,
    bindGateway,

    // Pagination controls
    goToPage,
    changePageSize,
    availablePageSizes: finalAvailablePageSizes,

    // Search controls
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
  };
};
