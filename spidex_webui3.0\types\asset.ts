export interface Asset {
  id: string;
  name: string;
  externalId: string;
  assetType: AssetType;
  status: string;
  deleted: boolean;
  createdBy: string;
  createdTime: string;
  modifiedBy: string;
  modifiedTime: string;
  tenantId: string;
  parentAssetId?: string;
  properties?: any;
}

export interface CreateAssetFormData {
  name: string;
  externalId: string;
  assetType: AssetType;
  status: boolean;
  type?: string; // For "Others" asset type
}

export interface UpdateAssetFormData extends CreateAssetFormData {
  id: string;
}

export type AssetType =
  | "Worker"
  | "Vendor"
  | "Vehicle"
  | "Sanitary"
  | "Others"
  | "Chair";

export interface AssetSearchFilters {
  searchTerm: string;
  assetType?: AssetType;
}

export interface AssetPaginationParams {
  page: number;
  size: number;
}

export type AssetPageSize = 10 | 25 | 50 | 100;

export interface AssetApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  error?: string;
}

export interface AssetPaginatedResponse {
  items: Asset[];
  current: number;
  total: number;
  size: number;
}
