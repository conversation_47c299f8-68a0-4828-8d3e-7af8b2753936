"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(action-browser)/./node_modules/next-auth/index.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthError: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.AuthError),\n/* harmony export */   CredentialsSignin: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.CredentialsSignin),\n/* harmony export */   customFetch: () => (/* reexport safe */ _auth_core__WEBPACK_IMPORTED_MODULE_0__.customFetch),\n/* harmony export */   \"default\": () => (/* binding */ NextAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(action-browser)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var _lib_env_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/env.js */ \"(action-browser)/./node_modules/next-auth/lib/env.js\");\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/index.js */ \"(action-browser)/./node_modules/next-auth/lib/index.js\");\n/* harmony import */ var _lib_actions_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/actions.js */ \"(action-browser)/./node_modules/next-auth/lib/actions.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @auth/core/errors */ \"(action-browser)/./node_modules/@auth/core/errors.js\");\n/**\n * _If you are looking to migrate from v4, visit the [Upgrade Guide (v5)](https://authjs.dev/getting-started/migrating-to-v5)._\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install next-auth@beta\n * ```\n *\n * ## Environment variable inference\n *\n * `NEXTAUTH_URL` and `NEXTAUTH_SECRET` have been inferred since v4.\n *\n * Since NextAuth.js v5 can also automatically infer environment variables that are prefixed with `AUTH_`.\n *\n * For example `AUTH_GITHUB_ID` and `AUTH_GITHUB_SECRET` will be used as the `clientId` and `clientSecret` options for the GitHub provider.\n *\n * :::tip\n * The environment variable name inferring has the following format for OAuth providers: `AUTH_{PROVIDER}_{ID|SECRET}`.\n *\n * `PROVIDER` is the uppercase snake case version of the provider's id, followed by either `ID` or `SECRET` respectively.\n * :::\n *\n * `AUTH_SECRET` and `AUTH_URL` are also aliased for `NEXTAUTH_SECRET` and `NEXTAUTH_URL` for consistency.\n *\n * To add social login to your app, the configuration becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth({ providers: [ GitHub ] })\n * ```\n *\n * And the `.env.local` file:\n *\n * ```sh title=\".env.local\"\n * AUTH_GITHUB_ID=...\n * AUTH_GITHUB_SECRET=...\n * AUTH_SECRET=...\n * ```\n *\n * :::tip\n * In production, `AUTH_SECRET` is a required environment variable - if not set, NextAuth.js will throw an error. See [MissingSecretError](https://authjs.dev/reference/core/errors#missingsecret) for more details.\n * :::\n *\n * If you need to override the default values for a provider, you can still call it as a function `GitHub({...})` as before.\n *\n * ## Lazy initialization\n * You can also initialize NextAuth.js lazily (previously known as advanced intialization), which allows you to access the request context in the configuration in some cases, like Route Handlers, Middleware, API Routes or `getServerSideProps`.\n * The above example becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth(req => {\n *  if (req) {\n *   console.log(req) // do something with the request\n *  }\n *  return { providers: [ GitHub ] }\n * })\n * ```\n *\n * :::tip\n * This is useful if you want to customize the configuration based on the request, for example, to add a different provider in staging/dev environments.\n * :::\n *\n * @module next-auth\n */\n\n\n\n\n\n\n/**\n *  Initialize NextAuth.js.\n *\n *  @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth({ providers: [GitHub] })\n * ```\n *\n * Lazy initialization:\n *\n * @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth(async (req) => {\n *   console.log(req) // do something with the request\n *   return {\n *     providers: [GitHub],\n *   },\n * })\n * ```\n */\nfunction NextAuth(config) {\n    if (typeof config === \"function\") {\n        const httpHandler = async (req) => {\n            const _config = await config(req);\n            (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n            return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), _config);\n        };\n        return {\n            handlers: { GET: httpHandler, POST: httpHandler },\n            // @ts-expect-error\n            auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config, (c) => (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(c)),\n            signIn: async (provider, options, authorizationParams) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, _config);\n            },\n            signOut: async (options) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, _config);\n            },\n            unstable_update: async (data) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, _config);\n            },\n        };\n    }\n    (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(config);\n    const httpHandler = (req) => (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), config);\n    return {\n        handlers: { GET: httpHandler, POST: httpHandler },\n        // @ts-expect-error\n        auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config),\n        signIn: (provider, options, authorizationParams) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, config);\n        },\n        signOut: (options) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, config);\n        },\n        unstable_update: (data) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, config);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next-auth/lib/actions.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/lib/actions.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   update: () => (/* binding */ update)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(action-browser)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(action-browser)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(action-browser)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\nasync function signIn(provider, options = {}, authorizationParams, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    const { redirect: shouldRedirect = true, redirectTo, ...rest } = options instanceof FormData ? Object.fromEntries(options) : options;\n    const callbackUrl = redirectTo?.toString() ?? headers.get(\"Referer\") ?? \"/\";\n    const signInURL = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signin\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    if (!provider) {\n        signInURL.searchParams.append(\"callbackUrl\", callbackUrl);\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(signInURL.toString());\n        return signInURL.toString();\n    }\n    let url = `${signInURL}/${provider}?${new URLSearchParams(authorizationParams)}`;\n    let foundProvider = {};\n    for (const providerConfig of config.providers) {\n        const { options, ...defaults } = typeof providerConfig === \"function\" ? providerConfig() : providerConfig;\n        const id = options?.id ?? defaults.id;\n        if (id === provider) {\n            foundProvider = {\n                id,\n                type: options?.type ?? defaults.type,\n            };\n            break;\n        }\n    }\n    if (!foundProvider.id) {\n        const url = `${signInURL}?${new URLSearchParams({ callbackUrl })}`;\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(url);\n        return url;\n    }\n    if (foundProvider.type === \"credentials\") {\n        url = url.replace(\"signin\", \"callback\");\n    }\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const body = new URLSearchParams({ ...rest, callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    const responseUrl = res instanceof Response ? res.headers.get(\"Location\") : res.redirect;\n    // NOTE: if for some unexpected reason the responseUrl is not set,\n    // we redirect to the original url\n    const redirectUrl = responseUrl ?? url;\n    if (shouldRedirect)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(redirectUrl);\n    return redirectUrl;\n}\nasync function signOut(options, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signout\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const callbackUrl = options?.redirectTo ?? headers.get(\"Referer\") ?? \"/\";\n    const body = new URLSearchParams({ callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    if (options?.redirect ?? true)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(res.redirect);\n    return res;\n}\nasync function update(data, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/json\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const body = JSON.stringify({ data });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    return res.body;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/lib/actions.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next-auth/lib/env.js":
/*!*******************************************!*\
  !*** ./node_modules/next-auth/lib/env.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reqWithEnvURL: () => (/* binding */ reqWithEnvURL),\n/* harmony export */   setEnvDefaults: () => (/* binding */ setEnvDefaults)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(action-browser)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core */ \"(action-browser)/./node_modules/@auth/core/index.js\");\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\n/** If `NEXTAUTH_URL` or `AUTH_URL` is defined, override the request's URL. */\nfunction reqWithEnvURL(req) {\n    const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n    if (!url)\n        return req;\n    const { origin: envOrigin } = new URL(url);\n    const { href, origin } = req.nextUrl;\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(href.replace(origin, envOrigin), req);\n}\n/**\n * For backwards compatibility, `next-auth` checks for `NEXTAUTH_URL`\n * and the `basePath` by default is `/api/auth` instead of `/auth`\n * (which is the default for all other Auth.js integrations).\n *\n * For the same reason, `NEXTAUTH_SECRET` is also checked.\n */\nfunction setEnvDefaults(config) {\n    try {\n        config.secret ?? (config.secret = process.env.AUTH_SECRET ?? process.env.NEXTAUTH_SECRET);\n        const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n        if (!url)\n            return;\n        const { pathname } = new URL(url);\n        if (pathname === \"/\")\n            return;\n        config.basePath || (config.basePath = pathname);\n    }\n    catch {\n        // Catching and swallowing potential URL parsing errors, we'll fall\n        // back to `/api/auth` below.\n    }\n    finally {\n        config.basePath || (config.basePath = \"/api/auth\");\n        (0,_auth_core__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(process.env, config, true);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/lib/env.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next-auth/lib/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/lib/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initAuth: () => (/* binding */ initAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(action-browser)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(action-browser)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(action-browser)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _env_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./env.js */ \"(action-browser)/./node_modules/next-auth/lib/env.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\nasync function getSession(headers, config) {\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const request = new Request(url, {\n        headers: { cookie: headers.get(\"cookie\") ?? \"\" },\n    });\n    return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(request, {\n        ...config,\n        callbacks: {\n            ...config.callbacks,\n            // Since we are server-side, we don't need to filter out the session data\n            // See https://authjs.dev/getting-started/migrating-to-v5#authenticating-server-side\n            // TODO: Taint the session data to prevent accidental leakage to the client\n            // https://react.dev/reference/react/experimental_taintObjectReference\n            async session(...args) {\n                const session = \n                // If the user defined a custom session callback, use that instead\n                (await config.callbacks?.session?.(...args)) ?? {\n                    ...args[0].session,\n                    expires: args[0].session.expires?.toISOString?.() ??\n                        args[0].session.expires,\n                };\n                const user = args[0].user ?? args[0].token;\n                return { user, ...session };\n            },\n        },\n    });\n}\nfunction isReqWrapper(arg) {\n    return typeof arg === \"function\";\n}\nfunction initAuth(config, onLazyLoad // To set the default env vars\n) {\n    if (typeof config === \"function\") {\n        return async (...args) => {\n            if (!args.length) {\n                // React Server Components\n                const _headers = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n                const _config = await config(undefined); // Review: Should we pass headers() here instead?\n                onLazyLoad?.(_config);\n                return getSession(_headers, _config).then((r) => r.json());\n            }\n            if (args[0] instanceof Request) {\n                // middleware.ts inline\n                // export { auth as default } from \"auth\"\n                const req = args[0];\n                const ev = args[1];\n                const _config = await config(req);\n                onLazyLoad?.(_config);\n                // args[0] is supposed to be NextRequest but the instanceof check is failing.\n                return handleAuth([req, ev], _config);\n            }\n            if (isReqWrapper(args[0])) {\n                // middleware.ts wrapper/route.ts\n                // import { auth } from \"auth\"\n                // export default auth((req) => { console.log(req.auth) }})\n                const userMiddlewareOrRoute = args[0];\n                return async (...args) => {\n                    const _config = await config(args[0]);\n                    onLazyLoad?.(_config);\n                    return handleAuth(args, _config, userMiddlewareOrRoute);\n                };\n            }\n            // API Routes, getServerSideProps\n            const request = \"req\" in args[0] ? args[0].req : args[0];\n            const response = \"res\" in args[0] ? args[0].res : args[1];\n            const _config = await config(request);\n            onLazyLoad?.(_config);\n            // @ts-expect-error -- request is NextRequest\n            return getSession(new Headers(request.headers), _config).then(async (authResponse) => {\n                const auth = await authResponse.json();\n                for (const cookie of authResponse.headers.getSetCookie())\n                    if (\"headers\" in response)\n                        response.headers.append(\"set-cookie\", cookie);\n                    else\n                        response.appendHeader(\"set-cookie\", cookie);\n                return auth;\n            });\n        };\n    }\n    return (...args) => {\n        if (!args.length) {\n            // React Server Components\n            return Promise.resolve((0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)()).then((h) => getSession(h, config).then((r) => r.json()));\n        }\n        if (args[0] instanceof Request) {\n            // middleware.ts inline\n            // export { auth as default } from \"auth\"\n            const req = args[0];\n            const ev = args[1];\n            return handleAuth([req, ev], config);\n        }\n        if (isReqWrapper(args[0])) {\n            // middleware.ts wrapper/route.ts\n            // import { auth } from \"auth\"\n            // export default auth((req) => { console.log(req.auth) }})\n            const userMiddlewareOrRoute = args[0];\n            return async (...args) => {\n                return handleAuth(args, config, userMiddlewareOrRoute).then((res) => {\n                    return res;\n                });\n            };\n        }\n        // API Routes, getServerSideProps\n        const request = \"req\" in args[0] ? args[0].req : args[0];\n        const response = \"res\" in args[0] ? args[0].res : args[1];\n        return getSession(\n        // @ts-expect-error\n        new Headers(request.headers), config).then(async (authResponse) => {\n            const auth = await authResponse.json();\n            for (const cookie of authResponse.headers.getSetCookie())\n                if (\"headers\" in response)\n                    response.headers.append(\"set-cookie\", cookie);\n                else\n                    response.appendHeader(\"set-cookie\", cookie);\n            return auth;\n        });\n    };\n}\nasync function handleAuth(args, config, userMiddlewareOrRoute) {\n    const request = (0,_env_js__WEBPACK_IMPORTED_MODULE_3__.reqWithEnvURL)(args[0]);\n    const sessionResponse = await getSession(request.headers, config);\n    const auth = await sessionResponse.json();\n    let authorized = true;\n    if (config.callbacks?.authorized) {\n        authorized = await config.callbacks.authorized({ request, auth });\n    }\n    let response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next?.();\n    if (authorized instanceof Response) {\n        // User returned a custom response, like redirecting to a page or 401, respect it\n        response = authorized;\n        const redirect = authorized.headers.get(\"Location\");\n        const { pathname } = request.nextUrl;\n        // If the user is redirecting to the same NextAuth.js action path as the current request,\n        // don't allow the redirect to prevent an infinite loop\n        if (redirect &&\n            isSameAuthAction(pathname, new URL(redirect).pathname, config)) {\n            authorized = true;\n        }\n    }\n    else if (userMiddlewareOrRoute) {\n        // Execute user's middleware/handler with the augmented request\n        const augmentedReq = request;\n        augmentedReq.auth = auth;\n        response =\n            (await userMiddlewareOrRoute(augmentedReq, args[1])) ??\n                next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next();\n    }\n    else if (!authorized) {\n        const signInPage = config.pages?.signIn ?? `${config.basePath}/signin`;\n        if (request.nextUrl.pathname !== signInPage) {\n            // Redirect to signin page by default if not authorized\n            const signInUrl = request.nextUrl.clone();\n            signInUrl.pathname = signInPage;\n            signInUrl.searchParams.set(\"callbackUrl\", request.nextUrl.href);\n            response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(signInUrl);\n        }\n    }\n    const finalResponse = new Response(response?.body, response);\n    // Preserve cookies from the session response\n    for (const cookie of sessionResponse.headers.getSetCookie())\n        finalResponse.headers.append(\"set-cookie\", cookie);\n    return finalResponse;\n}\nfunction isSameAuthAction(requestPath, redirectPath, config) {\n    const action = redirectPath.replace(`${requestPath}/`, \"\");\n    const pages = Object.values(config.pages ?? {});\n    return ((actions.has(action) || pages.includes(redirectPath)) &&\n        redirectPath === requestPath);\n}\nconst actions = new Set([\n    \"providers\",\n    \"session\",\n    \"csrf\",\n    \"signin\",\n    \"signout\",\n    \"callback\",\n    \"verify-request\",\n    \"error\",\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0LWF1dGgvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW1EO0FBQ25EO0FBQ3VDO0FBQ3ZDO0FBQzJDO0FBQ0Y7QUFDekM7QUFDQSxnQkFBZ0IsMkRBQWU7QUFDL0I7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHFDQUFxQztBQUN4RCxLQUFLO0FBQ0wsV0FBVyxnREFBSTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLGFBQWE7QUFDYixTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLHFEQUFPO0FBQzlDLHlEQUF5RDtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGtCQUFrQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsT0FBTztBQUNuQyxrREFBa0Qsd0JBQXdCO0FBQzFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLHFEQUFPO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixrQkFBa0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU87QUFDL0IsOENBQThDLHdCQUF3QjtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHNEQUFhO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseURBQXlELGVBQWU7QUFDeEU7QUFDQSxtQkFBbUIscURBQVk7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsV0FBVztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixxREFBWTtBQUM1QjtBQUNBO0FBQ0Esc0RBQXNELGdCQUFnQjtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHFEQUFZO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxZQUFZO0FBQ3ZELGtEQUFrRDtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpc1xcRG9jdW1lbnRzXFxQcm9qZWN0c1xcU3BpZGV4XFxzcGlkZXhfd2VidWkzLjBcXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEF1dGgsIGNyZWF0ZUFjdGlvblVSTCB9IGZyb20gXCJAYXV0aC9jb3JlXCI7XG4vLyBAdHMtZXhwZWN0LWVycm9yIE5leHQuanMgZG9lcyBub3QgeWV0IGNvcnJlY3RseSB1c2UgdGhlIGBwYWNrYWdlLmpzb24jZXhwb3J0c2AgZmllbGRcbmltcG9ydCB7IGhlYWRlcnMgfSBmcm9tIFwibmV4dC9oZWFkZXJzXCI7XG4vLyBAdHMtZXhwZWN0LWVycm9yIE5leHQuanMgZG9lcyBub3QgeWV0IGNvcnJlY3RseSB1c2UgdGhlIGBwYWNrYWdlLmpzb24jZXhwb3J0c2AgZmllbGRcbmltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xuaW1wb3J0IHsgcmVxV2l0aEVudlVSTCB9IGZyb20gXCIuL2Vudi5qc1wiO1xuYXN5bmMgZnVuY3Rpb24gZ2V0U2Vzc2lvbihoZWFkZXJzLCBjb25maWcpIHtcbiAgICBjb25zdCB1cmwgPSBjcmVhdGVBY3Rpb25VUkwoXCJzZXNzaW9uXCIsIFxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgYHgtZm9yd2FyZGVkLXByb3RvYCBpcyBub3QgbnVsbGFibGUsIG5leHQuanMgc2V0cyBpdCBieSBkZWZhdWx0XG4gICAgaGVhZGVycy5nZXQoXCJ4LWZvcndhcmRlZC1wcm90b1wiKSwgaGVhZGVycywgcHJvY2Vzcy5lbnYsIGNvbmZpZyk7XG4gICAgY29uc3QgcmVxdWVzdCA9IG5ldyBSZXF1ZXN0KHVybCwge1xuICAgICAgICBoZWFkZXJzOiB7IGNvb2tpZTogaGVhZGVycy5nZXQoXCJjb29raWVcIikgPz8gXCJcIiB9LFxuICAgIH0pO1xuICAgIHJldHVybiBBdXRoKHJlcXVlc3QsIHtcbiAgICAgICAgLi4uY29uZmlnLFxuICAgICAgICBjYWxsYmFja3M6IHtcbiAgICAgICAgICAgIC4uLmNvbmZpZy5jYWxsYmFja3MsXG4gICAgICAgICAgICAvLyBTaW5jZSB3ZSBhcmUgc2VydmVyLXNpZGUsIHdlIGRvbid0IG5lZWQgdG8gZmlsdGVyIG91dCB0aGUgc2Vzc2lvbiBkYXRhXG4gICAgICAgICAgICAvLyBTZWUgaHR0cHM6Ly9hdXRoanMuZGV2L2dldHRpbmctc3RhcnRlZC9taWdyYXRpbmctdG8tdjUjYXV0aGVudGljYXRpbmctc2VydmVyLXNpZGVcbiAgICAgICAgICAgIC8vIFRPRE86IFRhaW50IHRoZSBzZXNzaW9uIGRhdGEgdG8gcHJldmVudCBhY2NpZGVudGFsIGxlYWthZ2UgdG8gdGhlIGNsaWVudFxuICAgICAgICAgICAgLy8gaHR0cHM6Ly9yZWFjdC5kZXYvcmVmZXJlbmNlL3JlYWN0L2V4cGVyaW1lbnRhbF90YWludE9iamVjdFJlZmVyZW5jZVxuICAgICAgICAgICAgYXN5bmMgc2Vzc2lvbiguLi5hcmdzKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc2Vzc2lvbiA9IFxuICAgICAgICAgICAgICAgIC8vIElmIHRoZSB1c2VyIGRlZmluZWQgYSBjdXN0b20gc2Vzc2lvbiBjYWxsYmFjaywgdXNlIHRoYXQgaW5zdGVhZFxuICAgICAgICAgICAgICAgIChhd2FpdCBjb25maWcuY2FsbGJhY2tzPy5zZXNzaW9uPy4oLi4uYXJncykpID8/IHtcbiAgICAgICAgICAgICAgICAgICAgLi4uYXJnc1swXS5zZXNzaW9uLFxuICAgICAgICAgICAgICAgICAgICBleHBpcmVzOiBhcmdzWzBdLnNlc3Npb24uZXhwaXJlcz8udG9JU09TdHJpbmc/LigpID8/XG4gICAgICAgICAgICAgICAgICAgICAgICBhcmdzWzBdLnNlc3Npb24uZXhwaXJlcyxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIGNvbnN0IHVzZXIgPSBhcmdzWzBdLnVzZXIgPz8gYXJnc1swXS50b2tlbjtcbiAgICAgICAgICAgICAgICByZXR1cm4geyB1c2VyLCAuLi5zZXNzaW9uIH07XG4gICAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgIH0pO1xufVxuZnVuY3Rpb24gaXNSZXFXcmFwcGVyKGFyZykge1xuICAgIHJldHVybiB0eXBlb2YgYXJnID09PSBcImZ1bmN0aW9uXCI7XG59XG5leHBvcnQgZnVuY3Rpb24gaW5pdEF1dGgoY29uZmlnLCBvbkxhenlMb2FkIC8vIFRvIHNldCB0aGUgZGVmYXVsdCBlbnYgdmFyc1xuKSB7XG4gICAgaWYgKHR5cGVvZiBjb25maWcgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICByZXR1cm4gYXN5bmMgKC4uLmFyZ3MpID0+IHtcbiAgICAgICAgICAgIGlmICghYXJncy5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAvLyBSZWFjdCBTZXJ2ZXIgQ29tcG9uZW50c1xuICAgICAgICAgICAgICAgIGNvbnN0IF9oZWFkZXJzID0gYXdhaXQgaGVhZGVycygpO1xuICAgICAgICAgICAgICAgIGNvbnN0IF9jb25maWcgPSBhd2FpdCBjb25maWcodW5kZWZpbmVkKTsgLy8gUmV2aWV3OiBTaG91bGQgd2UgcGFzcyBoZWFkZXJzKCkgaGVyZSBpbnN0ZWFkP1xuICAgICAgICAgICAgICAgIG9uTGF6eUxvYWQ/LihfY29uZmlnKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZ2V0U2Vzc2lvbihfaGVhZGVycywgX2NvbmZpZykudGhlbigocikgPT4gci5qc29uKCkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGFyZ3NbMF0gaW5zdGFuY2VvZiBSZXF1ZXN0KSB7XG4gICAgICAgICAgICAgICAgLy8gbWlkZGxld2FyZS50cyBpbmxpbmVcbiAgICAgICAgICAgICAgICAvLyBleHBvcnQgeyBhdXRoIGFzIGRlZmF1bHQgfSBmcm9tIFwiYXV0aFwiXG4gICAgICAgICAgICAgICAgY29uc3QgcmVxID0gYXJnc1swXTtcbiAgICAgICAgICAgICAgICBjb25zdCBldiA9IGFyZ3NbMV07XG4gICAgICAgICAgICAgICAgY29uc3QgX2NvbmZpZyA9IGF3YWl0IGNvbmZpZyhyZXEpO1xuICAgICAgICAgICAgICAgIG9uTGF6eUxvYWQ/LihfY29uZmlnKTtcbiAgICAgICAgICAgICAgICAvLyBhcmdzWzBdIGlzIHN1cHBvc2VkIHRvIGJlIE5leHRSZXF1ZXN0IGJ1dCB0aGUgaW5zdGFuY2VvZiBjaGVjayBpcyBmYWlsaW5nLlxuICAgICAgICAgICAgICAgIHJldHVybiBoYW5kbGVBdXRoKFtyZXEsIGV2XSwgX2NvbmZpZyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXNSZXFXcmFwcGVyKGFyZ3NbMF0pKSB7XG4gICAgICAgICAgICAgICAgLy8gbWlkZGxld2FyZS50cyB3cmFwcGVyL3JvdXRlLnRzXG4gICAgICAgICAgICAgICAgLy8gaW1wb3J0IHsgYXV0aCB9IGZyb20gXCJhdXRoXCJcbiAgICAgICAgICAgICAgICAvLyBleHBvcnQgZGVmYXVsdCBhdXRoKChyZXEpID0+IHsgY29uc29sZS5sb2cocmVxLmF1dGgpIH19KVxuICAgICAgICAgICAgICAgIGNvbnN0IHVzZXJNaWRkbGV3YXJlT3JSb3V0ZSA9IGFyZ3NbMF07XG4gICAgICAgICAgICAgICAgcmV0dXJuIGFzeW5jICguLi5hcmdzKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IF9jb25maWcgPSBhd2FpdCBjb25maWcoYXJnc1swXSk7XG4gICAgICAgICAgICAgICAgICAgIG9uTGF6eUxvYWQ/LihfY29uZmlnKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGhhbmRsZUF1dGgoYXJncywgX2NvbmZpZywgdXNlck1pZGRsZXdhcmVPclJvdXRlKTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gQVBJIFJvdXRlcywgZ2V0U2VydmVyU2lkZVByb3BzXG4gICAgICAgICAgICBjb25zdCByZXF1ZXN0ID0gXCJyZXFcIiBpbiBhcmdzWzBdID8gYXJnc1swXS5yZXEgOiBhcmdzWzBdO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBcInJlc1wiIGluIGFyZ3NbMF0gPyBhcmdzWzBdLnJlcyA6IGFyZ3NbMV07XG4gICAgICAgICAgICBjb25zdCBfY29uZmlnID0gYXdhaXQgY29uZmlnKHJlcXVlc3QpO1xuICAgICAgICAgICAgb25MYXp5TG9hZD8uKF9jb25maWcpO1xuICAgICAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAtLSByZXF1ZXN0IGlzIE5leHRSZXF1ZXN0XG4gICAgICAgICAgICByZXR1cm4gZ2V0U2Vzc2lvbihuZXcgSGVhZGVycyhyZXF1ZXN0LmhlYWRlcnMpLCBfY29uZmlnKS50aGVuKGFzeW5jIChhdXRoUmVzcG9uc2UpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCBhdXRoID0gYXdhaXQgYXV0aFJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGNvb2tpZSBvZiBhdXRoUmVzcG9uc2UuaGVhZGVycy5nZXRTZXRDb29raWUoKSlcbiAgICAgICAgICAgICAgICAgICAgaWYgKFwiaGVhZGVyc1wiIGluIHJlc3BvbnNlKVxuICAgICAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UuaGVhZGVycy5hcHBlbmQoXCJzZXQtY29va2llXCIsIGNvb2tpZSk7XG4gICAgICAgICAgICAgICAgICAgIGVsc2VcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlLmFwcGVuZEhlYWRlcihcInNldC1jb29raWVcIiwgY29va2llKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gYXV0aDtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgIH1cbiAgICByZXR1cm4gKC4uLmFyZ3MpID0+IHtcbiAgICAgICAgaWYgKCFhcmdzLmxlbmd0aCkge1xuICAgICAgICAgICAgLy8gUmVhY3QgU2VydmVyIENvbXBvbmVudHNcbiAgICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoaGVhZGVycygpKS50aGVuKChoKSA9PiBnZXRTZXNzaW9uKGgsIGNvbmZpZykudGhlbigocikgPT4gci5qc29uKCkpKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoYXJnc1swXSBpbnN0YW5jZW9mIFJlcXVlc3QpIHtcbiAgICAgICAgICAgIC8vIG1pZGRsZXdhcmUudHMgaW5saW5lXG4gICAgICAgICAgICAvLyBleHBvcnQgeyBhdXRoIGFzIGRlZmF1bHQgfSBmcm9tIFwiYXV0aFwiXG4gICAgICAgICAgICBjb25zdCByZXEgPSBhcmdzWzBdO1xuICAgICAgICAgICAgY29uc3QgZXYgPSBhcmdzWzFdO1xuICAgICAgICAgICAgcmV0dXJuIGhhbmRsZUF1dGgoW3JlcSwgZXZdLCBjb25maWcpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChpc1JlcVdyYXBwZXIoYXJnc1swXSkpIHtcbiAgICAgICAgICAgIC8vIG1pZGRsZXdhcmUudHMgd3JhcHBlci9yb3V0ZS50c1xuICAgICAgICAgICAgLy8gaW1wb3J0IHsgYXV0aCB9IGZyb20gXCJhdXRoXCJcbiAgICAgICAgICAgIC8vIGV4cG9ydCBkZWZhdWx0IGF1dGgoKHJlcSkgPT4geyBjb25zb2xlLmxvZyhyZXEuYXV0aCkgfX0pXG4gICAgICAgICAgICBjb25zdCB1c2VyTWlkZGxld2FyZU9yUm91dGUgPSBhcmdzWzBdO1xuICAgICAgICAgICAgcmV0dXJuIGFzeW5jICguLi5hcmdzKSA9PiB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGhhbmRsZUF1dGgoYXJncywgY29uZmlnLCB1c2VyTWlkZGxld2FyZU9yUm91dGUpLnRoZW4oKHJlcykgPT4ge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICAvLyBBUEkgUm91dGVzLCBnZXRTZXJ2ZXJTaWRlUHJvcHNcbiAgICAgICAgY29uc3QgcmVxdWVzdCA9IFwicmVxXCIgaW4gYXJnc1swXSA/IGFyZ3NbMF0ucmVxIDogYXJnc1swXTtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBcInJlc1wiIGluIGFyZ3NbMF0gPyBhcmdzWzBdLnJlcyA6IGFyZ3NbMV07XG4gICAgICAgIHJldHVybiBnZXRTZXNzaW9uKFxuICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yXG4gICAgICAgIG5ldyBIZWFkZXJzKHJlcXVlc3QuaGVhZGVycyksIGNvbmZpZykudGhlbihhc3luYyAoYXV0aFJlc3BvbnNlKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBhdXRoID0gYXdhaXQgYXV0aFJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgIGZvciAoY29uc3QgY29va2llIG9mIGF1dGhSZXNwb25zZS5oZWFkZXJzLmdldFNldENvb2tpZSgpKVxuICAgICAgICAgICAgICAgIGlmIChcImhlYWRlcnNcIiBpbiByZXNwb25zZSlcbiAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2UuaGVhZGVycy5hcHBlbmQoXCJzZXQtY29va2llXCIsIGNvb2tpZSk7XG4gICAgICAgICAgICAgICAgZWxzZVxuICAgICAgICAgICAgICAgICAgICByZXNwb25zZS5hcHBlbmRIZWFkZXIoXCJzZXQtY29va2llXCIsIGNvb2tpZSk7XG4gICAgICAgICAgICByZXR1cm4gYXV0aDtcbiAgICAgICAgfSk7XG4gICAgfTtcbn1cbmFzeW5jIGZ1bmN0aW9uIGhhbmRsZUF1dGgoYXJncywgY29uZmlnLCB1c2VyTWlkZGxld2FyZU9yUm91dGUpIHtcbiAgICBjb25zdCByZXF1ZXN0ID0gcmVxV2l0aEVudlVSTChhcmdzWzBdKTtcbiAgICBjb25zdCBzZXNzaW9uUmVzcG9uc2UgPSBhd2FpdCBnZXRTZXNzaW9uKHJlcXVlc3QuaGVhZGVycywgY29uZmlnKTtcbiAgICBjb25zdCBhdXRoID0gYXdhaXQgc2Vzc2lvblJlc3BvbnNlLmpzb24oKTtcbiAgICBsZXQgYXV0aG9yaXplZCA9IHRydWU7XG4gICAgaWYgKGNvbmZpZy5jYWxsYmFja3M/LmF1dGhvcml6ZWQpIHtcbiAgICAgICAgYXV0aG9yaXplZCA9IGF3YWl0IGNvbmZpZy5jYWxsYmFja3MuYXV0aG9yaXplZCh7IHJlcXVlc3QsIGF1dGggfSk7XG4gICAgfVxuICAgIGxldCByZXNwb25zZSA9IE5leHRSZXNwb25zZS5uZXh0Py4oKTtcbiAgICBpZiAoYXV0aG9yaXplZCBpbnN0YW5jZW9mIFJlc3BvbnNlKSB7XG4gICAgICAgIC8vIFVzZXIgcmV0dXJuZWQgYSBjdXN0b20gcmVzcG9uc2UsIGxpa2UgcmVkaXJlY3RpbmcgdG8gYSBwYWdlIG9yIDQwMSwgcmVzcGVjdCBpdFxuICAgICAgICByZXNwb25zZSA9IGF1dGhvcml6ZWQ7XG4gICAgICAgIGNvbnN0IHJlZGlyZWN0ID0gYXV0aG9yaXplZC5oZWFkZXJzLmdldChcIkxvY2F0aW9uXCIpO1xuICAgICAgICBjb25zdCB7IHBhdGhuYW1lIH0gPSByZXF1ZXN0Lm5leHRVcmw7XG4gICAgICAgIC8vIElmIHRoZSB1c2VyIGlzIHJlZGlyZWN0aW5nIHRvIHRoZSBzYW1lIE5leHRBdXRoLmpzIGFjdGlvbiBwYXRoIGFzIHRoZSBjdXJyZW50IHJlcXVlc3QsXG4gICAgICAgIC8vIGRvbid0IGFsbG93IHRoZSByZWRpcmVjdCB0byBwcmV2ZW50IGFuIGluZmluaXRlIGxvb3BcbiAgICAgICAgaWYgKHJlZGlyZWN0ICYmXG4gICAgICAgICAgICBpc1NhbWVBdXRoQWN0aW9uKHBhdGhuYW1lLCBuZXcgVVJMKHJlZGlyZWN0KS5wYXRobmFtZSwgY29uZmlnKSkge1xuICAgICAgICAgICAgYXV0aG9yaXplZCA9IHRydWU7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSBpZiAodXNlck1pZGRsZXdhcmVPclJvdXRlKSB7XG4gICAgICAgIC8vIEV4ZWN1dGUgdXNlcidzIG1pZGRsZXdhcmUvaGFuZGxlciB3aXRoIHRoZSBhdWdtZW50ZWQgcmVxdWVzdFxuICAgICAgICBjb25zdCBhdWdtZW50ZWRSZXEgPSByZXF1ZXN0O1xuICAgICAgICBhdWdtZW50ZWRSZXEuYXV0aCA9IGF1dGg7XG4gICAgICAgIHJlc3BvbnNlID1cbiAgICAgICAgICAgIChhd2FpdCB1c2VyTWlkZGxld2FyZU9yUm91dGUoYXVnbWVudGVkUmVxLCBhcmdzWzFdKSkgPz9cbiAgICAgICAgICAgICAgICBOZXh0UmVzcG9uc2UubmV4dCgpO1xuICAgIH1cbiAgICBlbHNlIGlmICghYXV0aG9yaXplZCkge1xuICAgICAgICBjb25zdCBzaWduSW5QYWdlID0gY29uZmlnLnBhZ2VzPy5zaWduSW4gPz8gYCR7Y29uZmlnLmJhc2VQYXRofS9zaWduaW5gO1xuICAgICAgICBpZiAocmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lICE9PSBzaWduSW5QYWdlKSB7XG4gICAgICAgICAgICAvLyBSZWRpcmVjdCB0byBzaWduaW4gcGFnZSBieSBkZWZhdWx0IGlmIG5vdCBhdXRob3JpemVkXG4gICAgICAgICAgICBjb25zdCBzaWduSW5VcmwgPSByZXF1ZXN0Lm5leHRVcmwuY2xvbmUoKTtcbiAgICAgICAgICAgIHNpZ25JblVybC5wYXRobmFtZSA9IHNpZ25JblBhZ2U7XG4gICAgICAgICAgICBzaWduSW5Vcmwuc2VhcmNoUGFyYW1zLnNldChcImNhbGxiYWNrVXJsXCIsIHJlcXVlc3QubmV4dFVybC5ocmVmKTtcbiAgICAgICAgICAgIHJlc3BvbnNlID0gTmV4dFJlc3BvbnNlLnJlZGlyZWN0KHNpZ25JblVybCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgY29uc3QgZmluYWxSZXNwb25zZSA9IG5ldyBSZXNwb25zZShyZXNwb25zZT8uYm9keSwgcmVzcG9uc2UpO1xuICAgIC8vIFByZXNlcnZlIGNvb2tpZXMgZnJvbSB0aGUgc2Vzc2lvbiByZXNwb25zZVxuICAgIGZvciAoY29uc3QgY29va2llIG9mIHNlc3Npb25SZXNwb25zZS5oZWFkZXJzLmdldFNldENvb2tpZSgpKVxuICAgICAgICBmaW5hbFJlc3BvbnNlLmhlYWRlcnMuYXBwZW5kKFwic2V0LWNvb2tpZVwiLCBjb29raWUpO1xuICAgIHJldHVybiBmaW5hbFJlc3BvbnNlO1xufVxuZnVuY3Rpb24gaXNTYW1lQXV0aEFjdGlvbihyZXF1ZXN0UGF0aCwgcmVkaXJlY3RQYXRoLCBjb25maWcpIHtcbiAgICBjb25zdCBhY3Rpb24gPSByZWRpcmVjdFBhdGgucmVwbGFjZShgJHtyZXF1ZXN0UGF0aH0vYCwgXCJcIik7XG4gICAgY29uc3QgcGFnZXMgPSBPYmplY3QudmFsdWVzKGNvbmZpZy5wYWdlcyA/PyB7fSk7XG4gICAgcmV0dXJuICgoYWN0aW9ucy5oYXMoYWN0aW9uKSB8fCBwYWdlcy5pbmNsdWRlcyhyZWRpcmVjdFBhdGgpKSAmJlxuICAgICAgICByZWRpcmVjdFBhdGggPT09IHJlcXVlc3RQYXRoKTtcbn1cbmNvbnN0IGFjdGlvbnMgPSBuZXcgU2V0KFtcbiAgICBcInByb3ZpZGVyc1wiLFxuICAgIFwic2Vzc2lvblwiLFxuICAgIFwiY3NyZlwiLFxuICAgIFwic2lnbmluXCIsXG4gICAgXCJzaWdub3V0XCIsXG4gICAgXCJjYWxsYmFja1wiLFxuICAgIFwidmVyaWZ5LXJlcXVlc3RcIixcbiAgICBcImVycm9yXCIsXG5dKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/lib/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/next-auth/providers/credentials.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/providers/credentials.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/credentials */ \"(action-browser)/./node_modules/@auth/core/providers/credentials.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0LWF1dGgvcHJvdmlkZXJzL2NyZWRlbnRpYWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlEO0FBQ1UiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aXNcXERvY3VtZW50c1xcUHJvamVjdHNcXFNwaWRleFxcc3BpZGV4X3dlYnVpMy4wXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxccHJvdmlkZXJzXFxjcmVkZW50aWFscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiQGF1dGgvY29yZS9wcm92aWRlcnMvY3JlZGVudGlhbHNcIjtcbmV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tIFwiQGF1dGgvY29yZS9wcm92aWRlcnMvY3JlZGVudGlhbHNcIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-auth/providers/credentials.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/lib/client.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/lib/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSessionError: () => (/* binding */ ClientSessionError),\n/* harmony export */   apiBaseUrl: () => (/* binding */ apiBaseUrl),\n/* harmony export */   fetchData: () => (/* binding */ fetchData),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   useOnline: () => (/* binding */ useOnline)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core/errors */ \"(ssr)/./node_modules/@auth/core/errors.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSessionError,fetchData,apiBaseUrl,useOnline,now,parseUrl auto */ \n\n/** @todo */ class ClientFetchError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n/** @todo */ class ClientSessionError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */ async function fetchData(path, __NEXTAUTH, logger, req = {}) {\n    const url = `${apiBaseUrl(__NEXTAUTH)}/${path}`;\n    try {\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...req?.headers?.cookie ? {\n                    cookie: req.headers.cookie\n                } : {}\n            }\n        };\n        if (req?.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok) throw data;\n        return data;\n    } catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */ function apiBaseUrl(__NEXTAUTH) {\n    if (true) {\n        // Return absolute path when called server side\n        return `${__NEXTAUTH.baseUrlServer}${__NEXTAUTH.basePathServer}`;\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */ function useOnline() {\n    const [isOnline, setIsOnline] = react__WEBPACK_IMPORTED_MODULE_0__.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = ()=>setIsOnline(true);\n    const setOffline = ()=>setIsOnline(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useOnline.useEffect\": ()=>{\n            window.addEventListener(\"online\", setOnline);\n            window.addEventListener(\"offline\", setOffline);\n            return ({\n                \"useOnline.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", setOnline);\n                    window.removeEventListener(\"offline\", setOffline);\n                }\n            })[\"useOnline.useEffect\"];\n        }\n    }[\"useOnline.useEffect\"], []);\n    return isOnline;\n}\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */ function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */ function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)// Remove trailing slash\n    .replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/lib/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _lib_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/client.js */ \"(ssr)/./node_modules/next-auth/lib/client.js\");\n/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */ /* __next_internal_client_entry_do_not_use__ __NEXTAUTH,SessionContext,useSession,getSession,getCsrfToken,getProviders,signIn,signOut,SessionProvider auto */ \n\n\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nconst __NEXTAUTH = {\n    baseUrl: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePath: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePathServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: ()=>{}\n};\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: ()=>{},\n            addEventListener: ()=>{},\n            removeEventListener: ()=>{}\n        };\n    }\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn\n};\nconst SessionContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext?.(undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options ?? {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useSession.useEffect\": ()=>{\n            if (requiredAndNotLoading) {\n                const url = `${__NEXTAUTH.basePath}/signin?${new URLSearchParams({\n                    error: \"SessionRequired\",\n                    callbackUrl: window.location.href\n                })}`;\n                if (onUnauthenticated) onUnauthenticated();\n                else window.location.href = url;\n            }\n        }\n    }[\"useSession.useEffect\"], [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\nasync function getSession(params) {\n    const session = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, params);\n    if (params?.broadcast ?? true) {\n        const broadcastChannel = getNewBroadcastChannel();\n        broadcastChannel.postMessage({\n            event: \"session\",\n            data: {\n                trigger: \"getSession\"\n            }\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */ async function getCsrfToken() {\n    const response = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"csrf\", __NEXTAUTH, logger);\n    return response?.csrfToken ?? \"\";\n}\n/**\n * Returns a client-safe configuration object of the currently\n * available providers.\n */ async function getProviders() {\n    return (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"providers\", __NEXTAUTH, logger);\n}\n/**\n * Initiate a signin flow or send the user to the signin page listing all possible providers.\n * Handles CSRF protection.\n */ async function signIn(provider, options, authorizationParams) {\n    const { redirect = true } = options ?? {};\n    const redirectTo = options?.redirectTo ?? options?.callbackUrl ?? window.location.href;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        window.location.href = `${baseUrl}/error`;\n        return;\n    }\n    if (!provider || !(provider in providers)) {\n        window.location.href = `${baseUrl}/signin?${new URLSearchParams({\n            callbackUrl: redirectTo\n        })}`;\n        return;\n    }\n    const isCredentials = providers[provider].type === \"credentials\";\n    const isEmail = providers[provider].type === \"email\";\n    const isSupportingReturn = isCredentials || isEmail;\n    const signInUrl = `${baseUrl}/${isCredentials ? \"callback\" : \"signin\"}/${provider}`;\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        // @ts-expect-error\n        body: new URLSearchParams({\n            ...options,\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    // TODO: Do not redirect for Credentials and Email providers by default in next major\n    if (redirect || !isSupportingReturn) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\");\n    const code = new URL(data.url).searchParams.get(\"code\");\n    if (res.ok) {\n        await __NEXTAUTH._getSession({\n            event: \"storage\"\n        });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url\n    };\n}\n/**\n * Initiate a signout, by destroying the current session.\n * Handles CSRF protection.\n */ async function signOut(options) {\n    const redirectTo = options?.redirectTo ?? options?.callbackUrl ?? window.location.href;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${baseUrl}/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    broadcast().postMessage({\n        event: \"session\",\n        data: {\n            trigger: \"signout\"\n        }\n    });\n    if (options?.redirect ?? true) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        // @ts-expect-error\n        return;\n    }\n    await __NEXTAUTH._getSession({\n        event: \"storage\"\n    });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */ const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */ __NEXTAUTH._lastSync = hasInitialSession ? (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() : 0;\n    const [session, setSession] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        \"SessionProvider.useState\": ()=>{\n            if (hasInitialSession) __NEXTAUTH._session = props.session;\n            return props.session;\n        }\n    }[\"SessionProvider.useState\"]);\n    /** If session was passed, initialize as not loading */ const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(!hasInitialSession);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            __NEXTAUTH._getSession = ({\n                \"SessionProvider.useEffect\": async ({ event } = {})=>{\n                    try {\n                        const storageEvent = event === \"storage\";\n                        // We should always update if we don't have a client session yet\n                        // or if there are events from other tabs/windows\n                        if (storageEvent || __NEXTAUTH._session === undefined) {\n                            __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                            __NEXTAUTH._session = await getSession({\n                                broadcast: !storageEvent\n                            });\n                            setSession(__NEXTAUTH._session);\n                            return;\n                        }\n                        if (// If there is no time defined for when a session should be considered\n                        // stale, then it's okay to use the value we have until an event is\n                        // triggered which updates it\n                        !event || // If the client doesn't have a session then we don't need to call\n                        // the server to check if it does (if they have signed in via another\n                        // tab or window that will come through as a \"stroage\" event\n                        // event anyway)\n                        __NEXTAUTH._session === null || // Bail out early if the client session is not stale yet\n                        (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() < __NEXTAUTH._lastSync) {\n                            return;\n                        }\n                        // An event or session staleness occurred, update the client session.\n                        __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                        __NEXTAUTH._session = await getSession();\n                        setSession(__NEXTAUTH._session);\n                    } catch (error) {\n                        logger.error(new _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.ClientSessionError(error.message, error));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            })[\"SessionProvider.useEffect\"];\n            __NEXTAUTH._getSession();\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    __NEXTAUTH._lastSync = 0;\n                    __NEXTAUTH._session = undefined;\n                    __NEXTAUTH._getSession = ({\n                        \"SessionProvider.useEffect\": ()=>{}\n                    })[\"SessionProvider.useEffect\"];\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const handle = {\n                \"SessionProvider.useEffect.handle\": ()=>__NEXTAUTH._getSession({\n                        event: \"storage\"\n                    })\n            }[\"SessionProvider.useEffect.handle\"];\n            // Listen for storage events and update session if event fired from\n            // another window (but suppress firing another event to avoid a loop)\n            // Fetch new session data but tell it to not to fire another event to\n            // avoid an infinite loop.\n            // Note: We could pass session data through and do something like\n            // `setData(message.data)` but that can cause problems depending\n            // on how the session object is being used in the client; it is\n            // more robust to have each window/tab fetch it's own copy of the\n            // session object rather than share it across instances.\n            broadcast().addEventListener(\"message\", handle);\n            return ({\n                \"SessionProvider.useEffect\": ()=>broadcast().removeEventListener(\"message\", handle)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const { refetchOnWindowFocus = true } = props;\n            // Listen for when the page is visible, if the user switches tabs\n            // and makes our tab visible again, re-fetch the session, but only if\n            // this feature is not disabled.\n            const visibilityHandler = {\n                \"SessionProvider.useEffect.visibilityHandler\": ()=>{\n                    if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                        event: \"visibilitychange\"\n                    });\n                }\n            }[\"SessionProvider.useEffect.visibilityHandler\"];\n            document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n            return ({\n                \"SessionProvider.useEffect\": ()=>document.removeEventListener(\"visibilitychange\", visibilityHandler, false)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], [\n        props.refetchOnWindowFocus\n    ]);\n    const isOnline = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline)();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            if (refetchInterval && shouldRefetch) {\n                const refetchIntervalTimer = setInterval({\n                    \"SessionProvider.useEffect.refetchIntervalTimer\": ()=>{\n                        if (__NEXTAUTH._session) {\n                            __NEXTAUTH._getSession({\n                                event: \"poll\"\n                            });\n                        }\n                    }\n                }[\"SessionProvider.useEffect.refetchIntervalTimer\"], refetchInterval * 1000);\n                return ({\n                    \"SessionProvider.useEffect\": ()=>clearInterval(refetchIntervalTimer)\n                })[\"SessionProvider.useEffect\"];\n            }\n        }\n    }[\"SessionProvider.useEffect\"], [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SessionProvider.useMemo[value]\": ()=>({\n                data: session,\n                status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n                async update (data) {\n                    if (loading) return;\n                    setLoading(true);\n                    const newSession = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\" ? undefined : {\n                        body: {\n                            csrfToken: await getCsrfToken(),\n                            data\n                        }\n                    });\n                    setLoading(false);\n                    if (newSession) {\n                        setSession(newSession);\n                        broadcast().postMessage({\n                            event: \"session\",\n                            data: {\n                                trigger: \"getSession\"\n                            }\n                        });\n                    }\n                    return newSession;\n                }\n            })\n    }[\"SessionProvider.useMemo[value]\"], [\n        session,\n        loading\n    ]);\n    return(// @ts-expect-error\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react.js\n");

/***/ })

};
;