import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Select,
  GoogleMapW3W,
  Popconfirm,
  CommonCompactView,
  message,
  Row,
  Col,
  Input,
  Checkbox,
} from '../../../../components';
import { getAllLocations, addLocation, updateLocation, deleteLocation, getAllBranches } from '../../../../services';
import Context from '../../../../context';
import { Link } from 'react-router-dom';
import { PlusOutlined } from '@ant-design/icons';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import { buildCommonApiValues } from '../../../../utils';
import { useParams } from 'react-router-dom';
import mapSvg from '../../../../assets/images/gmap.svg';
import CommonDrawer from '../../../../components/CommonDrawer';
import { get } from 'lodash';
import { CRUD, Pages, ModuleNames } from '../../../../constants';

const { Option } = Select;

const Location = () => {
  const params = useParams();
  const [context, setContext] = useContext(Context);
  const [branches, setBranches] = useState([]);
  const [locations, setLocations] = useState([]);
  const [location, setLocation] = useState({});
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');
  const [isMaps, setIsMaps] = useState(false);
  const [mapValues, setMapValues] = useState({});
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setLocations(tableData) : setLocations(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const [branchId, setBranchId] = useState(params.id || null);

  useEffect(() => {
    setBranchId(params.id);
  }, [params]);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const makeActive = (data) => {
    updateLocationCall({ ...data, deleted: false });
  };

  const closeAdd = () => {
    setVisible(false);
    form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (action === 'new') {
      await saveLocationAction({ ...commonValues, ...values, properties: {} });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateLocationAction(values);
      setVisible(false);
    }
  };

  const saveLocationAction = async (location) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addLocation(location)
      .then((res) => {
        res.data.branchName = branches.filter((x) => x.id === location.branchId)[0].name;
        setLocations((state) => [res.data, ...state]);
        message.success('Succesfully Added location');
        form.resetFields();
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Location, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editLocationAction = (location) => {
    form.setFieldsValue({ ...location });
    setLocation(location);
    setAction('edit');
    setVisible(true);
  };

  const updateLocationCall = (values) => {
    const data = { ...location, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateLocation(data)
      .then((res) => {
        setLocations((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              res.data.branchName = branches.filter((x) => x.id === res.data.branchId)[0]?.name || i.branchName;
              Object.assign(i, { ...i, ...res.data });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated location');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Location, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateLocationAction = async (values) => {
    updateLocationCall(values);
  };

  const setDeleteLocationAction = (locationId, visible) => {
    setLocations((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === locationId).visible = visible;
      tempData
        .filter((x) => x.id !== locationId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteLocationAction = (locationId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteLocation(locationId)
      .then(() => {
        setLocations((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === locationId).visible = false;
          tempState.find((x) => x.id === locationId).deleted = true;
          return [...state].filter((x) => x.id !== locationId);
        });
        message.success('Succesfully Deleted location');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Location, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  useEffect(() => {
    const init = async () => {
      if (context.profile.tenantId) {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
        try {
          let res;
          res = await getAllBranches(context.profile.tenantId);
          setBranches(res.data.filter((x) => x.deleted === false));
          const locationRes = await getAllLocations(context.profile.tenantId);
          locationRes.data.forEach((l) => {
            try {
              l.branchName = res.data.filter((x) => x.id === l.branchId)[0]?.name;
            } catch {
              l.branchName = 'Unknown';
            }
          });
          setTableData(locationRes.data);
          setLocations(locationRes.data.filter((x) => x.deleted === false));
        } catch (e) {
          console.log(e);
          message.error('Unable to get Location details, try again later');
        } finally {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        }
      }
    };
    init();
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    form.setFieldsValue({ ...form.getFieldsValue(), ...mapValues });
    // eslint-disable-next-line
  }, [mapValues]);

  const clearForm = () => {
    form.resetFields();
  };

  const tableCols = [
    {
      title: <strong> Name </strong>,
      key: 'name',
      render: (record) => (
        <PermissionContainer page={Pages.AREA} permission={CRUD.VIEW}>
          <Link to={`/device/management/configure/area/${record.id}`}>{record.name}</Link>
        </PermissionContainer>
      ),
    },
    {
      title: <strong> Branch </strong>,
      key: 'branchName',
      dataIndex: 'branchName',
      filters: branches.map((x) => ({ text: x.name, value: x.id })) || [],
      defaultFilteredValue: branchId ? [branchId] : [],
      onFilter: (value, record) => {
        return record.branchId === value;
      },
    },
    { title: <strong> Address </strong>, key: 'address', dataIndex: 'address' },
    { title: <strong> Geo </strong>, key: 'geoJson', dataIndex: 'geoJson' },
    {
      title: <strong> GPS </strong>,
      key: 'gpsPoint',
      render: (record) => (
        <>
          {record.gpsPoint?.latitude},{record.gpsPoint?.longitude}
        </>
      ),
    },
    {
      title: <strong> Actions </strong>,
      width: 330,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <PermissionContainer page={Pages.LOCATION} permission={CRUD.UPDATE}>
                <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                  Activate
                </Button>
              </PermissionContainer>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.LOCATION} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editLocationAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.LOCATION} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete location ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteLocationAction(record.id)}
                onCancel={() => setDeleteLocationAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteLocationAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const locationBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.LOCATION}].displayName`,
    ModuleNames.LOCATION
  );

  return (
    <>
      <Row justify="space-between">
        <BreadcrumbList list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.CONFIGURE, locationBreadcrumbsName]} />
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.LOCATION} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Location
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="id"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={locations}
          rowClassName={(record) => record.deleted && 'rowInactive'}
        />
      ) : (
        <CommonCompactView
          data={locations}
          GPS={true}
          onEdit={editLocationAction}
          onDelete={deleteLocationAction}
          permissions={[
            { pageName: Pages.LOCATION, permission: CRUD.UPDATE, label: 'Edit' },
            { pageName: Pages.LOCATION, permission: CRUD.DELETE, label: 'Delete' },
          ]}
          title="name"
          dataList={[{ label: 'GEO', value: 'geoJson' }]}
        />
      )}
      <CommonDrawer title="Location" visible={visible} closeAdd={closeAdd}>
        <Form
          scrollToFirstError={true}
          layout="horizontal"
          initialValues={{}}
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          <Form.Item
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input location Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Location Name" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Branch"
            name="branchId"
            rules={[
              {
                required: true,
                message: 'Please select location Branch!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Location Branch"
            >
              {branches.map((b) => (
                <Option title={b.name} key={b.id} value={b.id}>
                  {b.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Address"
            name="address"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input location Address!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Location Address" />
          </Form.Item>
          <Form.Item required label="Geo">
            <Row justify="space-between" className="geoLocation">
              <Col xs={{ span: 21 }} span={22}>
                <Form.Item
                  hasFeedback
                  name="geoJson"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: 'Please input location Geo Location JSON!',
                      min: 1,
                      max: 2000,
                    },
                  ]}
                >
                  <Input placeholder="Branch Geo" />
                </Form.Item>
              </Col>
              <Col className="locationSelector">
                <img src={mapSvg} alt="mapSvg" onClick={() => setIsMaps(true)} />
              </Col>
            </Row>
          </Form.Item>
          <Form.Item label="GPS" required>
            <Input.Group compact>
              <Form.Item
                name={['gpsPoint', 'latitude']}
                noStyle
                hasFeedback
                rules={[{ required: true, message: 'Latitude is required' }]}
              >
                <Input style={{ width: '50%' }} placeholder="Latitude" />
              </Form.Item>
              <Form.Item
                name={['gpsPoint', 'longitude']}
                noStyle
                hasFeedback
                rules={[{ required: true, message: 'Longitude is required' }]}
              >
                <Input style={{ width: '50%' }} placeholder="Longitude" />
              </Form.Item>
            </Input.Group>
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" type="primary" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
        {isMaps && <GoogleMapW3W setIsMaps={setIsMaps} onCloseUpdate={setMapValues}></GoogleMapW3W>}
      </CommonDrawer>
    </>
  );
};

export default Location;
