"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Area, AreaPaginationParams, AreaPageSize } from "@/types/area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { AreaTablePagination } from "./area-table-pagination";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  RotateCcw,
  MapPin,
  Calendar,
  Building,
  Link as LinkIcon,
} from "lucide-react";

interface AreaDataTableProps {
  data: Area[];
  pagination: AreaPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: AreaPageSize[];
  onEdit: (area: Area) => void;
  onDelete: (areaId: string) => void;
  onToggleActive?: (area: Area) => void;
  onBindGateway?: (area: Area) => void;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: AreaPageSize) => void;
}

export function AreaDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onEdit,
  onDelete,
  onToggleActive,
  onBindGateway,
  onPageChange,
  onPageSizeChange,
}: AreaDataTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [areaToDelete, setAreaToDelete] = useState<Area | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Reset dropdown state when data changes
  useEffect(() => {
    setOpenDropdownId(null);
  }, [data]);

  const handleDeleteClick = (area: Area) => {
    setAreaToDelete(area);
    setDeleteDialogOpen(true);
    setOpenDropdownId(null); // Close dropdown
  };

  const handleDeleteConfirm = () => {
    if (areaToDelete) {
      onDelete(areaToDelete.id);
      setAreaToDelete(null);
      setDeleteDialogOpen(false);
    }
  };

  const handleEditClick = (area: Area) => {
    setOpenDropdownId(null); // Close dropdown first
    // Small delay to ensure dropdown closes before opening modal
    setTimeout(() => {
      onEdit(area);
    }, 50);
  };

  const handleDropdownOpenChange = (open: boolean, areaId: string) => {
    if (open) {
      setOpenDropdownId(areaId);
    } else {
      setOpenDropdownId(null);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatGpsPoint = (gpsPoint: {
    latitude: string;
    longitude: string;
  }) => {
    return `${parseFloat(gpsPoint.latitude).toFixed(4)}, ${parseFloat(
      gpsPoint.longitude
    ).toFixed(4)}`;
  };

  return (
    <div>
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[180px]">Name</TableHead>
              <TableHead className="hidden md:table-cell">Address</TableHead>
              <TableHead className="hidden lg:table-cell">Branch</TableHead>
              <TableHead className="hidden lg:table-cell">Location</TableHead>
              <TableHead className="hidden xl:table-cell">Geo</TableHead>
              <TableHead className="hidden xl:table-cell">
                GPS Coordinates
              </TableHead>
              <TableHead className="hidden xl:table-cell">Level</TableHead>
              <TableHead className="hidden xl:table-cell">Max</TableHead>
              <TableHead className="hidden xl:table-cell">Min</TableHead>
              <TableHead className="hidden xl:table-cell">Status</TableHead>
              <TableHead className="hidden 2xl:table-cell">Created</TableHead>
              <TableHead className="text-left w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={12} className="text-center py-8">
                  <div className="flex flex-col items-center gap-2">
                    <Building className="h-8 w-8 text-muted-foreground" />
                    <p className="text-muted-foreground">No areas found</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              data.map((area) => (
                <TableRow
                  key={area.id}
                  className={area.deleted ? "opacity-50" : ""}
                >
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <Link
                        href={`/device/management/configure/gateway/${area.id}`}
                        className="font-semibold text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {area.name}
                      </Link>
                      <span className="text-sm text-muted-foreground md:hidden">
                        {area.address}
                      </span>
                      <span className="text-xs text-muted-foreground lg:hidden">
                        {area.branchName} • {area.locationName}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell">
                    <div
                      className="max-w-[150px] truncate"
                      title={area.address}
                    >
                      {area.address}
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center gap-1">
                      <Building className="h-3 w-3 text-muted-foreground" />
                      <span
                        className="text-sm truncate max-w-[120px]"
                        title={area.branchName}
                      >
                        {area.branchName || "Unknown"}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden lg:table-cell">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span
                        className="text-sm truncate max-w-[120px]"
                        title={area.locationName}
                      >
                        {area.locationName || "Unknown"}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <div
                      className="max-w-[100px] truncate text-sm"
                      title={area.geoJson}
                    >
                      {area.geoJson || "N/A"}
                    </div>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-muted-foreground" />
                      <span className="text-sm font-mono">
                        {formatGpsPoint(area.gpsPoint)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <span className="text-sm">{area.level || "N/A"}</span>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <span className="text-sm">{area.max || "N/A"}</span>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <span className="text-sm">{area.min || "N/A"}</span>
                  </TableCell>
                  <TableCell className="hidden xl:table-cell">
                    <Badge variant={area.deleted ? "destructive" : "default"}>
                      {area.deleted ? "Inactive" : "Active"}
                    </Badge>
                  </TableCell>
                  <TableCell className="hidden 2xl:table-cell">
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {formatDate(area.createdTime)}
                    </div>
                  </TableCell>
                  <TableCell className="text-left">
                    <DropdownMenu
                      open={openDropdownId === area.id}
                      onOpenChange={(open) =>
                        handleDropdownOpenChange(open, area.id)
                      }
                    >
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {area.deleted ? (
                          onToggleActive && (
                            <DropdownMenuItem
                              onClick={() => {
                                onToggleActive(area);
                                setOpenDropdownId(null);
                              }}
                              className="text-green-600"
                            >
                              <RotateCcw className="mr-2 h-4 w-4" />
                              Activate
                            </DropdownMenuItem>
                          )
                        ) : (
                          <>
                            <DropdownMenuItem
                              onClick={() => handleEditClick(area)}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            {onBindGateway && (
                              <DropdownMenuItem
                                onClick={() => {
                                  onBindGateway(area);
                                  setOpenDropdownId(null);
                                }}
                                className="text-blue-600"
                              >
                                <LinkIcon className="mr-2 h-4 w-4" />
                                Bind Gateway
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={() => handleDeleteClick(area)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <AreaTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Area</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the area &quot;
              {areaToDelete?.name}&quot;? This action cannot be undone and will
              mark the area as inactive.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
