// Generic item type that can work with any hierarchical structure
interface MapItem {
  id: string;
  name: string;
  gpsPoint: {
    latitude: string | number;
    longitude: string | number;
  };
  children: MapItem[];
  [key: string]: any; // Allow additional properties
}

interface State {
  currentItems: MapItem[];
  parentItem: MapItem | null;
  navigationHistory: { currentItems: MapItem[]; parentItem: MapItem | null }[];
}

type Action =
  | { type: "NAVIGATE_DOWN"; payload: MapItem }
  | { type: "NAVIGATE_BACK" };

export const mapInitialState: State = {
  currentItems: [], // Initially empty until data is loaded
  parentItem: null,
  navigationHistory: [],
};

export const mapReducer: React.Reducer<State, Action> = (state, action) => {
  switch (action.type) {
    case "NAVIGATE_DOWN":
      const { currentItems, parentItem } = state;
      return {
        ...state,
        navigationHistory: [
          ...state.navigationHistory,
          { currentItems, parentItem },
        ],
        currentItems: action.payload.children,
        parentItem: action.payload,
      };
    case "NAVIGATE_BACK":
      if (state.navigationHistory.length === 0) {
        return state;
      }
      const { currentItems: prevItems, parentItem: prevParent } =
        state.navigationHistory[state.navigationHistory.length - 1];
      const newHistory = state.navigationHistory.slice(0, -1);
      return {
        ...state,
        navigationHistory: newHistory,
        currentItems: prevItems,
        parentItem: prevParent,
      };
    default:
      return state;
  }
};
