import React from "react";
import { useMapBounds } from "@/lib/hooks/useMapBounds";
import {
  Map,
  Marker,
  Popup,
  Source,
  Layer,
  NavigationControl,
  GeolocateControl,
  FullscreenControl,
  ScaleControl,
} from "react-map-gl";
import { format as formatDateTime } from "date-fns";
import { Timeline, TimelineItem } from "@/components/timeline";
import { X, Clock, Timer } from "lucide-react";
import FilledMapPin from "./filled-map-pin";
import SetBounds from "./set-bounds";
import { MAPBOX_STYLE } from "@/lib/constants/asset-tracking";

export default function TimelineWithMap({ events }: { events: any[] }) {
  const filteredEvents = React.useMemo(
    () => events.filter((e) => e.gps && e.gps.latitude && e.gps.longitude),
    [events]
  );
  const bounds = useMapBounds(filteredEvents, "gps");
  const initialViewState = filteredEvents[0]
    ? {
        latitude: filteredEvents[0].gps.latitude,
        longitude: filteredEvents[0].gps.longitude,
        zoom: 16,
      }
    : undefined;

  const geojsonLine = React.useMemo(
    () => ({
      type: "FeatureCollection",
      features: [
        {
          type: "Feature",
          geometry: {
            type: "LineString",
            coordinates: filteredEvents.map((item) => [
              parseFloat(item.gps.longitude),
              parseFloat(item.gps.latitude),
            ]),
          },
        },
      ],
    }),
    [filteredEvents]
  );

  const [popupInfo, setPopupInfo] = React.useState<any>(null);
  const [highlightedIdx, setHighlightedIdx] = React.useState<number | null>(
    null
  );

  function getMarkerColor(idx: number, total: number) {
    if (idx === 0) return "#22c55e"; // green for start
    if (idx === total - 1) return "#ef4444"; // red for end
    return "#2563eb"; // blue for intermediate
  }

  const pins = React.useMemo(
    () =>
      filteredEvents.map((item, idx) => {
        const { eventTime: id, gps, dataArea } = item;
        const color = getMarkerColor(idx, filteredEvents.length);
        const isHighlighted = highlightedIdx === idx;
        return (
          <Marker
            key={id}
            longitude={parseFloat(gps.longitude)}
            latitude={parseFloat(gps.latitude)}
            anchor="bottom"
            style={{ cursor: "pointer", zIndex: isHighlighted ? 2 : 1 }}
            onClick={(e) => {
              e.originalEvent.stopPropagation();
              setPopupInfo(item);
            }}
          >
            <div className="relative flex flex-col items-center group">
              <FilledMapPin
                color={isHighlighted ? "#f59e42" : color}
                isHighlighted={isHighlighted}
              />
              <span className="absolute -top-7 left-1/2 -translate-x-1/2 bg-white text-xs px-2 py-1 rounded shadow group-hover:bg-primary group-hover:text-white transition whitespace-nowrap border border-gray-200">
                {dataArea}
              </span>
            </div>
          </Marker>
        );
      }),
    [filteredEvents, highlightedIdx]
  );

  return (
    <div className="flex-1 flex flex-row gap-4 mb-4 min-h-0">
      <div className="max-w-[35%] h-full min-h-0 overflow-y-auto pr-2 bg-muted custom-scrollbar">
        <Timeline className="py-4 px-2 h-full min-h-[70vh] max-h-[70vh]">
          {filteredEvents.map((item: any, idx: number) => (
            <TimelineItem
              key={idx}
              date={formatDateTime(
                new Date(item.dataEventTime),
                "MMM d, HH:mm"
              )}
              title={item.dataArea}
              description={`Dwell: ${item.dwell}`}
              icon={
                <FilledMapPin
                  color={getMarkerColor(idx, filteredEvents.length)}
                  width={18}
                  height={18}
                />
              }
              className={
                "mb-2 last:mb-0 cursor-pointer min-w-[250px] " +
                (highlightedIdx === idx ? "bg-primary/10" : "")
              }
              onMouseEnter={() => setHighlightedIdx(idx)}
              onMouseLeave={() => setHighlightedIdx(null)}
              onClick={() => setPopupInfo(item)}
            />
          ))}
        </Timeline>
      </div>
      <div className="flex-1 min-w-[65%]">
        <Map
          initialViewState={initialViewState}
          mapStyle={MAPBOX_STYLE}
          mapboxAccessToken={process.env.NEXT_PUBLIC_MapboxAccessToken}
        >
          <GeolocateControl position="top-left" />
          <FullscreenControl position="top-left" />
          <NavigationControl position="top-left" />
          <ScaleControl />
          {/* Draw path */}
          <Source id="asset-path" type="geojson" data={geojsonLine}>
            <Layer
              id="line-layer"
              type="line"
              paint={{
                "line-color": "#6366f1",
                "line-width": 3,
                "line-opacity": 0.7,
              }}
            />
          </Source>
          {pins}
          {popupInfo && (
            <Popup
              longitude={parseFloat(popupInfo.gps.longitude)}
              latitude={parseFloat(popupInfo.gps.latitude)}
              anchor="top"
              onClose={() => setPopupInfo(null)}
              closeOnClick={true}
              maxWidth="260px"
              closeButton={false}
            >
              <div className="relative min-w-[200px] max-w-[260px] p-4 rounded-lg bg-white flex flex-col gap-3">
                <button
                  className="absolute top-2 right-2 p-1  hover:bg-gray-100 "
                  onClick={() => setPopupInfo(null)}
                  aria-label="Close"
                  type="button"
                >
                  <X className="w-5 h-5 text-gray-500 hover:text-gray-700" />
                </button>
                <div className="font-bold text-lg mb-1">
                  {popupInfo.dataArea}
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Clock className="w-4 h-4" />
                  <span>
                    {formatDateTime(
                      new Date(popupInfo.dataEventTime),
                      "MMM d, HH:mm"
                    )}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-base">
                  <Timer className="w-4 h-4 text-primary" />
                  <span className="font-medium">Dwell: {popupInfo.dwell}</span>
                </div>
              </div>
            </Popup>
          )}
          <SetBounds bounds={bounds} />
        </Map>
      </div>
    </div>
  );
}
