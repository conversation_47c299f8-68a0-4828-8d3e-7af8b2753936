image: node:14.17.0

pipelines:
  branches:
    master:
      - step:
          name: Build
          script:
            - git archive --format=tar.gz master -o app.tar.gz
          artifacts:
            - app.tar.gz
      - step:
          name: Deploy to production
          deployment: production
          script:
            - pipe: atlassian/heroku-deploy:1.2.1
              variables:
                HEROKU_API_KEY: $HEROKU_API_KEY
                HEROKU_APP_NAME: $HEROKU_APP_NAME
                ZIP_FILE: app.tar.gz
