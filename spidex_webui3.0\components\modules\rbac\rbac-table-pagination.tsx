"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RbacPaginationParams, RbacPageSize } from "@/types/rbac";

interface RbacTablePaginationProps {
  pagination: RbacPaginationParams;
  totalRecords: number;
  currentPageRecords: number; // Number of records on current page
  totalPages?: number;
  availablePageSizes: RbacPageSize[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: RbacPageSize) => void;
  entityName?: string; // e.g., "page", "role", "permission"
}

export function RbacTablePagination({
  pagination,
  totalRecords,
  currentPageRecords,
  totalPages = 1,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
  entityName = "record",
}: RbacTablePaginationProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center justify-between p-4 border-t">
        <div className="text-sm text-muted-foreground">Loading...</div>
        <div className="flex items-center gap-2">
          <div className="w-20 h-8 bg-muted rounded animate-pulse" />
        </div>
      </div>
    );
  }

  const pluralEntityName = entityName + (totalRecords !== 1 ? "s" : "");

  return (
    <div className="flex items-center justify-between p-4 border-t">
      {/* Results Summary */}
      <div className="text-sm text-muted-foreground">
        {pagination.pageSize === 1000 ? (
          <>
            Showing all {totalRecords} {pluralEntityName}
          </>
        ) : (
          <>
            Showing {currentPageRecords} of {totalRecords} {pluralEntityName} on
            page {pagination.page} of {totalPages}
          </>
        )}
      </div>

      <div className="flex items-center gap-4">
        {/* Page Size Selector */}
        <div className="flex items-center gap-2">
          <Label htmlFor="page-size">Show:</Label>
          <Select
            value={
              pagination.pageSize === 1000
                ? "all"
                : pagination.pageSize.toString()
            }
            onValueChange={(value) => {
              const newSize =
                value === "all" ? 1000 : (parseInt(value) as RbacPageSize);
              onPageSizeChange(newSize as any);
            }}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availablePageSizes.map((size) => (
                <SelectItem
                  key={size}
                  value={(size as any) === 1000 ? "all" : size.toString()}
                >
                  {(size as any) === 1000 ? "All" : size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pagination Controls */}
        {pagination.pageSize !== 1000 && (
          <div className="flex items-center gap-2">
            {/* First Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={pagination.page <= 1}
              className="px-3"
            >
              First
            </Button>

            {/* Previous Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="px-3"
            >
              ‹ Prev
            </Button>

            {/* Page Numbers */}
            <div className="flex items-center gap-1">
              {(() => {
                const currentPage = pagination.page;
                const pages = [];
                const maxVisiblePages = 5;

                let startPage = Math.max(
                  1,
                  currentPage - Math.floor(maxVisiblePages / 2)
                );
                let endPage = Math.min(
                  totalPages,
                  startPage + maxVisiblePages - 1
                );

                // Adjust start if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                // Show first page if not in range
                if (startPage > 1) {
                  pages.push(
                    <Button
                      key={1}
                      variant={1 === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => onPageChange(1)}
                      className="w-8 h-8 p-0"
                    >
                      1
                    </Button>
                  );
                  if (startPage > 2) {
                    pages.push(
                      <span
                        key="ellipsis1"
                        className="px-2 text-muted-foreground"
                      >
                        ...
                      </span>
                    );
                  }
                }

                // Show page range
                for (let i = startPage; i <= endPage; i++) {
                  pages.push(
                    <Button
                      key={i}
                      variant={i === currentPage ? "default" : "outline"}
                      size="sm"
                      onClick={() => onPageChange(i)}
                      className="w-8 h-8 p-0"
                    >
                      {i}
                    </Button>
                  );
                }

                // Show last page if not in range
                if (endPage < totalPages) {
                  if (endPage < totalPages - 1) {
                    pages.push(
                      <span
                        key="ellipsis2"
                        className="px-2 text-muted-foreground"
                      >
                        ...
                      </span>
                    );
                  }
                  pages.push(
                    <Button
                      key={totalPages}
                      variant={
                        totalPages === currentPage ? "default" : "outline"
                      }
                      size="sm"
                      onClick={() => onPageChange(totalPages)}
                      className="w-8 h-8 p-0"
                    >
                      {totalPages}
                    </Button>
                  );
                }

                return pages;
              })()}
            </div>

            {/* Next Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.page + 1)}
              disabled={pagination.page >= totalPages}
              className="px-3"
            >
              Next ›
            </Button>

            {/* Last Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={pagination.page >= totalPages}
              className="px-3"
            >
              Last
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
