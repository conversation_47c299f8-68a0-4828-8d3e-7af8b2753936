"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TagPaginationParams, TagPageSize } from "@/types/tag";

interface TagTablePaginationProps {
  pagination: TagPaginationParams;
  totalRecords: number;
  currentPageRecords: number; // Number of records on current page
  totalPages?: number;
  availablePageSizes: TagPageSize[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: TagPageSize) => void;
}

export function TagTablePagination({
  pagination,
  totalRecords,
  currentPageRecords,
  totalPages = 1,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
}: TagTablePaginationProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center justify-between p-4 border-t">
        <div className="text-sm text-muted-foreground">Loading...</div>
        <div className="flex items-center gap-2">
          <div className="w-20 h-8 bg-muted rounded animate-pulse" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between p-4 border-t">
      {/* Results Summary */}
      <div className="text-sm text-muted-foreground">
        {pagination.pageSize === "all" ? (
          <>
            Showing all {totalRecords} tag{totalRecords !== 1 ? "s" : ""}
          </>
        ) : (
          <>
            Showing {currentPageRecords} of {totalRecords} tag
            {totalRecords !== 1 ? "s" : ""} on page {pagination.pageNumber} of{" "}
            {totalPages}
          </>
        )}
      </div>

      <div className="flex items-center gap-4">
        {/* Page Size Selector */}
        <div className="flex items-center gap-2">
          <Label htmlFor="page-size">Show:</Label>
          <Select
            value={pagination.pageSize.toString()}
            onValueChange={(value) => {
              console.log("Page size select changed to:", value);
              const newSize =
                value === "all" ? "all" : (parseInt(value) as TagPageSize);
              console.log("Calling onPageSizeChange with:", newSize);
              onPageSizeChange(newSize);
            }}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {availablePageSizes.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size === "all" ? "All" : size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pagination Controls */}
        {pagination.pageSize !== "all" && (
          <div className="flex items-center gap-2">
            {/* First Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={pagination.pageNumber <= 1}
            >
              First
            </Button>

            {/* Previous Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.pageNumber - 1)}
              disabled={pagination.pageNumber <= 1}
            >
              Previous
            </Button>

            {/* Page Numbers */}
            <div className="flex items-center gap-1">
              {/* Show page numbers around current page */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (pagination.pageNumber <= 3) {
                  pageNum = i + 1;
                } else if (pagination.pageNumber >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = pagination.pageNumber - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={
                      pageNum === pagination.pageNumber ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => onPageChange(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            {/* Next Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(pagination.pageNumber + 1)}
              disabled={pagination.pageNumber >= totalPages}
            >
              Next
            </Button>

            {/* Last Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(totalPages)}
              disabled={pagination.pageNumber >= totalPages}
            >
              Last
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
