import { useEffect, useState, useContext } from 'react';
import { Table, Button, Input, message, Row, Col, Switch } from '../../../../components';
import { getAllTaggedAssets, updateTaggedAsset, getAllGateways, updateGateway } from '../../../../services';
import Context from '../../../../context';
import { BreadcrumbList } from '../../../../shared';
import { ModuleNames, Pages } from '../../../../constants';
import { get } from 'lodash';
import s from './index.module.less';

const Tag = () => {
  const [context, setContext] = useContext(Context);
  const [taggedAssets, setTaggedAssets] = useState([]);
  const [currentTaggedAssets, setCurrentTaggedAssets] = useState([]);
  const [gateways, setGateways] = useState([]);
  const [currentGateways, setCurrentGateways] = useState([]);

  useEffect(() => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      getAllTaggedAssets(context.profile.tenantId)
        .then((res) => {
          setTaggedAssets(res.data.filter((x) => x.deleted === false));
          setCurrentTaggedAssets(res.data.filter((x) => x.deleted === false));
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to get Tagged Asset details, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
      getAllGateways(context.profile.tenantId)
        .then((res) => {
          setGateways(res.data.filter((x) => x.deleted === false));
          setCurrentGateways(res.data.filter((x) => x.deleted === false));
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to get Gateway details, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
    }
    // eslint-disable-next-line
  }, []);

  const updateAllTaggedAssetRecord = () => {
    currentTaggedAssets.forEach((x) => {
      updateTaggedAssetRecord(x, true);
    });
  };

  const updateTaggedAssetRecord = (record, e) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    setTaggedAssets((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === record.id).provisioned = e;
      return tempData;
    });
    updateTaggedAsset({ ...record, provisioned: e, modifiedTime: new Date() })
      .then((res) => {
        setTaggedAssets((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, {
                ...res.data,
              });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated tagged asset');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Tagged Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const onSearch = (e) => {
    setCurrentTaggedAssets(() => {
      const tempData = [...taggedAssets];
      return tempData.filter((x) => x.taggedAssetInfo.assetName.toLowerCase().includes(e.target.value.toLowerCase()));
    });
  };

  const updateAllTGatewayRecord = () => {
    currentGateways.forEach((x) => {
      updateGatewayRecord(x, true);
    });
  };

  const updateGatewayRecord = (record, e) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    setGateways((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === record.id).provisioned = e;
      return tempData;
    });
    updateGateway({ ...record, provisioned: e, modifiedTime: new Date() })
      .then((res) => {
        setGateways((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, {
                ...res.data,
              });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated gateway');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Gateway, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const onSearchGateway = (e) => {
    setCurrentGateways(() => {
      const tempData = [...gateways];
      return tempData.filter((x) => x.name.toLowerCase().includes(e.target.value.toLowerCase()));
    });
  };

  const tableCols = [
    { title: <strong> Name </strong>, render: (record) => <>{record.taggedAssetInfo.assetName}</> },
    { title: <strong> Status </strong>, key: 'status', dataIndex: 'status' },
    {
      title: <strong> Provisioned </strong>,
      render: (record) => (
        <>
          <Switch onChange={(e) => updateTaggedAssetRecord(record, e)} checked={record.provisioned} />
        </>
      ),
    },
  ];

  const tableColsGateways = [
    { title: <strong> Name </strong>, render: (record) => <>{record.name}</> },
    { title: <strong> Deleted </strong>, key: 'deleted', render: (record) => <>{record.deleted ? 'TRUE' : 'FALSE'}</> },
    {
      title: <strong> Provisioned </strong>,
      render: (record) => (
        <>
          <Switch onChange={(e) => updateGatewayRecord(record, e)} checked={record.provisioned} />
        </>
      ),
    },
  ];

  const provisionBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.PROVISION}].displayName`,
    ModuleNames.PROVISION
  );

  return (
    <div className={s.onboardContainer}>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.ONBOARD, provisionBreadcrumbsName]} />
        </Col>
      </Row>
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <h3 style={{ marginTop: '10px' }}>Asset Onboarding</h3>
          <Row className={s.onboardSearchContainer}>
            <Col className={s.onboardSearch}>
              <Input placeholder="Search" onChange={onSearch}></Input>
              <Button onClick={updateAllTaggedAssetRecord} className="formButton">
                Provision All
              </Button>
            </Col>
          </Row>
          <Table
            size="small"
            scroll={{ x: true }}
            rowKey="id"
            loading={context.isLoading}
            columns={tableCols}
            dataSource={currentTaggedAssets}
          />
        </Col>
        <Col xs={24} lg={12}>
          <h3 style={{ marginTop: '10px' }}>Gateway Onboarding</h3>
          <Row className={s.onboardSearchContainer}>
            <Col className={s.onboardSearch}>
              <Input placeholder="Search" onChange={onSearchGateway}></Input>
              <Button onClick={updateAllTGatewayRecord} className="formButton">
                Provision All
              </Button>
            </Col>
          </Row>
          <Table
            size="small"
            scroll={{ x: true }}
            rowKey="id"
            loading={context.isLoading}
            columns={tableColsGateways}
            dataSource={currentGateways}
          />
        </Col>
      </Row>
    </div>
  );
};

export default Tag;
