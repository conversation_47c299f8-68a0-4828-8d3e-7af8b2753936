"use client";

import React, { useEffect, useState, useRef } from "react";
import { Loader } from "@googlemaps/js-api-loader";

// Declare google namespace for TypeScript
declare global {
  namespace google {
    namespace maps {
      class Map {
        constructor(element: HTMLElement, options?: any);
        addListener(eventName: string, handler: Function): void;
        getBounds(): any;
        getZoom(): number;
        setCenter(latLng: any): void;
        setZoom(zoom: number): void;
        fitBounds(bounds: any): void;
        data: any;
      }
      class Marker {
        constructor(options?: any);
        setMap(map: Map | null): void;
        setPosition(position: any): void;
        addListener(eventName: string, handler: Function): void;
      }
      class LatLng {
        constructor(lat: number, lng: number);
        lat(): number;
        lng(): number;
      }
      class LatLngBounds {
        constructor();
        extend(point: any): void;
        union(other: any): void;
      }
      interface MapMouseEvent {
        latLng: LatLng;
      }
      class Geocoder {
        geocode(
          request: any,
          callback: (results: any[], status: any) => void
        ): void;
      }
      namespace GeocoderStatus {
        const OK: string;
      }
      namespace event {
        function addListener(
          instance: any,
          eventName: string,
          handler: Function
        ): void;
      }
      namespace places {
        class SearchBox {
          constructor(input: HTMLInputElement);
          addListener(eventName: string, handler: Function): void;
          setBounds(bounds: any): void;
          getPlaces(): any[];
        }
      }
    }
  }
}
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";

interface GoogleMapModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLocationSelect: (location: {
    latitude: number;
    longitude: number;
    geoJson?: string;
  }) => void;
  initialLocation?: {
    latitude?: number;
    longitude?: number;
  };
}

declare global {
  interface Window {
    what3words: {
      api: {
        convertTo3wa: (
          coords: { lat: number; lng: number },
          lang: string
        ) => Promise<{
          words: string;
          coordinates: { lat: number; lng: number };
        }>;
        convertToCoordinates: (words: string) => Promise<{
          coordinates: { lat: number; lng: number };
        }>;
        gridSectionGeoJson: (bounds: {
          southwest: { lat: number; lng: number };
          northeast: { lat: number; lng: number };
        }) => Promise<any>;
      };
    };
  }

  namespace JSX {
    interface IntrinsicElements {
      "what3words-autosuggest": React.DetailedHTMLProps<
        React.HTMLAttributes<HTMLElement>,
        HTMLElement
      > & {
        id?: string;
        api_key?: string;
      };
    }
  }
}

export function GoogleMapModal({
  isOpen,
  onClose,
  onLocationSelect,
  initialLocation,
}: GoogleMapModalProps) {
  const [mapLoader, setMapLoader] = useState<Loader | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [allowSave, setAllowSave] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
    geoJson?: string;
  } | null>(null);

  const currentMarker = useRef<google.maps.Marker | null>(null);
  const w3Marker = useRef<google.maps.Marker | null>(null);
  const mapRef = useRef<google.maps.Map | null>(null);

  const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
  const W3W_API_KEY = process.env.NEXT_PUBLIC_W3W_API_KEY;

  const addMarker = (
    google: any,
    location: google.maps.LatLng,
    map: google.maps.Map
  ) => {
    const marker = new google.maps.Marker({
      position: location,
      map: map,
    });

    // Convert to What3Words if available
    if (window.what3words?.api) {
      window.what3words.api
        .convertTo3wa({ lat: location.lat(), lng: location.lng() }, "en")
        .then((res) => {
          const locationData = {
            latitude: res.coordinates.lat,
            longitude: res.coordinates.lng,
            geoJson: res.words,
          };
          setSelectedLocation(locationData);
          setAllowSave(true);
        })
        .catch((e) => {
          console.log("What3Words conversion error:", e);
          // Fallback without What3Words
          const locationData = {
            latitude: location.lat(),
            longitude: location.lng(),
          };
          setSelectedLocation(locationData);
          setAllowSave(true);
        });
    } else {
      // No What3Words available
      const locationData = {
        latitude: location.lat(),
        longitude: location.lng(),
      };
      setSelectedLocation(locationData);
      setAllowSave(true);
    }

    currentMarker.current = marker;
    if (w3Marker.current) w3Marker.current.setMap(null);
  };

  const removeMarker = () => {
    if (currentMarker.current) {
      currentMarker.current.setMap(null);
    }
  };

  // Helper function to handle What3Words selection
  const handleWhat3WordsSelection = (words: string, map: google.maps.Map) => {
    console.log("Converting What3Words to coordinates:", words);

    // Clean up the words (remove /// if present)
    const cleanWords = words.replace(/^\/\/\//, "");

    if (window.what3words?.api) {
      window.what3words.api
        .convertToCoordinates(cleanWords)
        .then((response) => {
          console.log("What3Words conversion response:", response);
          const w3pos = {
            lat: response.coordinates.lat,
            lng: response.coordinates.lng,
          };
          map.setCenter(w3pos);
          map.setZoom(20);

          // Remove existing markers
          if (currentMarker.current) currentMarker.current.setMap(null);
          if (w3Marker.current) w3Marker.current.setMap(null);

          // Add new marker
          w3Marker.current = new google.maps.Marker({
            position: w3pos,
            map: map,
          });

          // Set as selected location
          const locationData = {
            latitude: response.coordinates.lat,
            longitude: response.coordinates.lng,
            geoJson: cleanWords,
          };
          setSelectedLocation(locationData);
          setAllowSave(true);
          console.log("Location set:", locationData);
        })
        .catch((error) => {
          console.error("What3Words conversion error:", error);
        });
    }
  };

  useEffect(() => {
    if (!GOOGLE_MAPS_API_KEY) {
      console.warn("Google Maps API key not found");
      setIsLoading(false);
      return;
    }

    const loader = new Loader({
      apiKey: GOOGLE_MAPS_API_KEY,
      libraries: ["places"],
      version: "weekly",
    });
    setMapLoader(loader);
  }, [GOOGLE_MAPS_API_KEY]);

  useEffect(() => {
    if (!mapLoader || !isOpen) return;

    let map: google.maps.Map;
    let gridData: any;

    mapLoader
      .load()
      .then((google) => {
        const defaultPos = {
          lat: initialLocation?.latitude || -34.397,
          lng: initialLocation?.longitude || 150.644,
        };

        const storedLocation = localStorage.getItem("storedGoogleLocation");
        const currentPos = storedLocation
          ? JSON.parse(storedLocation)
          : defaultPos;

        map = new google.maps.Map(document.getElementById("google-map")!, {
          center: currentPos,
          zoom: 12,
        });

        mapRef.current = map;

        const input = document.getElementById(
          "google-map-input"
        ) as HTMLInputElement;
        if (input) {
          const searchBox = new google.maps.places.SearchBox(input);

          map.addListener("bounds_changed", () => {
            searchBox.setBounds(map.getBounds()!);
          });

          searchBox.addListener("places_changed", () => {
            const places = searchBox.getPlaces();
            if (places?.length === 0) return;

            removeMarker();

            const bounds = new google.maps.LatLngBounds();
            places?.forEach((place) => {
              if (!place.geometry || !place.geometry.location) return;

              addMarker(google, place.geometry.location, map);

              if (place.geometry.viewport) {
                bounds.union(place.geometry.viewport);
              } else {
                bounds.extend(place.geometry.location);
              }
            });
            map.fitBounds(bounds);
          });
        }

        map.addListener("click", (e: google.maps.MapMouseEvent) => {
          if (e.latLng) {
            removeMarker();
            addMarker(google, e.latLng, map);
            localStorage.setItem(
              "storedGoogleLocation",
              JSON.stringify({ lat: e.latLng.lat(), lng: e.latLng.lng() })
            );
          }
        });

        // What3Words autosuggest event listener
        const autosuggest = document.getElementById("w3w-autosuggest");
        if (autosuggest && window.what3words?.api) {
          console.log("Setting up What3Words autosuggest event listeners");

          // Try multiple event names that What3Words might use
          const eventNames = [
            "selected_suggestion",
            "suggestion_selected",
            "change",
            "select",
            "input",
            "blur",
          ];

          eventNames.forEach((eventName) => {
            autosuggest.addEventListener(eventName, (event: any) => {
              console.log(`What3Words ${eventName} event triggered:`, event);

              let words = null;

              // Try different ways to get the suggestion data
              if (event.detail?.suggestion?.words) {
                words = event.detail.suggestion.words;
              } else if (event.detail?.words) {
                words = event.detail.words;
              } else if (event.target?.value) {
                words = event.target.value;
              }

              console.log("Extracted words:", words);

              if (
                words &&
                words.includes(".") &&
                words.split(".").length === 3
              ) {
                console.log("Valid What3Words format detected:", words);
                handleWhat3WordsSelection(words, map);
              }
            });
          });

          // Also try to listen for input changes and Enter key
          const input =
            autosuggest.querySelector("input") ||
            document.getElementById("w3w-input-field");
          if (input) {
            console.log("Found input element in autosuggest:", input);

            input.addEventListener("keydown", (event: any) => {
              console.log("Keydown event:", event.key, event.target.value);
              if (event.key === "Enter") {
                const value = event.target.value;
                if (
                  value &&
                  value.includes(".") &&
                  value.split(".").length === 3
                ) {
                  console.log("Enter pressed with valid What3Words:", value);
                  handleWhat3WordsSelection(value, map);
                }
              }
            });

            // Also listen for input changes
            input.addEventListener("input", (event: any) => {
              const value = event.target.value;
              console.log("Input changed:", value);
              if (
                value &&
                value.includes(".") &&
                value.split(".").length === 3
              ) {
                // Debounce the conversion to avoid too many API calls
                setTimeout(() => {
                  if ((input as HTMLInputElement).value === value) {
                    // Only proceed if value hasn't changed
                    console.log("Auto-converting What3Words:", value);
                    handleWhat3WordsSelection(value, map);
                  }
                }, 1000);
              }
            });
          } else {
            console.log("No input element found in autosuggest component");
          }
        } else {
          console.log("Autosuggest element or What3Words API not found");
        }

        // What3Words grid (if available)
        if (window.what3words?.api) {
          map.addListener("bounds_changed", function () {
            const zoom = map.getZoom();
            const loadFeatures = zoom! > 17;

            if (loadFeatures) {
              const ne = map.getBounds()!.getNorthEast();
              const sw = map.getBounds()!.getSouthWest();
              window.what3words.api
                .gridSectionGeoJson({
                  southwest: {
                    lat: sw.lat(),
                    lng: sw.lng(),
                  },
                  northeast: {
                    lat: ne.lat(),
                    lng: ne.lng(),
                  },
                })
                .then(function (data) {
                  if (gridData !== undefined) {
                    for (let i = 0; i < gridData.length; i++) {
                      map.data.remove(gridData[i]);
                    }
                  }
                  gridData = map.data.addGeoJson(data);
                })
                .catch(console.error);
            }
            map.data.setStyle({
              visible: loadFeatures,
              strokeColor: "#777",
              strokeWeight: 0.5,
              clickable: false,
            });
          });
        }

        setIsLoading(false);
      })
      .catch((error) => {
        console.error("Error loading Google Maps:", error);
        setIsLoading(false);
      });

    return () => {
      removeMarker();
      if (w3Marker.current) w3Marker.current.setMap(null);
    };
  }, [mapLoader, isOpen, initialLocation]);

  const handleSave = () => {
    if (selectedLocation) {
      onLocationSelect(selectedLocation);
      onClose();
    }
  };

  const handleClose = () => {
    setSelectedLocation(null);
    setAllowSave(false);
    removeMarker();
    if (w3Marker.current) w3Marker.current.setMap(null);
    onClose();
  };

  if (!GOOGLE_MAPS_API_KEY) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-4xl h-[600px]">
          <DialogHeader>
            <DialogTitle>Location Selector</DialogTitle>
            <DialogDescription>
              Google Maps API key not configured. Please contact your
              administrator.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2 mt-4">
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl h-[700px]">
        <DialogHeader>
          <DialogTitle>Get Geo Info - Google Maps - What 3 Words</DialogTitle>
          <DialogDescription>
            Click on the map to select a location or search for a place
          </DialogDescription>
        </DialogHeader>

        {/* Search Inputs - Moved to top */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <Input
            id="google-map-input"
            placeholder="Search Google Maps"
            className="w-full"
          />
          {W3W_API_KEY && (
            <div className="flex gap-2">
              {/* @ts-ignore */}
              <what3words-autosuggest
                id="w3w-autosuggest"
                api_key={W3W_API_KEY}
                className="flex-1"
              >
                <Input
                  id="w3w-input-field"
                  placeholder="///fancy.arming.applauded"
                />
                {/* @ts-ignore */}
              </what3words-autosuggest>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  const input = document.getElementById(
                    "w3w-input-field"
                  ) as HTMLInputElement;
                  if (input?.value) {
                    console.log(
                      "Manual test button clicked with value:",
                      input.value
                    );
                    if (mapRef.current) {
                      handleWhat3WordsSelection(input.value, mapRef.current);
                    }
                  }
                }}
              >
                Test
              </Button>
            </div>
          )}
        </div>

        <div
          className="flex flex-col space-y-4"
          style={{ height: "calc(100% - 120px)" }}
        >
          {/* Map Container */}
          <div className="flex-1 relative min-h-[400px]">
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading Google Maps...</span>
              </div>
            )}
            <div id="google-map" className="w-full h-full rounded-md border" />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!allowSave}>
              Save & Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
