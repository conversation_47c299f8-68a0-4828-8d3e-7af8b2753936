"use client";

import React from "react";

interface TimelineProps {
  children: React.ReactNode;
  className?: string;
}

export function Timeline({ children, className = "" }: TimelineProps) {
  return (
    <div
      className={`flex flex-col gap-0 relative pl-2 overflow-y-auto custom-scrollbar ${className}`}
    >
      {/* Vertical line */}
      <div className="absolute left-3 top-0 bottom-0 w-px bg-gray-200 z-0" />
      {children}
    </div>
  );
}

export { TimelineItem } from "./timeline-item";

export default Timeline;
