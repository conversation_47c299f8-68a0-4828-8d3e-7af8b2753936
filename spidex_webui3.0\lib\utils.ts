import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import jwt from "jsonwebtoken";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const isExpired = (token: string): boolean => {
  const decode: any = jwt.decode(token);
  const timeNow = new Date();
  const tokenTime = new Date(decode.exp * 1000);
  if (tokenTime < timeNow) return true;
  return false;
};
