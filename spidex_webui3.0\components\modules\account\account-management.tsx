"use client";

import { useState, useCallback } from "react";
import { UserPlus } from "lucide-react";
import { useSession } from "next-auth/react";
import { useAccountManagement } from "@/hooks/use-account-management";
import {
  User,
  CreateAccountFormData,
  UpdateAccountFormData,
} from "@/types/account";
import { AccountDataTable } from "./account-data-table";
import { AccountSearchPagination } from "./account-search-pagination";
import { AccountForm } from "./account-form";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";
import { toast } from "sonner";

export default function AccountManagement() {
  const { data: session, status } = useSession();
  const {
    accounts: paginatedAccounts,
    roles,
    tenants,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    createAccount,
    updateAccount,
    deleteAccount,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useAccountManagement();

  // State hooks must be called before any early returns
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // All hooks must be called before any early returns
  const handleDeleteAccount = useCallback(
    async (userId: string) => {
      try {
        // Find the user to get their name for the toast message
        const user = paginatedAccounts.find((u: any) => u.id === userId);
        const userName = user?.name || "User";

        await deleteAccount(userId);
        toast.success("Account deleted successfully", {
          description: `User account for ${userName} has been deleted.`,
        });
        console.log("Account deleted successfully");
      } catch (error) {
        toast.error("Failed to delete account", {
          description:
            error instanceof Error
              ? error.message
              : "An unexpected error occurred while deleting the account.",
        });
        console.error("Failed to delete account");
        console.error("Error deleting account:", error);
      }
    },
    [paginatedAccounts, deleteAccount]
  );

  // Prevent rendering until searchFilters is properly initialized
  if (!searchFilters || typeof searchFilters.searchTerm === "undefined") {
    return (
      <div className="container mx-auto py-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-8 w-48 bg-gray-200 animate-pulse rounded" />
              <div className="h-4 w-96 bg-gray-200 animate-pulse rounded" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Ensure searchFilters is properly initialized to prevent controlled/uncontrolled input errors
  const safeSearchFilters = {
    searchTerm: searchFilters.searchTerm || "",
    tenantId: searchFilters.tenantId || "",
    roleId: searchFilters.roleId || "",
    active: searchFilters.active,
    deleted: searchFilters.deleted ?? false,
  };

  const handleCreateAccount = async (data: CreateAccountFormData) => {
    try {
      setFormLoading(true);
      await createAccount(data);
      setIsFormOpen(false);
      toast.success("Account created successfully", {
        description: `User account for ${data.name} has been created.`,
      });
      console.log("Account created successfully");
    } catch (error) {
      toast.error("Failed to create account", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while creating the account.",
      });
      console.error("Failed to create account");
      console.error("Error creating account:", error);
    } finally {
      setFormLoading(false);
    }
  };

  const handleUpdateAccount = async (data: UpdateAccountFormData) => {
    try {
      setFormLoading(true);
      await updateAccount(data);
      setIsFormOpen(false);
      setEditingUser(null);
      toast.success("Account updated successfully", {
        description: `User account for ${data.name} has been updated.`,
      });
      console.log("Account updated successfully");
    } catch (error) {
      toast.error("Failed to update account", {
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred while updating the account.",
      });
      console.error("Failed to update account");
      console.error("Error updating account:", error);
    } finally {
      setFormLoading(false);
    }
  };

  // COMMENTED OUT - Reset Password functionality
  /* const handleResetPassword = async (userId: string) => {
    try {
      await resetPassword({
        userId,
        newPassword: "spidex1234",
        confirmPassword: "spidex1234",
      });
      console.log("Password reset successfully");
    } catch (error) {
      console.error("Failed to reset password");
      console.error("Error resetting password:", error);
    }
  }; */

  // Toggle Active functionality - for activating deleted accounts
  const handleToggleActive = async (userId: string, active: boolean) => {
    const user = paginatedAccounts.find((u: any) => u.id === userId);
    if (!user) return;

    try {
      await updateAccount({
        id: user.id,
        name: user.name,
        userId: user.userId,
        emailId: user.email,
        mobileNumber: user.mobileNumber || "",
        tenantId: user.tenantId,
        roles: user.roles.map((r: any) => r.id),
        deleted: !active, // If activating (active=true), set deleted=false
      });
      toast.success(
        `Account ${active ? "activated" : "deactivated"} successfully`,
        {
          description: `User account for ${user.name} has been ${
            active ? "activated" : "deactivated"
          }.`,
        }
      );
      console.log(
        `Account ${active ? "activated" : "deactivated"} successfully`
      );
    } catch (error) {
      toast.error(`Failed to ${active ? "activate" : "deactivate"} account`, {
        description:
          error instanceof Error
            ? error.message
            : `An unexpected error occurred while ${
                active ? "activating" : "deactivating"
              } the account.`,
      });
      console.error(`Failed to ${active ? "activate" : "deactivate"} account`);
      console.error("Error toggling account status:", error);
    }
  };

  const openCreateForm = () => {
    setEditingUser(null);
    setIsFormOpen(true);
  };

  const openEditForm = (user: User) => {
    setEditingUser(user);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setEditingUser(null);
  };

  const handleFormSubmit = async (
    data: CreateAccountFormData | UpdateAccountFormData
  ) => {
    if (editingUser) {
      await handleUpdateAccount(data as UpdateAccountFormData);
    } else {
      await handleCreateAccount(data as CreateAccountFormData);
    }
  };

  // Show loading state while checking authentication
  if (status === "loading") {
    return (
      <div className="container mx-auto py-6">
        <div className="space-y-6">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-96" />
          <div className="grid gap-4 md:grid-cols-3">
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
          </div>
          <Skeleton className="h-96" />
        </div>
      </div>
    );
  }

  // Show authentication error if not authenticated
  if (status === "unauthenticated") {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertDescription>
            You must be logged in to access account management. Please log in
            and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Show API errors
  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertDescription>
            Error loading account data: {error}
            {status === "authenticated" && session?.user?.token
              ? " Please check your permissions or try refreshing the page."
              : " Please ensure you are properly authenticated."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/user-management">
                User Management
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Account Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={openCreateForm} className="gap-2 ml-auto">
          <UserPlus className="h-4 w-4" />
          Add Account
        </Button>
      </header>
      <main className="p-4">
        {/* Search and Filters */}
        <AccountSearchPagination
          searchFilters={safeSearchFilters}
          showDeleted={showDeleted}
          roles={roles}
          tenants={tenants}
          onSearchChange={updateSearchFilters}
          onToggleShowDeleted={toggleShowDeleted}
          onClearFilters={clearSearch}
        />

        {/* Data Table */}
        <AccountDataTable
          data={paginatedAccounts}
          pagination={pagination}
          totalRecords={totalRecords}
          totalPages={totalPages}
          availablePageSizes={availablePageSizes}
          onEdit={openEditForm}
          onDelete={handleDeleteAccount}
          // onResetPassword={handleResetPassword} // COMMENTED OUT
          onToggleActive={handleToggleActive}
          onPageChange={goToPage}
          onPageSizeChange={changePageSize}
        />
      </main>

      {/* Account Form Sheet */}
      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-1/4 sm:max-w-4xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle>
              {editingUser ? "Edit Account" : "Create New Account"}
            </SheetTitle>
            <SheetDescription>
              {editingUser
                ? "Update the account information and permissions."
                : "Create a new user account with appropriate roles and permissions."}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <AccountForm
              user={editingUser || undefined}
              roles={roles}
              tenants={tenants}
              isLoading={formLoading}
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
              onClear={() => console.log("Form cleared")}
            />
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
