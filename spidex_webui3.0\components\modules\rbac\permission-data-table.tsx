"use client";

import { Shield } from "lucide-react";
import {
  RolePagePermissionDisplay,
  RbacPaginationParams,
  RbacPageSize,
} from "@/types/rbac";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { RbacTablePagination } from "./rbac-table-pagination";

interface PermissionDataTableProps {
  data: RolePagePermissionDisplay[];
  pagination: RbacPaginationParams;
  totalRecords: number;
  totalPages: number;
  availablePageSizes: RbacPageSize[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: RbacPageSize) => void;
  isLoading?: boolean;
}

export function PermissionDataTable({
  data,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
}: PermissionDataTableProps) {
  const toTitleCase = (str: string): string => {
    return str
      .toLowerCase()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const formatPermissions = (permissions: string) => {
    const permissionArray = permissions.split(", ").filter((p) => p.trim());
    return permissionArray.map((permission) => (
      <Badge key={permission} variant="secondary" className="mr-1">
        {toTitleCase(permission.toUpperCase())}
      </Badge>
    ));
  };

  if (isLoading) {
    return (
      <>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[150px]">
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Role
                  </div>
                </TableHead>
                <TableHead>Page</TableHead>
                <TableHead>Permissions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[...Array(5)].map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <Skeleton className="h-4 w-[120px]" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-[150px]" />
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Skeleton className="h-6 w-[60px]" />
                      <Skeleton className="h-6 w-[50px]" />
                      <Skeleton className="h-6 w-[70px]" />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <RbacTablePagination
          pagination={pagination}
          totalRecords={0}
          currentPageRecords={0}
          totalPages={0}
          availablePageSizes={availablePageSizes}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
          entityName="permission"
        />
      </>
    );
  }

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[150px]">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Role
                </div>
              </TableHead>
              <TableHead>Page</TableHead>
              <TableHead>Permissions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="h-24 text-center">
                  No permissions found.
                </TableCell>
              </TableRow>
            ) : (
              data.map((permission) => (
                <TableRow key={permission.key}>
                  <TableCell className="font-medium">
                    {permission.roleName || permission.role}
                  </TableCell>
                  <TableCell>{permission.page}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {formatPermissions(permission.permissions)}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <RbacTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        entityName="permission"
      />
    </div>
  );
}
