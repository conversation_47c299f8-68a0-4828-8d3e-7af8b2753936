import { SensorType } from "@/types/asset-tracking";

export const SENSOR_TYPES = {
  CPU: "Cpu",
  MEMORY: "memoryUsage",
  DISK: "diskUsage",
  INBOUND: "inboundTraffic",
  OUTBOUND: "outboundTraffic",
  BATTERY: "battery",
  TEMPERATURE: "temperature",
  HUMIDITY: "humidity",
  AMBLIGHT: "amblight",
  PROXIMITY: "proximity",
  PRESSURE: "pressure",
} as const;

export const LOCATION_TYPES = {
  ENTRY: "Entry",
  EXIT: "Exit",
  FIXED: "Fixed",
} as const;

export const ASSET_TYPES = {
  VENDOR: "Vendor",
  VEHICLE: "Vehicle",
  WORKER: "Worker",
  CHAIR: "Chair",
  WASHROOM: "Sanitary",
  OTHERS: "Others",
} as const;

export const DATE_FORMATS = {
  TYPE_1: "yyyy-MM-dd=HH:mm",
  TYPE_2: "dd-MM-yy hh:mm:ss",
  TYPE_3: "yyyy-MM-dd",
  TYPE_4: "dd-MM-yyyy",
  TYPE_5: "hh:mm:ss",
  TYPE_6: "HH:mm yyyy-MM-dd",
  TYPE_7: "yyyy-MM-dd HH:mm",
  TYPE_8: "dd/MM/yy",
  TYPE_9: "HH:mm",
} as const;

export const SOCKET_EVENTS = {
  CONNECT: "connect",
  DISCONNECT: "disconnect",
  ERROR: "error",
  RECONNECT: "reconnect",
  PROXIMITY_DATA: "proximity_data",
  SENSOR_DATA: "sensor_data",
} as const;

export const API_ENDPOINTS = {
  // Device Management
  GATEWAYS: "/gateway/all",
  LOCATIONS: "/location/all",
  AREAS: "/area/all",
  ASSETS: "/asset/all",
  TAGGED_ASSETS: "/taggedAsset/all",

  // Sensor Data
  SENSOR_HEALTH: "/sensor/health/",
  SENSOR_PROXIMITY: "/sensor/proximity/",
  SENSOR_TEMP: "/sensor/temp/",
  SENSOR_HUMIDITY: "/sensor/humid/",
  SENSOR_AMBLIGHT: "/sensor/ambl/",
  SENSOR_PRESSURE: "/sensor/pressure/",

  // Reports
  RFID_REPORT: "/report/rfid/",
} as const;

export const SOCKET_TOPICS = {
  PROXIMITY: (gatewayId: string) => `/data/proximity/${gatewayId}`,
  GPS: (gatewayId: string) => `/data/gps/${gatewayId}`,
  SENSOR: (sensorType: string, deviceId: string) =>
    `/data/${sensorType}/${deviceId}`,
} as const;

export const SENSOR_UNITS = {
  [SensorType.TEMPERATURE]: "°C",
  [SensorType.HUMIDITY]: "%",
  [SensorType.PRESSURE]: "Pa",
  [SensorType.AMBLIGHT]: "lux",
  [SensorType.BATTERY]: "%",
  [SensorType.CPU]: "%",
  [SensorType.MEMORY]: "%",
  [SensorType.DISK]: "%",
  [SensorType.INBOUND]: "Mbps",
  [SensorType.OUTBOUND]: "Mbps",
} as const;

export const SENSOR_COLORS = {
  [SensorType.TEMPERATURE]: "#ff4d4f",
  [SensorType.HUMIDITY]: "#1890ff",
  [SensorType.PRESSURE]: "#52c41a",
  [SensorType.AMBLIGHT]: "#faad14",
  [SensorType.BATTERY]: "#722ed1",
  [SensorType.CPU]: "#f5222d",
  [SensorType.MEMORY]: "#fa541c",
  [SensorType.DISK]: "#13c2c2",
  [SensorType.INBOUND]: "#2f54eb",
  [SensorType.OUTBOUND]: "#eb2f96",
} as const;

export const CHART_CONFIG = {
  ANIMATION_DURATION: 300,
  MAX_DATA_POINTS: 50,
  REFRESH_INTERVAL: 5000, // 5 seconds
  CHART_HEIGHT: 300,
  CHART_MARGIN: {
    top: 20,
    right: 30,
    left: 20,
    bottom: 5,
  },
} as const;

export const WEBSOCKET_CONFIG = {
  RECONNECT_INTERVAL: 3000,
  MAX_RECONNECT_ATTEMPTS: 5,
  HEARTBEAT_INTERVAL: 30000,
  CONNECTION_TIMEOUT: 10000,
} as const;

export const ASSET_STATUS = {
  ONLINE: "online",
  OFFLINE: "offline",
  LOW_BATTERY: "low_battery",
  UNKNOWN: "unknown",
} as const;

export const BATTERY_THRESHOLDS = {
  LOW: 20,
  CRITICAL: 10,
} as const;

export const RSSI_THRESHOLDS = {
  EXCELLENT: -30,
  GOOD: -50,
  FAIR: -70,
  POOR: -80,
} as const;

export const MAP_CONFIG = {
  DEFAULT_ZOOM: 15,
  MIN_ZOOM: 10,
  MAX_ZOOM: 20,
  DEFAULT_CENTER: {
    latitude: 17.435784,
    longitude: 78.461947, // Hyderabad coordinates (updated to match GPS data)
  },
  MARKER_SIZE: {
    SMALL: 20,
    MEDIUM: 30,
    LARGE: 40,
  },
} as const;

export const MAPBOX_STYLE =
  "mapbox://styles/ganache-lives/cmcp9taxq00kc01sdf21l91tc";

export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const;

export const SEARCH_CONFIG = {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 2,
} as const;

export const NOTIFICATION_TYPES = {
  SUCCESS: "success",
  ERROR: "error",
  WARNING: "warning",
  INFO: "info",
} as const;

export const ASSET_TRACKING_ROUTES = {
  DASHBOARD: "/asset-tracking",
  ANALYTICS: "/asset-tracking/analytics",
  MAP_VIEW: "/asset-tracking/map",
  REPORTS: "/asset-tracking/reports",
} as const;

export const SENSOR_GROUPS = {
  HEALTH: [
    SensorType.CPU,
    SensorType.MEMORY,
    SensorType.DISK,
    SensorType.INBOUND,
    SensorType.OUTBOUND,
    SensorType.BATTERY,
  ],
  ENVIRONMENTAL: [
    SensorType.TEMPERATURE,
    SensorType.HUMIDITY,
    SensorType.AMBLIGHT,
    SensorType.PRESSURE,
  ],
  TRACKING: [SensorType.PROXIMITY],
} as const;

export const DEFAULT_SENSOR_DATA = {
  value: 0,
  loading: true,
  selected: false,
  lineChartData: [],
} as const;

export const ERROR_MESSAGES = {
  WEBSOCKET_CONNECTION_FAILED: "Failed to connect to real-time data stream",
  API_REQUEST_FAILED: "Failed to fetch data from server",
  INVALID_SENSOR_DATA: "Invalid sensor data received",
  GATEWAY_NOT_FOUND: "Gateway not found",
  ASSET_NOT_FOUND: "Asset not found",
  LOCATION_NOT_FOUND: "Location not found",
} as const;
