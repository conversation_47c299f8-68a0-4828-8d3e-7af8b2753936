# RBAC Implementation Guide

## Overview

This document outlines the complete Role-Based Access Control (RBAC) implementation for Spidex WebUI 3.0. The implementation separates the old combined RBAC interface into three distinct management pages: Roles, Permissions, and Pages.

## Architecture

### 1. Types and Interfaces (`types/rbac.ts`)
- **Role**: User role definitions with enable/disable status
- **Page**: System page definitions with descriptions
- **RolePageLink**: Links between roles and pages with specific permissions
- **PermissionMatrix**: Interactive permission assignment matrix
- **CRUD Permissions**: Create, Read, Update, Delete operations

### 2. API Services (`lib/api/spidex-api.ts`)
Extended SpidexApiService with RBAC methods:
- `getAllRolesForRbac()` - Fetch all roles
- `createRole()` - Create new role
- `deleteRole()` - Delete role
- `getAllPages()` - Fetch all pages
- `createPage()` - Create new page
- `deletePage()` - Delete page
- `getAllPagesRolesLinks()` - Fetch role-page links
- `linkPageRole()` - Create role-page permission link
- `deletePageRoleLink()` - Delete permission link
- `getPagesRolesLinksByRole()` - Get permissions for specific role

### 3. Custom Hooks
- `useRoleManagement()` - Role CRUD operations with search/pagination
- `usePageManagement()` - Page CRUD operations with search/pagination
- `usePermissionManagement()` - Permission matrix management

### 4. Components Structure
```
components/modules/rbac/
├── role-management.tsx          # Main role management page
├── role-data-table.tsx         # Role data table with pagination
├── role-search-pagination.tsx  # Role search and filters
├── role-form.tsx              # Role creation/editing form
├── permission-management.tsx   # Main permission management page
├── permission-data-table.tsx  # Permission data table
├── permission-search-pagination.tsx # Permission search and filters
├── page-management.tsx        # Main page management page
├── page-data-table.tsx       # Page data table
├── page-search-pagination.tsx # Page search and filters
└── page-form.tsx             # Page creation/editing form
```

### 5. Routes
```
app/(main-layout)/user-management/
├── page.tsx                   # Main user management dashboard
├── roles/page.tsx            # Role management page
├── permissions/page.tsx      # Permission management page
└── pages/page.tsx           # Page management page
```

## Features

### Role Management (`/user-management/roles`)
- **Create Roles**: Add new roles with permission matrix
- **View Roles**: Paginated table with search and filters
- **Edit Roles**: Modify role information and permissions
- **Delete Roles**: Soft delete roles
- **Permission Matrix**: Interactive CRUD permission assignment per page
- **Search & Filter**: By role name, status (active/inactive)
- **Show Deleted**: Toggle to view deleted roles

### Permission Management (`/user-management/permissions`)
- **View Permissions**: Table showing all role-page permission assignments
- **Quick Editor**: Select role and edit permissions in modal
- **Permission Matrix**: Visual CRUD permission toggles
- **Search & Filter**: By role, page, or specific permission type
- **Bulk Operations**: Select all/clear all permissions per page

### Page Management (`/user-management/pages`)
- **Create Pages**: Add new system pages
- **View Pages**: Paginated table with search
- **Edit Pages**: Modify page information
- **Delete Pages**: Soft delete pages
- **Search & Filter**: By page name
- **Show Deleted**: Toggle to view deleted pages

## Testing Guide

### 1. Role Management Testing

#### Create Role Test
1. Navigate to `/user-management/roles`
2. Click "Add Role" button
3. Fill in role name (e.g., "Test Manager")
4. Select permissions for various pages using CRUD buttons
5. Click "Create Role"
6. Verify role appears in table
7. Check that permissions are correctly assigned

#### Edit Role Test
1. Click edit button on existing role
2. Modify role name and permissions
3. Save changes
4. Verify updates are reflected in table and permission matrix

#### Delete Role Test
1. Click delete button on role
2. Confirm deletion in dialog
3. Verify role is marked as deleted
4. Toggle "Show Deleted" to see deleted roles

#### Search and Filter Test
1. Use role name search
2. Filter by status (Active/Inactive)
3. Test pagination controls
4. Change page size options

### 2. Permission Management Testing

#### View Permissions Test
1. Navigate to `/user-management/permissions`
2. Verify all role-page permissions are displayed
3. Check permission badges show correct CRUD operations

#### Quick Editor Test
1. Select a role from dropdown
2. Verify permission matrix loads with current permissions
3. Toggle various permissions on/off
4. Save changes
5. Verify changes are reflected in permissions table

#### Search and Filter Test
1. Filter by specific role
2. Filter by specific page
3. Filter by permission type (add, view, update, delete)
4. Test pagination

### 3. Page Management Testing

#### Create Page Test
1. Navigate to `/user-management/pages`
2. Click "Add Page" button
3. Fill in page name and description
4. Click "Create Page"
5. Verify page appears in table

#### Edit Page Test
1. Click edit button on existing page
2. Modify page information
3. Save changes
4. Verify updates in table

#### Delete Page Test
1. Click delete button on page
2. Confirm deletion
3. Verify page is marked as deleted

### 4. Integration Testing

#### End-to-End Workflow Test
1. Create a new page in Page Management
2. Create a new role in Role Management
3. Assign permissions to the new role for the new page
4. Verify the assignment appears in Permission Management
5. Edit permissions through Permission Management
6. Verify changes are reflected across all views

#### Data Persistence Test
1. Create roles, pages, and permissions
2. Refresh browser
3. Navigate between different RBAC pages
4. Verify all data persists correctly

#### Error Handling Test
1. Test with invalid data (empty role names, etc.)
2. Test network error scenarios
3. Verify appropriate error messages are displayed
4. Test form validation

## API Integration

### Authentication
All RBAC operations require valid authentication token from session.

### Error Handling
- Network errors are caught and displayed to user
- Form validation prevents invalid submissions
- Loading states provide user feedback

### Data Flow
1. Components use custom hooks for data management
2. Hooks call SpidexApiService methods
3. API service handles authentication and error responses
4. Data is cached and managed in hook state
5. UI updates reactively based on state changes

## Security Considerations

1. **Authentication Required**: All RBAC operations require valid session
2. **Input Validation**: Forms validate data before submission
3. **Soft Deletes**: Records are marked as deleted, not permanently removed
4. **Audit Trail**: Created/modified timestamps and user tracking
5. **Permission Checks**: Future implementation should check user permissions before allowing RBAC operations

## Future Enhancements

1. **Bulk Operations**: Select multiple roles/pages for bulk actions
2. **Import/Export**: CSV import/export for roles and permissions
3. **Role Templates**: Predefined role templates for common use cases
4. **Permission Inheritance**: Hierarchical role inheritance
5. **Audit Logs**: Detailed logging of all RBAC changes
6. **Real-time Updates**: WebSocket updates for multi-user scenarios
