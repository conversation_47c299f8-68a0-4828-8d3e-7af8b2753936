"use client";

import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { CheckCircle, XCircle } from "lucide-react";
import { Gateway, GatewayPageSize } from "@/types/gateway";
import { DataTable } from "@/components/ui/data-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";

import { cn } from "@/lib/utils";
import { GatewayTablePagination } from "../gateway/gateway-table-pagination";

interface OnboardGatewayDataTableProps {
  data: Gateway[];
  isLoading?: boolean;
  onProvision: (gateway: Gateway, provisioned: boolean) => void;

  // Pagination props
  pagination: { page: number; pageSize: number };
  totalRecords: number;
  totalPages: number;
  availablePageSizes: GatewayPageSize[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: GatewayPageSize) => void;
}

export function OnboardGatewayDataTable({
  data,
  isLoading = false,
  onProvision,
  pagination,
  totalRecords,
  totalPages,
  availablePageSizes,
  onPageChange,
  onPageSizeChange,
}: OnboardGatewayDataTableProps) {
  const columns: ColumnDef<Gateway>[] = [
    {
      accessorKey: "name",
      header: "Gateway Name",
      cell: ({ row }) => {
        const name = row.getValue("name") as string;
        return <div className="font-medium">{name || "N/A"}</div>;
      },
    },
    {
      accessorKey: "externalId",
      header: "External ID (MAC)",
      cell: ({ row }) => {
        const externalId = row.getValue("externalId") as string;
        return (
          <div className="text-sm text-muted-foreground font-mono">
            {externalId || "N/A"}
          </div>
        );
      },
    },
    {
      accessorKey: "gatewayType",
      header: "Gateway Type",
      cell: ({ row }) => {
        const gatewayType = row.getValue("gatewayType") as string;
        return (
          <Badge variant="outline" className="capitalize">
            {gatewayType || "N/A"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "categoryType",
      header: "Category Type",
      cell: ({ row }) => {
        const categoryType = row.getValue("categoryType") as string;
        return (
          <Badge variant="secondary" className="capitalize">
            {categoryType || "N/A"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "areaName",
      header: "Area",
      cell: ({ row }) => {
        const areaName = row.getValue("areaName") as string;
        return <div className="text-sm">{areaName || "N/A"}</div>;
      },
    },
    {
      accessorKey: "locationName",
      header: "Location",
      cell: ({ row }) => {
        const locationName = row.getValue("locationName") as string;
        return <div className="text-sm">{locationName || "N/A"}</div>;
      },
    },
    {
      accessorKey: "deleted",
      header: "Deleted",
      cell: ({ row }) => {
        const deleted = row.getValue("deleted") as boolean;

        return (
          <Badge variant={"outline"}>
            {deleted ? (
              <>
                <CheckCircle className="mr-1 h-3 w-3" />
                TRUE
              </>
            ) : (
              <>
                <XCircle className="mr-1 h-3 w-3" />
                FALSE
              </>
            )}
          </Badge>
        );
      },
    },
    {
      accessorKey: "provisioned",
      header: "Provisioned",
      cell: ({ row }) => {
        const gateway = row.original;
        const provisioned = gateway.provisioned || false;

        return (
          <div className="flex items-center space-x-2">
            <Switch
              checked={provisioned}
              onCheckedChange={(checked) => onProvision(gateway, checked)}
              disabled={isLoading || gateway.deleted}
            />
            <span className="text-sm text-muted-foreground">
              {provisioned ? "Yes" : "No"}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "createdBy",
      header: "Created By",
      cell: ({ row }) => {
        const createdBy = row.getValue("createdBy") as string;
        return (
          <div className="text-sm text-muted-foreground">
            {createdBy || "N/A"}
          </div>
        );
      },
    },
    {
      accessorKey: "createdTime",
      header: "Created Date",
      cell: ({ row }) => {
        const createdTime = row.getValue("createdTime") as number;
        if (!createdTime)
          return <div className="text-sm text-muted-foreground">N/A</div>;

        try {
          const date = new Date(createdTime);
          return (
            <div className="text-sm text-muted-foreground">
              {date.toLocaleDateString()}
            </div>
          );
        } catch {
          return (
            <div className="text-sm text-muted-foreground">Invalid Date</div>
          );
        }
      },
    },
  ];

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="rounded-md border">
        <DataTable
          columns={columns}
          data={data}
          isLoading={isLoading}
          rowClassName={() =>
            cn(
              "[&_tr[data-state=selected]]:bg-muted/50",
              "[&_tr:hover]:bg-muted/50"
            )
          }
        />
      </div>

      {/* Pagination */}
      <GatewayTablePagination
        pagination={pagination}
        totalRecords={totalRecords}
        currentPageRecords={data.length}
        totalPages={totalPages}
        availablePageSizes={availablePageSizes}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />
    </div>
  );
}
