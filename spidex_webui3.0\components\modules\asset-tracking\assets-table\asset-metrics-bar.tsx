import React from "react";
import { Power, PowerOff, AlertTriangle, Box } from "lucide-react";
import MetricCard from "./metric-card";
import AreaBadge from "./area-badge";

export default function AssetMetricsBar({
  data,
  areas,
  onMetricFilter,
  onAreaFilter,
  activeMetric,
  activeArea,
}: {
  data: any[];
  areas: { id: string; name: string }[];
  onMetricFilter?: (type: string) => void;
  onAreaFilter?: (areaId: string) => void;
  activeMetric?: string;
  activeArea?: string;
}) {
  const total = data.length;
  const online = data.filter(
    (a) => String(a.statusOnline).toLowerCase() === "online"
  ).length;
  const offline = total - online;
  const now = Math.floor(Date.now() / 1000);
  const alertThreshold = 60 * 30;
  const alerts = data.filter(
    (a) => a.lastSeen && now - a.lastSeen > alertThreshold
  ).length;
  const areaCounts = areas.map((area) => ({
    id: area.id,
    name: area.name,
    count: data.filter((a) => a.areaId === area.id).length,
  }));
  return (
    <section aria-label="Asset Metrics" className="mb-6">
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
        <MetricCard
          icon={<Power className="w-6 h-6" />}
          label="Online"
          value={online}
          color="green"
          onClick={() => onMetricFilter && onMetricFilter("online")}
          tooltip="Assets currently online"
          active={activeMetric === "online"}
        />
        <MetricCard
          icon={<PowerOff className="w-6 h-6" />}
          label="Offline"
          value={offline}
          color="red"
          onClick={() => onMetricFilter && onMetricFilter("offline")}
          tooltip="Assets currently offline"
          active={activeMetric === "offline"}
        />
        <MetricCard
          icon={<AlertTriangle className="w-6 h-6" />}
          label="Alerts"
          value={alerts}
          color="yellow"
          onClick={() => onMetricFilter && onMetricFilter("alerts")}
          tooltip="Assets not seen in over 30 minutes"
          active={activeMetric === "alerts"}
        />
        <MetricCard
          icon={<Box className="w-6 h-6" />}
          label="Total"
          value={total}
          color="blue"
          tooltip="Total number of tracked assets"
          active={activeMetric === "total"}
        />
      </div>
      <div
        className="flex flex-wrap gap-2 justify-center mt-4"
        aria-label="Area Breakdown"
      >
        {areaCounts.map((area) => (
          <AreaBadge
            key={area.id}
            label={area.name}
            value={area.count}
            onClick={() => onAreaFilter && onAreaFilter(area.id)}
            active={activeArea === area.id}
          />
        ))}
      </div>
    </section>
  );
}
