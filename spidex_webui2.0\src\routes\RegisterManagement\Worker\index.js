import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Select,
  Popconfirm,
  Input,
  Checkbox,
  Radio,
  Upload,
  CommonDrawer,
  CommonCompactView,
  message,
  Row,
  Col,
  Pagination,
} from '../../../components';
import {
  updateWorker,
  deleteWorker,
  getAllWorkersByPagination,
  addWorker,
  getAllVendors,
  getSearchWorkers,
} from '../../../services';
import Context from '../../../context';
import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { buildCommonApiValues, csvFile, normFile } from '../../../utils';
import { BreadcrumbList, PermissionContainer } from '../../../shared';
import { get } from 'lodash';
import { country, CRUD, Pages, ModuleNames, MastersPageSizeDefault } from '../../../constants';

const { Search } = Input;
const { Option } = Select;

const Worker = () => {
  const [context, setContext] = useContext(Context);
  const [totalWorkers, setTotalWorkers] = useState({ items: 0, current: 0, pageSize: MastersPageSizeDefault });
  const [workers, setWorkers] = useState([]);
  const [workersOriginal, setWorkersOriginal] = useState([]);
  const [worker, setWorker] = useState({});
  const [visible, setVisible] = useState(false);
  const [workerInfo, setWorkerInfo] = useState(false);
  const [action, setAction] = useState('new');
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [vendorData, setVendorData] = useState([]);

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setWorkers(tableData) : setWorkers(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const formInitState = { attributes: [{ type: null, value: null, name: null }] };

  const [formInitValues, setFormInitValues] = useState({ ...formInitState });

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const closeAdd = () => {
    setVisible(false);
    setWorkerInfo(false);
    if (action === 'edit') form.resetFields();
  };

  const dummyCustomRequest = (e) => {
    e.onSuccess(null, e.file);
  };

  const [tableModeNormal, setTableModeNormal] = useState(true);

  const onWorkerSearch = async (e) => {
    if (e) {
      try {
        const workers = await getSearchWorkers(context.profile.tenantId, e);
        setWorkers(workers.data.filter((x) => x.deleted === false));
        setTableModeNormal(false);
      } catch {
        message.error('Could not retrieve workers');
      }
    } else {
      setTableModeNormal(true);
      setWorkers(workersOriginal);
    }
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();

    const commonValues = buildCommonApiValues(context.profile);
    const vendorRes = vendorData.find((e) => e.id === values.vendorId);
    if (action === 'new') {
      await saveWorkerAction({
        registrationTime: commonValues.createdTime,
        lastModified: commonValues.modifiedTime,
        modifiedBy: commonValues.modifiedBy,
        createdBy: commonValues.createdBy,
        tenantId: commonValues.tenantId,
        deleted: commonValues.deleted,
        vendorName: vendorRes.vendorName,
        ...values,
      });
      if (type === 'save') setVisible(false);
      if (type === 'add') {
        form.resetFields();
        setFormInitValues({ ...formInitState, vendorId: vendorRes.id });
      }
    }
    if (action === 'edit') {
      updateWorkerAction(values);
      if (type === 'add') form.resetFields({});
      setFormInitValues({ ...formInitState });
      setVisible(false);
    }
  };

  const saveWorkerAction = async (worker) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    const newWorker = { ...worker, workerIdImage: worker.workerIdImageUpload };
    delete newWorker.workerIdImageUpload;
    addWorker(newWorker)
      .then((res) => {
        setWorkers((state) => [res.data, ...state]);
        message.success('Succesfully Added Worker');
        form.resetFields();
        setFormInitValues(null);
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Worker, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editWorkerAction = (worker) => {
    form.setFieldsValue({ ...worker });
    setAction('edit');
    setWorker(worker);
    setVisible(true);
  };

  const updateWorkerCall = (values) => {
    const data = { ...worker, ...values, modifiedTime: new Date() };

    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateWorker(data)
      .then((res) => {
        setWorkers((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, res.data);
            }
          });
          return tempData;
        });
        form.resetFields();
        setFormInitValues(null);
        message.success('Succesfully Updated Worker');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update worker, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateWorkerAction = (values) => {
    const newWorker = { ...values, workerIdImage: worker.workerIdImageUpload };
    delete newWorker.workerIdImageUpload;
    updateWorkerCall(newWorker);
  };

  const makeActive = (data) => {
    updateWorkerCall({ ...data, deleted: false });
  };

  const setDeleteWorkerAction = (userId, visible) => {
    setWorkers((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === userId).visible = visible;
      tempData
        .filter((x) => x.id !== userId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteWorkerAction = (userId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteWorker(userId)
      .then(() => {
        setWorkers((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === userId).visible = false;
          tempState.find((x) => x.id === userId).deleted = true;
          return [...state].filter((x) => x.id !== userId);
        });
        message.success('Succesfully Deleted Worker');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Worker, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const clearForm = () => {
    form.resetFields();
  };

  const onPaginationChange = (e) => {
    init(+e - 1);
  };

  const init = async (page = 0) => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: true,
        };
      });
      const pagination = {
        size: totalWorkers.pageSize,
        page: page,
      };

      try {
        const workerRes = await getAllWorkersByPagination(context.profile.tenantId, pagination);
        const vendorRes = await getAllVendors(context.profile.tenantId);
        const workers = (workerRes.data?.content || []).filter((x) => x.deleted === false);
        setTableData(workerRes.data.content);
        setVendorData(vendorRes.data.filter((x) => x.deleted === false));
        setWorkers(workers);
        setWorkersOriginal(workers);
        setTotalWorkers((ps) => ({ ...ps, current: page, items: workerRes.data.totalElements }));
      } catch (e) {
        console.log(e);
        message.error('Unable to get Worker details, try again later');
      } finally {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      }
    }
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, []);

  const viewWorkerInfo = (e) => {
    setWorker(e);
    setWorkerInfo(true);
  };

  const csvFileDownload = () => {
    const exportData = tableData.map(({ workerName, vendorName, skillCategory, skillType }) => {
      return { workerName, vendorName, skillCategory, skillType };
    });
    csvFile({ data: exportData, fileName: `workers_${new Date().toDateString()}`.replaceAll(' ', '_').toLowerCase() });
  };

  const [gender, setGender] = useState('male');
  const onChangeGender = (e) => {
    setGender(e.target.value);
  };

  const [skillTypess, setSkillTypess] = useState([]);
  const onChangeSkill = (e) => {
    if (e === 'skilled')
      setSkillTypess([
        'Carpenter',
        'Welder',
        'Fitter',
        'Grinder',
        'Rigger',
        'Electrician',
        'Trigger',
        'GasCutter',
        'Masson',
        'Other',
      ]);
    else if (e === 'unSkilled') setSkillTypess(['Helper', 'Security', 'Cook']);
    else if (e === 'semiSkilled') setSkillTypess(['Supervisor', 'Foreman']);
    else if (e === 'others') setSkillTypess(false);
  };

  const idTypes = ['Aadhaar', 'PAN', 'Driving License', 'Voter ID', 'Others'];

  const tableCols = [
    { title: <strong> Name </strong>, key: 'workerName', dataIndex: 'workerName' },
    { title: <strong> Vendor </strong>, dataIndex: 'vendorName' },
    { title: <strong> Skill Category </strong>, key: 'skillCategory', dataIndex: 'skillCategory', width: 160 },
    { title: <strong> Skill Type </strong>, key: 'skillType', dataIndex: 'skillType', width: 160 },
    {
      title: <strong> Actions </strong>,
      width: 300,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <Col>
              <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                Activate
              </Button>
            </Col>
            <Col>
              <p className="lastModifiedDate">
                Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
              </p>
            </Col>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.WORKER} permission={CRUD.VIEW}>
              <Button type="link" onClick={() => viewWorkerInfo(record)} className="actionButton">
                View
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.WORKER} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editWorkerAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>
            <PermissionContainer page={Pages.WORKER} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete user ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteWorkerAction(record.id)}
                onCancel={() => setDeleteWorkerAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteWorkerAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const workerBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.WORKER}].displayName`,
    ModuleNames.WORKER
  );

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.MASTERS, workerBreadcrumbsName, 'Register Worker']} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.WORKER} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Worker
            </Button>
          </PermissionContainer>
          <PermissionContainer page={Pages.WORKER} permission={CRUD.VIEW}>
            <Button size="small" onClick={csvFileDownload} type="primary" className="downloadButton">
              Export <DownloadOutlined />
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <Row className="searchDiv" justify="end">
        <Col xs={24} lg={6}>
          <Search placeholder="Enter Search" allowClear enterButton onSearch={onWorkerSearch} />
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <>
            {tableModeNormal ? (
              <>
                <Table
                  size="small"
                  scroll={{ x: true }}
                  rowKey="id"
                  columns={tableCols}
                  dataSource={workers}
                  rowClassName={(record) => record.deleted && 'rowInactive'}
                  pagination={false}
                />
                <Row justify="end">
                  <Col>
                    <div className="m-2">
                      <Pagination
                        onChange={onPaginationChange}
                        current={totalWorkers.current + 1}
                        total={totalWorkers.items}
                        defaultPageSize={totalWorkers.pageSize}
                        showSizeChanger={false}
                        showQuickJumper={false}
                      />
                    </div>
                  </Col>
                </Row>
              </>
            ) : (
              <Table
                size="small"
                scroll={{ x: true }}
                rowKey="id"
                columns={tableCols}
                dataSource={workers}
                rowClassName={(record) => record.deleted && 'rowInactive'}
              />
            )}
          </>
        ) : (
          <>
            <CommonCompactView
              data={workers}
              onEdit={editWorkerAction}
              onDelete={deleteWorkerAction}
              permissions={[
                { pageName: Pages.WORKER, permission: CRUD.UPDATE, label: 'Edit' },
                { pageName: Pages.WORKER, permission: CRUD.DELETE, label: 'Delete' },
              ]}
              title="workerName"
              dataList={[
                { label: 'Vendor', value: 'vendorName' },
                { label: 'Skill Level', value: 'skillLevel' },
              ]}
            />
            <Row justify="end">
              <Col>
                <div className="m-2">
                  <Pagination
                    onChange={onPaginationChange}
                    current={totalWorkers.current + 1}
                    total={totalWorkers.items}
                    defaultPageSize={totalWorkers.pageSize}
                    showSizeChanger={false}
                    showQuickJumper={false}
                  />
                </div>
              </Col>
            </Row>
          </>
        )}
      </>

      <CommonDrawer title="Worker Information" visible={workerInfo} closeAdd={closeAdd}>
        <div className="infoDrawer">
          {worker ? (
            <Row className="infoContainer" span={24}>
              <Col className="info" span={24}>
                <Row className="infoImg" justify="center" span={24}>
                  <Col>
                    <img
                      src={`data:image/jpg;base64,${worker.idProofImage}`}
                      onError={({ currentTarget }) => {
                        currentTarget.onerror = null; // prevents looping
                        currentTarget.src = '/assets/images/notFound.png';
                      }}
                      alt="Not Found"
                    />
                  </Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Name :
                  </Col>
                  <Col>{worker.workerName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Worker ID :
                  </Col>
                  <Col>{worker.workerId}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Vendor :
                  </Col>
                  <Col>{worker.vendorName}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Gender :
                  </Col>
                  <Col>{worker.gender}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Skill Category :
                  </Col>
                  <Col>{worker.skillCategory}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Skill Type :
                  </Col>
                  <Col>{worker.skillType}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Package :
                  </Col>
                  <Col>{worker.resPackage}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Nationality :
                  </Col>
                  <Col>{worker.nationality}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Age :
                  </Col>
                  <Col>{worker.age}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Skill Level :
                  </Col>
                  <Col>{worker.skillLevel}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Tag ID :
                  </Col>
                  <Col>{worker.tagId}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Address 1 :
                  </Col>
                  <Col>{worker.workerAddress?.address1}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Address 2 :
                  </Col>
                  <Col>{worker.workerAddress?.address2}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    City :
                  </Col>
                  <Col>{worker.workerAddress?.city}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    State :
                  </Col>
                  <Col>{worker.workerAddress?.state}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Country :
                  </Col>
                  <Col>{worker.workerAddress?.country}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Pincode :
                  </Col>
                  <Col>{worker.workerAddress?.pincode}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Phone Number :
                  </Col>
                  <Col>{worker.workerAddress?.phonenumber}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Properties 1 :
                  </Col>
                  <Col>{worker.properties?.additionalProp1}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Properties 2 :
                  </Col>
                  <Col>{worker.properties?.additionalProp2}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Properties 3 :
                  </Col>
                  <Col>{worker.properties?.additionalProp3}</Col>
                </Row>
                <Row>
                  <Col className="infoTitle" span={10}>
                    Mail ID :
                  </Col>
                  <Col>{worker.mailId}</Col>
                </Row>
                {worker.attributes && (
                  <Row>
                    <Col className="infoTitle" span={10}>
                      Attribute :
                    </Col>
                    <Col>
                      {
                        <div>
                          <Row gutter={20} justify="space-between">
                            <Col> Name </Col>
                            <Col> Value </Col>
                            <Col> Type </Col>
                          </Row>
                          {worker.attributes?.map((i) => (
                            <Row gutter={20} key={i.name} justify="space-between">
                              <Col>{i.name}</Col>
                              <Col>{i.value}</Col>
                              <Col>{i.type}</Col>
                            </Row>
                          ))}
                        </div>
                      }
                    </Col>
                  </Row>
                )}

                <br />
                <Row justify="center">
                  <Button type="primary" onClick={() => editWorkerAction(worker)}>
                    Edit
                  </Button>
                </Row>
              </Col>
            </Row>
          ) : (
            <></>
          )}
        </div>
      </CommonDrawer>

      <CommonDrawer title="Worker" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={formInitValues}
          scrollToFirstError={true}
          autoComplete={'new-password'}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 16 }}
          labelCol={{ span: 8 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Name"
            name="workerName"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Worker Name!',
                min: 3,
                max: 50,
              },
              { pattern: `^[a-zA-Z ]+$`, message: 'Use Letters only!' },
            ]}
          >
            <Input placeholder="Full Name" />
          </Form.Item>

          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Worker ID"
            name="workerId"
            rules={[
              {
                required: false,
                whitespace: true,
                message: 'Please input Worker ID!',
                min: 12,
                max: 12,
              },
            ]}
          >
            <Input placeholder="Worker ID" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Vendor"
            name="vendorId"
            rules={[
              {
                required: true,
                message: 'Please select Vendor!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Vendor"
            >
              {vendorData.map((b) => (
                <Option title={b.vendorName} key={b.id} value={b.id}>
                  {b.vendorName}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Gender"
            name="gender"
            rules={[
              {
                required: true,
                message: 'Please Select Gender!',
              },
            ]}
          >
            <Radio.Group onChange={onChangeGender} value={gender}>
              <Radio value={'male'}>Male</Radio>
              <Radio value={'female'}>Female</Radio>
              <Radio value={'others'}>Others</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Skill Category"
            name="skillCategory"
            rules={[
              {
                required: true,
                message: 'Please Select Skill Category!',
              },
            ]}
          >
            <Select
              showSearch
              onChange={onChangeSkill}
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Skills"
            >
              <Option value="skilled">Skilled</Option>
              <Option value="unSkilled">Unskilled</Option>
              <Option value="semiSkilled">Semi-Skilled</Option>
              <Option value="others">Others</Option>
            </Select>
          </Form.Item>

          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Skill Type"
            name="skillType"
            rules={[
              {
                required: true,
                message: 'Please Select Skill Type!',
              },
            ]}
          >
            {!skillTypess ? (
              <Input placeholder="Skill Type" />
            ) : (
              <Select
                showSearch
                filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                placeholder="Skills"
              >
                {skillTypess.map((skill, index) => {
                  return (
                    <Option key={index} value={skill}>
                      {skill}
                    </Option>
                  );
                })}
              </Select>
            )}
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Package"
            name="resPackage"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Package in INR!',
                min: 3,
                max: 5,
              },
              { pattern: `^[0-9]+$`, message: 'Use Numbers only!' },
            ]}
          >
            <Input placeholder="Package in INR" />
          </Form.Item>

          <Form.Item
            hasFeedback
            initialValue="India"
            label="Nationality"
            name="nationality"
            rules={[
              {
                required: true,
                message: 'Please Select Resident Country!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Country"
            >
              {Object.keys(country).map((b) => (
                <Option title={country[b]} key={country[b]} value={country[b]}>
                  {country[b]}
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* WIP */}
          {/*
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Age"
            name="age"
            rules={[
              {
                required: true,
                message: 'Please input worker Age!',
              },
            ]}
          >
            <InputNumber min={18} max={120} />
          </Form.Item>
                    <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Skill Level"
            name="skillLevel"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Skill Level!',
              },
            ]}
          >
            <Input placeholder="Skill Level" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Tag ID"
            name={'tagId'}
            rules={[
              {
                whitespace: true,
                message: 'Please input Tag ID!',
              },
            ]}
          >
            <Input placeholder="Tag ID" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Address Line 1"
            name={['workerAddress', 'address1']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Address!',
              },
            ]}
          >
            <Input placeholder="Address Line 1" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Address Line 2"
            name={['workerAddress', 'address2']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Address!',
              },
            ]}
          >
            <Input placeholder="Address Line 2" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Address Phone"
            name={['workerAddress', 'phonenumber']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Contact Person Number!',
              },
              { pattern: InputRegex.Mobile, message: 'Enter a valid Mobile Number' },
            ]}
          >
            <Input placeholder="Phone Number" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="City"
            name={['workerAddress', 'city']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input City!',
              },
              { pattern: `^[A-Za-z]+$`, message: 'Use Letters only!' },
            ]}
          >
            <Input placeholder="City" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="State"
            name={['workerAddress', 'state']}
            rules={[
              {
                required: true,
                message: 'Please input State!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="State"
            >
              {Object.keys(state).map((b) => (
                <Option title={state[b]} key={state[b]} value={state[b]}>
                  {state[b]}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            initialValue="India"
            label="Country"
            name={['workerAddress', 'country']}
            rules={[
              {
                required: true,
                message: 'Please Select Country!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Country"
            >
              {Object.keys(country).map((b) => (
                <Option title={country[b]} key={country[b]} value={country[b]}>
                  {country[b]}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            hasFeedback
            label="Pincode"
            name={['workerAddress', 'pincode']}
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input Pincode!',
                min: 3,
                max: 200,
              },
              { pattern: `^[0-9]+$`, message: 'Use Numbers only!' },
            ]}
          >
            <Input placeholder="Pincode" />
          </Form.Item> */}

          <Form.Item
            hasFeedback
            label="ID Proof"
            name="idProof"
            rules={[
              {
                message: 'Please input ID Proof!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="ID Type"
            >
              {idTypes.map((id, index) => (
                <Option title={id} key={index} value={id}>
                  {id}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item valuePropName="file" name="idProofImage" label="ID Upload" getValueFromEvent={normFile}>
            <Upload maxCount={1} customRequest={dummyCustomRequest} name="idProofImage">
              <Button icon={<UploadOutlined />}>Upload</Button>
            </Upload>
          </Form.Item>

          <Form.Item valuePropName="file" name="photoImage" label="Photo Upload" getValueFromEvent={normFile}>
            <Upload maxCount={1} customRequest={dummyCustomRequest} name="photoImage">
              <Button icon={<UploadOutlined />}>Upload</Button>
            </Upload>
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </>
  );
};

export default Worker;
