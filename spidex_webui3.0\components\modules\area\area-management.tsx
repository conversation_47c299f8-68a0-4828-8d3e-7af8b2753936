"use client";

import { useState } from "react";
import { Plus } from "lucide-react";
import { useAreaManagement } from "@/hooks/use-area-management";
import { getTableConfig } from "@/config/table-config";
import {
  Area,
  CreateAreaFormData,
  UpdateAreaFormData,
  AreaPageSize,
} from "@/types/area";
import { AreaDataTable } from "./area-data-table";
import { AreaSearchPagination } from "./area-search-pagination";
import { AreaForm } from "./area-form";
import { BindGatewayModal } from "./bind-gateway-modal";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";
import { toast } from "sonner";

interface AreaManagementProps {
  locationId?: string;
}

export default function AreaManagement({ locationId }: AreaManagementProps) {
  // Get table configuration for this page
  const tableConfig = getTableConfig("area-management");

  const {
    areas: paginatedAreas,
    branches,
    locations,
    gateways,
    isLoading,
    error,
    showDeleted,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    totalAllRecords,
    activeRecordsCount,
    inactiveRecordsCount,
    loadData,
    createArea,
    updateArea,
    deleteArea,
    bindGateway,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
    toggleShowDeleted,
    availablePageSizes,
  } = useAreaManagement({
    initialPageSize: tableConfig.defaultPageSize,
    availablePageSizes: tableConfig.availablePageSizes as AreaPageSize[],
    locationId,
  });

  // Form state
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingArea, setEditingArea] = useState<Area | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // Bind Gateway Modal state
  const [isBindGatewayModalOpen, setIsBindGatewayModalOpen] = useState(false);
  const [bindingArea, setBindingArea] = useState<Area | null>(null);

  // Handle create area
  const handleCreateArea = async (data: CreateAreaFormData) => {
    try {
      setFormLoading(true);
      await createArea(data);
      toast.success("Area created successfully", {
        description: `${data.name} has been added to the system.`,
      });
      setIsFormOpen(false);
    } catch (error) {
      console.error("Error creating area:", error);
      toast.error("Failed to create area", {
        description: "Please check your input and try again.",
      });
    } finally {
      setFormLoading(false);
    }
  };

  // Handle update area
  const handleUpdateArea = async (data: UpdateAreaFormData) => {
    try {
      setFormLoading(true);
      await updateArea(data);
      toast.success("Area updated successfully", {
        description: `${data.name} has been updated.`,
      });
      setIsFormOpen(false);
      setEditingArea(null);
    } catch (error) {
      console.error("Error updating area:", error);
      toast.error("Failed to update area", {
        description: "Please check your input and try again.",
      });
    } finally {
      setFormLoading(false);
    }
  };

  // Handle delete area
  const handleDeleteArea = async (areaId: string) => {
    try {
      await deleteArea(areaId);
      toast.success("Area deleted successfully", {
        description: "The area has been removed from the system.",
      });
    } catch (error) {
      console.error("Error deleting area:", error);
      toast.error("Failed to delete area", {
        description: "Please try again later.",
      });
    }
  };

  // Toggle Active functionality - for activating deleted areas
  const handleToggleActive = async (areaId: string, active: boolean) => {
    const area = paginatedAreas.find((a: any) => a.id === areaId);
    if (!area) return;

    try {
      await updateArea({
        id: area.id,
        name: area.name,
        address: area.address,
        branchId: area.branchId,
        locationId: area.locationId,
        gpsPoint: area.gpsPoint,
        geoJson: area.geoJson || "",
        level: area.level || "",
        min: area.min || "",
        max: area.max || "",
      });
      toast.success(
        `Area ${active ? "activated" : "deactivated"} successfully`,
        {
          description: `${area.name} has been ${
            active ? "activated" : "deactivated"
          }.`,
        }
      );
    } catch (error) {
      console.error("Error toggling area status:", error);
      toast.error(`Failed to ${active ? "activate" : "deactivate"} area`, {
        description: "Please try again later.",
      });
    }
  };

  // Handle bind gateway action
  const handleBindGateway = async (area: Area) => {
    setBindingArea(area);
    setIsBindGatewayModalOpen(true);
  };

  // Handle gateway binding from modal
  const handleGatewayBind = async (gatewayId: string) => {
    if (!bindingArea) return;

    try {
      await bindGateway(gatewayId, bindingArea.id);
      toast.success("Gateway bound successfully", {
        description: `Gateway has been bound to area "${bindingArea.name}".`,
      });
      setIsBindGatewayModalOpen(false);
      setBindingArea(null);
    } catch (error) {
      console.error("Error binding gateway:", error);
      toast.error("Failed to bind gateway", {
        description: "Please try again later.",
      });
    }
  };

  // Close bind gateway modal
  const closeBindGatewayModal = () => {
    setIsBindGatewayModalOpen(false);
    setBindingArea(null);
  };

  const openCreateForm = () => {
    setEditingArea(null);
    setIsFormOpen(true);
  };

  const openEditForm = (area: Area) => {
    console.log("✏️ Opening edit form for area:", area);
    setEditingArea(area);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setEditingArea(null);
  };

  const handleFormSubmit = async (
    data: CreateAreaFormData | UpdateAreaFormData
  ) => {
    if (editingArea) {
      await handleUpdateArea(data as UpdateAreaFormData);
    } else {
      await handleCreateArea(data as CreateAreaFormData);
    }
  };

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/digital-carpet">
                Digital Carpet
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Area Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button onClick={openCreateForm} className="gap-2 ml-auto">
          <Plus className="h-4 w-4" />
          Add Area
        </Button>
      </header>
      <main>
        {/* Search and Filter */}
        <div className="p-4">
          <AreaSearchPagination
            searchFilters={searchFilters}
            showDeleted={showDeleted}
            totalRecords={totalRecords}
            branches={branches}
            onSearchChange={updateSearchFilters}
            onClearSearch={clearSearch}
            onToggleShowDeleted={toggleShowDeleted}
          />
        </div>

        {/* Data Table */}
        <div className="p-4 pt-0">
          <AreaDataTable
            data={paginatedAreas}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onEdit={openEditForm}
            onDelete={handleDeleteArea}
            onToggleActive={(area) =>
              handleToggleActive(area.id, !area.deleted)
            }
            onBindGateway={handleBindGateway}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
          />
        </div>
      </main>

      {/* Area Form Sheet */}
      <Sheet open={isFormOpen} onOpenChange={setIsFormOpen}>
        <SheetContent className="w-1/4 sm:max-w-4xl overflow-y-auto">
          <SheetHeader>
            <SheetTitle>
              {editingArea ? "Edit Area" : "Create New Area"}
            </SheetTitle>
            <SheetDescription>
              {editingArea
                ? "Update the area information and location details."
                : "Create a new area with appropriate branch, location, and GPS coordinates."}
            </SheetDescription>
          </SheetHeader>
          <div className="mt-6">
            <AreaForm
              key={editingArea?.id || "new"}
              area={editingArea || undefined}
              branches={branches}
              locations={locations}
              isLoading={formLoading}
              onSubmit={handleFormSubmit}
              onCancel={closeForm}
              onClear={() => console.log("Form cleared")}
            />
          </div>
        </SheetContent>
      </Sheet>

      {/* Bind Gateway Modal */}
      <BindGatewayModal
        isOpen={isBindGatewayModalOpen}
        area={bindingArea}
        gateways={gateways}
        isLoading={isLoading}
        onClose={closeBindGatewayModal}
        onBindGateway={handleGatewayBind}
      />
    </>
  );
}
