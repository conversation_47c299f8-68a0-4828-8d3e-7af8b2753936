"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { Tag, TagModel } from "@/types/tag";
import {
  CreateTagSchema,
  UpdateTagSchema,
  CreateTagFormData,
  UpdateTagFormData,
} from "@/lib/schemas";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Tag as TagIcon, Loader2, Cpu } from "lucide-react";

interface TagFormProps {
  tag?: Tag;
  tagModels: TagModel[];
  isLoading?: boolean;
  onSubmit: (data: CreateTagFormData | UpdateTagFormData) => Promise<void>;
  onCancel: () => void;
  onClear?: () => void;
}

export function TagForm({
  tag,
  tagModels,
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: TagFormProps) {
  const isEditing = !!tag;
  const schema = isEditing ? UpdateTagSchema : CreateTagSchema;

  const form = useForm<CreateTagFormData | UpdateTagFormData>({
    resolver: zodResolver(schema),
    defaultValues: isEditing
      ? {
          id: tag.id,
          name: tag.name,
          description: tag.description,
          modelId: tag.modelId?.toString() || "", // Ensure modelId is string for Select component
          externalId: tag.externalId || "",
          status: tag.status,
        }
      : {
          name: "",
          description: "",
          modelId: "",
          externalId: "",
          status: true,
        },
  });

  // Update form values when tag prop changes to prevent controlled/uncontrolled input errors
  useEffect(() => {
    if (isEditing && tag) {
      form.reset({
        id: tag.id,
        name: tag.name,
        description: tag.description,
        modelId: tag.modelId?.toString() || "", // Ensure modelId is string for Select component
        externalId: tag.externalId || "",
        status: tag.status,
      });
    } else if (!isEditing) {
      form.reset({
        name: "",
        description: "",
        modelId: "",
        externalId: "",
        status: true,
      });
    }
  }, [tag, isEditing, form]);

  const handleFormSubmit = async (
    data: CreateTagFormData | UpdateTagFormData
  ) => {
    try {
      await onSubmit(data);
      if (!isEditing) {
        form.reset();
      }
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  const handleClear = () => {
    form.reset();
    if (onClear) {
      onClear();
    }
  };

  const selectedModel = tagModels.find(
    (model) => model.modelId === form.watch("modelId")
  );

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleFormSubmit)}
        className="space-y-6"
      >
        <div className="flex flex-col gap-6">
          {/* Basic Information */}
          <Card>
            
            <CardContent className="space-y-4 pt-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tag Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="(1-200 characters)"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="(1-200 characters)"
                        {...field}
                        disabled={isLoading}
                        rows={3}
                      />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="externalId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>External ID</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="(1-100 characters)"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
              {/* Model Configuration */}
                <div>
                  <FormField
                control={form.control}
                name="modelId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tag Model</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger ref={field.ref}>
                          <SelectValue placeholder="Select the device model" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {tagModels.length === 0 ? (
                          <SelectItem value="no-models" disabled>
                            No tag models available
                          </SelectItem>
                        ) : (
                          tagModels
                            .filter(
                              (model) =>
                                model.modelName !== null &&
                                model.modelName !== undefined
                            )
                            .map((model) => (
                              <SelectItem
                                key={model.modelId}
                                value={model.modelId}
                              >
                                <div className="flex items-center gap-2">
                                  <span>{model.modelName}</span>
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {model.modelId}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))
                        )}
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedModel && (
                <div className="p-3 bg-muted rounded-lg">
                  <h4 className="font-medium text-sm mb-2">Model Details</h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p>
                      <span className="font-medium">Model ID:</span>{" "}
                      {selectedModel.modelId}
                    </p>
                    <p>
                      <span className="font-medium">Model Name:</span>{" "}
                      {selectedModel.modelName}
                    </p>
                    <p>
                      <span className="font-medium">Device Type:</span>{" "}
                      {selectedModel.deviceType}
                    </p>
                  </div>
                </div>
              )}

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-4 pt-1">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Active Status</FormLabel>
                      <FormDescription>
                        Enable this tag for tracking and monitoring
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
                </div>
            </CardContent>
          </Card>

          
          
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-between pt-6 border-t">
          <div className="flex gap-2">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 sm:flex-none bg-green-700 text-white hover:bg-green-800 rounded"
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? "Update Tag" : "Create Tag"}
            </Button>

            {!isEditing && (
              <Button
                type="button"
                variant="outline"
                onClick={handleClear}
                disabled={isLoading}
                className="border bg-white border-gray-700 text-black hover:bg-green-20 rounded"
              >
                Clear
              </Button>
            )}
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="border bg-white border-red-700 text-black hover:bg-green-20 rounded"
            >
              Close
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
