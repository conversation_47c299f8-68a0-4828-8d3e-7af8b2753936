// RBAC Types and Interfaces for Spidex WebUI 3.0

/**
 * CRUD Permission types
 */
export type CrudPermission = "add" | "view" | "update" | "delete";

/**
 * Role interface
 */
export interface Role {
  id: string;
  name: string;
  enable: boolean;
  deleted: boolean;
  createdTime: string;
  modifiedTime: string;
  createdBy: string;
  modifiedBy: string;
}

/**
 * Page interface
 */
export interface Page {
  id: string;
  pageName: string;
  deleted: boolean;
  description?: string;
  createdTime?: string;
  modifiedTime?: string;
  createdBy?: string;
  modifiedBy?: string;
}

/**
 * Page permission for a specific page
 */
export interface PagePermission {
  pageName: string;
  permissions: CrudPermission[];
}

/**
 * Role-Page link interface
 */
export interface RolePageLink {
  id: string;
  roleId: string;
  roleName?: string;
  pagePermissions: PagePermission[];
  createdTime?: string;
  modifiedTime?: string;
  createdBy?: string;
  modifiedBy?: string;
}

/**
 * Role-Page permission display interface for tables
 */
export interface RolePagePermissionDisplay {
  key: string;
  role: string; // Role ID for filtering
  roleName?: string; // Role name for display
  page: string;
  permissions: string;
}

/**
 * Permission matrix state for role creation/editing
 */
export interface PermissionMatrix {
  [pageId: string]: {
    [key in CrudPermission]: boolean;
  };
}

/**
 * Form data for creating a new role
 */
export interface CreateRoleFormData {
  name: string;
  pagePermissions: PermissionMatrix;
}

/**
 * Form data for updating an existing role
 */
export interface UpdateRoleFormData {
  id: string;
  name: string;
  enable: boolean;
  pagePermissions: PermissionMatrix;
}

/**
 * Form data for creating a new page
 */
export interface CreatePageFormData {
  pageName: string;
  description?: string;
}

/**
 * Form data for updating an existing page
 */
export interface UpdatePageFormData {
  id: string;
  pageName: string;
  description?: string;
}

/**
 * Search filters for roles
 */
export interface RoleSearchFilters {
  name?: string;
  enable?: boolean;
  showDeleted?: boolean;
}

/**
 * Search filters for pages
 */
export interface PageSearchFilters {
  pageName?: string;
  showDeleted?: boolean;
}

/**
 * Search filters for permissions
 */
export interface PermissionSearchFilters {
  role?: string;
  page?: string;
  permission?: CrudPermission;
}

/**
 * Pagination parameters for RBAC entities
 */
export interface RbacPaginationParams {
  page: number;
  pageSize: number;
}

/**
 * Page size options for RBAC tables
 */
export type RbacPageSize = 10 | 50 | 100 | "all";

export const RBAC_PAGE_SIZES: RbacPageSize[] = [10, 50, 100, "all"];
export const DEFAULT_RBAC_PAGE_SIZE: RbacPageSize = 10;

/**
 * Constants for CRUD permissions
 */
export const CRUD_PERMISSIONS: Record<string, CrudPermission> = {
  ADD: "add",
  VIEW: "view",
  UPDATE: "update",
  DELETE: "delete",
} as const;

/**
 * Permission labels for UI display
 */
export const PERMISSION_LABELS: Record<CrudPermission, string> = {
  add: "Create",
  view: "Read",
  update: "Update",
  delete: "Delete",
} as const;

/**
 * Permission abbreviations for compact display
 */
export const PERMISSION_ABBREVIATIONS: Record<CrudPermission, string> = {
  add: "C",
  view: "R",
  update: "U",
  delete: "D",
} as const;
