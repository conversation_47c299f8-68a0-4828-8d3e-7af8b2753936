"use client";

import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Gateway, GATEWAY_TYPES, CATEGORY_TYPES } from "@/types/gateway";
import { Area, Location } from "@/types/asset-tracking";
import {
  CreateGatewaySchema,
  UpdateGatewaySchema,
  CreateGatewayFormData,
  UpdateGatewayFormData,
} from "@/lib/schemas/gateway";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Plus, Trash2 } from "lucide-react";
import { GoogleMapModal } from "@/components/ui/google-map-modal";
import { MapIcon } from "@/components/ui/map-icon";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

interface GatewayFormProps {
  gateway?: Gateway;
  areas: Area[];
  locations: Location[];
  models: any[];
  allGateways: Gateway[];
  isLoading?: boolean;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
}

export function GatewayForm({
  gateway,
  areas,
  locations,
  models,
  allGateways,
  isLoading = false,
  onSubmit,
  onCancel,
}: GatewayFormProps) {
  const isEditing = !!gateway;
  const schema = isEditing ? UpdateGatewaySchema : CreateGatewaySchema;
  const [isMapOpen, setIsMapOpen] = useState(false);
  const [selectedGatewayType, setSelectedGatewayType] = useState<string>("");

  const form = useForm<CreateGatewayFormData | UpdateGatewayFormData>({
    resolver: zodResolver(schema),
    mode: "onSubmit", // Only validate on submit, not on change
    defaultValues: {
      name: "",
      description: "",
      categoryType: "fixed" as const,
      modelId: "",
      externalId: "",
      areaId: "",
      locId: "",
      xyzCoordinates: {
        additionalProp1: "",
        additionalProp2: "",
        additionalProp3: "",
      },
      gatewayLint: [],
      coverageMin: 1, // Default to 1 like old app
      coverageMax: 1, // Default to 1 like old app
      communicationType: "BLE", // Default to BLE like old app
      antenna: 0, // Default antenna value
      antennaConfigs: [], // Default empty antenna configurations
    },
  });

  // Watch category type and communication type to show/hide conditional fields
  const watchedCategoryType = form.watch("categoryType");
  const watchedCommunicationType = form.watch("communicationType");

  useEffect(() => {
    setSelectedGatewayType(watchedCategoryType);
  }, [watchedCategoryType]);

  // Field array for gateway lint (for lint type gateways)
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "gatewayLint",
  });

  // Field array for RFID antenna configurations
  const {
    fields: antennaFields,
    append: appendAntenna,
    remove: removeAntenna,
  } = useFieldArray({
    control: form.control,
    name: "antennaConfigs",
  });

  // Reset form when gateway data changes (for editing)
  useEffect(() => {
    if (gateway) {
      const formData = {
        id: gateway.id,
        name: gateway.name,
        description: gateway.description || "",
        categoryType: gateway.categoryType as "fixed" | "transit" | "lint",
        modelId: gateway.modelId?.toString() || "",
        externalId: gateway.externalId || "",
        areaId: gateway.areaId || "",
        locId: gateway.locId || "",
        xyzCoordinates: {
          additionalProp1: gateway.xyzCoordinates?.additionalProp1 || "",
          additionalProp2: gateway.xyzCoordinates?.additionalProp2 || "",
          additionalProp3: gateway.xyzCoordinates?.additionalProp3 || "",
        },
        gatewayLint: gateway.gatewayLint || [],
        coverageMin: gateway.coverageMin || 1,
        coverageMax: gateway.coverageMax || 1,
        communicationType: gateway.communicationType || "BLE",
        antenna: gateway.antenna || 0,
        antennaConfigs: gateway.antennaConfigs || [],
      };

      // Reset form with a small delay to ensure proper state update
      setTimeout(() => {
        form.reset(formData);
        // Also update the selected gateway type for conditional rendering
        setSelectedGatewayType(gateway.categoryType);
      }, 0);
    } else {
      const emptyFormData = {
        name: "",
        description: "",
        categoryType: "fixed" as const,
        modelId: "",
        externalId: "",
        areaId: "",
        locId: "",
        xyzCoordinates: {
          additionalProp1: "",
          additionalProp2: "",
          additionalProp3: "",
        },
        gatewayLint: [],
        coverageMin: 1, // Default to 1 like old app
        coverageMax: 1, // Default to 1 like old app
        communicationType: "BLE", // Default to BLE like old app
        antenna: 0, // Default antenna value
        antennaConfigs: [], // Default empty antenna configurations
      };
      form.reset(emptyFormData);
      setSelectedGatewayType("fixed");
    }
  }, [gateway, form]);

  const handleSubmit = async (
    data: CreateGatewayFormData | UpdateGatewayFormData
  ) => {
    try {
      // Trigger validation manually to ensure all fields are validated
      const isValid = await form.trigger();

      if (!isValid) {
        return;
      }

      // Transform data to match API format like old application
      const transformedData = {
        ...data,
        // Ensure communicationType is set (default to BLE like old app)
        communicationType: data.communicationType || "BLE",
        // Ensure coverageMin/Max are set with defaults
        coverageMin: data.coverageMin || 1,
        coverageMax: data.coverageMax || 1,
      } as CreateGatewayFormData | UpdateGatewayFormData;

      await onSubmit(transformedData);

      // Reset form after successful submission for new gateways only
      if (!isEditing) {
        form.reset();
        setSelectedGatewayType("fixed");
      }
    } catch (error) {
      // Don't reset form on error - let user modify and retry
      console.error("Form submission error:", error);
      // Error handling is done in the parent component
    }
  };

  const handleMapLocationSelect = (locationData: {
    latitude: number;
    longitude: number;
  }) => {
    form.setValue(
      "xyzCoordinates.additionalProp1",
      locationData.latitude.toString()
    );
    form.setValue(
      "xyzCoordinates.additionalProp2",
      locationData.longitude.toString()
    );
    setIsMapOpen(false);
  };

  const addLintConfiguration = () => {
    append({
      areaId: "",
      areaName: "",
      externalId: "",
      modelId: "",
      antenna: 0,
    });
  };

  // Filter areas based on selected location for fixed/lint gateways
  const filteredAreas = areas.filter((area) => {
    const selectedLocationId = form.watch("locId");
    return !selectedLocationId || area.locationId === selectedLocationId;
  });

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Category Type */}
          <Card className="space-y-4 mb-6">
            <CardContent className="py-6 space-y-4">
              <FormField
                control={form.control}
                name="categoryType" 
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Gateway Category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="fixed">FIXED</SelectItem>
                        <SelectItem value="transit">TRANSIT</SelectItem>
                        <SelectItem value="lint">LINT</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Gateway Name"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Gateway Description"
                        {...field}
                        disabled={isLoading}
                        rows={2}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Dynamic fields based on category type */}
              {selectedGatewayType && (
                <>
                  {/* Model ID */}
                  <FormField
                    control={form.control}
                    name="modelId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Model ID</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || undefined}
                          disabled={isLoading}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Gateway Model ID" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {models.length === 0 ? (
                              <div className="px-2 py-1.5 text-sm text-muted-foreground">
                                No models available
                              </div>
                            ) : (
                              models.map((model) => (
                                <SelectItem key={model.modelId} value={model.modelId}>
                                  {model.modelName}
                                </SelectItem>
                              ))
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* MAC ID - Only show when not RFID Fixed Reader */}

                  <FormField
                    control={form.control}
                    name="externalId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>MAC ID</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Gateway MAC ID"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Coverage Min */}
                  <FormField
                    control={form.control}
                    name="coverageMin"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Coverage Min</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Coverage Min"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Coverage Max */}
                  <FormField
                    control={form.control}
                    name="coverageMax"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Coverage Max</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Coverage Max"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Communication Type */}
                  <FormField
                    control={form.control}
                    name="communicationType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Communication Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || "BLE"} // Default to BLE like old app
                          disabled={isLoading}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Communication Type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="BLE">BLE</SelectItem>
                            <SelectItem value="RFID Fixed Reader">
                              RFID Fixed Reader
                            </SelectItem>
                            <SelectItem value="RFID-HandHeld">
                              RFID-HandHeld
                            </SelectItem>
                            <SelectItem value="ModBus">ModBus</SelectItem>
                            <SelectItem value="CPDC">CPDC</SelectItem>
                            <SelectItem value="Others">Others</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {selectedGatewayType === GATEWAY_TYPES.LINT && (
                    <>
                      {/* Parent Gateway */}
                      <FormField
                        control={form.control}
                        name={"parentId" as any}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Parent Gateway</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value || undefined}
                              disabled={isLoading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Gateway Parent" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {allGateways.filter(
                                  (g) => !g.deleted && g.categoryType !== "lint"
                                ).length === 0 ? (
                                  <div className="px-2 py-1.5 text-sm text-muted-foreground">
                                    No parent gateways available
                                  </div>
                                ) : (
                                  allGateways
                                    .filter(
                                      (g) => !g.deleted && g.categoryType !== "lint"
                                    )
                                    .map((parentGateway) => (
                                      <SelectItem
                                        key={parentGateway.id}
                                        value={parentGateway.id}
                                      >
                                        {parentGateway.name} (
                                        {parentGateway.categoryType})
                                      </SelectItem>
                                    ))
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {/* RFID Fixed Reader specific fields */}
                  {watchedCommunicationType === "RFID Fixed Reader" &&
                    selectedGatewayType === GATEWAY_TYPES.LINT && (
                      <>
                        {/* Antenna Configurations */}
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Antenna</Label>
                          </div>

                          {antennaFields.length === 0 && (
                            <div className="text-center py-4 text-muted-foreground">
                              No antenna configurations added yet. Click "Add
                              Attribute" to add one.
                            </div>
                          )}

                          {antennaFields.map((field, index) => (
                            <div
                              key={field.id}
                              className="border border-dashed rounded-lg p-4 space-y-4"
                            >
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  Antenna {index + 1}
                                </h4>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeAntenna(index)}
                                  disabled={isLoading}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>

                              <div className="grid gap-4 md:grid-cols-2">
                                {/* Numeric Value */}
                                <FormField
                                  control={form.control}
                                  name={`antennaConfigs.${index}.numericValue` as any}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Numeric Value</FormLabel>
                                      <FormControl>
                                        <Input
                                          type="number"
                                          placeholder="Numeric Value"
                                          value={field.value || ""}
                                          onChange={(e) =>
                                            field.onChange(Number(e.target.value))
                                          }
                                          disabled={isLoading}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />

                                {/* Name */}
                                <FormField
                                  control={form.control}
                                  name={`antennaConfigs.${index}.name` as any}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Name</FormLabel>
                                      <FormControl>
                                        <Input
                                          placeholder="Name"
                                          value={field.value || ""}
                                          onChange={field.onChange}
                                          disabled={isLoading}
                                        />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            </div>
                          ))}

                          {/* Add Attribute Button */}
                          <div className="flex justify-start">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                appendAntenna({
                                  numericValue: 0,
                                  name: "",
                                });
                              }}
                              disabled={isLoading}
                              className="flex items-center gap-2"
                            >
                              <Plus className="h-4 w-4" />
                              Add Attribute
                            </Button>
                          </div>
                        </div>
                      </>
                    )}
                </>
              )}

              {/* LINT specific fields */}
              {selectedGatewayType === GATEWAY_TYPES.LINT && (
                <>
                  {/* Gateway Lint List - Only show for BLE communication type */}
                  {watchedCommunicationType === "BLE" && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Gateway Lint</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={addLintConfiguration}
                          disabled={isLoading}
                          className="flex items-center gap-2"
                        >
                          <Plus className="h-4 w-4" />
                          Add Gateway Lint
                        </Button>
                      </div>

                      {fields.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                          No lint configurations added yet. Click "Add Gateway Lint"
                          to add one.
                        </div>
                      )}

                      {fields.map((field, index) => (
                        <div
                          key={field.id}
                          className="border border-dashed rounded-lg p-4 space-y-4"
                        >
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium">
                              Lint Configuration {index + 1}
                            </h4>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => remove(index)}
                              disabled={isLoading}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="grid gap-4 md:grid-cols-2">
                            {/* Coverage Min */}
                            <FormField
                              control={form.control}
                              name={`gatewayLint.${index}.coverageMin` as any}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Coverage Min</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="Coverage Min"
                                      value={field.value || ""}
                                      onChange={(e) =>
                                        field.onChange(Number(e.target.value))
                                      }
                                      disabled={isLoading}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Coverage Max */}
                            <FormField
                              control={form.control}
                              name={`gatewayLint.${index}.coverageMax` as any}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Coverage Max</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="Coverage Max"
                                      value={field.value || ""}
                                      onChange={(e) =>
                                        field.onChange(Number(e.target.value))
                                      }
                                      disabled={isLoading}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* Radio buttons for Antenna/MAC ID */}
                          <FormField
                            control={form.control}
                            name={`gatewayLint.${index}.radioSelection` as any}
                            render={({ field }) => (
                              <FormItem>
                                <RadioGroup
                                  onValueChange={field.onChange}
                                  value={field.value}
                                  className="flex flex-row space-x-6"
                                >
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                      value="antenna"
                                      id={`antenna-${index}`}
                                    />
                                    <Label htmlFor={`antenna-${index}`}>
                                      Antenna
                                    </Label>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                      value="macId"
                                      id={`macId-${index}`}
                                    />
                                    <Label htmlFor={`macId-${index}`}>MAC ID</Label>
                                  </div>
                                </RadioGroup>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Conditional Antenna/MAC ID field */}
                          {form.watch(
                            `gatewayLint.${index}.radioSelection` as any
                          ) === "antenna" && (
                              <FormField
                                control={form.control}
                                name={`gatewayLint.${index}.antenna` as any}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Antenna</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        placeholder="Antenna"
                                        value={field.value || ""}
                                        onChange={(e) =>
                                          field.onChange(Number(e.target.value))
                                        }
                                        disabled={isLoading}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            )}

                          {form.watch(
                            `gatewayLint.${index}.radioSelection` as any
                          ) === "macId" && (
                              <FormField
                                control={form.control}
                                name={`gatewayLint.${index}.externalId` as any}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>MAC ID</FormLabel>
                                    <FormControl>
                                      <Input
                                        placeholder="Gateway MAC ID"
                                        value={field.value || ""}
                                        onChange={field.onChange}
                                        disabled={isLoading}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            )}

                          {/* Radio buttons for Gateway Model/Tag Model */}
                          <FormField
                            control={form.control}
                            name={`gatewayLint.${index}.modelType` as any}
                            render={({ field }) => (
                              <FormItem>
                                <RadioGroup
                                  onValueChange={field.onChange}
                                  value={field.value}
                                  className="flex flex-row space-x-6"
                                >
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                      value="gatewayModel"
                                      id={`gatewayModel-${index}`}
                                    />
                                    <Label htmlFor={`gatewayModel-${index}`}>
                                      Gateway Model
                                    </Label>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <RadioGroupItem
                                      value="tagModel"
                                      id={`tagModel-${index}`}
                                    />
                                    <Label htmlFor={`tagModel-${index}`}>
                                      Tag Model
                                    </Label>
                                  </div>
                                </RadioGroup>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* Conditional Model field */}
                          {form.watch(`gatewayLint.${index}.modelType` as any) && (
                            <FormField
                              control={form.control}
                              name={`gatewayLint.${index}.modelId` as any}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>
                                    {form.watch(
                                      `gatewayLint.${index}.modelType` as any
                                    ) === "gatewayModel"
                                      ? "Gateway Model"
                                      : "Tag Model"}
                                  </FormLabel>
                                  <Select
                                    onValueChange={field.onChange}
                                    value={field.value || undefined}
                                    disabled={isLoading}
                                  >
                                    <FormControl>
                                      <SelectTrigger>
                                        <SelectValue
                                          placeholder={
                                            form.watch(
                                              `gatewayLint.${index}.modelType` as any
                                            ) === "gatewayModel"
                                              ? "Gateway Model"
                                              : "Tag Model"
                                          }
                                        />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      {models.map((model) => (
                                        <SelectItem
                                          key={model.modelId}
                                          value={model.modelId}
                                        >
                                          {model.modelName}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          )}

                          {/* Bind To Area */}
                          <FormField
                            control={form.control}
                            name={`gatewayLint.${index}.areaId` as any}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Bind To</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value || undefined}
                                  disabled={isLoading}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Area" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {areas.length === 0 ? (
                                      <div className="px-2 py-1.5 text-sm text-muted-foreground">
                                        No areas available
                                      </div>
                                    ) : (
                                      areas.map((area) => (
                                        <SelectItem key={area.id} value={area.id}>
                                          {area.name}
                                        </SelectItem>
                                      ))
                                    )}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {/* XYZ Coordinates */}
                          <div className="space-y-2 pt-6">
                            <Label className="text-sm font-medium">
                              XYZ Coordinates:
                            </Label>
                            <div className="grid gap-4 md:grid-cols-3">
                              <FormField
                                control={form.control}
                                name={
                                  `gatewayLint.${index}.xyzCoordinates.additionalProp1` as any
                                }
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        placeholder="Property 1"
                                        value={field.value || ""}
                                        onChange={field.onChange}
                                        disabled={isLoading}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={
                                  `gatewayLint.${index}.xyzCoordinates.additionalProp2` as any
                                }
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        placeholder="Property 2"
                                        value={field.value || ""}
                                        onChange={field.onChange}
                                        disabled={isLoading}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={
                                  `gatewayLint.${index}.xyzCoordinates.additionalProp3` as any
                                }
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Input
                                        placeholder="Property 3"
                                        value={field.value || ""}
                                        onChange={field.onChange}
                                        disabled={isLoading}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Location Type for LINT */}
                  <FormField
                    control={form.control}
                    name={"locationType" as any}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || undefined}
                          disabled={isLoading}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Location Type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="entry">Entry</SelectItem>
                            <SelectItem value="exit">Exit</SelectItem>
                            <SelectItem value="fixed">Fixed</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {/* Location/Area binding for TRANSIT and FIXED/LINT */}
              {selectedGatewayType === GATEWAY_TYPES.TRANSIT && (
                <FormField
                  control={form.control}
                  name="locId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value || undefined}
                        disabled={isLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Gateway Location" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {locations.length === 0 ? (
                            <div className="px-2 py-1.5 text-sm text-muted-foreground">
                              No locations available
                            </div>
                          ) : (
                            locations.map((location) => (
                              <SelectItem key={location.id} value={location.id}>
                                {location.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {(selectedGatewayType === GATEWAY_TYPES.FIXED ||
                selectedGatewayType === GATEWAY_TYPES.LINT) && (
                  <>
                    {/* Area Selection for Fixed/Lint Gateways */}
                    <FormField
                      control={form.control}
                      name="areaId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bind To Area</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value || undefined}
                            disabled={isLoading}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Area" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {filteredAreas.length === 0 ? (
                                <div className="px-2 py-1.5 text-sm text-muted-foreground">
                                  No areas available
                                </div>
                              ) : (
                                filteredAreas.map((area) => (
                                  <SelectItem key={area.id} value={area.id}>
                                    {area.name}
                                  </SelectItem>
                                ))
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </>
                )}

              {/* XYZ Coordinates for all gateway types */}
              {selectedGatewayType && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">XYZ Coordinates:</Label>
                  <div className="grid gap-4 md:grid-cols-3">
                    <FormField
                      control={form.control}
                      name="xyzCoordinates.additionalProp1"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Property 1"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="xyzCoordinates.additionalProp2"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Property 2"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="xyzCoordinates.additionalProp3"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              placeholder="Property 3"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          {/* Form Actions */}

          <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t ">
            <div
              className={`grid gap-2 ${isEditing ? "grid-cols-1" : "grid-cols-2"
                }`}
            >
              <Button type="submit" className="w-full bg-green-700 text-white hover:bg-green-800 rounded" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save
              </Button>
              {!isEditing && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    form.reset();
                    setSelectedGatewayType("fixed");
                  }}
                  disabled={isLoading}
                  className="w-full border bg-white border-gray-700 text-black hover:bg-green-20 rounded"
                >
                  Clear
                </Button>
              )}
            </div>
            <div className="grid grid-cols-1 gap-2">
              <Button
                type="button"
                variant="destructive"
                onClick={onCancel}
                disabled={isLoading}
                className="flex-1 sm:flex-none border bg-white border-red-700 text-black hover:bg-green-20 rounded"
              >
                Close
              </Button>
            </div>
          </div>
        </form>

        {/* Google Map Modal */}
        <GoogleMapModal
          isOpen={isMapOpen}
          onClose={() => setIsMapOpen(false)}
          onLocationSelect={handleMapLocationSelect}
          initialLocation={
            form.watch("xyzCoordinates.additionalProp1") &&
              form.watch("xyzCoordinates.additionalProp2")
              ? {
                latitude: parseFloat(
                  form.watch("xyzCoordinates.additionalProp1") || "0"
                ),
                longitude: parseFloat(
                  form.watch("xyzCoordinates.additionalProp2") || "0"
                ),
              }
              : undefined
          }
        />
      </Form>
    </div>
  );
}
