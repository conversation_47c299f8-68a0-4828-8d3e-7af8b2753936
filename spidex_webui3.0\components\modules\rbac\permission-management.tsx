"use client";

import { Shield } from "lucide-react";
import { useSession } from "next-auth/react";
import { usePermissionManagement } from "@/hooks/use-permission-management";

import { PermissionDataTable } from "./permission-data-table";
import { PermissionSearchPagination } from "./permission-search-pagination";
import { But<PERSON> } from "@/components/ui/button";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";

export default function PermissionManagement() {
  const { data: session, status } = useSession();
  const {
    roles,
    pages,
    permissions: paginatedPermissions,
    error,
    searchFilters,
    pagination,
    totalPages,
    totalRecords,
    availablePageSizes,
    goToPage,
    changePageSize,
    updateSearchFilters,
    clearSearch,
  } = usePermissionManagement();

  const availablePages = pages.filter((page) => !page.deleted);
  const activeRoles = roles.filter((role) => !role.deleted);

  // Get unique pages for dropdown (remove duplicates by pageName)
  const uniquePages = availablePages.filter(
    (page, index, self) =>
      index === self.findIndex((p) => p.pageName === page.pageName)
  );

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  if (!session) {
    return <div>Please log in to access permission management.</div>;
  }

  return (
    <>
      {/* Header bar similar to event attribute page */}
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-card">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink href="/user-management">
                User Management
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              <BreadcrumbPage>Permission Management</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <main className="p-4">
        {/* Search and Pagination Controls */}
        <PermissionSearchPagination
          searchFilters={searchFilters}
          totalRecords={totalRecords}
          roles={activeRoles}
          pages={uniquePages}
          onUpdateSearchFilters={updateSearchFilters}
          onClearSearch={clearSearch}
        />

        {/* Data Table */}
        <div className="mt-6">
          <PermissionDataTable
            data={paginatedPermissions}
            pagination={pagination}
            totalRecords={totalRecords}
            totalPages={totalPages}
            availablePageSizes={availablePageSizes}
            onPageChange={goToPage}
            onPageSizeChange={changePageSize}
          />
        </div>
      </main>
    </>
  );
}
