import { useEffect, useState, useContext } from 'react';
import {
  Table,
  Button,
  Form,
  Switch,
  Popconfirm,
  Select,
  CommonCompactView,
  message,
  Row,
  Col,
  Input,
  Checkbox,
  Pagination,
} from '../../../../components';
import { getAllAssetsByPagination, addAsset, deleteAsset, updateAsset } from '../../../../services';
import Context from '../../../../context';
import { PlusOutlined } from '@ant-design/icons';
import { BreadcrumbList, PermissionContainer } from '../../../../shared';
import { buildCommonApiValues } from '../../../../utils';
import CommonDrawer from '../../../../components/CommonDrawer';
import {
  CRUD,
  Pages,
  AssetsTypes,
  ModuleNames,
  MastersPageSizeDefault,
  MastersPageSizeOptions,
} from '../../../../constants';
import { capitalize, get } from 'lodash';
import s from './index.module.less';

const { Option } = Select;

const Asset = () => {
  const [context, setContext] = useContext(Context);
  const [assets, setAssets] = useState([]);
  const [asset, setAsset] = useState({});
  const [totalAssets, setTotalAssets] = useState({ items: 0, current: 1, pageSize: MastersPageSizeDefault });
  const [visible, setVisible] = useState(false);
  const [action, setAction] = useState('new');
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [assetType, setAssetType] = useState('');

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setAssets(tableData) : setAssets(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const makeActive = (data) => {
    updateAssetCall({ ...data, deleted: false });
  };

  const closeAdd = () => {
    setVisible(false);
    form.resetFields();
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    if (assetType === AssetsTypes.OTHERS) {
      values.assetType = values.type;
    }
    if (action === 'new') {
      await saveAssetAction({ ...commonValues, ...values });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateAssetAction(values);
      setVisible(false);
    }
  };

  const onAssetTypeSelect = (e) => {
    setAssetType(e);
  };

  const saveAssetAction = async (asset) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addAsset(asset)
      .then((res) => {
        const tempData = { ...res.data };
        tempData.status = tempData.status === 'true' ? true : false;
        setAssets((state) => [tempData, ...state]);
        message.success('Succesfully Added asset');
        form.resetFields();
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editAssetAction = (asset) => {
    form.setFieldsValue({ ...asset });
    setAction('edit');
    setAsset(asset);
    setVisible(true);
  };

  const updateAssetCall = (values) => {
    const data = { ...asset, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateAsset(data)
      .then((res) => {
        setAssets((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, {
                ...res.data,
                status: res.data.status === 'true' ? true : false,
              });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated asset');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateAssetAction = async (values) => {
    updateAssetCall(values);
  };

  const setDeleteAssetAction = (assetId, visible) => {
    setAssets((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === assetId).visible = visible;
      tempData
        .filter((x) => x.id !== assetId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteAssetAction = (assetId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteAsset(assetId)
      .then(() => {
        setAssets((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === assetId).visible = false;
          tempState.find((x) => x.id === assetId).deleted = true;
          return [...state].filter((x) => x.id !== assetId);
        });
        message.success('Succesfully Deleted asset');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const onPaginationChange = (e, size) => {
    setTotalAssets((ps) => ({ ...ps, current: +e, pageSize: size }));
    init(+e - 1, size);
  };
  const init = (page = 0, pageSize = 0) => {
    if (context.profile.tenantId) {
      setContext((state) => {
        return {
          ...state,
          isLoading: false,
        };
      });
      getAllAssetsByPagination(context.profile.tenantId, { page: page, size: pageSize || totalAssets.pageSize })
        .then((res) => {
          const tempData = [...res.data.content];
          tempData.forEach((i) => {
            i.status = i.status === 'true' ? true : false;
          });
          setTableData(tempData);
          setAssets(tempData.filter((x) => x.deleted === false));
          setTotalAssets((ps) => ({ ...ps, items: res.data.totalElements }));
        })
        .catch((e) => {
          console.log(e);
          message.error('Unable to get Asset details, try again later');
        })
        .finally(() => {
          setContext((state) => {
            return {
              ...state,
              isLoading: false,
            };
          });
        });
    }
  };
  useEffect(() => {
    init();
    // eslint-disable-next-line
  }, []);

  const clearForm = () => {
    form.resetFields();
  };

  const tableCols = [
    {
      title: <strong> Name </strong>,
      render: (record) => (
        <>
          {record.assetType === AssetsTypes.WORKER ? (
            <img src="/assets/icons/worker.svg" alt={AssetsTypes.WORKER} className={s.assetTypeSvg} />
          ) : record.assetType === AssetsTypes.VENDOR ? (
            <img src="/assets/icons/vendor.svg" alt={AssetsTypes.VENDOR} className={s.assetTypeSvg} />
          ) : record.assetType === AssetsTypes.VECHICLE ? (
            <img src="/assets/icons/vehicle.svg" alt={AssetsTypes.VECHICLE} className={s.assetTypeSvg} />
          ) : record.assetType === AssetsTypes.WASHROOM ? (
            <img src="/assets/icons/sanitary.svg" alt={AssetsTypes.WASHROOM} className={s.assetTypeSvg} />
          ) : (
            <img src="/assets/icons/other.svg" alt={AssetsTypes.OTHERS} className={s.assetTypeSvg} />
          )}
          {` ${capitalize(record.name)}`}
        </>
      ),
    },
    { title: <strong> External ID </strong>, key: 'externalId', dataIndex: 'externalId' },
    { title: <strong> Type </strong>, key: 'assetType', dataIndex: 'assetType' },
    {
      title: <strong> Status </strong>,
      key: 'status',
      render: (record) => <>{record.status ? 'True' : 'False'}</>,
    },
    {
      title: <strong> Actions </strong>,
      width: 320,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <PermissionContainer page={Pages.ASSET} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                Activate
              </Button>
            </PermissionContainer>
            <p className="lastModifiedDate">
              Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
            </p>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.ASSET} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editAssetAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>

            <PermissionContainer page={Pages.ASSET} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete asset ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteAssetAction(record.id)}
                onCancel={() => setDeleteAssetAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteAssetAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const assetBreadcrumbsName = get(context.tenantProfile, `moduleNames[${Pages.ASSET}].displayName`, ModuleNames.ASSET);

  return (
    <>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.DIGITAL_CARPET, ModuleNames.CONFIGURE, assetBreadcrumbsName]} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.ASSET} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Asset
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      {!context.isCompact ? (
        <Table
          size="small"
          scroll={{ x: true }}
          rowKey="id"
          loading={context.isLoading}
          columns={tableCols}
          dataSource={assets}
          rowClassName={(record) => record.deleted && 'rowInactive'}
          pagination={{
            showSizeChanger: true,
            defaultPageSize: MastersPageSizeDefault,
            pageSizeOptions: MastersPageSizeOptions,
            onChange: onPaginationChange,
            current: totalAssets.current,
            total: totalAssets.items,
          }}
        />
      ) : (
        <>
          <CommonCompactView
            data={assets}
            onEdit={editAssetAction}
            onDelete={deleteAssetAction}
            permissions={[
              { pageName: Pages.ASSET, permission: CRUD.UPDATE, label: 'Edit' },
              { pageName: Pages.ASSET, permission: CRUD.DELETE, label: 'Delete' },
            ]}
            title="name"
            dataList={[
              { label: 'External ID', value: 'externalId' },
              { label: 'Status', value: 'status', type: 'boolean' },
            ]}
          />
          <Row justify="end">
            <Col>
              <div className="m-2">
                <Pagination
                  onChange={onPaginationChange}
                  current={totalAssets.current}
                  total={totalAssets.items}
                  showSizeChanger={false}
                />
              </div>
            </Col>
          </Row>
        </>
      )}
      <CommonDrawer title="Asset" visible={visible} closeAdd={closeAdd}>
        <Form
          scrollToFirstError={true}
          layout="horizontal"
          initialValues={{}}
          form={form}
          wrapperCol={{ span: 18 }}
          labelCol={{ span: 6 }}
        >
          <Form.Item
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input tag Name!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Tag Name" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="External ID"
            name="externalId"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input tag external ID!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Tag External ID" />
          </Form.Item>
          <Form.Item
            hasFeedback
            label="Assets Type"
            name="assetType"
            rules={[
              {
                required: true,
                message: 'Please select Assets Type!',
              },
            ]}
          >
            <Select placeholder="Assets Type" onSelect={onAssetTypeSelect} onClear={() => setAssetType('')}>
              {Object.keys(AssetsTypes).map((type) => (
                <Option key={AssetsTypes[type]} value={AssetsTypes[type]}>
                  {AssetsTypes[type]}
                </Option>
              ))}
            </Select>
          </Form.Item>
          {assetType === AssetsTypes.OTHERS && (
            <Form.Item
              hasFeedback
              label="Type"
              name="type"
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: 'Please input asset type!',
                  min: 1,
                  max: 200,
                },
              ]}
            >
              <Input placeholder="Asset Type" />
            </Form.Item>
          )}

          <Form.Item
            required={false}
            name="status"
            valuePropName="checked"
            label="Status"
            initialValue={false}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Switch />
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" type="primary" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12, offset: 0 }} lg={{ offset: 0, span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </>
  );
};

export default Asset;
