import getAxiosInstance from '../index';
import { RolesPages } from '../../constants';

// process argv 2 = token
const axios = getAxiosInstance(process.argv[2]);
const ROLE_ACCESS_URI = '/roleAccess/';

export const linkPageRole = (link) => {
  return axios.post(`${ROLE_ACCESS_URI}`, link);
};

export const updatePageRole = (link) => {
  return axios.put(`${ROLE_ACCESS_URI}`, link);
};

export const deletePageRoleLink = (linkId) => {
  return axios.delete(`${ROLE_ACCESS_URI}${linkId}`);
};

export const getAllPagesRolesLinks = () => {
  return axios.get(`${ROLE_ACCESS_URI}all`);
};

const init = async () => {
  const isDelete = process.argv[3] === 'delete';
  try {
    const { data } = await getAllPagesRolesLinks();
    if (isDelete) {
      for (const link of data) {
        try {
          await deletePageRoleLink(link.id);
          console.log('Deleted Link: ', link.id);
        } catch (e) {
          console.log('Unable to delete Link', e);
        }
      }
      Object.keys(RolesPages).forEach(async (link) => {
        const dateTimeNow = new Date().toISOString();
        try {
          await linkPageRole({
            id: link,
            pagePermissions: RolesPages[link],
            enable: true,
            deleted: false,
            createdTime: dateTimeNow,
            modifiedTime: dateTimeNow,
            createdBy: 'system',
            modifiedBy: 'system',
          });
          console.log('Created Link: ', link);
        } catch (e) {
          try {
            await updatePageRole({
              id: link,
              pagePermissions: RolesPages[link],
              enable: true,
              deleted: false,
              createdTime: dateTimeNow,
              modifiedTime: dateTimeNow,
              createdBy: 'system',
              modifiedBy: 'system',
            });
            console.log('Updated Link: ', link);
          } catch (e) {
            console.log('Unable to update Link', e);
          }
          console.log(e);
          console.log('Unable to create Link', link);
        }
      });
    } else {
      const serverRoles = data.filter((x) => x.deleted === false).map((y) => y.id);
      Object.keys(RolesPages).forEach(async (link) => {
        if (!serverRoles.includes(link)) {
          const dateTimeNow = new Date().toISOString();
          try {
            await linkPageRole({
              id: link,
              pagePermissions: RolesPages[link],
              enable: true,
              deleted: false,
              createdTime: dateTimeNow,
              modifiedTime: dateTimeNow,
              createdBy: 'system',
              modifiedBy: 'system',
            });
            console.log('Created Link: ', link);
          } catch (e) {
            console.log('Unable to create Link', link);
          }
        }
      });
    }
  } catch (err) {
    console.log(err);
  }
};

init();
