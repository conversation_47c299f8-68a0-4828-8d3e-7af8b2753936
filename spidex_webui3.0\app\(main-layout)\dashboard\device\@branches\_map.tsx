"use client";
import { useMapBounds } from "@/lib/hooks/useMapBounds";
import { Branch } from "@/types/branch";
import { Location } from "@/types/location";
import { Area } from "@/types/area";
import { Gateway } from "@/types/gateway";

// Define the hierarchical item types to match server data
interface BranchItem extends Branch {
  children: LocationItem[];
}

interface LocationItem extends Location {
  children: AreaItem[];
}

interface AreaItem extends Area {
  children: GatewayItem[];
}

interface GatewayItem extends Gateway {
  children: [];
}

// Generic item type for map operations
interface MapItem {
  id: string;
  name: string;
  gpsPoint: {
    latitude: string | number;
    longitude: string | number;
  };
  children: MapItem[];
  [key: string]: any;
}
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from "react";
import Map, {
  FullscreenControl,
  GeolocateControl,
  Layer,
  MapRef,
  Marker,
  NavigationControl,
  Popup,
  ScaleControl,
  Source,
  useMap,
} from "react-map-gl";
import ControlPanel from "./_map-control-panel";
import { mapInitialState, mapReducer } from "./_map-reducer";

const initialViewState = {
  latitude: 80.5937,
  longitude: 78.9629,
  zoom: 4,
};

export default function MapView({ branches }: { branches: BranchItem[] }) {
  // const mapRef = useRef<MapRef>(null);
  const [state, dispatch] = useReducer(mapReducer, {
    ...mapInitialState,
    currentItems: branches,
  });
  const { currentItems, parentItem } = state;
  // const [currentItems, setCurrentItems] = useState<Item[]>(branches);
  const [popupInfo, setPopupInfo] = useState<MapItem | null>(null);
  const bounds = useMapBounds(currentItems);

  const [geojsonData, setGeojsonData] = useState(null);
  useEffect(() => {
    // Fetch the local GeoJSON file
    fetch("/data.geojson")
      .then((response) => response.json())
      .then((data) => setGeojsonData(data))
      .catch((error) => console.error("Error loading GeoJSON:", error));
  }, []);

  const pins = useMemo(
    () =>
      currentItems.map((item) => {
        const { id, gpsPoint, children } = item;
        return (
          <Marker
            key={id}
            longitude={parseFloat(String(gpsPoint.longitude))}
            latitude={parseFloat(String(gpsPoint.latitude))}
            anchor="bottom"
            style={{ cursor: "pointer" }}
            onClick={(e) => {
              // If we let the click event propagates to the map, it will immediately close the popup
              // with `closeOnClick: true`
              e.originalEvent.stopPropagation();
              setPopupInfo(item);
            }}
            color="red"
          >
            {/* <Pin /> */}
          </Marker>
        );
      }),
    [currentItems]
  );

  const onSelectItem = useCallback((item: MapItem) => {
    if (item.children.length > 0)
      dispatch({ type: "NAVIGATE_DOWN", payload: item });

    // if (item.children.length > 0) setCurrentItems(item.children);
    // const { longitude: lon, latitude: lat } = item.gpsPoint;
    // mapRef.current?.flyTo({
    //   center: [parseFloat(lon), parseFloat(lat)],
    //   duration: 3000,
    //   zoom: 18,
    // });
  }, []);

  const onBack = () => {
    dispatch({ type: "NAVIGATE_BACK" });
  };

  return (
    <div className="flex flex-row h-[600px]">
      <Map
        initialViewState={initialViewState}
        mapStyle="mapbox://styles/ganache-lives/clzu5zd8m00i301pf1laee2x3s"
        mapboxAccessToken={process.env.NEXT_PUBLIC_MapboxAccessToken}
      >
        <Source type="geojson" data={geojsonData || ""}>
          <Layer
            id="data-layer"
            type="fill"
            paint={{
              "fill-color": "#888888",
              "fill-opacity": 0.4,
            }}
          />
        </Source>
        <GeolocateControl position="top-left" />
        <FullscreenControl position="top-left" />
        <NavigationControl position="top-left" />
        <ScaleControl />
        {pins}
        {popupInfo && (
          <Popup
            className="text-foreground"
            anchor="top"
            longitude={Number(popupInfo.gpsPoint.longitude)}
            latitude={Number(popupInfo.gpsPoint.latitude)}
            onClose={() => setPopupInfo(null)}
            closeButton={false}
          >
            <div>
              {popupInfo.name}, {popupInfo.address}
            </div>
          </Popup>
        )}
        <SetBounds bounds={bounds} />
      </Map>
      <ControlPanel
        parentItem={parentItem}
        items={currentItems}
        onSelectItem={onSelectItem}
        onBack={onBack}
      />
    </div>
  );
}

function SetBounds({ bounds }: any) {
  const { current: map } = useMap();
  useEffect(() => {
    if (map && bounds) {
      map.fitBounds(bounds, { padding: 100, duration: 3000 });
    }
  }, [bounds, map]);
  return <></>;
}
