"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { User, Role, Tenant } from "@/types/account";
import {
  CreateAccountSchema,
  UpdateAccountSchema,
  CreateAccountFormData,
  UpdateAccountFormData,
} from "@/lib/schemas";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

interface AccountFormProps {
  user?: User;
  roles: Role[];
  tenants: Tenant[];
  isLoading?: boolean;
  onSubmit: (
    data: CreateAccountFormData | UpdateAccountFormData
  ) => Promise<void>;
  onCancel: () => void;
  onClear?: () => void;
}

export function AccountForm({
  user,
  roles,
  tenants,
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: AccountFormProps) {
  const isEditing = !!user;
  const schema = isEditing ? UpdateAccountSchema : CreateAccountSchema;

  const form = useForm<CreateAccountFormData | UpdateAccountFormData>({
    resolver: zodResolver(schema),
    defaultValues: isEditing
      ? {
          id: user.id,
          name: user.name,
          userId: user.userId,
          emailId: user.email || "",
          mobileNumber: user.mobileNumber || "",
          tenantId: user.tenantId,
          roles: user.roles.map((role) => role.id),
          active: user.active,
        }
      : {
          name: "",
          userId: "",
          emailId: "",
          mobileNumber: "",
          tenantId: "",
          roles: [],
          password: "",
        },
  });

  const selectedRoleIds = form.watch("roles");
  const selectedRoles = roles.filter((role) =>
    selectedRoleIds.includes(role.id)
  );

  const handleRoleToggle = (roleId: string, checked: boolean) => {
    const currentRoles = form.getValues("roles");
    if (checked) {
      form.setValue("roles", [...currentRoles, roleId]);
    } else {
      form.setValue(
        "roles",
        currentRoles.filter((id) => id !== roleId)
      );
    }
  };

  const removeRole = (roleId: string) => {
    const currentRoles = form.getValues("roles");
    form.setValue(
      "roles",
      currentRoles.filter((id) => id !== roleId)
    );
  };

  const handleFormSubmit = async (
    data: CreateAccountFormData | UpdateAccountFormData
  ) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleFormSubmit)}
        className="space-y-6 "
      >
        <div>
          {/* Basic Information */}
          <Card>
            <CardContent className="space-y-4 mt-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter full name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="userId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter username"
                        {...field}
                        disabled={isEditing} // Username cannot be changed after creation
                      />
                    </FormControl>
                    <FormDescription>
                      {isEditing
                        ? "Username cannot be changed after account creation"
                        : "This will be used for login"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="emailId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter email address"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="mobileNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mobile Number</FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder="Enter mobile number"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {!isEditing && (
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Enter password"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Leave empty to use default password (spidex1234)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Account Status toggle - COMMENTED OUT */}
              {/* {isEditing && (
                <FormField
                  control={form.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Account Status</FormLabel>
                        <FormDescription>
                          Enable or disable this user account
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              )} */}

              {/* Tenant and Roles */}
              <FormField
                control={form.control}
                name="tenantId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tenant</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a tenant" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {tenants.map((tenant) => (
                          <SelectItem key={tenant.id} value={tenant.id}>
                            {tenant.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="roles"
                render={() => (
                  <FormItem>
                    <FormLabel>Roles</FormLabel>
                    <FormDescription>
                      Select one or more roles for this user
                    </FormDescription>

                    {/* Selected Roles Display */}
                    {selectedRoles.length > 0 && (
                      <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-muted/50">
                        {selectedRoles.map((role) => (
                          <Badge
                            key={role.id}
                            variant="secondary"
                            className="gap-1"
                          >
                            {role.name}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 hover:bg-transparent"
                              onClick={() => removeRole(role.id)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Role Selection */}
                    <div className="space-y-2 max-h-40 overflow-y-auto border rounded-md p-2">
                      {roles.map((role) => (
                        <FormItem
                          key={role.id}
                          className="flex flex-row items-start space-x-3 space-y-0"
                        >
                          <FormControl>
                            <Checkbox
                              checked={selectedRoleIds.includes(role.id)}
                              onCheckedChange={(checked) =>
                                handleRoleToggle(role.id, checked as boolean)
                              }
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel className="text-sm font-medium">
                              {role.name}
                            </FormLabel>
                            {role.description && (
                              <FormDescription className="text-xs">
                                {role.description}
                              </FormDescription>
                            )}
                          </div>
                        </FormItem>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        </div>

        {/* Form Actions */}
        <div className="flex justify-start gap-4 border-t pt-4">
          <Button
            type="submit"
            disabled={isLoading}
            className="flex-1 sm:flex-none bg-green-700 text-white hover:bg-green-800 rounded"
          >
            {isLoading
              ? isEditing
                ? "Updating..."
                : "Creating..."
              : isEditing
              ? "Update Account"
              : "Create Account"}
          </Button>

          {!isEditing && onClear && (
            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                form.reset();
                onClear();
              }}
              className="border bg-white border-gray-700 text-black hover:bg-green-20 rounded"
            >
              Clear
            </Button>
          )}
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="border bg-white border-red-700 text-black hover:bg-green-20 rounded"
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
