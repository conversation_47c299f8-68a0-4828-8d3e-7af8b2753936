"use client";

import { useState, useEffect } from "react";
import { Search, X, Filter } from "lucide-react";
import { TenantSearchFilters } from "@/types/tenant";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

interface TenantSearchPaginationProps {
  searchFilters: TenantSearchFilters;
  showDeleted: boolean;
  onSearchChange: (filters: Partial<TenantSearchFilters>) => void;
  onToggleShowDeleted: () => void;
  onClearFilters: () => void;
}

export function TenantSearchPagination({
  searchFilters,
  showDeleted,
  onSearchChange,
  onToggleShowDeleted,
  onClearFilters,
}: TenantSearchPaginationProps) {
  const [mounted, setMounted] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent rendering until searchFilters is properly initialized
  if (!searchFilters || typeof searchFilters.searchTerm === "undefined") {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between gap-4">
          <div className="h-10 w-80 bg-gray-200 animate-pulse rounded" />
          <div className="h-10 w-32 bg-gray-200 animate-pulse rounded" />
        </div>
      </div>
    );
  }

  const hasActiveFilters =
    searchFilters.searchTerm ||
    searchFilters.type ||
    searchFilters.orgName ||
    searchFilters.enable !== undefined;

  if (!mounted) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search tenants..."
                className="pl-10 w-80"
                disabled
              />
            </div>
          </div>
        </div>
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div>Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main Search Bar */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search tenants by name, type, or organization..."
              value={searchFilters.searchTerm}
              onChange={(e) => onSearchChange({ searchTerm: e.target.value })}
              className="pl-10 w-80"
            />
          </div>

          <Collapsible
            open={showAdvancedFilters}
            onOpenChange={setShowAdvancedFilters}
          >
            <CollapsibleTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </CollapsibleTrigger>
          </Collapsible>

          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="h-9 px-2 lg:px-3"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="show-deleted"
              checked={showDeleted}
              onCheckedChange={onToggleShowDeleted}
            />
            <Label htmlFor="show-deleted" className="text-sm font-medium">
              Show Deleted
            </Label>
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      <Collapsible
        open={showAdvancedFilters}
        onOpenChange={setShowAdvancedFilters}
      >
        <CollapsibleContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg bg-muted/50">
            <div className="space-y-2">
              <Label htmlFor="type-filter">Type</Label>
              <Input
                id="type-filter"
                placeholder="Filter by type..."
                value={searchFilters.type || ""}
                onChange={(e) =>
                  onSearchChange({ type: e.target.value || undefined })
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="org-filter">Organization</Label>
              <Input
                id="org-filter"
                placeholder="Filter by organization..."
                value={searchFilters.orgName || ""}
                onChange={(e) =>
                  onSearchChange({ orgName: e.target.value || undefined })
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status-filter">Status</Label>
              <Select
                value={
                  searchFilters.enable === undefined
                    ? "all"
                    : searchFilters.enable
                    ? "enabled"
                    : "disabled"
                }
                onValueChange={(value) => {
                  if (value === "all") {
                    onSearchChange({ enable: undefined });
                  } else {
                    onSearchChange({ enable: value === "enabled" });
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="enabled">Enabled</SelectItem>
                  <SelectItem value="disabled">Disabled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Filter Summary */}
      {hasActiveFilters && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Active filters:</span>
          {searchFilters.searchTerm && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs">
              Search: "{searchFilters.searchTerm}"
            </span>
          )}
          {searchFilters.type && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs">
              Type: "{searchFilters.type}"
            </span>
          )}
          {searchFilters.orgName && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs">
              Organization: "{searchFilters.orgName}"
            </span>
          )}
          {searchFilters.enable !== undefined && (
            <span className="bg-primary/10 text-primary px-2 py-1 rounded text-xs">
              Status: {searchFilters.enable ? "Enabled" : "Disabled"}
            </span>
          )}
        </div>
      )}
    </div>
  );
}
