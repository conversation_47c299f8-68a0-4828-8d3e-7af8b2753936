"use client";

import { useState } from "react";
import { Search, Filter, X, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

import { GatewaySearchFilters } from "@/types/gateway";
import { Area, Location } from "@/types/asset-tracking";

interface GatewaySearchPaginationProps {
  searchFilters: GatewaySearchFilters;
  showDeleted: boolean;
  totalRecords: number;
  totalAllRecords: number;
  activeRecordsCount: number;
  inactiveRecordsCount: number;
  areas: Area[];
  locations: Location[];
  models: any[];
  onSearchChange: (filters: Partial<GatewaySearchFilters>) => void;
  onClearSearch: () => void;
  onToggleShowDeleted: () => void;
}

export function GatewaySearchPagination({
  searchFilters,
  showDeleted,
  totalRecords,
  totalAllRecords,
  activeRecordsCount,
  inactiveRecordsCount,
  areas,
  locations,
  models,
  onSearchChange,
  onClearSearch,
  onToggleShowDeleted,
}: GatewaySearchPaginationProps) {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  // Ensure searchFilters has default values to prevent hydration mismatch
  const safeSearchFilters = {
    searchTerm: searchFilters?.searchTerm || "",
    categoryType: searchFilters?.categoryType,
    areaId: searchFilters?.areaId,
    locationId: searchFilters?.locationId,
    modelId: searchFilters?.modelId,
    active: searchFilters?.active,
  };

  // Check if any filters are active
  const hasActiveFilters =
    safeSearchFilters.searchTerm ||
    safeSearchFilters.categoryType ||
    safeSearchFilters.areaId ||
    safeSearchFilters.locationId ||
    safeSearchFilters.modelId ||
    safeSearchFilters.active !== undefined;

  return (
    <div className="space-y-4">
      {/* Main Search Bar */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search gateways by name, description, or MAC ID..."
              value={safeSearchFilters.searchTerm}
              onChange={(e) => onSearchChange({ searchTerm: e.target.value })}
              className="pl-10 w-80"
            />
          </div>

          <Collapsible
            open={showAdvancedFilters}
            onOpenChange={setShowAdvancedFilters}
          >
            <CollapsibleTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </CollapsibleTrigger>
          </Collapsible>

          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClearSearch}
              className="text-red-600 hover:text-red-700"
            >
              <X className="h-4 w-4 mr-2" />
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      <Collapsible
        open={showAdvancedFilters}
        onOpenChange={setShowAdvancedFilters}
      >
        <CollapsibleContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg bg-muted/50">
            <div className="space-y-2">
              <Label htmlFor="category-type-filter">Gateway Type</Label>
              <Select
                value={safeSearchFilters.categoryType || "all"}
                onValueChange={(value) =>
                  onSearchChange({
                    categoryType: value === "all" ? undefined : value,
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="fixed">Fixed</SelectItem>
                  <SelectItem value="transit">Transit</SelectItem>
                  <SelectItem value="lint">Lint</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location-filter">Location</Label>
              <Select
                value={safeSearchFilters.locationId || "all"}
                onValueChange={(value) =>
                  onSearchChange({
                    locationId: value === "all" ? undefined : value,
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All locations" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="area-filter">Area</Label>
              <Select
                value={safeSearchFilters.areaId || "all"}
                onValueChange={(value) =>
                  onSearchChange({
                    areaId: value === "all" ? undefined : value,
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All areas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Areas</SelectItem>
                  {areas.map((area) => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="model-filter">Model</Label>
              <Select
                value={safeSearchFilters.modelId || "all"}
                onValueChange={(value) =>
                  onSearchChange({
                    modelId: value === "all" ? undefined : value,
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All models" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Models</SelectItem>
                  {models.map((model) => (
                    <SelectItem key={model.modelId} value={model.modelId}>
                      {model.modelName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status-filter">Status</Label>
              <Select
                value={
                  safeSearchFilters.active === undefined
                    ? "all"
                    : safeSearchFilters.active
                    ? "active"
                    : "inactive"
                }
                onValueChange={(value) =>
                  onSearchChange({
                    active:
                      value === "all"
                        ? undefined
                        : value === "active"
                        ? true
                        : false,
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={onClearSearch}
                className="w-full"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset Filters
              </Button>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Show deleted checkbox and record counts */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="show-deleted"
              checked={showDeleted}
              onCheckedChange={onToggleShowDeleted}
            />
            <Label htmlFor="show-deleted">Show inactive gateways</Label>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <span>
            Active: <strong>{activeRecordsCount}</strong>
          </span>
          <span>
            Inactive: <strong>{inactiveRecordsCount}</strong>
          </span>
          <span>
            Total: <strong>{totalAllRecords}</strong>
          </span>
          <span>
            Showing: <strong>{totalRecords}</strong>
          </span>
        </div>
      </div>
    </div>
  );
}
