"use client";

import { useEffect } from "react";

export function What3WordsScript() {
  useEffect(() => {
    // Only load the script on the client side to prevent hydration issues
    const script = document.createElement("script");
    script.type = "module";
    script.src = "https://cdn.what3words.com/javascript-components@4.0.3/dist/what3words/what3words.esm.js";
    script.async = true;
    
    // Check if script is already loaded
    const existingScript = document.querySelector(`script[src="${script.src}"]`);
    if (!existingScript) {
      document.head.appendChild(script);
    }

    return () => {
      // Cleanup: remove script when component unmounts
      const scriptToRemove = document.querySelector(`script[src="${script.src}"]`);
      if (scriptToRemove) {
        document.head.removeChild(scriptToRemove);
      }
    };
  }, []);

  return null; // This component doesn't render anything
}
