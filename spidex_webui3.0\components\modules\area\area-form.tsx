"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect, useMemo } from "react";
import { Area, BranchOption, LocationOption } from "@/types/area";
import {
  CreateAreaSchema,
  UpdateAreaSchema,
  CreateAreaFormData,
  UpdateAreaFormData,
} from "@/lib/schemas";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  MapPin,
  Loader2,
  Building,
  MapIcon as MapIconLucide,
} from "lucide-react";
import { GoogleMapModal } from "@/components/ui/google-map-modal";
import { MapIcon } from "@/components/ui/map-icon";

interface AreaFormProps {
  area?: Area;
  branches: BranchOption[];
  locations: LocationOption[];
  isLoading?: boolean;
  onSubmit: (data: CreateAreaFormData | UpdateAreaFormData) => Promise<void>;
  onCancel: () => void;
  onClear?: () => void;
}

export function AreaForm({
  area,
  branches,
  locations,
  isLoading = false,
  onSubmit,
  onCancel,
  onClear,
}: AreaFormProps) {
  const isEditing = !!area;
  const schema = isEditing ? UpdateAreaSchema : CreateAreaSchema;
  const [isMapOpen, setIsMapOpen] = useState(false);

  const form = useForm<CreateAreaFormData | UpdateAreaFormData>({
    resolver: zodResolver(schema),
    defaultValues: isEditing
      ? {
        id: area.id,
        name: area.name,
        address: area.address,
        branchId: area.branchId,
        locationId: area.locationId,
        gpsPoint: {
          latitude: area.gpsPoint.latitude,
          longitude: area.gpsPoint.longitude,
        },
        geoJson: area.geoJson || "",
        level: area.level || "",
        min: area.min || "",
        max: area.max || "",
      }
      : {
        name: "",
        address: "",
        branchId: "",
        locationId: "",
        gpsPoint: {
          latitude: "",
          longitude: "",
        },
        geoJson: "",
        level: "",
        min: "",
        max: "",
      },
  });

  // Watch branch selection to filter locations
  const selectedBranchId = form.watch("branchId");

  // Filter locations based on selected branch
  const filteredLocations = useMemo(() => {
    if (!selectedBranchId) return [];
    const filtered = locations.filter(
      (location) => location.branchId === selectedBranchId
    );
    return filtered;
  }, [locations, selectedBranchId]);

  // Reset form when area data changes (for editing)
  useEffect(() => {
    if (area) {
      console.log("🔄 Resetting area form with data:", area);
      form.reset({
        id: area.id,
        name: area.name,
        address: area.address,
        branchId: area.branchId,
        locationId: area.locationId,
        gpsPoint: {
          latitude: area.gpsPoint.latitude,
          longitude: area.gpsPoint.longitude,
        },
        geoJson: area.geoJson || "",
        level: area.level || "",
        min: area.min || "",
        max: area.max || "",
      });
    } else {
      console.log("🆕 Resetting area form for new area");
      form.reset({
        name: "",
        address: "",
        branchId: "",
        locationId: "",
        gpsPoint: {
          latitude: "",
          longitude: "",
        },
        geoJson: "",
        level: "",
        min: "",
        max: "",
      });
    }
  }, [area, form]);

  // Reset location when branch changes (only for new areas, not when editing)
  useEffect(() => {
    if (selectedBranchId && !isEditing) {
      const currentLocationId = form.getValues("locationId");
      // Only reset if the current location doesn't belong to the selected branch
      if (currentLocationId) {
        const currentLocation = locations.find(
          (loc) => loc.id === currentLocationId
        );
        if (currentLocation && currentLocation.branchId !== selectedBranchId) {
          form.setValue("locationId", "");
          form.trigger("locationId");
        }
      }
    }
  }, [selectedBranchId, form, isEditing, locations]);

  // Reset map modal state when component unmounts or area changes
  useEffect(() => {
    return () => {
      setIsMapOpen(false);
    };
  }, [area]);

  const handleSubmit = async (
    data: CreateAreaFormData | UpdateAreaFormData
  ) => {
    try {
      console.log("📝 Area form submission data:", data);
      console.log("🔧 Is editing:", isEditing);
      await onSubmit(data);
      if (!isEditing) {
        form.reset();
      }
    } catch (error) {
      console.error("Form submission error:", error);
    }
  };

  const handleClear = () => {
    form.reset();
    setIsMapOpen(false);
    if (onClear) {
      onClear();
    }
  };

  const handleCancel = () => {
    form.reset();
    setIsMapOpen(false);
    onCancel();
  };

  const handleLocationSelect = (location: {
    latitude: number;
    longitude: number;
    geoJson?: string;
  }) => {
    form.setValue("gpsPoint.latitude", location.latitude.toString());
    form.setValue("gpsPoint.longitude", location.longitude.toString());
    if (location.geoJson) {
      form.setValue("geoJson", location.geoJson);
    }
    setIsMapOpen(false);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>

          <CardContent className="space-y-4 pt-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Area Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="(1-200 characters)"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="(1-200 characters)"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
            {/* Branch and Location Selection */}
            <FormField
              control={form.control}
              name="locationId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      // Trigger validation to clear any existing errors
                      form.trigger("locationId");
                    }}
                    value={field.value}
                    disabled={isLoading || !selectedBranchId}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select the location" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {filteredLocations.map((location) => (
                        <SelectItem key={location.id} value={location.id}>
                          {location.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="branchId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Branch</FormLabel>
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      // Trigger validation to clear any existing errors
                      form.trigger("branchId");
                    }}
                    value={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select the branch" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {branches.map((branch) => (
                        <SelectItem key={branch.id} value={branch.id}>
                          {branch.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <FormMessage />
                </FormItem>
              )}
            />
            <div>

              <div className="flex items-end gap-4">
                <div className="flex-1">
                  <FormField
                    control={form.control}
                    name="geoJson"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Geo</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="(1-2000 characters)"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>

                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Map Icon Button */}
                <div className="flex-shrink-0">
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => setIsMapOpen(true)}
                    disabled={isLoading}
                    className="h-10 w-10"
                  >
                    <MapIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* GPS Coordinates */}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                <FormField
                  control={form.control}
                  name="gpsPoint.latitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Latitude</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="(-90 to 90)"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="gpsPoint.longitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Longitude</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="(-180 to 180)"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
                <FormField
                  control={form.control}
                  name="level"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Level</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Level is required"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Level, Max, Min */}
                <FormField
                  control={form.control}
                  name="max"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Max is required"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="min"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Min</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Min is required"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>

                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t">
          <Button
            type="submit"
            disabled={isLoading}
            className="flex-1 sm:flex-none bg-green-700 text-white hover:bg-green-800 rounded"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? "Update Area" : "Create Area"}
          </Button>

          {!isEditing && (
            <Button
              type="button"
              variant="outline"
              onClick={handleClear}
              disabled={isLoading}
              className="flex-1 sm:flex-none border bg-white border-gray-700 text-black hover:bg-green-20 rounded"
            >
              Clear
            </Button>
          )}

          <Button
            type="button"
            variant="secondary"
            onClick={handleCancel}
            disabled={isLoading}
            className="flex-1 sm:flex-none border bg-white border-red-700 text-black hover:bg-red-20 rounded"
          >
            Cancel
          </Button>
        </div>
      </form>

      {/* Google Maps Modal */}
      <GoogleMapModal
        isOpen={isMapOpen}
        onClose={() => setIsMapOpen(false)}
        onLocationSelect={handleLocationSelect}
        initialLocation={{
          latitude:
            parseFloat(form.getValues("gpsPoint.latitude")) || undefined,
          longitude:
            parseFloat(form.getValues("gpsPoint.longitude")) || undefined,
        }}
      />
    </Form>
  );
}
