import { useState, useContext, useEffect } from 'react';
import {
  Table,
  Button,
  Form,
  Spin,
  CommonCompactView,
  CommonDrawer,
  Input,
  Select,
  Popconfirm,
  Checkbox,
  Radio,
  Switch,
  Row,
  Col,
  message,
} from '../../../components';
import Context from '../../../context';
import { <PERSON>read<PERSON>rumbList, PermissionContainer } from '../../../shared';
import { CRUD, InputRegex, Pages, sanitaryTypes, ModuleNames } from '../../../constants';
import { getAllAssets, addAsset, deleteAsset, updateAsset } from '../../../services';
import { PlusOutlined } from '@ant-design/icons';
import { buildCommonApiValues } from '../../../utils';
import { capitalize, get } from 'lodash';

const { Option } = Select;

const Sanitary = () => {
  const [context, setContext] = useContext(Context);
  const [visible, setVisible] = useState(false);
  const [assets, setAssets] = useState([]);
  const [asset, setAsset] = useState({});
  const [action, setAction] = useState('new');
  const [showDeleted, setShowDeleted] = useState(false);
  const [tableData, setTableData] = useState([]);

  const [form] = Form.useForm();

  const switchTableData = () => {
    showDeleted ? setAssets(tableData) : setAssets(tableData.filter((x) => x.deleted === false));
  };

  useEffect(() => {
    switchTableData();
    // eslint-disable-next-line
  }, [showDeleted]);

  const openAdd = () => {
    setAction('new');
    setVisible(true);
  };

  const makeActive = (data) => {
    updateAssetCall({ ...data, deleted: false });
  };

  const finishAdd = async (type) => {
    const values = await form.validateFields();
    const commonValues = buildCommonApiValues(context.profile);
    values.assetType = 'sanitary';
    if (action === 'new') {
      await saveAssetAction({ ...commonValues, ...values });
      if (type === 'save') setVisible(false);
      if (type === 'add') form.resetFields();
    }
    if (action === 'edit') {
      await updateAssetAction(values);
      setVisible(false);
    }
  };

  const saveAssetAction = async (asset) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    addAsset(asset)
      .then((res) => {
        const tempData = { ...res.data };
        tempData.status = tempData.status === 'true' ? true : false;
        setAssets((state) => [tempData, ...state]);
        message.success('Succesfully Added asset');
        form.resetFields();
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to add Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const editAssetAction = (asset) => {
    form.setFieldsValue({ ...asset });
    setAction('edit');
    setAsset(asset);
    setVisible(true);
  };

  const updateAssetCall = (values) => {
    const data = { ...asset, ...values, modifiedTime: new Date() };
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    updateAsset(data)
      .then((res) => {
        setAssets((state) => {
          const tempData = [...state];
          tempData.forEach((i) => {
            if (i.id === res.data.id) {
              Object.assign(i, {
                ...res.data,
                status: res.data.status === 'true' ? true : false,
              });
            }
          });
          return tempData;
        });
        message.success('Succesfully Updated asset');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to update Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  const updateAssetAction = async (values) => {
    updateAssetCall(values);
  };

  const setDeleteAssetAction = (assetId, visible) => {
    setAssets((state) => {
      const tempData = [...state];
      tempData.find((x) => x.id === assetId).visible = visible;
      tempData
        .filter((x) => x.id !== assetId)
        .forEach((u) => {
          u.visible = false;
        });
      return tempData;
    });
  };

  const deleteAssetAction = (assetId) => {
    setContext((state) => {
      return {
        ...state,
        isLoading: true,
      };
    });
    deleteAsset(assetId)
      .then(() => {
        setAssets((state) => {
          const tempState = [...state];
          tempState.find((x) => x.id === assetId).visible = false;
          tempState.find((x) => x.id === assetId).deleted = true;
          return [...state].filter((x) => x.id !== assetId);
        });
        message.success('Succesfully Deleted asset');
      })
      .catch((e) => {
        console.log(e);
        message.error('Unable to delete Asset, try again later');
      })
      .finally(() => {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      });
  };

  useEffect(() => {
    const init = async () => {
      setContext((state) => {
        return {
          ...state,
          isLoading: true,
        };
      });
      try {
        const assetsRes = await getAllAssets(context.profile.tenantId);
        const activeAssets = assetsRes.data.filter((asset) => asset.assetType === 'sanitary');
        const temp = activeAssets.map((x) => {
          x.category = x?.properties?.category;
          return { ...x };
        });
        setTableData(temp);
        setAssets(temp.filter((x) => x.deleted === false));
      } catch (e) {
        console.log('error', e);
      } finally {
        setContext((state) => {
          return {
            ...state,
            isLoading: false,
          };
        });
      }
    };
    init();
    // eslint-disable-next-line
  }, []);

  const closeAdd = () => {
    setVisible(false);
    if (action === 'edit') form.resetFields();
  };

  const [gender, setGender] = useState('male');
  const onChangeGender = (e) => {
    setGender(e.target.value);
  };

  const clearForm = () => {
    form.resetFields();
  };

  const tableCols = [
    { title: <strong> Name </strong>, key: 'name', dataIndex: 'name' },
    { title: <strong> Number </strong>, key: 'number', dataIndex: 'externalId' },
    {
      title: <strong> Category </strong>,
      render: (record) => capitalize(record.properties?.category),
    },
    {
      title: <strong> Gender </strong>,
      render: (record) => capitalize(record?.properties?.gender),
    },
    {
      title: <strong> Actions </strong>,
      width: 350,
      key: 'Actions',
      render: (record) =>
        record.deleted ? (
          <Row>
            <PermissionContainer page={Pages.WASHROOM} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => makeActive(record)} className="actionButton">
                Activate
              </Button>
            </PermissionContainer>
            <p className="lastModifiedDate">
              Last Modified on {new Date(record.modifiedTime).toLocaleDateString().toString()}
            </p>
          </Row>
        ) : (
          <>
            <PermissionContainer page={Pages.WASHROOM} permission={CRUD.UPDATE}>
              <Button type="link" onClick={() => editAssetAction(record)} className="actionButton">
                Edit
              </Button>
            </PermissionContainer>

            <PermissionContainer page={Pages.WASHROOM} permission={CRUD.DELETE}>
              <Popconfirm
                title={`Are you sure to delete asset ${record.name}?`}
                visible={record.visible || false}
                onConfirm={() => deleteAssetAction(record.id)}
                onCancel={() => setDeleteAssetAction(record.id, false)}
              >
                <Button type="link" onClick={() => setDeleteAssetAction(record.id, true)} className="actionButton">
                  Delete
                </Button>
              </Popconfirm>
            </PermissionContainer>
          </>
        ),
    },
  ];

  const sanitaryBreadcrumbsName = get(
    context.tenantProfile,
    `moduleNames[${Pages.WASHROOM}].displayName`,
    ModuleNames.SANITARY
  );

  return (
    <Spin spinning={context.isLoading}>
      <Row justify="space-between">
        <Col>
          <BreadcrumbList list={['Home', ModuleNames.MASTERS, sanitaryBreadcrumbsName, 'Register Sanitary']} />
        </Col>
        <Col>
          <Checkbox
            onChange={() => {
              setShowDeleted(!showDeleted);
            }}
          >
            Inactive
          </Checkbox>
          <PermissionContainer page={Pages.WASHROOM} permission={CRUD.ADD}>
            <Button type="link" className="commonAddButton actionButton" onClick={openAdd} icon={<PlusOutlined />}>
              Add Washroom
            </Button>
          </PermissionContainer>
        </Col>
      </Row>
      <>
        {!context.isCompact ? (
          <Table
            size="small"
            scroll={{ x: true }}
            rowKey="id"
            loading={context.isLoading}
            columns={tableCols}
            dataSource={assets}
            rowClassName={(record) => record.deleted && 'rowInactive'}
          />
        ) : (
          <CommonCompactView
            data={assets}
            onEdit={editAssetAction}
            onDelete={deleteAssetAction}
            permissions={[
              { pageName: Pages.WASHROOM, permission: CRUD.UPDATE, label: 'Edit' },
              { pageName: Pages.WASHROOM, permission: CRUD.DELETE, label: 'Delete' },
            ]}
            title="Washroom"
            dataList={[
              { label: 'Name', value: 'name' },
              { label: 'Category', value: 'category' },
            ]}
          />
        )}
      </>

      <CommonDrawer title="Washroom" visible={visible} closeAdd={closeAdd}>
        <Form
          initialValues={{}}
          scrollToFirstError={true}
          layout="horizontal"
          form={form}
          wrapperCol={{ span: 16 }}
          labelCol={{ span: 8 }}
        >
          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Name"
            name="name"
            rules={[
              {
                whitespace: true,
                min: 3,
                max: 30,
              },
              { pattern: InputRegex.Name, message: 'Use Letters only!' },
            ]}
          >
            <Input placeholder="Name" />
          </Form.Item>

          <Form.Item
            hasFeedback
            label="External ID"
            name="externalId"
            rules={[
              {
                required: true,
                whitespace: true,
                message: 'Please input tag external ID!',
                min: 1,
                max: 200,
              },
            ]}
          >
            <Input placeholder="Tag External ID" />
          </Form.Item>

          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Category"
            name={['properties', 'category']}
            rules={[
              {
                required: true,
                message: 'Please select Category!',
              },
            ]}
          >
            <Select
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
              placeholder="Category"
            >
              {Object.keys(sanitaryTypes).map((type) => (
                <Option title={sanitaryTypes[type]} key={sanitaryTypes[type]} value={sanitaryTypes[type]}>
                  {capitalize(sanitaryTypes[type])}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            shouldUpdate={true}
            hasFeedback
            label="Gender"
            name={['properties', 'gender']}
            rules={[
              {
                required: true,
                message: 'Please Select Gender!',
              },
            ]}
          >
            <Radio.Group onChange={onChangeGender} value={gender}>
              <Radio value="male">Male</Radio>
              <Radio value="female">Female</Radio>
              <Radio value="unisex">Unisex</Radio>
              <Radio value="others">PC</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            required={false}
            name="status"
            valuePropName="checked"
            label="Status"
            initialValue={false}
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Switch />
          </Form.Item>

          <Row gutter={6}>
            <Col xs={{ span: 12 }} lg={{ offset: 6, span: 9 }}>
              <Button block className="commonSaveButton formButton" onClick={() => finishAdd('save')}>
                Save
              </Button>
            </Col>
            <Col xs={{ span: 12 }} lg={{ span: 9 }}>
              <div>
                {action === 'new' && (
                  <Col>
                    <Button block onClick={() => finishAdd('add')}>
                      Save + 1
                    </Button>
                  </Col>
                )}
              </div>
            </Col>
          </Row>
          <Row className="footer" gutter={6}>
            <Col xs={{ span: 12 }} lg={{ offset: 6, span: 9 }}>
              <Button
                block
                className={['clearButton', !context.isCompact ? 'clear' : null]}
                onClick={() => clearForm()}
              >
                Clear
              </Button>
            </Col>
            <Col xs={{ span: 12 }} lg={{ span: 9 }}>
              <Button block danger type="primary" onClick={closeAdd}>
                Close
              </Button>
            </Col>
          </Row>
        </Form>
      </CommonDrawer>
    </Spin>
  );
};

export default Sanitary;
